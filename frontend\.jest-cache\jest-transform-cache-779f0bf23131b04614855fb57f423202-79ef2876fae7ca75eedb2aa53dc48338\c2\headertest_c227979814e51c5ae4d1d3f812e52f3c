290e4fd8e70eea6578792b43f9fc7d4b
"use strict";
// Mock Next.js navigation
jest.mock("next/navigation", ()=>({
        usePathname: jest.fn()
    }));
// Mock the dashboard store
jest.mock("@/stores/dashboard-store", ()=>({
        useDashboardStore: jest.fn(),
        useConnectionStatus: jest.fn(),
        useUnacknowledgedAlerts: jest.fn()
    }));
// Mock the main nav component
jest.mock("../main-nav", ()=>({
        MainNav: ({ onNavigate })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "main-nav",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                    onClick: onNavigate,
                    "data-testid": "nav-item",
                    children: "Dashboard"
                })
            })
    }));
// Mock UI components
jest.mock("@/components/ui/sheet", ()=>({
        Sheet: ({ children, open, onOpenChange })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "sheet",
                "data-open": open,
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    onClick: ()=>onOpenChange?.(false),
                    children: children
                })
            }),
        SheetContent: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "sheet-content",
                children: children
            }),
        SheetTrigger: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "sheet-trigger",
                children: children
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _header = require("../header");
const _dashboardstore = require("../../../stores/dashboard-store");
const _navigation = require("next/navigation");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock framer-motion
(0, _testutils.mockFramerMotion)();
describe("Header Component", ()=>{
    const mockOnMenuClick = jest.fn();
    beforeEach(()=>{
        jest.clearAllMocks();
        _navigation.usePathname.mockReturnValue("/dashboard");
        _dashboardstore.useDashboardStore.mockReturnValue({
            alerts: [],
            isConnected: true
        });
        // Mock individual hooks
        const { useConnectionStatus, useUnacknowledgedAlerts } = require("@/stores/dashboard-store");
        useConnectionStatus.mockReturnValue({
            isConnected: true,
            lastConnected: new Date(),
            reconnectAttempts: 0
        });
        useUnacknowledgedAlerts.mockReturnValue([]);
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {
            onMenuClick: mockOnMenuClick
        }));
        expect(_testutils.screen.getByRole("banner")).toBeInTheDocument();
        expect(_testutils.screen.getAllByText("UAV Control")).toHaveLength(2) // One in mobile menu, one in desktop
        ;
        expect(_testutils.screen.getByText("Management System")).toBeInTheDocument();
    });
    it("displays logo and branding", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        expect(_testutils.screen.getAllByText("UAV Control")).toHaveLength(2) // One in mobile menu, one in desktop
        ;
        expect(_testutils.screen.getByText("Management System")).toBeInTheDocument();
        // Check for Shield icon (logo)
        const logoLink = _testutils.screen.getByRole("link", {
            name: /uav control/i
        });
        expect(logoLink).toHaveAttribute("href", "/dashboard");
    });
    it("shows mobile menu trigger", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        expect(menuButton).toBeInTheDocument();
        expect(menuButton).toHaveClass("md:hidden");
    });
    it("handles mobile menu interaction", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        const sheet = _testutils.screen.getByTestId("sheet");
        // Initially closed
        expect(sheet).toHaveAttribute("data-open", "false");
        // Open mobile menu
        _testutils.fireEvent.click(menuButton);
        expect(sheet).toHaveAttribute("data-open", "true");
    });
    it("displays search functionality", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search/i);
        expect(searchInput).toBeInTheDocument();
    });
    it("handles search form submission", ()=>{
        const consoleSpy = jest.spyOn(console, "log").mockImplementation();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search/i);
        const searchForm = searchInput.closest("form");
        _testutils.fireEvent.change(searchInput, {
            target: {
                value: "UAV-001"
            }
        });
        _testutils.fireEvent.submit(searchForm);
        expect(consoleSpy).toHaveBeenCalledWith("Searching for:", "UAV-001");
        consoleSpy.mockRestore();
    });
    it("does not search with empty query", ()=>{
        const consoleSpy = jest.spyOn(console, "log").mockImplementation();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search/i);
        const searchForm = searchInput.closest("form");
        _testutils.fireEvent.submit(searchForm);
        expect(consoleSpy).not.toHaveBeenCalled();
        consoleSpy.mockRestore();
    });
    it("displays connection status", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        // Should show connected status by default
        const connectionIndicator = _testutils.screen.getByTestId("connection-status");
        expect(connectionIndicator).toBeInTheDocument();
    });
    it("shows disconnected state", ()=>{
        _dashboardstore.useDashboardStore.mockReturnValue({
            alerts: [],
            isConnected: false
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const connectionIndicator = _testutils.screen.getByTestId("connection-status");
        expect(connectionIndicator).toHaveClass("text-destructive");
    });
    it("displays notification badge with alert count", ()=>{
        _dashboardstore.useDashboardStore.mockReturnValue({
            alerts: [
                {
                    id: "1",
                    type: "ERROR",
                    acknowledged: false
                },
                {
                    id: "2",
                    type: "WARNING",
                    acknowledged: false
                }
            ],
            isConnected: true
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const notificationButton = _testutils.screen.getByRole("button", {
            name: /notifications/i
        });
        expect(notificationButton).toBeInTheDocument();
        const badge = _testutils.screen.getByText("2");
        expect(badge).toBeInTheDocument();
    });
    it("handles dark mode toggle", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const darkModeButton = _testutils.screen.getByRole("button", {
            name: /toggle theme/i
        });
        expect(darkModeButton).toBeInTheDocument();
        _testutils.fireEvent.click(darkModeButton);
    // Dark mode toggle functionality would be tested here
    });
    it("displays user menu", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const userMenuButton = _testutils.screen.getByRole("button", {
            name: /user menu/i
        });
        expect(userMenuButton).toBeInTheDocument();
    });
    it("shows page title based on pathname", ()=>{
        _navigation.usePathname.mockReturnValue("/uavs");
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        expect(_testutils.screen.getByText("UAV Management")).toBeInTheDocument();
    });
    it("handles different pathnames correctly", ()=>{
        const pathTitleMap = [
            [
                "/dashboard",
                "Dashboard"
            ],
            [
                "/uavs",
                "UAV Management"
            ],
            [
                "/map",
                "Map View"
            ],
            [
                "/hibernate-pod",
                "Hibernate Pod"
            ],
            [
                "/battery",
                "Battery Monitor"
            ]
        ];
        pathTitleMap.forEach(([path, title])=>{
            _navigation.usePathname.mockReturnValue(path);
            const { unmount } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
            expect(_testutils.screen.getByText(title)).toBeInTheDocument();
            unmount();
        });
    });
    it("calls onMenuClick when provided", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {
            onMenuClick: mockOnMenuClick
        }));
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        _testutils.fireEvent.click(menuButton);
        expect(mockOnMenuClick).toHaveBeenCalledTimes(1);
    });
    it("maintains accessibility standards", async ()=>{
        const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        // Check for proper header role
        expect(_testutils.screen.getByRole("banner")).toBeInTheDocument();
        // Check for proper button labels
        expect(_testutils.screen.getByRole("button", {
            name: /toggle menu/i
        })).toBeInTheDocument();
        expect(_testutils.screen.getByRole("button", {
            name: /toggle theme/i
        })).toBeInTheDocument();
        // Run accessibility tests
        await (0, _testutils.runAxeTest)(container);
    });
    it("handles keyboard navigation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search/i);
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        // Test tab navigation
        searchInput.focus();
        expect(searchInput).toHaveFocus();
        menuButton.focus();
        expect(menuButton).toHaveFocus();
    });
    it("supports responsive design", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        // Check responsive classes
        const header = _testutils.screen.getByRole("banner");
        expect(header).toHaveClass("sticky", "top-0", "z-50", "w-full");
        // Mobile menu should be hidden on desktop
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        expect(menuButton).toHaveClass("md:hidden");
        // Logo text should be hidden on small screens
        const logoText = _testutils.screen.getByText("UAV Control").parentElement;
        expect(logoText).toHaveClass("hidden", "sm:block");
    });
    it("closes mobile menu when navigation occurs", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        const sheet = _testutils.screen.getByTestId("sheet");
        // Open mobile menu
        _testutils.fireEvent.click(menuButton);
        expect(sheet).toHaveAttribute("data-open", "true");
        // Navigate (simulate clicking nav item)
        const navItem = _testutils.screen.getByTestId("nav-item");
        _testutils.fireEvent.click(navItem);
        // Menu should close
        expect(sheet).toHaveAttribute("data-open", "false");
    });
    it("handles search input changes", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search/i);
        _testutils.fireEvent.change(searchInput, {
            target: {
                value: "test query"
            }
        });
        expect(searchInput).toHaveValue("test query");
    });
    it("displays proper backdrop blur effect", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const header = _testutils.screen.getByRole("banner");
        expect(header).toHaveClass("bg-background/95", "backdrop-blur", "supports-[backdrop-filter]:bg-background/60");
    });
});

//# sourceMappingURL=data:application/json;base64,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