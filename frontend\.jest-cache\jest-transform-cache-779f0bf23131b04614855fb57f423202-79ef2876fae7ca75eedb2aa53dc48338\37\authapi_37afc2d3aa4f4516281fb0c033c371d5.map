{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\api\\auth-api.ts"], "sourcesContent": ["import apiClient from '@/lib/api-client'\nimport {\n  User,\n  LoginRequest,\n  LoginResponse,\n  RegisterRequest,\n  RegisterResponse,\n  RefreshTokenRequest,\n  RefreshTokenResponse,\n  ChangePasswordRequest,\n  ForgotPasswordRequest,\n  ResetPasswordRequest,\n} from '@/types/auth'\n\nexport class AuthApi {\n  private basePath = '/api/auth'\n\n  // Authentication endpoints\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    return apiClient.post<LoginResponse>(`${this.basePath}/login`, credentials)\n  }\n\n  async logout(): Promise<void> {\n    return apiClient.post(`${this.basePath}/logout`)\n  }\n\n  async register(userData: RegisterRequest): Promise<RegisterResponse> {\n    return apiClient.post<RegisterResponse>(`${this.basePath}/register`, userData)\n  }\n\n  async refreshToken(request: RefreshTokenRequest): Promise<RefreshTokenResponse> {\n    return apiClient.post<RefreshTokenResponse>(`${this.basePath}/refresh`, request)\n  }\n\n  async validateToken(): Promise<boolean> {\n    try {\n      await apiClient.get(`${this.basePath}/validate`)\n      return true\n    } catch (error) {\n      return false\n    }\n  }\n\n  // Password management\n  async changePassword(passwords: ChangePasswordRequest): Promise<{ success: boolean; message: string }> {\n    return apiClient.post(`${this.basePath}/change-password`, passwords)\n  }\n\n  async forgotPassword(request: ForgotPasswordRequest): Promise<{ success: boolean; message: string }> {\n    return apiClient.post(`${this.basePath}/forgot-password`, request)\n  }\n\n  async resetPassword(request: ResetPasswordRequest): Promise<{ success: boolean; message: string }> {\n    return apiClient.post(`${this.basePath}/reset-password`, request)\n  }\n\n  async resendVerificationEmail(request: { email: string }): Promise<{ success: boolean; message: string }> {\n    return apiClient.post(`${this.basePath}/resend-verification`, request)\n  }\n\n  // User profile\n  async getUserProfile(): Promise<User> {\n    return apiClient.get<User>(`${this.basePath}/profile`)\n  }\n\n  async updateProfile(userData: Partial<User>): Promise<{ success: boolean; message: string; data?: User }> {\n    return apiClient.put(`${this.basePath}/profile`, userData)\n  }\n\n  // Session management\n  async checkSession(): Promise<{ success: boolean; data?: { isValid: boolean; user?: User } }> {\n    return apiClient.get(`${this.basePath}/session`)\n  }\n\n  async getSessions(): Promise<any[]> {\n    return apiClient.get(`${this.basePath}/sessions`)\n  }\n\n  async terminateSession(sessionId: string): Promise<void> {\n    return apiClient.delete(`${this.basePath}/sessions/${sessionId}`)\n  }\n\n  async terminateAllSessions(): Promise<void> {\n    return apiClient.delete(`${this.basePath}/sessions`)\n  }\n\n  // Two-factor authentication\n  async setupTwoFactor(): Promise<any> {\n    return apiClient.post(`${this.basePath}/2fa/setup`)\n  }\n\n  async verifyTwoFactor(code: string): Promise<{ success: boolean; message: string }> {\n    return apiClient.post(`${this.basePath}/2fa/verify`, { code })\n  }\n\n  async disableTwoFactor(code: string): Promise<{ success: boolean; message: string }> {\n    return apiClient.post(`${this.basePath}/2fa/disable`, { code })\n  }\n\n  // OAuth\n  async oauthLogin(provider: string, code: string, state: string): Promise<LoginResponse> {\n    return apiClient.post<LoginResponse>(`${this.basePath}/oauth/${provider}`, { code, state })\n  }\n\n  // Security\n  async getSecurityEvents(): Promise<any[]> {\n    return apiClient.get(`${this.basePath}/security/events`)\n  }\n\n  async reportSecurityIncident(incident: any): Promise<void> {\n    return apiClient.post(`${this.basePath}/security/incident`, incident)\n  }\n\n  // Token management\n  setAuthToken(token: string) {\n    apiClient.setAuthToken(token)\n  }\n\n  clearAuthToken() {\n    apiClient.clearAuthToken()\n  }\n}\n\n// Create singleton instance\nexport const authApi = new AuthApi()\n\nexport default authApi\n"], "names": ["AuthA<PERSON>", "authApi", "login", "credentials", "apiClient", "post", "basePath", "logout", "register", "userData", "refreshToken", "request", "validateToken", "get", "error", "changePassword", "passwords", "forgotPassword", "resetPassword", "resendVerificationEmail", "getUserProfile", "updateProfile", "put", "checkSession", "getSessions", "terminateSession", "sessionId", "delete", "terminateAllSessions", "setupTwoFactor", "verifyTwoFactor", "code", "disableTwoFactor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "provider", "state", "getSecurityEvents", "reportSecurityIncident", "incident", "setAuthToken", "token", "clearAuthToken"], "mappings": ";;;;;;;;;;;IAcaA,OAAO;eAAPA;;IA8GAC,OAAO;eAAPA;;IAEb,OAAsB;eAAtB;;;kEA9HsB;;;;;;AAcf,MAAMD;IAGX,2BAA2B;IAC3B,MAAME,MAAMC,WAAyB,EAA0B;QAC7D,OAAOC,kBAAS,CAACC,IAAI,CAAgB,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAEH;IACjE;IAEA,MAAMI,SAAwB;QAC5B,OAAOH,kBAAS,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC;IACjD;IAEA,MAAME,SAASC,QAAyB,EAA6B;QACnE,OAAOL,kBAAS,CAACC,IAAI,CAAmB,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAEG;IACvE;IAEA,MAAMC,aAAaC,OAA4B,EAAiC;QAC9E,OAAOP,kBAAS,CAACC,IAAI,CAAuB,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAEK;IAC1E;IAEA,MAAMC,gBAAkC;QACtC,IAAI;YACF,MAAMR,kBAAS,CAACS,GAAG,CAAC,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,SAAS,CAAC;YAC/C,OAAO;QACT,EAAE,OAAOQ,OAAO;YACd,OAAO;QACT;IACF;IAEA,sBAAsB;IACtB,MAAMC,eAAeC,SAAgC,EAAkD;QACrG,OAAOZ,kBAAS,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAEU;IAC5D;IAEA,MAAMC,eAAeN,OAA8B,EAAkD;QACnG,OAAOP,kBAAS,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAEK;IAC5D;IAEA,MAAMO,cAAcP,OAA6B,EAAkD;QACjG,OAAOP,kBAAS,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAEK;IAC3D;IAEA,MAAMQ,wBAAwBR,OAA0B,EAAkD;QACxG,OAAOP,kBAAS,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,oBAAoB,CAAC,EAAEK;IAChE;IAEA,eAAe;IACf,MAAMS,iBAAgC;QACpC,OAAOhB,kBAAS,CAACS,GAAG,CAAO,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,QAAQ,CAAC;IACvD;IAEA,MAAMe,cAAcZ,QAAuB,EAA+D;QACxG,OAAOL,kBAAS,CAACkB,GAAG,CAAC,CAAC,EAAE,IAAI,CAAChB,QAAQ,CAAC,QAAQ,CAAC,EAAEG;IACnD;IAEA,qBAAqB;IACrB,MAAMc,eAAwF;QAC5F,OAAOnB,kBAAS,CAACS,GAAG,CAAC,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,QAAQ,CAAC;IACjD;IAEA,MAAMkB,cAA8B;QAClC,OAAOpB,kBAAS,CAACS,GAAG,CAAC,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,SAAS,CAAC;IAClD;IAEA,MAAMmB,iBAAiBC,SAAiB,EAAiB;QACvD,OAAOtB,kBAAS,CAACuB,MAAM,CAAC,CAAC,EAAE,IAAI,CAACrB,QAAQ,CAAC,UAAU,EAAEoB,UAAU,CAAC;IAClE;IAEA,MAAME,uBAAsC;QAC1C,OAAOxB,kBAAS,CAACuB,MAAM,CAAC,CAAC,EAAE,IAAI,CAACrB,QAAQ,CAAC,SAAS,CAAC;IACrD;IAEA,4BAA4B;IAC5B,MAAMuB,iBAA+B;QACnC,OAAOzB,kBAAS,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC;IACpD;IAEA,MAAMwB,gBAAgBC,IAAY,EAAkD;QAClF,OAAO3B,kBAAS,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;YAAEyB;QAAK;IAC9D;IAEA,MAAMC,iBAAiBD,IAAY,EAAkD;QACnF,OAAO3B,kBAAS,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;YAAEyB;QAAK;IAC/D;IAEA,QAAQ;IACR,MAAME,WAAWC,QAAgB,EAAEH,IAAY,EAAEI,KAAa,EAA0B;QACtF,OAAO/B,kBAAS,CAACC,IAAI,CAAgB,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,OAAO,EAAE4B,SAAS,CAAC,EAAE;YAAEH;YAAMI;QAAM;IAC3F;IAEA,WAAW;IACX,MAAMC,oBAAoC;QACxC,OAAOhC,kBAAS,CAACS,GAAG,CAAC,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,gBAAgB,CAAC;IACzD;IAEA,MAAM+B,uBAAuBC,QAAa,EAAiB;QACzD,OAAOlC,kBAAS,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAEgC;IAC9D;IAEA,mBAAmB;IACnBC,aAAaC,KAAa,EAAE;QAC1BpC,kBAAS,CAACmC,YAAY,CAACC;IACzB;IAEAC,iBAAiB;QACfrC,kBAAS,CAACqC,cAAc;IAC1B;;aAzGQnC,WAAW;;AA0GrB;AAGO,MAAML,UAAU,IAAID;MAE3B,WAAeC"}