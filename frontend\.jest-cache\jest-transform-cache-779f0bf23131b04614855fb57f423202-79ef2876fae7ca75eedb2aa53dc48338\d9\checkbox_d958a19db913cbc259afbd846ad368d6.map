{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\checkbox.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": ["Checkbox", "React", "forwardRef", "className", "props", "ref", "CheckboxPrimitive", "Root", "cn", "Indicator", "Check", "displayName"], "mappings": ";;;;+BA2BSA;;;eAAAA;;;;+DA3Bc;uEACY;6BACb;uBAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnB,MAAMA,yBAAWC,OAAMC,UAAU,CAG/B,CAAC,EAAEC,SAAS,EAAE,GAAGC,OAAO,EAAEC,oBAC1B,qBAACC,eAAkBC,IAAI;QACrBF,KAAKA;QACLF,WAAWK,IAAAA,SAAE,EACX,kTACAL;QAED,GAAGC,KAAK;kBAET,cAAA,qBAACE,eAAkBG,SAAS;YAC1BN,WAAWK,IAAAA,SAAE,EAAC;sBAEd,cAAA,qBAACE,kBAAK;gBAACP,WAAU;;;;AAIvBH,SAASW,WAAW,GAAGL,eAAkBC,IAAI,CAACI,WAAW"}