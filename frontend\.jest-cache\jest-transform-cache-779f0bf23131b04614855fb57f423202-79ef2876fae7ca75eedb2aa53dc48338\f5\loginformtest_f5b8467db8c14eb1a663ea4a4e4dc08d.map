{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\auth\\__tests__\\login-form.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { LoginForm } from '../login-form'\nimport { useAuthStore } from '@/stores/auth-store'\nimport { createMockUser, runAxeTest } from '@/lib/test-utils'\nimport { useRouter } from 'next/navigation'\n\n// Mock Next.js router\njest.mock('next/navigation', () => ({\n  useRouter: jest.fn(),\n}))\n\n// Mock the auth store\njest.mock('@/stores/auth-store')\nconst mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>\n\n// Mock react-hot-toast\njest.mock('react-hot-toast', () => ({\n  toast: {\n    success: jest.fn(),\n    error: jest.fn(),\n  },\n}))\n\ndescribe('LoginForm Component', () => {\n  const mockPush = jest.fn()\n  const mockLogin = jest.fn()\n  const mockClearError = jest.fn()\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n    \n    // Mock router\n    ;(useRouter as jest.Mock).mockReturnValue({\n      push: mockPush,\n      replace: jest.fn(),\n      prefetch: jest.fn(),\n    })\n\n    // Mock auth store\n    mockUseAuthStore.mockReturnValue({\n      login: mockLogin,\n      isLoading: false,\n      error: null,\n      clearError: mockClearError,\n      user: null,\n      token: null,\n      refreshToken: null,\n      isAuthenticated: false,\n      logout: jest.fn(),\n      register: jest.fn(),\n      refreshToken: jest.fn(),\n      changePassword: jest.fn(),\n      updateProfile: jest.fn(),\n      hasPermission: jest.fn(),\n      hasRole: jest.fn(),\n      canAccess: jest.fn(),\n      checkSession: jest.fn(),\n      setLoading: jest.fn(),\n      fetchUserProfile: jest.fn(),\n      updateLastActivity: jest.fn(),\n    })\n  })\n\n  it('renders correctly', () => {\n    render(<LoginForm />)\n    \n    expect(screen.getByText('UAV Control System')).toBeInTheDocument()\n    expect(screen.getByText('Sign in to access the UAV management dashboard')).toBeInTheDocument()\n    expect(screen.getByLabelText('Username')).toBeInTheDocument()\n    expect(screen.getByLabelText('Password')).toBeInTheDocument()\n    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()\n  })\n\n  it('handles form submission with valid data', async () => {\n    mockLogin.mockResolvedValue(true)\n    \n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText('Username')\n    const passwordInput = screen.getByLabelText('Password')\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(mockClearError).toHaveBeenCalled()\n      expect(mockLogin).toHaveBeenCalledWith({\n        username: 'testuser',\n        password: 'password123',\n        rememberMe: false,\n      })\n    })\n  })\n\n  it('redirects to dashboard on successful login', async () => {\n    mockLogin.mockResolvedValue(true)\n    \n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText('Username')\n    const passwordInput = screen.getByLabelText('Password')\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(mockPush).toHaveBeenCalledWith('/dashboard')\n    })\n  })\n\n  it('calls onSuccess callback when provided', async () => {\n    const mockOnSuccess = jest.fn()\n    mockLogin.mockResolvedValue(true)\n    \n    render(<LoginForm onSuccess={mockOnSuccess} />)\n    \n    const usernameInput = screen.getByLabelText('Username')\n    const passwordInput = screen.getByLabelText('Password')\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(mockOnSuccess).toHaveBeenCalled()\n      expect(mockPush).not.toHaveBeenCalled()\n    })\n  })\n\n  it('redirects to custom path when provided', async () => {\n    mockLogin.mockResolvedValue(true)\n    \n    render(<LoginForm redirectTo=\"/custom-path\" />)\n    \n    const usernameInput = screen.getByLabelText('Username')\n    const passwordInput = screen.getByLabelText('Password')\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(mockPush).toHaveBeenCalledWith('/custom-path')\n    })\n  })\n\n  it('handles remember me checkbox', async () => {\n    mockLogin.mockResolvedValue(true)\n    \n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText('Username')\n    const passwordInput = screen.getByLabelText('Password')\n    const rememberMeCheckbox = screen.getByLabelText(/remember me/i)\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(rememberMeCheckbox)\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(mockLogin).toHaveBeenCalledWith({\n        username: 'testuser',\n        password: 'password123',\n        rememberMe: true,\n      })\n    })\n  })\n\n  it('toggles password visibility', () => {\n    render(<LoginForm />)\n\n    const passwordInput = screen.getByLabelText('Password')\n    const toggleButton = screen.getByRole('button', { name: /show password/i })\n\n    expect(passwordInput).toHaveAttribute('type', 'password')\n\n    fireEvent.click(toggleButton)\n    expect(passwordInput).toHaveAttribute('type', 'text')\n    expect(screen.getByRole('button', { name: /hide password/i })).toBeInTheDocument()\n\n    fireEvent.click(toggleButton)\n    expect(passwordInput).toHaveAttribute('type', 'password')\n  })\n\n  it('displays loading state during login', () => {\n    mockUseAuthStore.mockReturnValue({\n      ...mockUseAuthStore(),\n      isLoading: true,\n    })\n    \n    render(<LoginForm />)\n    \n    const submitButton = screen.getByRole('button', { name: /signing in/i })\n    expect(submitButton).toBeDisabled()\n    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()\n  })\n\n  it('displays error message when login fails', () => {\n    const errorMessage = 'Invalid credentials'\n    mockUseAuthStore.mockReturnValue({\n      ...mockUseAuthStore(),\n      error: errorMessage,\n    })\n    \n    render(<LoginForm />)\n    \n    expect(screen.getByText(errorMessage)).toBeInTheDocument()\n    expect(screen.getByRole('alert')).toBeInTheDocument()\n  })\n\n  it('validates required fields', async () => {\n    render(<LoginForm />)\n    \n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(screen.getByText(/username is required/i)).toBeInTheDocument()\n      expect(screen.getByText(/password is required/i)).toBeInTheDocument()\n    })\n    \n    expect(mockLogin).not.toHaveBeenCalled()\n  })\n\n  it('validates required username', async () => {\n    render(<LoginForm />)\n\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n\n    // Submit without entering username\n    fireEvent.click(submitButton)\n\n    await waitFor(() => {\n      expect(screen.getByText('Username is required')).toBeInTheDocument()\n    })\n  })\n\n  it('validates required password', async () => {\n    render(<LoginForm />)\n\n    const usernameInput = screen.getByLabelText('Username')\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n\n    // Enter username but not password\n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.click(submitButton)\n\n    await waitFor(() => {\n      expect(screen.getByText('Password is required')).toBeInTheDocument()\n    })\n  })\n\n  it('displays error messages', () => {\n    mockUseAuthStore.mockReturnValue({\n      ...mockUseAuthStore(),\n      error: 'Invalid credentials',\n    })\n\n    render(<LoginForm />)\n\n    expect(screen.getByText('Invalid credentials')).toBeInTheDocument()\n    expect(screen.getByRole('alert')).toBeInTheDocument()\n  })\n\n  it('handles keyboard navigation', () => {\n    render(<LoginForm />)\n\n    const usernameInput = screen.getByLabelText('Username')\n    const passwordInput = screen.getByLabelText('Password')\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n\n    // Test that elements can receive focus\n    usernameInput.focus()\n    expect(usernameInput).toHaveFocus()\n\n    passwordInput.focus()\n    expect(passwordInput).toHaveFocus()\n\n    submitButton.focus()\n    expect(submitButton).toHaveFocus()\n  })\n\n  it('handles form submission', async () => {\n    mockLogin.mockResolvedValue(true)\n\n    render(<LoginForm />)\n\n    const usernameInput = screen.getByLabelText('Username')\n    const passwordInput = screen.getByLabelText('Password')\n    const form = document.querySelector('form')\n\n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n\n    // Submit the form directly\n    if (form) {\n      fireEvent.submit(form)\n\n      await waitFor(() => {\n        expect(mockLogin).toHaveBeenCalled()\n      })\n    }\n  })\n\n  it('maintains accessibility standards', async () => {\n    const { container } = render(<LoginForm />)\n    \n    // Check for proper labeling\n    expect(screen.getByLabelText('Username')).toBeInTheDocument()\n    expect(screen.getByLabelText('Password')).toBeInTheDocument()\n    \n    // Check for form structure\n    expect(container.querySelector('form')).toBeInTheDocument()\n    \n    // Run accessibility tests\n    await runAxeTest(container)\n  })\n\n  it('has proper input types', () => {\n    render(<LoginForm />)\n\n    const usernameInput = screen.getByLabelText('Username')\n    const passwordInput = screen.getByLabelText('Password')\n\n    expect(usernameInput).toHaveAttribute('type', 'text')\n    expect(passwordInput).toHaveAttribute('type', 'password')\n  })\n\n  it('disables submit button during loading', () => {\n    mockUseAuthStore.mockReturnValue({\n      ...mockUseAuthStore(),\n      isLoading: true,\n    })\n\n    render(<LoginForm />)\n\n    const submitButton = screen.getByRole('button', { name: /signing in/i })\n    expect(submitButton).toBeDisabled()\n    expect(screen.getByText('Signing in...')).toBeInTheDocument()\n  })\n\n  it('handles network errors gracefully', async () => {\n    mockLogin.mockResolvedValue(false)\n    mockUseAuthStore.mockReturnValue({\n      ...mockUseAuthStore(),\n      error: 'Network error',\n    })\n    \n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText('Username')\n    const passwordInput = screen.getByLabelText('Password')\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(screen.getByText('Network error')).toBeInTheDocument()\n      expect(mockPush).not.toHaveBeenCalled()\n    })\n  })\n})\n"], "names": ["jest", "mock", "useRouter", "fn", "toast", "success", "error", "mockUseAuthStore", "useAuthStore", "describe", "mockPush", "mockLogin", "mockClearError", "beforeEach", "clearAllMocks", "mockReturnValue", "push", "replace", "prefetch", "login", "isLoading", "clearError", "user", "token", "refreshToken", "isAuthenticated", "logout", "register", "changePassword", "updateProfile", "hasPermission", "hasRole", "canAccess", "checkSession", "setLoading", "fetchUserProfile", "updateLastActivity", "it", "render", "LoginForm", "expect", "screen", "getByText", "toBeInTheDocument", "getByLabelText", "getByRole", "name", "mockResolvedValue", "usernameInput", "passwordInput", "submitButton", "fireEvent", "change", "target", "value", "click", "waitFor", "toHaveBeenCalled", "toHaveBeenCalledWith", "username", "password", "rememberMe", "mockOnSuccess", "onSuccess", "not", "redirectTo", "rememberMeCheckbox", "to<PERSON><PERSON><PERSON><PERSON>", "toHaveAttribute", "toBeDisabled", "getByTestId", "errorMessage", "focus", "toHaveFocus", "form", "document", "querySelector", "submit", "container", "runAxeTest"], "mappings": ";AAOA,sBAAsB;AACtBA,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC,WAAWF,KAAKG,EAAE;IACpB,CAAA;AAEA,sBAAsB;AACtBH,KAAKC,IAAI,CAAC;AAGV,uBAAuB;AACvBD,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCG,OAAO;YACLC,SAASL,KAAKG,EAAE;YAChBG,OAAON,KAAKG,EAAE;QAChB;IACF,CAAA;;;;;8DAtBkB;2BACiC;2BACzB;2BACG;4BAEH;;;;;;AAS1B,MAAMI,mBAAmBC,uBAAY;AAUrCC,SAAS,uBAAuB;IAC9B,MAAMC,WAAWV,KAAKG,EAAE;IACxB,MAAMQ,YAAYX,KAAKG,EAAE;IACzB,MAAMS,iBAAiBZ,KAAKG,EAAE;IAE9BU,WAAW;QACTb,KAAKc,aAAa;QAGhBZ,qBAAS,CAAea,eAAe,CAAC;YACxCC,MAAMN;YACNO,SAASjB,KAAKG,EAAE;YAChBe,UAAUlB,KAAKG,EAAE;QACnB;QAEA,kBAAkB;QAClBI,iBAAiBQ,eAAe,CAAC;YAC/BI,OAAOR;YACPS,WAAW;YACXd,OAAO;YACPe,YAAYT;YACZU,MAAM;YACNC,OAAO;YACPC,cAAc;YACdC,iBAAiB;YACjBC,QAAQ1B,KAAKG,EAAE;YACfwB,UAAU3B,KAAKG,EAAE;YACjBqB,cAAcxB,KAAKG,EAAE;YACrByB,gBAAgB5B,KAAKG,EAAE;YACvB0B,eAAe7B,KAAKG,EAAE;YACtB2B,eAAe9B,KAAKG,EAAE;YACtB4B,SAAS/B,KAAKG,EAAE;YAChB6B,WAAWhC,KAAKG,EAAE;YAClB8B,cAAcjC,KAAKG,EAAE;YACrB+B,YAAYlC,KAAKG,EAAE;YACnBgC,kBAAkBnC,KAAKG,EAAE;YACzBiC,oBAAoBpC,KAAKG,EAAE;QAC7B;IACF;IAEAkC,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjBC,OAAOC,iBAAM,CAACC,SAAS,CAAC,uBAAuBC,iBAAiB;QAChEH,OAAOC,iBAAM,CAACC,SAAS,CAAC,mDAAmDC,iBAAiB;QAC5FH,OAAOC,iBAAM,CAACG,cAAc,CAAC,aAAaD,iBAAiB;QAC3DH,OAAOC,iBAAM,CAACG,cAAc,CAAC,aAAaD,iBAAiB;QAC3DH,OAAOC,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW,IAAIH,iBAAiB;IAC5E;IAEAN,GAAG,2CAA2C;QAC5C1B,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAO5B,gBAAgB6C,gBAAgB;YACvCjB,OAAO7B,WAAW+C,oBAAoB,CAAC;gBACrCC,UAAU;gBACVC,UAAU;gBACVC,YAAY;YACd;QACF;IACF;IAEAxB,GAAG,8CAA8C;QAC/C1B,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAO9B,UAAUgD,oBAAoB,CAAC;QACxC;IACF;IAEArB,GAAG,0CAA0C;QAC3C,MAAMyB,gBAAgB9D,KAAKG,EAAE;QAC7BQ,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;YAACwB,WAAWD;;QAE7B,MAAMd,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAOsB,eAAeL,gBAAgB;YACtCjB,OAAO9B,UAAUsD,GAAG,CAACP,gBAAgB;QACvC;IACF;IAEApB,GAAG,0CAA0C;QAC3C1B,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;YAAC0B,YAAW;;QAE7B,MAAMjB,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAO9B,UAAUgD,oBAAoB,CAAC;QACxC;IACF;IAEArB,GAAG,gCAAgC;QACjC1B,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMsB,qBAAqBzB,iBAAM,CAACG,cAAc,CAAC;QACjD,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACW;QAChBf,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAO7B,WAAW+C,oBAAoB,CAAC;gBACrCC,UAAU;gBACVC,UAAU;gBACVC,YAAY;YACd;QACF;IACF;IAEAxB,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMU,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMuB,eAAe1B,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAiB;QAEzEN,OAAOS,eAAemB,eAAe,CAAC,QAAQ;QAE9CjB,oBAAS,CAACI,KAAK,CAACY;QAChB3B,OAAOS,eAAemB,eAAe,CAAC,QAAQ;QAC9C5B,OAAOC,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAiB,IAAIH,iBAAiB;QAEhFQ,oBAAS,CAACI,KAAK,CAACY;QAChB3B,OAAOS,eAAemB,eAAe,CAAC,QAAQ;IAChD;IAEA/B,GAAG,uCAAuC;QACxC9B,iBAAiBQ,eAAe,CAAC;YAC/B,GAAGR,kBAAkB;YACrBa,WAAW;QACb;QAEAkB,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMW,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAc;QACtEN,OAAOU,cAAcmB,YAAY;QACjC7B,OAAOC,iBAAM,CAAC6B,WAAW,CAAC,oBAAoB3B,iBAAiB;IACjE;IAEAN,GAAG,2CAA2C;QAC5C,MAAMkC,eAAe;QACrBhE,iBAAiBQ,eAAe,CAAC;YAC/B,GAAGR,kBAAkB;YACrBD,OAAOiE;QACT;QAEAjC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjBC,OAAOC,iBAAM,CAACC,SAAS,CAAC6B,eAAe5B,iBAAiB;QACxDH,OAAOC,iBAAM,CAACI,SAAS,CAAC,UAAUF,iBAAiB;IACrD;IAEAN,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMW,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QACnEK,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAOC,iBAAM,CAACC,SAAS,CAAC,0BAA0BC,iBAAiB;YACnEH,OAAOC,iBAAM,CAACC,SAAS,CAAC,0BAA0BC,iBAAiB;QACrE;QAEAH,OAAO7B,WAAWqD,GAAG,CAACP,gBAAgB;IACxC;IAEApB,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMW,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnE,mCAAmC;QACnCK,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAOC,iBAAM,CAACC,SAAS,CAAC,yBAAyBC,iBAAiB;QACpE;IACF;IAEAN,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnE,kCAAkC;QAClCK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAOC,iBAAM,CAACC,SAAS,CAAC,yBAAyBC,iBAAiB;QACpE;IACF;IAEAN,GAAG,2BAA2B;QAC5B9B,iBAAiBQ,eAAe,CAAC;YAC/B,GAAGR,kBAAkB;YACrBD,OAAO;QACT;QAEAgC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjBC,OAAOC,iBAAM,CAACC,SAAS,CAAC,wBAAwBC,iBAAiB;QACjEH,OAAOC,iBAAM,CAACI,SAAS,CAAC,UAAUF,iBAAiB;IACrD;IAEAN,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnE,uCAAuC;QACvCE,cAAcwB,KAAK;QACnBhC,OAAOQ,eAAeyB,WAAW;QAEjCxB,cAAcuB,KAAK;QACnBhC,OAAOS,eAAewB,WAAW;QAEjCvB,aAAasB,KAAK;QAClBhC,OAAOU,cAAcuB,WAAW;IAClC;IAEApC,GAAG,2BAA2B;QAC5B1B,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAM8B,OAAOC,SAASC,aAAa,CAAC;QAEpCzB,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QAEnE,2BAA2B;QAC3B,IAAIoB,MAAM;YACRvB,oBAAS,CAAC0B,MAAM,CAACH;YAEjB,MAAMlB,IAAAA,kBAAO,EAAC;gBACZhB,OAAO7B,WAAW8C,gBAAgB;YACpC;QACF;IACF;IAEApB,GAAG,qCAAqC;QACtC,MAAM,EAAEyC,SAAS,EAAE,GAAGxC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEvC,4BAA4B;QAC5BC,OAAOC,iBAAM,CAACG,cAAc,CAAC,aAAaD,iBAAiB;QAC3DH,OAAOC,iBAAM,CAACG,cAAc,CAAC,aAAaD,iBAAiB;QAE3D,2BAA2B;QAC3BH,OAAOsC,UAAUF,aAAa,CAAC,SAASjC,iBAAiB;QAEzD,0BAA0B;QAC1B,MAAMoC,IAAAA,qBAAU,EAACD;IACnB;IAEAzC,GAAG,0BAA0B;QAC3BC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAE5CJ,OAAOQ,eAAeoB,eAAe,CAAC,QAAQ;QAC9C5B,OAAOS,eAAemB,eAAe,CAAC,QAAQ;IAChD;IAEA/B,GAAG,yCAAyC;QAC1C9B,iBAAiBQ,eAAe,CAAC;YAC/B,GAAGR,kBAAkB;YACrBa,WAAW;QACb;QAEAkB,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMW,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAc;QACtEN,OAAOU,cAAcmB,YAAY;QACjC7B,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;IAC7D;IAEAN,GAAG,qCAAqC;QACtC1B,UAAUoC,iBAAiB,CAAC;QAC5BxC,iBAAiBQ,eAAe,CAAC;YAC/B,GAAGR,kBAAkB;YACrBD,OAAO;QACT;QAEAgC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC3DH,OAAO9B,UAAUsD,GAAG,CAACP,gBAAgB;QACvC;IACF;AACF"}