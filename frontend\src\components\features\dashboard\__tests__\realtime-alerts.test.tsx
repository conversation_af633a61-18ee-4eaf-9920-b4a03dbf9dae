import React from 'react'
import { render, screen, fireEvent, waitFor } from '@/lib/test-utils'
import { RealtimeAlerts } from '../realtime-alerts'
import { useDashboardStore } from '@/stores/dashboard-store'
import { createMockAlert } from '@/lib/test-utils'
import { SystemAlert } from '@/types/uav'

// Mock the dashboard store
jest.mock('@/stores/dashboard-store', () => ({
  useDashboardStore: jest.fn(),
  useAlerts: jest.fn(),
}))
const mockUseDashboardStore = useDashboardStore as jest.MockedFunction<typeof useDashboardStore>
const { useAlerts } = require('@/stores/dashboard-store')
const mockUseAlerts = useAlerts as jest.MockedFunction<typeof useAlerts>

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    li: ({ children, ...props }: any) => <li {...props}>{children}</li>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock animated components
jest.mock('@/components/ui/animated-alert', () => ({
  AnimatedAlert: ({ children, ...props }: any) => (
    <div data-testid="animated-alert" {...props}>
      {children}
    </div>
  ),
  RealtimeAlerts: ({ children }: any) => (
    <div data-testid="realtime-alerts-container">
      {children}
    </div>
  ),
}))

jest.mock('@/components/ui/animated-components', () => ({
  StaggerContainer: ({ children }: any) => (
    <div data-testid="stagger-container">{children}</div>
  ),
  StaggerItem: ({ children }: any) => (
    <div data-testid="stagger-item">{children}</div>
  ),
}))

describe('RealtimeAlerts Component', () => {
  const mockAlerts: SystemAlert[] = [
    createMockAlert({
      id: 1,
      type: 'ERROR',
      title: 'Critical Battery',
      message: 'UAV-001 battery level is critically low (5%)',
      severity: 'HIGH',
      acknowledged: false,
      timestamp: '2024-01-01T10:00:00Z',
    }),
    createMockAlert({
      id: 2,
      type: 'WARNING',
      title: 'Maintenance Required',
      message: 'UAV-002 requires scheduled maintenance',
      severity: 'MEDIUM',
      acknowledged: false,
      timestamp: '2024-01-01T09:30:00Z',
    }),
    createMockAlert({
      id: 3,
      type: 'INFO',
      title: 'Flight Completed',
      message: 'UAV-003 has successfully completed mission Alpha',
      severity: 'LOW',
      acknowledged: true,
      timestamp: '2024-01-01T09:00:00Z',
    }),
  ]

  const mockAcknowledgeAlert = jest.fn()
  const mockRemoveAlert = jest.fn()
  const mockClearAlerts = jest.fn()
  const mockToggleAlerts = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock useAlerts hook
    mockUseAlerts.mockReturnValue(mockAlerts)

    mockUseDashboardStore.mockReturnValue({
      alerts: mockAlerts,
      showAlerts: true,
      acknowledgeAlert: mockAcknowledgeAlert,
      removeAlert: mockRemoveAlert,
      clearAlerts: mockClearAlerts,
      toggleAlerts: mockToggleAlerts,
      addAlert: jest.fn(),
      getAlertCounts: jest.fn(() => ({
        total: 3,
        error: 1,
        warning: 1,
        info: 1,
        high: 1,
        medium: 1,
        low: 1,
      })),
      getUnacknowledgedAlerts: jest.fn(() => mockAlerts.filter(a => !a.acknowledged)),
      // Other store properties
      metrics: null,
      flightActivity: null,
      batteryStats: null,
      hibernatePodMetrics: null,
      chartData: null,
      recentLocationUpdates: [],
      isConnected: true,
      lastUpdate: null,
      connectionError: null,
      selectedTimeRange: '24h',
      autoRefresh: true,
      refreshInterval: 30,
      updateMetrics: jest.fn(),
      updateFlightActivity: jest.fn(),
      updateBatteryStats: jest.fn(),
      updateHibernatePodMetrics: jest.fn(),
      updateChartData: jest.fn(),
      addAlert: jest.fn(),
      addLocationUpdate: jest.fn(),
      setConnectionStatus: jest.fn(),
      clearConnectionError: jest.fn(),
      setTimeRange: jest.fn(),
      toggleAutoRefresh: jest.fn(),
      setRefreshInterval: jest.fn(),
      resetData: jest.fn(),
    })
  })

  it('renders correctly', () => {
    render(<RealtimeAlerts />)
    
    expect(screen.getByText('Real-time Alerts')).toBeInTheDocument()
    expect(screen.getByText('System notifications and warnings')).toBeInTheDocument()
    expect(screen.getByTestId('stagger-container')).toBeInTheDocument()
  })

  it('displays all alerts', () => {
    render(<RealtimeAlerts />)
    
    expect(screen.getByText('Critical Battery')).toBeInTheDocument()
    expect(screen.getByText('Maintenance Required')).toBeInTheDocument()
    expect(screen.getByText('Flight Completed')).toBeInTheDocument()
  })

  it('shows alert count in header', () => {
    render(<RealtimeAlerts />)
    
    expect(screen.getByText('3')).toBeInTheDocument() // Total alert count
  })

  it('displays different alert types with correct styling', () => {
    render(<RealtimeAlerts />)
    
    const errorAlert = screen.getByText('Critical Battery').closest('[data-testid="stagger-item"]')
    const warningAlert = screen.getByText('Maintenance Required').closest('[data-testid="stagger-item"]')
    const infoAlert = screen.getByText('Flight Completed').closest('[data-testid="stagger-item"]')
    
    expect(errorAlert).toBeInTheDocument()
    expect(warningAlert).toBeInTheDocument()
    expect(infoAlert).toBeInTheDocument()
  })

  it('handles alert acknowledgment', () => {
    render(<RealtimeAlerts />)
    
    const acknowledgeButton = screen.getAllByRole('button', { name: /acknowledge/i })[0]
    fireEvent.click(acknowledgeButton)
    
    expect(mockAcknowledgeAlert).toHaveBeenCalledWith(1)
  })

  it('handles alert removal', () => {
    render(<RealtimeAlerts />)
    
    const removeButton = screen.getAllByRole('button', { name: /remove|dismiss/i })[0]
    fireEvent.click(removeButton)
    
    expect(mockRemoveAlert).toHaveBeenCalledWith(1)
  })

  it('handles clear all alerts', () => {
    render(<RealtimeAlerts />)
    
    const clearAllButton = screen.getByRole('button', { name: /clear all/i })
    fireEvent.click(clearAllButton)
    
    expect(mockClearAlerts).toHaveBeenCalled()
  })

  it('toggles alerts visibility', () => {
    render(<RealtimeAlerts />)
    
    const toggleButton = screen.getByRole('button', { name: /toggle alerts/i })
    fireEvent.click(toggleButton)
    
    expect(mockToggleAlerts).toHaveBeenCalled()
  })

  it('hides alerts when showAlerts is false', () => {
    mockUseDashboardStore.mockReturnValue({
      ...mockUseDashboardStore(),
      showAlerts: false,
    })
    
    render(<RealtimeAlerts />)
    
    expect(screen.queryByText('Critical Battery')).not.toBeInTheDocument()
    expect(screen.queryByText('Maintenance Required')).not.toBeInTheDocument()
  })

  it('displays empty state when no alerts', () => {
    mockUseDashboardStore.mockReturnValue({
      ...mockUseDashboardStore(),
      alerts: [],
    })
    
    render(<RealtimeAlerts />)
    
    expect(screen.getByText(/no alerts/i)).toBeInTheDocument()
    expect(screen.getByText(/all systems are running normally/i)).toBeInTheDocument()
  })

  it('filters alerts by type', () => {
    render(<RealtimeAlerts />)
    
    const errorFilter = screen.getByRole('button', { name: /error/i })
    fireEvent.click(errorFilter)
    
    expect(screen.getByText('Critical Battery')).toBeInTheDocument()
    expect(screen.queryByText('Maintenance Required')).not.toBeInTheDocument()
    expect(screen.queryByText('Flight Completed')).not.toBeInTheDocument()
  })

  it('filters alerts by severity', () => {
    render(<RealtimeAlerts />)
    
    const highSeverityFilter = screen.getByRole('button', { name: /high/i })
    fireEvent.click(highSeverityFilter)
    
    expect(screen.getByText('Critical Battery')).toBeInTheDocument()
    expect(screen.queryByText('Maintenance Required')).not.toBeInTheDocument()
    expect(screen.queryByText('Flight Completed')).not.toBeInTheDocument()
  })

  it('shows only unacknowledged alerts when filter is applied', () => {
    render(<RealtimeAlerts />)
    
    const unacknowledgedFilter = screen.getByRole('button', { name: /unacknowledged/i })
    fireEvent.click(unacknowledgedFilter)
    
    expect(screen.getByText('Critical Battery')).toBeInTheDocument()
    expect(screen.getByText('Maintenance Required')).toBeInTheDocument()
    expect(screen.queryByText('Flight Completed')).not.toBeInTheDocument()
  })

  it('displays alert timestamps', () => {
    render(<RealtimeAlerts />)
    
    // Timestamps would be formatted and displayed
    expect(screen.getByText(/10:00/)).toBeInTheDocument()
    expect(screen.getByText(/09:30/)).toBeInTheDocument()
    expect(screen.getByText(/09:00/)).toBeInTheDocument()
  })

  it('shows alert severity badges', () => {
    render(<RealtimeAlerts />)
    
    expect(screen.getByText('HIGH')).toBeInTheDocument()
    expect(screen.getByText('MEDIUM')).toBeInTheDocument()
    expect(screen.getByText('LOW')).toBeInTheDocument()
  })

  it('handles real-time alert updates', async () => {
    const { rerender } = render(<RealtimeAlerts />)
    
    const newAlert = createMockAlert({
      id: 4,
      type: 'ERROR',
      title: 'Connection Lost',
      message: 'Lost connection to UAV-004',
      severity: 'HIGH',
      acknowledged: false,
      timestamp: new Date().toISOString(),
    })
    
    mockUseDashboardStore.mockReturnValue({
      ...mockUseDashboardStore(),
      alerts: [...mockAlerts, newAlert],
    })
    
    rerender(<RealtimeAlerts />)
    
    await waitFor(() => {
      expect(screen.getByText('Connection Lost')).toBeInTheDocument()
    })
  })

  it('sorts alerts by timestamp (newest first)', () => {
    render(<RealtimeAlerts />)
    
    const alertTitles = screen.getAllByRole('heading', { level: 4 })
    expect(alertTitles[0]).toHaveTextContent('Critical Battery') // 10:00
    expect(alertTitles[1]).toHaveTextContent('Maintenance Required') // 09:30
    expect(alertTitles[2]).toHaveTextContent('Flight Completed') // 09:00
  })

  it('limits displayed alerts to maximum count', () => {
    const manyAlerts = Array.from({ length: 25 }, (_, i) => 
      createMockAlert({
        id: i + 1,
        title: `Alert ${i + 1}`,
        timestamp: new Date(Date.now() - i * 60000).toISOString(),
      })
    )
    
    mockUseDashboardStore.mockReturnValue({
      ...mockUseDashboardStore(),
      alerts: manyAlerts,
    })
    
    render(<RealtimeAlerts maxAlerts={20} />)
    
    const alertItems = screen.getAllByTestId('stagger-item')
    expect(alertItems).toHaveLength(20)
  })

  it('shows load more button when there are more alerts', () => {
    const manyAlerts = Array.from({ length: 25 }, (_, i) => 
      createMockAlert({
        id: i + 1,
        title: `Alert ${i + 1}`,
      })
    )
    
    mockUseDashboardStore.mockReturnValue({
      ...mockUseDashboardStore(),
      alerts: manyAlerts,
    })
    
    render(<RealtimeAlerts maxAlerts={20} />)
    
    expect(screen.getByRole('button', { name: /load more/i })).toBeInTheDocument()
  })

  it('handles alert search', () => {
    render(<RealtimeAlerts />)
    
    const searchInput = screen.getByPlaceholderText(/search alerts/i)
    fireEvent.change(searchInput, { target: { value: 'battery' } })
    
    expect(screen.getByText('Critical Battery')).toBeInTheDocument()
    expect(screen.queryByText('Maintenance Required')).not.toBeInTheDocument()
    expect(screen.queryByText('Flight Completed')).not.toBeInTheDocument()
  })

  it('supports keyboard navigation', () => {
    render(<RealtimeAlerts />)
    
    const firstAlert = screen.getByText('Critical Battery').closest('[data-testid="stagger-item"]')
    const acknowledgeButton = screen.getAllByRole('button', { name: /acknowledge/i })[0]
    
    acknowledgeButton.focus()
    expect(acknowledgeButton).toHaveFocus()
    
    fireEvent.keyDown(acknowledgeButton, { key: 'Enter' })
    expect(mockAcknowledgeAlert).toHaveBeenCalledWith(1)
  })

  it('shows connection status indicator', () => {
    render(<RealtimeAlerts />)
    
    expect(screen.getByTestId('connection-indicator')).toBeInTheDocument()
    expect(screen.getByText(/connected/i)).toBeInTheDocument()
  })

  it('handles disconnected state', () => {
    mockUseDashboardStore.mockReturnValue({
      ...mockUseDashboardStore(),
      isConnected: false,
      connectionError: 'Connection lost',
    })
    
    render(<RealtimeAlerts />)
    
    expect(screen.getByText(/disconnected/i)).toBeInTheDocument()
    expect(screen.getByText('Connection lost')).toBeInTheDocument()
  })

  it('auto-refreshes alerts when connected', () => {
    jest.useFakeTimers()
    
    render(<RealtimeAlerts autoRefresh={true} refreshInterval={5000} />)
    
    // Fast-forward time
    jest.advanceTimersByTime(5000)
    
    // Auto-refresh would trigger store updates
    expect(screen.getByTestId('stagger-container')).toBeInTheDocument()
    
    jest.useRealTimers()
  })
})
