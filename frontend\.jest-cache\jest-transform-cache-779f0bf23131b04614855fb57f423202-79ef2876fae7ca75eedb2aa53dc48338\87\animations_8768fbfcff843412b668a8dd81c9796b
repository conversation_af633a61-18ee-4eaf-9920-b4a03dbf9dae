75f536b5fa99deecd8c903cbcbc8d418
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    ANIMATION_DURATION: function() {
        return ANIMATION_DURATION;
    },
    EASING: function() {
        return EASING;
    },
    alertVariants: function() {
        return alertVariants;
    },
    buttonVariants: function() {
        return buttonVariants;
    },
    cardVariants: function() {
        return cardVariants;
    },
    fadeVariants: function() {
        return fadeVariants;
    },
    getAnimationVariants: function() {
        return getAnimationVariants;
    },
    getTransition: function() {
        return getTransition;
    },
    markerVariants: function() {
        return markerVariants;
    },
    modalVariants: function() {
        return modalVariants;
    },
    pageVariants: function() {
        return pageVariants;
    },
    prefersReducedMotion: function() {
        return prefersReducedMotion;
    },
    sidebarVariants: function() {
        return sidebarVariants;
    },
    slideVariants: function() {
        return slideVariants;
    },
    spinnerVariants: function() {
        return spinnerVariants;
    },
    staggerContainer: function() {
        return staggerContainer;
    },
    staggerItem: function() {
        return staggerItem;
    },
    transitions: function() {
        return transitions;
    }
});
const ANIMATION_DURATION = {
    fast: 0.2,
    normal: 0.3,
    slow: 0.5,
    page: 0.4,
    modal: 0.3,
    hover: 0.2
};
const EASING = {
    easeOut: [
        0.0,
        0.0,
        0.2,
        1
    ],
    easeIn: [
        0.4,
        0.0,
        1,
        1
    ],
    easeInOut: [
        0.4,
        0.0,
        0.2,
        1
    ],
    spring: {
        type: "spring",
        damping: 25,
        stiffness: 300
    },
    springBouncy: {
        type: "spring",
        damping: 15,
        stiffness: 400
    }
};
const transitions = {
    default: {
        duration: ANIMATION_DURATION.normal,
        ease: EASING.easeOut
    },
    fast: {
        duration: ANIMATION_DURATION.fast,
        ease: EASING.easeOut
    },
    slow: {
        duration: ANIMATION_DURATION.slow,
        ease: EASING.easeOut
    },
    spring: EASING.spring,
    springBouncy: EASING.springBouncy
};
const pageVariants = {
    initial: {
        opacity: 0,
        y: 20,
        scale: 0.98
    },
    animate: {
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
            duration: ANIMATION_DURATION.page,
            ease: EASING.easeOut
        }
    },
    exit: {
        opacity: 0,
        y: -20,
        scale: 0.98,
        transition: {
            duration: ANIMATION_DURATION.fast,
            ease: EASING.easeIn
        }
    }
};
const cardVariants = {
    hidden: {
        opacity: 0,
        y: 20,
        scale: 0.95
    },
    visible: {
        opacity: 1,
        y: 0,
        scale: 1,
        transition: transitions.default
    },
    hover: {
        y: -2,
        scale: 1.02,
        transition: transitions.fast
    },
    tap: {
        scale: 0.98,
        transition: transitions.fast
    }
};
const staggerContainer = {
    hidden: {
        opacity: 0
    },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.1
        }
    }
};
const staggerItem = {
    hidden: {
        opacity: 0,
        y: 20
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: transitions.default
    }
};
const modalVariants = {
    hidden: {
        opacity: 0,
        scale: 0.95,
        y: 20
    },
    visible: {
        opacity: 1,
        scale: 1,
        y: 0,
        transition: {
            duration: ANIMATION_DURATION.modal,
            ease: EASING.easeOut
        }
    },
    exit: {
        opacity: 0,
        scale: 0.95,
        y: 20,
        transition: {
            duration: ANIMATION_DURATION.fast,
            ease: EASING.easeIn
        }
    }
};
const sidebarVariants = {
    collapsed: {
        width: 64,
        transition: {
            duration: ANIMATION_DURATION.normal,
            ease: EASING.easeInOut
        }
    },
    expanded: {
        width: 256,
        transition: {
            duration: ANIMATION_DURATION.normal,
            ease: EASING.easeInOut
        }
    }
};
const buttonVariants = {
    rest: {
        scale: 1
    },
    hover: {
        scale: 1.05,
        transition: transitions.fast
    },
    tap: {
        scale: 0.95,
        transition: transitions.fast
    }
};
const spinnerVariants = {
    animate: {
        rotate: 360,
        transition: {
            duration: 1,
            repeat: Infinity,
            ease: "linear"
        }
    }
};
const fadeVariants = {
    hidden: {
        opacity: 0
    },
    visible: {
        opacity: 1,
        transition: transitions.default
    },
    exit: {
        opacity: 0,
        transition: transitions.fast
    }
};
const slideVariants = {
    up: {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: transitions.default
        },
        exit: {
            opacity: 0,
            y: -20,
            transition: transitions.fast
        }
    },
    down: {
        hidden: {
            opacity: 0,
            y: -20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: transitions.default
        },
        exit: {
            opacity: 0,
            y: 20,
            transition: transitions.fast
        }
    },
    left: {
        hidden: {
            opacity: 0,
            x: 20
        },
        visible: {
            opacity: 1,
            x: 0,
            transition: transitions.default
        },
        exit: {
            opacity: 0,
            x: -20,
            transition: transitions.fast
        }
    },
    right: {
        hidden: {
            opacity: 0,
            x: -20
        },
        visible: {
            opacity: 1,
            x: 0,
            transition: transitions.default
        },
        exit: {
            opacity: 0,
            x: 20,
            transition: transitions.fast
        }
    }
};
const alertVariants = {
    hidden: {
        opacity: 0,
        x: 100,
        scale: 0.95
    },
    visible: {
        opacity: 1,
        x: 0,
        scale: 1,
        transition: {
            duration: ANIMATION_DURATION.normal,
            ease: EASING.easeOut
        }
    },
    exit: {
        opacity: 0,
        x: 100,
        scale: 0.95,
        transition: {
            duration: ANIMATION_DURATION.fast,
            ease: EASING.easeIn
        }
    }
};
const markerVariants = {
    hidden: {
        scale: 0,
        opacity: 0
    },
    visible: {
        scale: 1,
        opacity: 1,
        transition: transitions.springBouncy
    },
    hover: {
        scale: 1.2,
        transition: transitions.fast
    },
    selected: {
        scale: 1.3,
        transition: transitions.spring
    }
};
const prefersReducedMotion = ()=>{
    if (typeof window === "undefined") return false;
    return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
};
const getAnimationVariants = (variants)=>{
    if (prefersReducedMotion()) {
        // Return simplified variants for reduced motion
        return Object.keys(variants).reduce((acc, key)=>{
            acc[key] = {
                opacity: variants[key]?.opacity || 1
            };
            return acc;
        }, {});
    }
    return variants;
};
const getTransition = (transition)=>{
    if (prefersReducedMotion()) {
        return {
            duration: 0.01
        };
    }
    return transition;
};

//# sourceMappingURL=data:application/json;base64,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