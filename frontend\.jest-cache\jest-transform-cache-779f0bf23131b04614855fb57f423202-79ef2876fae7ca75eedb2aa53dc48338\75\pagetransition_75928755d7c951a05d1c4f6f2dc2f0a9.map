{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\layout\\page-transition.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { usePathname } from 'next/navigation'\nimport { pageVariants, getAnimationVariants } from '@/lib/animations'\n\ninterface PageTransitionProps {\n  children: React.ReactNode\n}\n\nexport function PageTransition({ children }: PageTransitionProps) {\n  const pathname = usePathname()\n\n  return (\n    <AnimatePresence mode=\"wait\" initial={false}>\n      <motion.div\n        key={pathname}\n        variants={getAnimationVariants(pageVariants)}\n        initial=\"initial\"\n        animate=\"animate\"\n        exit=\"exit\"\n        className=\"w-full h-full\"\n      >\n        {children}\n      </motion.div>\n    </AnimatePresence>\n  )\n}\n"], "names": ["PageTransition", "children", "pathname", "usePathname", "AnimatePresence", "mode", "initial", "motion", "div", "variants", "getAnimationVariants", "pageVariants", "animate", "exit", "className"], "mappings": "AAAA;;;;;+BAWgBA;;;eAAAA;;;;8DATE;8BACsB;4BACZ;4BACuB;;;;;;AAM5C,SAASA,eAAe,EAAEC,QAAQ,EAAuB;IAC9D,MAAMC,WAAWC,IAAAA,uBAAW;IAE5B,qBACE,qBAACC,6BAAe;QAACC,MAAK;QAAOC,SAAS;kBACpC,cAAA,qBAACC,oBAAM,CAACC,GAAG;YAETC,UAAUC,IAAAA,gCAAoB,EAACC,wBAAY;YAC3CL,SAAQ;YACRM,SAAQ;YACRC,MAAK;YACLC,WAAU;sBAETb;WAPIC;;AAWb"}