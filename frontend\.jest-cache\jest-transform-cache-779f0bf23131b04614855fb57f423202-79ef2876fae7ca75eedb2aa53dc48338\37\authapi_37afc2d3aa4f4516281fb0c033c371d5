ff37196edc53ec34c8b4aa423a1ed4b9
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AuthApi: function() {
        return AuthApi;
    },
    authApi: function() {
        return authApi;
    },
    default: function() {
        return _default;
    }
});
const _apiclient = /*#__PURE__*/ _interop_require_default(require("../lib/api-client"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
class AuthApi {
    // Authentication endpoints
    async login(credentials) {
        return _apiclient.default.post(`${this.basePath}/login`, credentials);
    }
    async logout() {
        return _apiclient.default.post(`${this.basePath}/logout`);
    }
    async register(userData) {
        return _apiclient.default.post(`${this.basePath}/register`, userData);
    }
    async refreshToken(request) {
        return _apiclient.default.post(`${this.basePath}/refresh`, request);
    }
    async validateToken() {
        try {
            await _apiclient.default.get(`${this.basePath}/validate`);
            return true;
        } catch (error) {
            return false;
        }
    }
    // Password management
    async changePassword(passwords) {
        return _apiclient.default.post(`${this.basePath}/change-password`, passwords);
    }
    async forgotPassword(request) {
        return _apiclient.default.post(`${this.basePath}/forgot-password`, request);
    }
    async resetPassword(request) {
        return _apiclient.default.post(`${this.basePath}/reset-password`, request);
    }
    async resendVerificationEmail(request) {
        return _apiclient.default.post(`${this.basePath}/resend-verification`, request);
    }
    // User profile
    async getUserProfile() {
        return _apiclient.default.get(`${this.basePath}/profile`);
    }
    async updateProfile(userData) {
        return _apiclient.default.put(`${this.basePath}/profile`, userData);
    }
    // Session management
    async checkSession() {
        return _apiclient.default.get(`${this.basePath}/session`);
    }
    async getSessions() {
        return _apiclient.default.get(`${this.basePath}/sessions`);
    }
    async terminateSession(sessionId) {
        return _apiclient.default.delete(`${this.basePath}/sessions/${sessionId}`);
    }
    async terminateAllSessions() {
        return _apiclient.default.delete(`${this.basePath}/sessions`);
    }
    // Two-factor authentication
    async setupTwoFactor() {
        return _apiclient.default.post(`${this.basePath}/2fa/setup`);
    }
    async verifyTwoFactor(code) {
        return _apiclient.default.post(`${this.basePath}/2fa/verify`, {
            code
        });
    }
    async disableTwoFactor(code) {
        return _apiclient.default.post(`${this.basePath}/2fa/disable`, {
            code
        });
    }
    // OAuth
    async oauthLogin(provider, code, state) {
        return _apiclient.default.post(`${this.basePath}/oauth/${provider}`, {
            code,
            state
        });
    }
    // Security
    async getSecurityEvents() {
        return _apiclient.default.get(`${this.basePath}/security/events`);
    }
    async reportSecurityIncident(incident) {
        return _apiclient.default.post(`${this.basePath}/security/incident`, incident);
    }
    // Token management
    setAuthToken(token) {
        _apiclient.default.setAuthToken(token);
    }
    clearAuthToken() {
        _apiclient.default.clearAuthToken();
    }
    constructor(){
        this.basePath = "/api/auth";
    }
}
const authApi = new AuthApi();
const _default = authApi;

//# sourceMappingURL=data:application/json;base64,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