94d922c0618b62d358f6cf88859445fd
"use client";
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AppLayout", {
    enumerable: true,
    get: function() {
        return AppLayout;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _header = require("./header");
const _sidebar = require("./sidebar");
const _pagetransition = require("./page-transition");
const _utils = require("../../lib/utils");
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
function AppLayout({ children, className }) {
    const [sidebarCollapsed, setSidebarCollapsed] = (0, _react.useState)(false);
    const toggleSidebar = ()=>{
        setSidebarCollapsed(!sidebarCollapsed);
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "flex h-screen bg-background",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "hidden md:flex",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
                    collapsed: sidebarCollapsed,
                    onToggle: toggleSidebar
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "flex-1 flex flex-col overflow-hidden",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {
                        onMenuClick: toggleSidebar
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("main", {
                        className: (0, _utils.cn)("flex-1 overflow-y-auto", className),
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "container mx-auto px-4 py-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
                                children: children
                            })
                        })
                    })
                ]
            })
        ]
    });
}

//# sourceMappingURL=data:application/json;base64,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