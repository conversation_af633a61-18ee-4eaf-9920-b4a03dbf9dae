{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\__tests__\\accessibility.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { runAxeTest, checkAriaAttributes } from '@/lib/test-utils'\n\n// Test components for accessibility\nconst AccessibleButton = ({ children, ...props }: any) => (\n  <button\n    type=\"button\"\n    aria-label={props['aria-label']}\n    aria-describedby={props['aria-describedby']}\n    disabled={props.disabled}\n    {...props}\n  >\n    {children}\n  </button>\n)\n\nconst AccessibleForm = () => (\n  <form role=\"form\" aria-label=\"UAV Registration Form\">\n    <div>\n      <label htmlFor=\"uav-name\">UAV Name</label>\n      <input\n        id=\"uav-name\"\n        type=\"text\"\n        required\n        aria-describedby=\"uav-name-help\"\n        aria-invalid=\"false\"\n      />\n      <div id=\"uav-name-help\">Enter a unique name for your UAV</div>\n    </div>\n    \n    <div>\n      <label htmlFor=\"uav-type\">UAV Type</label>\n      <select id=\"uav-type\" required aria-describedby=\"uav-type-help\">\n        <option value=\"\">Select type</option>\n        <option value=\"quadcopter\">Quadcopter</option>\n        <option value=\"fixed-wing\">Fixed Wing</option>\n      </select>\n      <div id=\"uav-type-help\">Choose the type of UAV</div>\n    </div>\n    \n    <button type=\"submit\">Register UAV</button>\n  </form>\n)\n\nconst AccessibleModal = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {\n  React.useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden'\n    } else {\n      document.body.style.overflow = 'unset'\n    }\n    \n    return () => {\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen])\n\n  if (!isOpen) return null\n\n  return (\n    <div\n      role=\"dialog\"\n      aria-modal=\"true\"\n      aria-labelledby=\"modal-title\"\n      aria-describedby=\"modal-description\"\n      data-testid=\"modal\"\n    >\n      <div onClick={onClose} data-testid=\"modal-backdrop\" />\n      <div>\n        <h2 id=\"modal-title\">UAV Details</h2>\n        <p id=\"modal-description\">View and edit UAV information</p>\n        <button onClick={onClose} aria-label=\"Close modal\">\n          ×\n        </button>\n      </div>\n    </div>\n  )\n}\n\nconst AccessibleDataTable = () => (\n  <table role=\"table\" aria-label=\"UAV Fleet Status\">\n    <caption>Current status of all UAVs in the fleet</caption>\n    <thead>\n      <tr>\n        <th scope=\"col\">UAV ID</th>\n        <th scope=\"col\">Status</th>\n        <th scope=\"col\">Battery</th>\n        <th scope=\"col\">Actions</th>\n      </tr>\n    </thead>\n    <tbody>\n      <tr>\n        <td>UAV-001</td>\n        <td>\n          <span aria-label=\"Active status\">🟢 Active</span>\n        </td>\n        <td>\n          <div role=\"progressbar\" aria-label=\"Battery level\" aria-valuenow={85} aria-valuemin={0} aria-valuemax={100}>\n            85%\n          </div>\n        </td>\n        <td>\n          <button aria-label=\"View details for UAV-001\">View</button>\n          <button aria-label=\"Edit UAV-001\">Edit</button>\n        </td>\n      </tr>\n    </tbody>\n  </table>\n)\n\ndescribe('Accessibility Tests', () => {\n  describe('ARIA Attributes', () => {\n    it('provides proper ARIA labels', () => {\n      render(\n        <AccessibleButton aria-label=\"Close dialog\">\n          ×\n        </AccessibleButton>\n      )\n\n      const button = screen.getByRole('button')\n      expect(button).toHaveAttribute('aria-label', 'Close dialog')\n    })\n\n    it('uses aria-describedby correctly', () => {\n      render(\n        <div>\n          <AccessibleButton aria-describedby=\"help-text\">\n            Submit\n          </AccessibleButton>\n          <div id=\"help-text\">This will submit the form</div>\n        </div>\n      )\n\n      const button = screen.getByRole('button')\n      checkAriaAttributes(button, {\n        'aria-describedby': 'help-text'\n      })\n    })\n\n    it('handles aria-expanded for collapsible content', () => {\n      const CollapsibleComponent = () => {\n        const [isExpanded, setIsExpanded] = React.useState(false)\n        \n        return (\n          <div>\n            <button\n              aria-expanded={isExpanded}\n              aria-controls=\"collapsible-content\"\n              onClick={() => setIsExpanded(!isExpanded)}\n            >\n              Toggle Content\n            </button>\n            <div id=\"collapsible-content\" hidden={!isExpanded}>\n              Collapsible content\n            </div>\n          </div>\n        )\n      }\n\n      render(<CollapsibleComponent />)\n      \n      const button = screen.getByRole('button')\n      expect(button).toHaveAttribute('aria-expanded', 'false')\n      \n      fireEvent.click(button)\n      expect(button).toHaveAttribute('aria-expanded', 'true')\n    })\n\n    it('uses aria-invalid for form validation', () => {\n      const ValidationForm = () => {\n        const [error, setError] = React.useState('')\n        \n        return (\n          <div>\n            <input\n              aria-invalid={!!error}\n              aria-describedby={error ? 'error-message' : undefined}\n              onChange={(e) => {\n                if (e.target.value.length < 3) {\n                  setError('Name must be at least 3 characters')\n                } else {\n                  setError('')\n                }\n              }}\n            />\n            {error && (\n              <div id=\"error-message\" role=\"alert\">\n                {error}\n              </div>\n            )}\n          </div>\n        )\n      }\n\n      render(<ValidationForm />)\n      \n      const input = screen.getByRole('textbox')\n      expect(input).toHaveAttribute('aria-invalid', 'false')\n      \n      fireEvent.change(input, { target: { value: 'ab' } })\n      expect(input).toHaveAttribute('aria-invalid', 'true')\n      expect(screen.getByRole('alert')).toBeInTheDocument()\n    })\n  })\n\n  describe('Keyboard Navigation', () => {\n    it('supports tab navigation', () => {\n      render(\n        <div>\n          <button>First</button>\n          <button>Second</button>\n          <input type=\"text\" />\n          <button>Third</button>\n        </div>\n      )\n\n      const buttons = screen.getAllByRole('button')\n      const input = screen.getByRole('textbox')\n\n      // Test tab order\n      buttons[0].focus()\n      expect(buttons[0]).toHaveFocus()\n\n      fireEvent.keyDown(buttons[0], { key: 'Tab' })\n      buttons[1].focus()\n      expect(buttons[1]).toHaveFocus()\n\n      fireEvent.keyDown(buttons[1], { key: 'Tab' })\n      input.focus()\n      expect(input).toHaveFocus()\n    })\n\n    it('handles Enter and Space key activation', () => {\n      const handleClick = jest.fn()\n      \n      render(\n        <AccessibleButton onClick={handleClick}>\n          Activate\n        </AccessibleButton>\n      )\n\n      const button = screen.getByRole('button')\n      \n      fireEvent.keyDown(button, { key: 'Enter' })\n      expect(handleClick).toHaveBeenCalledTimes(1)\n      \n      fireEvent.keyDown(button, { key: ' ' })\n      expect(handleClick).toHaveBeenCalledTimes(2)\n    })\n\n    it('handles Escape key for modals', () => {\n      const handleClose = jest.fn()\n      \n      render(<AccessibleModal isOpen={true} onClose={handleClose} />)\n      \n      fireEvent.keyDown(document, { key: 'Escape' })\n      expect(handleClose).toHaveBeenCalledTimes(1)\n    })\n\n    it('traps focus in modals', () => {\n      const FocusTrapModal = ({ isOpen }: { isOpen: boolean }) => {\n        if (!isOpen) return null\n        \n        return (\n          <div role=\"dialog\" aria-modal=\"true\">\n            <button>First</button>\n            <input type=\"text\" />\n            <button>Last</button>\n          </div>\n        )\n      }\n\n      render(<FocusTrapModal isOpen={true} />)\n      \n      const buttons = screen.getAllByRole('button')\n      const input = screen.getByRole('textbox')\n\n      // Focus should be trapped within modal\n      buttons[0].focus()\n      expect(buttons[0]).toHaveFocus()\n    })\n  })\n\n  describe('Screen Reader Support', () => {\n    it('provides proper headings hierarchy', () => {\n      render(\n        <div>\n          <h1>Main Title</h1>\n          <h2>Section Title</h2>\n          <h3>Subsection Title</h3>\n        </div>\n      )\n\n      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument()\n      expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument()\n      expect(screen.getByRole('heading', { level: 3 })).toBeInTheDocument()\n    })\n\n    it('uses landmarks correctly', () => {\n      render(\n        <div>\n          <header>Site Header</header>\n          <nav aria-label=\"Main navigation\">Navigation</nav>\n          <main>Main Content</main>\n          <aside>Sidebar</aside>\n          <footer>Site Footer</footer>\n        </div>\n      )\n\n      expect(screen.getByRole('banner')).toBeInTheDocument()\n      expect(screen.getByRole('navigation')).toBeInTheDocument()\n      expect(screen.getByRole('main')).toBeInTheDocument()\n      expect(screen.getByRole('complementary')).toBeInTheDocument()\n      expect(screen.getByRole('contentinfo')).toBeInTheDocument()\n    })\n\n    it('provides live regions for dynamic content', () => {\n      const LiveRegionComponent = () => {\n        const [message, setMessage] = React.useState('')\n        \n        return (\n          <div>\n            <button onClick={() => setMessage('UAV status updated')}>\n              Update Status\n            </button>\n            <div aria-live=\"polite\" aria-atomic=\"true\">\n              {message}\n            </div>\n          </div>\n        )\n      }\n\n      render(<LiveRegionComponent />)\n      \n      const button = screen.getByRole('button')\n      const liveRegion = screen.getByText('').parentElement\n\n      expect(liveRegion).toHaveAttribute('aria-live', 'polite')\n      \n      fireEvent.click(button)\n      expect(screen.getByText('UAV status updated')).toBeInTheDocument()\n    })\n\n    it('uses proper table structure', async () => {\n      const { container } = render(<AccessibleDataTable />)\n\n      expect(screen.getByRole('table')).toBeInTheDocument()\n      expect(screen.getByText('Current status of all UAVs in the fleet')).toBeInTheDocument()\n      \n      const headers = screen.getAllByRole('columnheader')\n      expect(headers).toHaveLength(4)\n      \n      const progressbar = screen.getByRole('progressbar')\n      expect(progressbar).toHaveAttribute('aria-valuenow', '85')\n\n      await runAxeTest(container)\n    })\n  })\n\n  describe('Form Accessibility', () => {\n    it('associates labels with form controls', async () => {\n      const { container } = render(<AccessibleForm />)\n\n      const nameInput = screen.getByLabelText('UAV Name')\n      const typeSelect = screen.getByLabelText('UAV Type')\n\n      expect(nameInput).toHaveAttribute('id', 'uav-name')\n      expect(typeSelect).toHaveAttribute('id', 'uav-type')\n\n      await runAxeTest(container)\n    })\n\n    it('provides helpful error messages', () => {\n      const ErrorForm = () => {\n        const [errors, setErrors] = React.useState<Record<string, string>>({})\n        \n        const validate = () => {\n          const newErrors: Record<string, string> = {}\n          const nameInput = document.getElementById('name') as HTMLInputElement\n          \n          if (!nameInput.value) {\n            newErrors.name = 'Name is required'\n          }\n          \n          setErrors(newErrors)\n        }\n        \n        return (\n          <form>\n            <label htmlFor=\"name\">Name</label>\n            <input\n              id=\"name\"\n              aria-invalid={!!errors.name}\n              aria-describedby={errors.name ? 'name-error' : undefined}\n            />\n            {errors.name && (\n              <div id=\"name-error\" role=\"alert\">\n                {errors.name}\n              </div>\n            )}\n            <button type=\"button\" onClick={validate}>\n              Validate\n            </button>\n          </form>\n        )\n      }\n\n      render(<ErrorForm />)\n      \n      const validateButton = screen.getByText('Validate')\n      fireEvent.click(validateButton)\n      \n      expect(screen.getByRole('alert')).toBeInTheDocument()\n      expect(screen.getByText('Name is required')).toBeInTheDocument()\n    })\n\n    it('handles required field indicators', () => {\n      render(\n        <div>\n          <label htmlFor=\"required-field\">\n            Required Field <span aria-label=\"required\">*</span>\n          </label>\n          <input id=\"required-field\" required />\n        </div>\n      )\n\n      const input = screen.getByRole('textbox')\n      expect(input).toHaveAttribute('required')\n    })\n  })\n\n  describe('Color and Contrast', () => {\n    it('does not rely solely on color for information', () => {\n      render(\n        <div>\n          <span style={{ color: 'red' }}>\n            <span aria-label=\"Error\">⚠️</span> Error message\n          </span>\n          <span style={{ color: 'green' }}>\n            <span aria-label=\"Success\">✅</span> Success message\n          </span>\n        </div>\n      )\n\n      // Icons provide additional context beyond color\n      expect(screen.getByLabelText('Error')).toBeInTheDocument()\n      expect(screen.getByLabelText('Success')).toBeInTheDocument()\n    })\n\n    it('provides sufficient contrast ratios', () => {\n      render(\n        <div>\n          <button style={{ backgroundColor: '#007bff', color: '#ffffff' }}>\n            High Contrast Button\n          </button>\n          <p style={{ color: '#333333', backgroundColor: '#ffffff' }}>\n            High contrast text\n          </p>\n        </div>\n      )\n\n      // These would pass WCAG contrast requirements\n      expect(screen.getByRole('button')).toBeInTheDocument()\n      expect(screen.getByText('High contrast text')).toBeInTheDocument()\n    })\n  })\n\n  describe('Comprehensive Accessibility Tests', () => {\n    it('passes axe accessibility tests for complex components', async () => {\n      const ComplexComponent = () => (\n        <div>\n          <AccessibleForm />\n          <AccessibleDataTable />\n          <AccessibleModal isOpen={false} onClose={() => {}} />\n        </div>\n      )\n\n      const { container } = render(<ComplexComponent />)\n      await runAxeTest(container)\n    })\n\n    it('maintains accessibility during state changes', async () => {\n      const StatefulComponent = () => {\n        const [isModalOpen, setIsModalOpen] = React.useState(false)\n        \n        return (\n          <div>\n            <button onClick={() => setIsModalOpen(true)}>\n              Open Modal\n            </button>\n            <AccessibleModal \n              isOpen={isModalOpen} \n              onClose={() => setIsModalOpen(false)} \n            />\n          </div>\n        )\n      }\n\n      const { container } = render(<StatefulComponent />)\n      \n      // Test initial state\n      await runAxeTest(container)\n      \n      // Open modal and test again\n      fireEvent.click(screen.getByText('Open Modal'))\n      await runAxeTest(container)\n    })\n  })\n})\n"], "names": ["AccessibleButton", "children", "props", "button", "type", "aria-label", "aria-<PERSON><PERSON>", "disabled", "AccessibleForm", "form", "role", "div", "label", "htmlFor", "input", "id", "required", "aria-invalid", "select", "option", "value", "AccessibleModal", "isOpen", "onClose", "React", "useEffect", "document", "body", "style", "overflow", "aria-modal", "aria-<PERSON>by", "data-testid", "onClick", "h2", "p", "AccessibleDataTable", "table", "caption", "thead", "tr", "th", "scope", "tbody", "td", "span", "aria-valuenow", "aria-valuemin", "aria-valuemax", "describe", "it", "render", "screen", "getByRole", "expect", "toHaveAttribute", "checkAriaAttributes", "CollapsibleComponent", "isExpanded", "setIsExpanded", "useState", "aria-expanded", "aria-controls", "hidden", "fireEvent", "click", "ValidationForm", "error", "setError", "undefined", "onChange", "e", "target", "length", "change", "toBeInTheDocument", "buttons", "getAllByRole", "focus", "toHaveFocus", "keyDown", "key", "handleClick", "jest", "fn", "toHaveBeenCalledTimes", "handleClose", "FocusTrapModal", "h1", "h3", "level", "header", "nav", "main", "aside", "footer", "LiveRegionComponent", "message", "setMessage", "aria-live", "aria-atomic", "liveRegion", "getByText", "parentElement", "container", "headers", "toHave<PERSON>ength", "progressbar", "runAxeTest", "nameInput", "getByLabelText", "typeSelect", "ErrorForm", "errors", "setErrors", "validate", "newErrors", "getElementById", "name", "validate<PERSON><PERSON><PERSON>", "color", "backgroundColor", "ComplexComponent", "StatefulComponent", "isModalOpen", "setIsModalOpen"], "mappings": ";;;;;8DAAkB;2BACiC;;;;;;AAGnD,oCAAoC;AACpC,MAAMA,mBAAmB,CAAC,EAAEC,QAAQ,EAAE,GAAGC,OAAY,iBACnD,qBAACC;QACCC,MAAK;QACLC,cAAYH,KAAK,CAAC,aAAa;QAC/BI,oBAAkBJ,KAAK,CAAC,mBAAmB;QAC3CK,UAAUL,MAAMK,QAAQ;QACvB,GAAGL,KAAK;kBAERD;;AAIL,MAAMO,iBAAiB,kBACrB,sBAACC;QAAKC,MAAK;QAAOL,cAAW;;0BAC3B,sBAACM;;kCACC,qBAACC;wBAAMC,SAAQ;kCAAW;;kCAC1B,qBAACC;wBACCC,IAAG;wBACHX,MAAK;wBACLY,QAAQ;wBACRV,oBAAiB;wBACjBW,gBAAa;;kCAEf,qBAACN;wBAAII,IAAG;kCAAgB;;;;0BAG1B,sBAACJ;;kCACC,qBAACC;wBAAMC,SAAQ;kCAAW;;kCAC1B,sBAACK;wBAAOH,IAAG;wBAAWC,QAAQ;wBAACV,oBAAiB;;0CAC9C,qBAACa;gCAAOC,OAAM;0CAAG;;0CACjB,qBAACD;gCAAOC,OAAM;0CAAa;;0CAC3B,qBAACD;gCAAOC,OAAM;0CAAa;;;;kCAE7B,qBAACT;wBAAII,IAAG;kCAAgB;;;;0BAG1B,qBAACZ;gBAAOC,MAAK;0BAAS;;;;AAI1B,MAAMiB,kBAAkB,CAAC,EAAEC,MAAM,EAAEC,OAAO,EAA4C;IACpFC,cAAK,CAACC,SAAS,CAAC;QACd,IAAIH,QAAQ;YACVI,SAASC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG;QACjC,OAAO;YACLH,SAASC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACLH,SAASC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG;QACjC;IACF,GAAG;QAACP;KAAO;IAEX,IAAI,CAACA,QAAQ,OAAO;IAEpB,qBACE,sBAACX;QACCD,MAAK;QACLoB,cAAW;QACXC,mBAAgB;QAChBzB,oBAAiB;QACjB0B,eAAY;;0BAEZ,qBAACrB;gBAAIsB,SAASV;gBAASS,eAAY;;0BACnC,sBAACrB;;kCACC,qBAACuB;wBAAGnB,IAAG;kCAAc;;kCACrB,qBAACoB;wBAAEpB,IAAG;kCAAoB;;kCAC1B,qBAACZ;wBAAO8B,SAASV;wBAASlB,cAAW;kCAAc;;;;;;AAM3D;AAEA,MAAM+B,sBAAsB,kBAC1B,sBAACC;QAAM3B,MAAK;QAAQL,cAAW;;0BAC7B,qBAACiC;0BAAQ;;0BACT,qBAACC;0BACC,cAAA,sBAACC;;sCACC,qBAACC;4BAAGC,OAAM;sCAAM;;sCAChB,qBAACD;4BAAGC,OAAM;sCAAM;;sCAChB,qBAACD;4BAAGC,OAAM;sCAAM;;sCAChB,qBAACD;4BAAGC,OAAM;sCAAM;;;;;0BAGpB,qBAACC;0BACC,cAAA,sBAACH;;sCACC,qBAACI;sCAAG;;sCACJ,qBAACA;sCACC,cAAA,qBAACC;gCAAKxC,cAAW;0CAAgB;;;sCAEnC,qBAACuC;sCACC,cAAA,qBAACjC;gCAAID,MAAK;gCAAcL,cAAW;gCAAgByC,iBAAe;gCAAIC,iBAAe;gCAAGC,iBAAe;0CAAK;;;sCAI9G,sBAACJ;;8CACC,qBAACzC;oCAAOE,cAAW;8CAA2B;;8CAC9C,qBAACF;oCAAOE,cAAW;8CAAe;;;;;;;;;AAO5C4C,SAAS,uBAAuB;IAC9BA,SAAS,mBAAmB;QAC1BC,GAAG,+BAA+B;YAChCC,IAAAA,iBAAM,gBACJ,qBAACnD;gBAAiBK,cAAW;0BAAe;;YAK9C,MAAMF,SAASiD,iBAAM,CAACC,SAAS,CAAC;YAChCC,OAAOnD,QAAQoD,eAAe,CAAC,cAAc;QAC/C;QAEAL,GAAG,mCAAmC;YACpCC,IAAAA,iBAAM,gBACJ,sBAACxC;;kCACC,qBAACX;wBAAiBM,oBAAiB;kCAAY;;kCAG/C,qBAACK;wBAAII,IAAG;kCAAY;;;;YAIxB,MAAMZ,SAASiD,iBAAM,CAACC,SAAS,CAAC;YAChCG,IAAAA,8BAAmB,EAACrD,QAAQ;gBAC1B,oBAAoB;YACtB;QACF;QAEA+C,GAAG,iDAAiD;YAClD,MAAMO,uBAAuB;gBAC3B,MAAM,CAACC,YAAYC,cAAc,GAAGnC,cAAK,CAACoC,QAAQ,CAAC;gBAEnD,qBACE,sBAACjD;;sCACC,qBAACR;4BACC0D,iBAAeH;4BACfI,iBAAc;4BACd7B,SAAS,IAAM0B,cAAc,CAACD;sCAC/B;;sCAGD,qBAAC/C;4BAAII,IAAG;4BAAsBgD,QAAQ,CAACL;sCAAY;;;;YAKzD;YAEAP,IAAAA,iBAAM,gBAAC,qBAACM;YAER,MAAMtD,SAASiD,iBAAM,CAACC,SAAS,CAAC;YAChCC,OAAOnD,QAAQoD,eAAe,CAAC,iBAAiB;YAEhDS,oBAAS,CAACC,KAAK,CAAC9D;YAChBmD,OAAOnD,QAAQoD,eAAe,CAAC,iBAAiB;QAClD;QAEAL,GAAG,yCAAyC;YAC1C,MAAMgB,iBAAiB;gBACrB,MAAM,CAACC,OAAOC,SAAS,GAAG5C,cAAK,CAACoC,QAAQ,CAAC;gBAEzC,qBACE,sBAACjD;;sCACC,qBAACG;4BACCG,gBAAc,CAAC,CAACkD;4BAChB7D,oBAAkB6D,QAAQ,kBAAkBE;4BAC5CC,UAAU,CAACC;gCACT,IAAIA,EAAEC,MAAM,CAACpD,KAAK,CAACqD,MAAM,GAAG,GAAG;oCAC7BL,SAAS;gCACX,OAAO;oCACLA,SAAS;gCACX;4BACF;;wBAEDD,uBACC,qBAACxD;4BAAII,IAAG;4BAAgBL,MAAK;sCAC1ByD;;;;YAKX;YAEAhB,IAAAA,iBAAM,gBAAC,qBAACe;YAER,MAAMpD,QAAQsC,iBAAM,CAACC,SAAS,CAAC;YAC/BC,OAAOxC,OAAOyC,eAAe,CAAC,gBAAgB;YAE9CS,oBAAS,CAACU,MAAM,CAAC5D,OAAO;gBAAE0D,QAAQ;oBAAEpD,OAAO;gBAAK;YAAE;YAClDkC,OAAOxC,OAAOyC,eAAe,CAAC,gBAAgB;YAC9CD,OAAOF,iBAAM,CAACC,SAAS,CAAC,UAAUsB,iBAAiB;QACrD;IACF;IAEA1B,SAAS,uBAAuB;QAC9BC,GAAG,2BAA2B;YAC5BC,IAAAA,iBAAM,gBACJ,sBAACxC;;kCACC,qBAACR;kCAAO;;kCACR,qBAACA;kCAAO;;kCACR,qBAACW;wBAAMV,MAAK;;kCACZ,qBAACD;kCAAO;;;;YAIZ,MAAMyE,UAAUxB,iBAAM,CAACyB,YAAY,CAAC;YACpC,MAAM/D,QAAQsC,iBAAM,CAACC,SAAS,CAAC;YAE/B,iBAAiB;YACjBuB,OAAO,CAAC,EAAE,CAACE,KAAK;YAChBxB,OAAOsB,OAAO,CAAC,EAAE,EAAEG,WAAW;YAE9Bf,oBAAS,CAACgB,OAAO,CAACJ,OAAO,CAAC,EAAE,EAAE;gBAAEK,KAAK;YAAM;YAC3CL,OAAO,CAAC,EAAE,CAACE,KAAK;YAChBxB,OAAOsB,OAAO,CAAC,EAAE,EAAEG,WAAW;YAE9Bf,oBAAS,CAACgB,OAAO,CAACJ,OAAO,CAAC,EAAE,EAAE;gBAAEK,KAAK;YAAM;YAC3CnE,MAAMgE,KAAK;YACXxB,OAAOxC,OAAOiE,WAAW;QAC3B;QAEA7B,GAAG,0CAA0C;YAC3C,MAAMgC,cAAcC,KAAKC,EAAE;YAE3BjC,IAAAA,iBAAM,gBACJ,qBAACnD;gBAAiBiC,SAASiD;0BAAa;;YAK1C,MAAM/E,SAASiD,iBAAM,CAACC,SAAS,CAAC;YAEhCW,oBAAS,CAACgB,OAAO,CAAC7E,QAAQ;gBAAE8E,KAAK;YAAQ;YACzC3B,OAAO4B,aAAaG,qBAAqB,CAAC;YAE1CrB,oBAAS,CAACgB,OAAO,CAAC7E,QAAQ;gBAAE8E,KAAK;YAAI;YACrC3B,OAAO4B,aAAaG,qBAAqB,CAAC;QAC5C;QAEAnC,GAAG,iCAAiC;YAClC,MAAMoC,cAAcH,KAAKC,EAAE;YAE3BjC,IAAAA,iBAAM,gBAAC,qBAAC9B;gBAAgBC,QAAQ;gBAAMC,SAAS+D;;YAE/CtB,oBAAS,CAACgB,OAAO,CAACtD,UAAU;gBAAEuD,KAAK;YAAS;YAC5C3B,OAAOgC,aAAaD,qBAAqB,CAAC;QAC5C;QAEAnC,GAAG,yBAAyB;YAC1B,MAAMqC,iBAAiB,CAAC,EAAEjE,MAAM,EAAuB;gBACrD,IAAI,CAACA,QAAQ,OAAO;gBAEpB,qBACE,sBAACX;oBAAID,MAAK;oBAASoB,cAAW;;sCAC5B,qBAAC3B;sCAAO;;sCACR,qBAACW;4BAAMV,MAAK;;sCACZ,qBAACD;sCAAO;;;;YAGd;YAEAgD,IAAAA,iBAAM,gBAAC,qBAACoC;gBAAejE,QAAQ;;YAE/B,MAAMsD,UAAUxB,iBAAM,CAACyB,YAAY,CAAC;YACpC,MAAM/D,QAAQsC,iBAAM,CAACC,SAAS,CAAC;YAE/B,uCAAuC;YACvCuB,OAAO,CAAC,EAAE,CAACE,KAAK;YAChBxB,OAAOsB,OAAO,CAAC,EAAE,EAAEG,WAAW;QAChC;IACF;IAEA9B,SAAS,yBAAyB;QAChCC,GAAG,sCAAsC;YACvCC,IAAAA,iBAAM,gBACJ,sBAACxC;;kCACC,qBAAC6E;kCAAG;;kCACJ,qBAACtD;kCAAG;;kCACJ,qBAACuD;kCAAG;;;;YAIRnC,OAAOF,iBAAM,CAACC,SAAS,CAAC,WAAW;gBAAEqC,OAAO;YAAE,IAAIf,iBAAiB;YACnErB,OAAOF,iBAAM,CAACC,SAAS,CAAC,WAAW;gBAAEqC,OAAO;YAAE,IAAIf,iBAAiB;YACnErB,OAAOF,iBAAM,CAACC,SAAS,CAAC,WAAW;gBAAEqC,OAAO;YAAE,IAAIf,iBAAiB;QACrE;QAEAzB,GAAG,4BAA4B;YAC7BC,IAAAA,iBAAM,gBACJ,sBAACxC;;kCACC,qBAACgF;kCAAO;;kCACR,qBAACC;wBAAIvF,cAAW;kCAAkB;;kCAClC,qBAACwF;kCAAK;;kCACN,qBAACC;kCAAM;;kCACP,qBAACC;kCAAO;;;;YAIZzC,OAAOF,iBAAM,CAACC,SAAS,CAAC,WAAWsB,iBAAiB;YACpDrB,OAAOF,iBAAM,CAACC,SAAS,CAAC,eAAesB,iBAAiB;YACxDrB,OAAOF,iBAAM,CAACC,SAAS,CAAC,SAASsB,iBAAiB;YAClDrB,OAAOF,iBAAM,CAACC,SAAS,CAAC,kBAAkBsB,iBAAiB;YAC3DrB,OAAOF,iBAAM,CAACC,SAAS,CAAC,gBAAgBsB,iBAAiB;QAC3D;QAEAzB,GAAG,6CAA6C;YAC9C,MAAM8C,sBAAsB;gBAC1B,MAAM,CAACC,SAASC,WAAW,GAAG1E,cAAK,CAACoC,QAAQ,CAAC;gBAE7C,qBACE,sBAACjD;;sCACC,qBAACR;4BAAO8B,SAAS,IAAMiE,WAAW;sCAAuB;;sCAGzD,qBAACvF;4BAAIwF,aAAU;4BAASC,eAAY;sCACjCH;;;;YAIT;YAEA9C,IAAAA,iBAAM,gBAAC,qBAAC6C;YAER,MAAM7F,SAASiD,iBAAM,CAACC,SAAS,CAAC;YAChC,MAAMgD,aAAajD,iBAAM,CAACkD,SAAS,CAAC,IAAIC,aAAa;YAErDjD,OAAO+C,YAAY9C,eAAe,CAAC,aAAa;YAEhDS,oBAAS,CAACC,KAAK,CAAC9D;YAChBmD,OAAOF,iBAAM,CAACkD,SAAS,CAAC,uBAAuB3B,iBAAiB;QAClE;QAEAzB,GAAG,+BAA+B;YAChC,MAAM,EAAEsD,SAAS,EAAE,GAAGrD,IAAAA,iBAAM,gBAAC,qBAACf;YAE9BkB,OAAOF,iBAAM,CAACC,SAAS,CAAC,UAAUsB,iBAAiB;YACnDrB,OAAOF,iBAAM,CAACkD,SAAS,CAAC,4CAA4C3B,iBAAiB;YAErF,MAAM8B,UAAUrD,iBAAM,CAACyB,YAAY,CAAC;YACpCvB,OAAOmD,SAASC,YAAY,CAAC;YAE7B,MAAMC,cAAcvD,iBAAM,CAACC,SAAS,CAAC;YACrCC,OAAOqD,aAAapD,eAAe,CAAC,iBAAiB;YAErD,MAAMqD,IAAAA,qBAAU,EAACJ;QACnB;IACF;IAEAvD,SAAS,sBAAsB;QAC7BC,GAAG,wCAAwC;YACzC,MAAM,EAAEsD,SAAS,EAAE,GAAGrD,IAAAA,iBAAM,gBAAC,qBAAC3C;YAE9B,MAAMqG,YAAYzD,iBAAM,CAAC0D,cAAc,CAAC;YACxC,MAAMC,aAAa3D,iBAAM,CAAC0D,cAAc,CAAC;YAEzCxD,OAAOuD,WAAWtD,eAAe,CAAC,MAAM;YACxCD,OAAOyD,YAAYxD,eAAe,CAAC,MAAM;YAEzC,MAAMqD,IAAAA,qBAAU,EAACJ;QACnB;QAEAtD,GAAG,mCAAmC;YACpC,MAAM8D,YAAY;gBAChB,MAAM,CAACC,QAAQC,UAAU,GAAG1F,cAAK,CAACoC,QAAQ,CAAyB,CAAC;gBAEpE,MAAMuD,WAAW;oBACf,MAAMC,YAAoC,CAAC;oBAC3C,MAAMP,YAAYnF,SAAS2F,cAAc,CAAC;oBAE1C,IAAI,CAACR,UAAUzF,KAAK,EAAE;wBACpBgG,UAAUE,IAAI,GAAG;oBACnB;oBAEAJ,UAAUE;gBACZ;gBAEA,qBACE,sBAAC3G;;sCACC,qBAACG;4BAAMC,SAAQ;sCAAO;;sCACtB,qBAACC;4BACCC,IAAG;4BACHE,gBAAc,CAAC,CAACgG,OAAOK,IAAI;4BAC3BhH,oBAAkB2G,OAAOK,IAAI,GAAG,eAAejD;;wBAEhD4C,OAAOK,IAAI,kBACV,qBAAC3G;4BAAII,IAAG;4BAAaL,MAAK;sCACvBuG,OAAOK,IAAI;;sCAGhB,qBAACnH;4BAAOC,MAAK;4BAAS6B,SAASkF;sCAAU;;;;YAK/C;YAEAhE,IAAAA,iBAAM,gBAAC,qBAAC6D;YAER,MAAMO,iBAAiBnE,iBAAM,CAACkD,SAAS,CAAC;YACxCtC,oBAAS,CAACC,KAAK,CAACsD;YAEhBjE,OAAOF,iBAAM,CAACC,SAAS,CAAC,UAAUsB,iBAAiB;YACnDrB,OAAOF,iBAAM,CAACkD,SAAS,CAAC,qBAAqB3B,iBAAiB;QAChE;QAEAzB,GAAG,qCAAqC;YACtCC,IAAAA,iBAAM,gBACJ,sBAACxC;;kCACC,sBAACC;wBAAMC,SAAQ;;4BAAiB;0CACf,qBAACgC;gCAAKxC,cAAW;0CAAW;;;;kCAE7C,qBAACS;wBAAMC,IAAG;wBAAiBC,QAAQ;;;;YAIvC,MAAMF,QAAQsC,iBAAM,CAACC,SAAS,CAAC;YAC/BC,OAAOxC,OAAOyC,eAAe,CAAC;QAChC;IACF;IAEAN,SAAS,sBAAsB;QAC7BC,GAAG,iDAAiD;YAClDC,IAAAA,iBAAM,gBACJ,sBAACxC;;kCACC,sBAACkC;wBAAKjB,OAAO;4BAAE4F,OAAO;wBAAM;;0CAC1B,qBAAC3E;gCAAKxC,cAAW;0CAAQ;;4BAAS;;;kCAEpC,sBAACwC;wBAAKjB,OAAO;4BAAE4F,OAAO;wBAAQ;;0CAC5B,qBAAC3E;gCAAKxC,cAAW;0CAAU;;4BAAQ;;;;;YAKzC,gDAAgD;YAChDiD,OAAOF,iBAAM,CAAC0D,cAAc,CAAC,UAAUnC,iBAAiB;YACxDrB,OAAOF,iBAAM,CAAC0D,cAAc,CAAC,YAAYnC,iBAAiB;QAC5D;QAEAzB,GAAG,uCAAuC;YACxCC,IAAAA,iBAAM,gBACJ,sBAACxC;;kCACC,qBAACR;wBAAOyB,OAAO;4BAAE6F,iBAAiB;4BAAWD,OAAO;wBAAU;kCAAG;;kCAGjE,qBAACrF;wBAAEP,OAAO;4BAAE4F,OAAO;4BAAWC,iBAAiB;wBAAU;kCAAG;;;;YAMhE,8CAA8C;YAC9CnE,OAAOF,iBAAM,CAACC,SAAS,CAAC,WAAWsB,iBAAiB;YACpDrB,OAAOF,iBAAM,CAACkD,SAAS,CAAC,uBAAuB3B,iBAAiB;QAClE;IACF;IAEA1B,SAAS,qCAAqC;QAC5CC,GAAG,yDAAyD;YAC1D,MAAMwE,mBAAmB,kBACvB,sBAAC/G;;sCACC,qBAACH;sCACD,qBAAC4B;sCACD,qBAACf;4BAAgBC,QAAQ;4BAAOC,SAAS,KAAO;;;;YAIpD,MAAM,EAAEiF,SAAS,EAAE,GAAGrD,IAAAA,iBAAM,gBAAC,qBAACuE;YAC9B,MAAMd,IAAAA,qBAAU,EAACJ;QACnB;QAEAtD,GAAG,gDAAgD;YACjD,MAAMyE,oBAAoB;gBACxB,MAAM,CAACC,aAAaC,eAAe,GAAGrG,cAAK,CAACoC,QAAQ,CAAC;gBAErD,qBACE,sBAACjD;;sCACC,qBAACR;4BAAO8B,SAAS,IAAM4F,eAAe;sCAAO;;sCAG7C,qBAACxG;4BACCC,QAAQsG;4BACRrG,SAAS,IAAMsG,eAAe;;;;YAItC;YAEA,MAAM,EAAErB,SAAS,EAAE,GAAGrD,IAAAA,iBAAM,gBAAC,qBAACwE;YAE9B,qBAAqB;YACrB,MAAMf,IAAAA,qBAAU,EAACJ;YAEjB,4BAA4B;YAC5BxC,oBAAS,CAACC,KAAK,CAACb,iBAAM,CAACkD,SAAS,CAAC;YACjC,MAAMM,IAAAA,qBAAU,EAACJ;QACnB;IACF;AACF"}