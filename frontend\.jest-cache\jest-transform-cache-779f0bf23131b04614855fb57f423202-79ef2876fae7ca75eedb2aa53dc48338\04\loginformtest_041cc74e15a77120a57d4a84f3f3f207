a2207f27f813c87bdedd55e45072dfad
"use strict";
// Mock Next.js router
jest.mock("next/navigation", ()=>({
        useRouter: jest.fn()
    }));
// Mock the auth store
jest.mock("@/stores/auth-store");
// Mock react-hot-toast
jest.mock("react-hot-toast", ()=>({
        toast: {
            success: jest.fn(),
            error: jest.fn()
        }
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _loginform = require("../login-form");
const _authstore = require("../../../stores/auth-store");
const _navigation = require("next/navigation");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockUseAuthStore = _authstore.useAuthStore;
describe("LoginForm Component", ()=>{
    const mockPush = jest.fn();
    const mockLogin = jest.fn();
    const mockClearError = jest.fn();
    beforeEach(()=>{
        jest.clearAllMocks();
        _navigation.useRouter.mockReturnValue({
            push: mockPush,
            replace: jest.fn(),
            prefetch: jest.fn()
        });
        // Mock auth store
        mockUseAuthStore.mockReturnValue({
            login: mockLogin,
            isLoading: false,
            error: null,
            clearError: mockClearError,
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            logout: jest.fn(),
            register: jest.fn(),
            refreshToken: jest.fn(),
            changePassword: jest.fn(),
            updateProfile: jest.fn(),
            hasPermission: jest.fn(),
            hasRole: jest.fn(),
            canAccess: jest.fn(),
            checkSession: jest.fn(),
            setLoading: jest.fn(),
            fetchUserProfile: jest.fn(),
            updateLastActivity: jest.fn()
        });
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        expect(_testutils.screen.getByText("UAV Control System")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Sign in to access the UAV management dashboard")).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText(/username/i)).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText(/password/i)).toBeInTheDocument();
        expect(_testutils.screen.getByRole("button", {
            name: /sign in/i
        })).toBeInTheDocument();
    });
    it("handles form submission with valid data", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockClearError).toHaveBeenCalled();
            expect(mockLogin).toHaveBeenCalledWith({
                username: "testuser",
                password: "password123",
                rememberMe: false
            });
        });
    });
    it("redirects to dashboard on successful login", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockPush).toHaveBeenCalledWith("/dashboard");
        });
    });
    it("calls onSuccess callback when provided", async ()=>{
        const mockOnSuccess = jest.fn();
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {
            onSuccess: mockOnSuccess
        }));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockOnSuccess).toHaveBeenCalled();
            expect(mockPush).not.toHaveBeenCalled();
        });
    });
    it("redirects to custom path when provided", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {
            redirectTo: "/custom-path"
        }));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockPush).toHaveBeenCalledWith("/custom-path");
        });
    });
    it("handles remember me checkbox", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const rememberMeCheckbox = _testutils.screen.getByLabelText(/remember me/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(rememberMeCheckbox);
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockLogin).toHaveBeenCalledWith({
                username: "testuser",
                password: "password123",
                rememberMe: true
            });
        });
    });
    it("toggles password visibility", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const toggleButton = _testutils.screen.getByRole("button", {
            name: /show password/i
        });
        expect(passwordInput).toHaveAttribute("type", "password");
        _testutils.fireEvent.click(toggleButton);
        expect(passwordInput).toHaveAttribute("type", "text");
        expect(_testutils.screen.getByRole("button", {
            name: /hide password/i
        })).toBeInTheDocument();
        _testutils.fireEvent.click(toggleButton);
        expect(passwordInput).toHaveAttribute("type", "password");
    });
    it("displays loading state during login", ()=>{
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            isLoading: true
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const submitButton = _testutils.screen.getByRole("button", {
            name: /signing in/i
        });
        expect(submitButton).toBeDisabled();
        expect(_testutils.screen.getByTestId("loading-spinner")).toBeInTheDocument();
    });
    it("displays error message when login fails", ()=>{
        const errorMessage = "Invalid credentials";
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            error: errorMessage
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        expect(_testutils.screen.getByText(errorMessage)).toBeInTheDocument();
        expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
    });
    it("validates required fields", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText(/username is required/i)).toBeInTheDocument();
            expect(_testutils.screen.getByText(/password is required/i)).toBeInTheDocument();
        });
        expect(mockLogin).not.toHaveBeenCalled();
    });
    it("validates required username", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        // Submit without entering username
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Username is required")).toBeInTheDocument();
        });
    });
    it("validates required password", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        // Enter username but not password
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Password is required")).toBeInTheDocument();
        });
    });
    it("displays error messages", ()=>{
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            error: "Invalid credentials"
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        expect(_testutils.screen.getByText("Invalid credentials")).toBeInTheDocument();
        expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
    });
    it("handles keyboard navigation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        // Test that elements can receive focus
        usernameInput.focus();
        expect(usernameInput).toHaveFocus();
        passwordInput.focus();
        expect(passwordInput).toHaveFocus();
        submitButton.focus();
        expect(submitButton).toHaveFocus();
    });
    it("handles form submission", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const form = document.querySelector("form");
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        // Submit the form directly
        if (form) {
            _testutils.fireEvent.submit(form);
            await (0, _testutils.waitFor)(()=>{
                expect(mockLogin).toHaveBeenCalled();
            });
        }
    });
    it("maintains accessibility standards", async ()=>{
        const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        // Check for proper labeling
        expect(_testutils.screen.getByLabelText(/username/i)).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText(/password/i)).toBeInTheDocument();
        // Check for form structure
        expect(container.querySelector("form")).toBeInTheDocument();
        // Run accessibility tests
        await (0, _testutils.runAxeTest)(container);
    });
    it("has proper input types", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        expect(usernameInput).toHaveAttribute("type", "text");
        expect(passwordInput).toHaveAttribute("type", "password");
    });
    it("disables submit button during loading", ()=>{
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            isLoading: true
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const submitButton = _testutils.screen.getByRole("button", {
            name: /signing in/i
        });
        expect(submitButton).toBeDisabled();
        expect(_testutils.screen.getByText("Signing in...")).toBeInTheDocument();
    });
    it("handles network errors gracefully", async ()=>{
        mockLogin.mockResolvedValue(false);
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            error: "Network error"
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Network error")).toBeInTheDocument();
            expect(mockPush).not.toHaveBeenCalled();
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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