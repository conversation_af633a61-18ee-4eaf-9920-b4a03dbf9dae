{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning:\n          \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        info:\n          \"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": ["Badge", "badgeVariants", "cva", "variants", "variant", "default", "secondary", "destructive", "outline", "success", "warning", "info", "defaultVariants", "className", "props", "div", "cn"], "mappings": ";;;;;;;;;;;IAyCSA,KAAK;eAALA;;IAAOC,aAAa;eAAbA;;;;+DAzCO;wCACgB;uBAEpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnB,MAAMA,gBAAgBC,IAAAA,2BAAG,EACvB,0KACA;IACEC,UAAU;QACRC,SAAS;YACPC,SACE;YACFC,WACE;YACFC,aACE;YACFC,SAAS;YACTC,SACE;YACFC,SACE;YACFC,MACE;QACJ;IACF;IACAC,iBAAiB;QACfR,SAAS;IACX;AACF;AAOF,SAASJ,MAAM,EAAEa,SAAS,EAAET,OAAO,EAAE,GAAGU,OAAmB;IACzD,qBACE,qBAACC;QAAIF,WAAWG,IAAAA,SAAE,EAACf,cAAc;YAAEG;QAAQ,IAAIS;QAAa,GAAGC,KAAK;;AAExE"}