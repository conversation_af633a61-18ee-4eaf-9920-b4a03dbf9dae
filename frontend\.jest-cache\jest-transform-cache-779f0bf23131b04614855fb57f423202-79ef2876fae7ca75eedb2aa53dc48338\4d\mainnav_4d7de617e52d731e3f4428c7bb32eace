32f281d132dc7ed1fe7ae4a537658bf0
"use client";
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    MainNav: function() {
        return MainNav;
    },
    MobileNav: function() {
        return MobileNav;
    },
    QuickNav: function() {
        return QuickNav;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _link = /*#__PURE__*/ _interop_require_default(require("next/link"));
const _navigation = require("next/navigation");
const _framermotion = require("framer-motion");
const _utils = require("../../lib/utils");
const _lucidereact = require("lucide-react");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const navigation = [
    {
        name: "Dashboard",
        href: "/dashboard",
        icon: _lucidereact.LayoutDashboard,
        description: "System overview and real-time monitoring"
    },
    {
        name: "UAV Management",
        href: "/uavs",
        icon: _lucidereact.Plane,
        description: "Manage UAV fleet and operations"
    },
    {
        name: "Map View",
        href: "/map",
        icon: _lucidereact.Map,
        description: "Real-time UAV tracking and geofences"
    },
    {
        name: "Hibernate Pod",
        href: "/hibernate-pod",
        icon: _lucidereact.Home,
        description: "Manage UAV hibernation and storage"
    },
    {
        name: "Docking Stations",
        href: "/docking-stations",
        icon: _lucidereact.MapPin,
        description: "Manage docking station network"
    },
    {
        name: "Battery Monitor",
        href: "/battery",
        icon: _lucidereact.Battery,
        description: "Monitor UAV battery status and health"
    },
    {
        name: "Flight Logs",
        href: "/flight-logs",
        icon: _lucidereact.FileText,
        description: "View flight history and logs"
    },
    {
        name: "Analytics",
        href: "/analytics",
        icon: _lucidereact.BarChart3,
        description: "Performance metrics and reports"
    },
    {
        name: "Alerts",
        href: "/alerts",
        icon: _lucidereact.AlertTriangle,
        description: "System alerts and notifications"
    },
    {
        name: "Regions",
        href: "/regions",
        icon: _lucidereact.Shield,
        description: "Manage operational regions"
    },
    {
        name: "Users",
        href: "/users",
        icon: _lucidereact.Users,
        description: "User management and permissions"
    },
    {
        name: "Settings",
        href: "/settings",
        icon: _lucidereact.Settings,
        description: "System configuration and preferences"
    }
];
function MainNav({ className, onNavigate }) {
    const pathname = (0, _navigation.usePathname)();
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("nav", {
        className: (0, _utils.cn)("flex flex-col space-y-1", className),
        children: navigation.map((item)=>{
            const isActive = pathname === item.href || pathname.startsWith(item.href + "/");
            const Icon = item.icon;
            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_link.default, {
                href: item.href,
                onClick: onNavigate,
                className: (0, _utils.cn)("group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors", "hover:bg-accent hover:text-accent-foreground", "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2", isActive ? "bg-primary text-primary-foreground shadow-sm" : "text-muted-foreground"),
                title: item.description,
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(Icon, {
                        className: (0, _utils.cn)("mr-3 h-5 w-5 flex-shrink-0 transition-colors", isActive ? "text-primary-foreground" : "text-muted-foreground group-hover:text-accent-foreground")
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                        className: "truncate",
                        children: item.name
                    }),
                    isActive && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "ml-auto h-2 w-2 rounded-full bg-primary-foreground"
                    })
                ]
            }, item.name);
        })
    });
}
function MobileNav({ className, onNavigate }) {
    const pathname = (0, _navigation.usePathname)();
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("nav", {
        className: (0, _utils.cn)("grid grid-cols-2 gap-2 p-4", className),
        children: navigation.slice(0, 8).map((item)=>{
            const isActive = pathname === item.href || pathname.startsWith(item.href + "/");
            const Icon = item.icon;
            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                whileHover: {
                    scale: 1.05,
                    y: -2
                },
                whileTap: {
                    scale: 0.95
                },
                transition: {
                    duration: 0.2
                },
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_link.default, {
                    href: item.href,
                    onClick: onNavigate,
                    className: (0, _utils.cn)("flex flex-col items-center justify-center p-3 rounded-lg transition-colors", "hover:bg-accent hover:text-accent-foreground", "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2", isActive ? "bg-primary text-primary-foreground" : "text-muted-foreground"),
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                            animate: isActive ? {
                                scale: 1.1
                            } : {
                                scale: 1
                            },
                            transition: {
                                duration: 0.2
                            },
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Icon, {
                                className: "h-6 w-6 mb-1"
                            })
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                            className: "text-xs font-medium text-center leading-tight",
                            children: item.name
                        })
                    ]
                })
            }, item.name);
        })
    });
}
function QuickNav({ className }) {
    const pathname = (0, _navigation.usePathname)();
    const quickItems = navigation.slice(0, 4) // Dashboard, UAVs, Map, Hibernate Pod
    ;
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("nav", {
        className: (0, _utils.cn)("flex space-x-1", className),
        children: quickItems.map((item)=>{
            const isActive = pathname === item.href || pathname.startsWith(item.href + "/");
            const Icon = item.icon;
            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                whileHover: {
                    scale: 1.05
                },
                whileTap: {
                    scale: 0.95
                },
                transition: {
                    duration: 0.2
                },
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_link.default, {
                    href: item.href,
                    className: (0, _utils.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors", "hover:bg-accent hover:text-accent-foreground", "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2", isActive ? "bg-primary text-primary-foreground" : "text-muted-foreground"),
                    title: item.description,
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                            animate: isActive ? {
                                rotate: 360
                            } : {
                                rotate: 0
                            },
                            transition: {
                                duration: 0.3
                            },
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Icon, {
                                className: "h-4 w-4 mr-2"
                            })
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                            className: "hidden sm:inline",
                            children: item.name
                        })
                    ]
                })
            }, item.name);
        })
    });
}

//# sourceMappingURL=data:application/json;base64,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