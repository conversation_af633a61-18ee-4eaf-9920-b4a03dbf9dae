ed0726ad58b85e8c8d23b7d432b915fe
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../lib/test-utils");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Test components for accessibility
const AccessibleButton = ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
        type: "button",
        "aria-label": props["aria-label"],
        "aria-describedby": props["aria-describedby"],
        disabled: props.disabled,
        ...props,
        children: children
    });
const AccessibleForm = ()=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("form", {
        role: "form",
        "aria-label": "UAV Registration Form",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                        htmlFor: "uav-name",
                        children: "UAV Name"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                        id: "uav-name",
                        type: "text",
                        required: true,
                        "aria-describedby": "uav-name-help",
                        "aria-invalid": "false"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        id: "uav-name-help",
                        children: "Enter a unique name for your UAV"
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                        htmlFor: "uav-type",
                        children: "UAV Type"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("select", {
                        id: "uav-type",
                        required: true,
                        "aria-describedby": "uav-type-help",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                value: "",
                                children: "Select type"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                value: "quadcopter",
                                children: "Quadcopter"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                value: "fixed-wing",
                                children: "Fixed Wing"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        id: "uav-type-help",
                        children: "Choose the type of UAV"
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                type: "submit",
                children: "Register UAV"
            })
        ]
    });
const AccessibleModal = ({ isOpen, onClose })=>{
    _react.default.useEffect(()=>{
        if (isOpen) {
            document.body.style.overflow = "hidden";
        } else {
            document.body.style.overflow = "unset";
        }
        return ()=>{
            document.body.style.overflow = "unset";
        };
    }, [
        isOpen
    ]);
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        role: "dialog",
        "aria-modal": "true",
        "aria-labelledby": "modal-title",
        "aria-describedby": "modal-description",
        "data-testid": "modal",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                onClick: onClose,
                "data-testid": "modal-backdrop"
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                        id: "modal-title",
                        children: "UAV Details"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                        id: "modal-description",
                        children: "View and edit UAV information"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        onClick: onClose,
                        "aria-label": "Close modal",
                        children: "\xd7"
                    })
                ]
            })
        ]
    });
};
const AccessibleDataTable = ()=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("table", {
        role: "table",
        "aria-label": "UAV Fleet Status",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("caption", {
                children: "Current status of all UAVs in the fleet"
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("thead", {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("tr", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("th", {
                            scope: "col",
                            children: "UAV ID"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("th", {
                            scope: "col",
                            children: "Status"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("th", {
                            scope: "col",
                            children: "Battery"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("th", {
                            scope: "col",
                            children: "Actions"
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("tbody", {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("tr", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("td", {
                            children: "UAV-001"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("td", {
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                "aria-label": "Active status",
                                children: "\uD83D\uDFE2 Active"
                            })
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("td", {
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                role: "progressbar",
                                "aria-valuenow": 85,
                                "aria-valuemin": 0,
                                "aria-valuemax": 100,
                                children: "85%"
                            })
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("td", {
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                    "aria-label": "View details for UAV-001",
                                    children: "View"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                    "aria-label": "Edit UAV-001",
                                    children: "Edit"
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
describe("Accessibility Tests", ()=>{
    describe("ARIA Attributes", ()=>{
        it("provides proper ARIA labels", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleButton, {
                "aria-label": "Close dialog",
                children: "\xd7"
            }));
            const button = _testutils.screen.getByRole("button");
            expect(button).toHaveAttribute("aria-label", "Close dialog");
        });
        it("uses aria-describedby correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleButton, {
                        "aria-describedby": "help-text",
                        children: "Submit"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        id: "help-text",
                        children: "This will submit the form"
                    })
                ]
            }));
            const button = _testutils.screen.getByRole("button");
            (0, _testutils.checkAriaAttributes)(button, {
                "aria-describedby": "help-text"
            });
        });
        it("handles aria-expanded for collapsible content", ()=>{
            const CollapsibleComponent = ()=>{
                const [isExpanded, setIsExpanded] = _react.default.useState(false);
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            "aria-expanded": isExpanded,
                            "aria-controls": "collapsible-content",
                            onClick: ()=>setIsExpanded(!isExpanded),
                            children: "Toggle Content"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            id: "collapsible-content",
                            hidden: !isExpanded,
                            children: "Collapsible content"
                        })
                    ]
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(CollapsibleComponent, {}));
            const button = _testutils.screen.getByRole("button");
            expect(button).toHaveAttribute("aria-expanded", "false");
            _testutils.fireEvent.click(button);
            expect(button).toHaveAttribute("aria-expanded", "true");
        });
        it("uses aria-invalid for form validation", ()=>{
            const ValidationForm = ()=>{
                const [error, setError] = _react.default.useState("");
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                            "aria-invalid": !!error,
                            "aria-describedby": error ? "error-message" : undefined,
                            onChange: (e)=>{
                                if (e.target.value.length < 3) {
                                    setError("Name must be at least 3 characters");
                                } else {
                                    setError("");
                                }
                            }
                        }),
                        error && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            id: "error-message",
                            role: "alert",
                            children: error
                        })
                    ]
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(ValidationForm, {}));
            const input = _testutils.screen.getByRole("textbox");
            expect(input).toHaveAttribute("aria-invalid", "false");
            _testutils.fireEvent.change(input, {
                target: {
                    value: "ab"
                }
            });
            expect(input).toHaveAttribute("aria-invalid", "true");
            expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
        });
    });
    describe("Keyboard Navigation", ()=>{
        it("supports tab navigation", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        children: "First"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        children: "Second"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                        type: "text"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        children: "Third"
                    })
                ]
            }));
            const buttons = _testutils.screen.getAllByRole("button");
            const input = _testutils.screen.getByRole("textbox");
            // Test tab order
            buttons[0].focus();
            expect(buttons[0]).toHaveFocus();
            _testutils.fireEvent.keyDown(buttons[0], {
                key: "Tab"
            });
            buttons[1].focus();
            expect(buttons[1]).toHaveFocus();
            _testutils.fireEvent.keyDown(buttons[1], {
                key: "Tab"
            });
            input.focus();
            expect(input).toHaveFocus();
        });
        it("handles Enter and Space key activation", ()=>{
            const handleClick = jest.fn();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleButton, {
                onClick: handleClick,
                children: "Activate"
            }));
            const button = _testutils.screen.getByRole("button");
            _testutils.fireEvent.keyDown(button, {
                key: "Enter"
            });
            expect(handleClick).toHaveBeenCalledTimes(1);
            _testutils.fireEvent.keyDown(button, {
                key: " "
            });
            expect(handleClick).toHaveBeenCalledTimes(2);
        });
        it("handles Escape key for modals", ()=>{
            const handleClose = jest.fn();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleModal, {
                isOpen: true,
                onClose: handleClose
            }));
            _testutils.fireEvent.keyDown(document, {
                key: "Escape"
            });
            expect(handleClose).toHaveBeenCalledTimes(1);
        });
        it("traps focus in modals", ()=>{
            const FocusTrapModal = ({ isOpen })=>{
                if (!isOpen) return null;
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    role: "dialog",
                    "aria-modal": "true",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            children: "First"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                            type: "text"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            children: "Last"
                        })
                    ]
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(FocusTrapModal, {
                isOpen: true
            }));
            const buttons = _testutils.screen.getAllByRole("button");
            const input = _testutils.screen.getByRole("textbox");
            // Focus should be trapped within modal
            buttons[0].focus();
            expect(buttons[0]).toHaveFocus();
        });
    });
    describe("Screen Reader Support", ()=>{
        it("provides proper headings hierarchy", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                        children: "Main Title"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                        children: "Section Title"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                        children: "Subsection Title"
                    })
                ]
            }));
            expect(_testutils.screen.getByRole("heading", {
                level: 1
            })).toBeInTheDocument();
            expect(_testutils.screen.getByRole("heading", {
                level: 2
            })).toBeInTheDocument();
            expect(_testutils.screen.getByRole("heading", {
                level: 3
            })).toBeInTheDocument();
        });
        it("uses landmarks correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("header", {
                        children: "Site Header"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("nav", {
                        "aria-label": "Main navigation",
                        children: "Navigation"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("main", {
                        children: "Main Content"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("aside", {
                        children: "Sidebar"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("footer", {
                        children: "Site Footer"
                    })
                ]
            }));
            expect(_testutils.screen.getByRole("banner")).toBeInTheDocument();
            expect(_testutils.screen.getByRole("navigation")).toBeInTheDocument();
            expect(_testutils.screen.getByRole("main")).toBeInTheDocument();
            expect(_testutils.screen.getByRole("complementary")).toBeInTheDocument();
            expect(_testutils.screen.getByRole("contentinfo")).toBeInTheDocument();
        });
        it("provides live regions for dynamic content", ()=>{
            const LiveRegionComponent = ()=>{
                const [message, setMessage] = _react.default.useState("");
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            onClick: ()=>setMessage("UAV status updated"),
                            children: "Update Status"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            "aria-live": "polite",
                            "aria-atomic": "true",
                            children: message
                        })
                    ]
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(LiveRegionComponent, {}));
            const button = _testutils.screen.getByRole("button");
            const liveRegion = _testutils.screen.getByText("").parentElement;
            expect(liveRegion).toHaveAttribute("aria-live", "polite");
            _testutils.fireEvent.click(button);
            expect(_testutils.screen.getByText("UAV status updated")).toBeInTheDocument();
        });
        it("uses proper table structure", async ()=>{
            const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleDataTable, {}));
            expect(_testutils.screen.getByRole("table")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Current status of all UAVs in the fleet")).toBeInTheDocument();
            const headers = _testutils.screen.getAllByRole("columnheader");
            expect(headers).toHaveLength(4);
            const progressbar = _testutils.screen.getByRole("progressbar");
            expect(progressbar).toHaveAttribute("aria-valuenow", "85");
            await (0, _testutils.runAxeTest)(container);
        });
    });
    describe("Form Accessibility", ()=>{
        it("associates labels with form controls", async ()=>{
            const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleForm, {}));
            const nameInput = _testutils.screen.getByLabelText("UAV Name");
            const typeSelect = _testutils.screen.getByLabelText("UAV Type");
            expect(nameInput).toHaveAttribute("id", "uav-name");
            expect(typeSelect).toHaveAttribute("id", "uav-type");
            await (0, _testutils.runAxeTest)(container);
        });
        it("provides helpful error messages", ()=>{
            const ErrorForm = ()=>{
                const [errors, setErrors] = _react.default.useState({});
                const validate = ()=>{
                    const newErrors = {};
                    const nameInput = document.getElementById("name");
                    if (!nameInput.value) {
                        newErrors.name = "Name is required";
                    }
                    setErrors(newErrors);
                };
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("form", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                            htmlFor: "name",
                            children: "Name"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                            id: "name",
                            "aria-invalid": !!errors.name,
                            "aria-describedby": errors.name ? "name-error" : undefined
                        }),
                        errors.name && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            id: "name-error",
                            role: "alert",
                            children: errors.name
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            type: "button",
                            onClick: validate,
                            children: "Validate"
                        })
                    ]
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(ErrorForm, {}));
            const validateButton = _testutils.screen.getByText("Validate");
            _testutils.fireEvent.click(validateButton);
            expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Name is required")).toBeInTheDocument();
        });
        it("handles required field indicators", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("label", {
                        htmlFor: "required-field",
                        children: [
                            "Required Field ",
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                "aria-label": "required",
                                children: "*"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                        id: "required-field",
                        required: true
                    })
                ]
            }));
            const input = _testutils.screen.getByRole("textbox");
            expect(input).toHaveAttribute("required");
        });
    });
    describe("Color and Contrast", ()=>{
        it("does not rely solely on color for information", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                        style: {
                            color: "red"
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                "aria-label": "Error",
                                children: "⚠️"
                            }),
                            " Error message"
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                        style: {
                            color: "green"
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                "aria-label": "Success",
                                children: "✅"
                            }),
                            " Success message"
                        ]
                    })
                ]
            }));
            // Icons provide additional context beyond color
            expect(_testutils.screen.getByLabelText("Error")).toBeInTheDocument();
            expect(_testutils.screen.getByLabelText("Success")).toBeInTheDocument();
        });
        it("provides sufficient contrast ratios", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        style: {
                            backgroundColor: "#007bff",
                            color: "#ffffff"
                        },
                        children: "High Contrast Button"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                        style: {
                            color: "#333333",
                            backgroundColor: "#ffffff"
                        },
                        children: "High contrast text"
                    })
                ]
            }));
            // These would pass WCAG contrast requirements
            expect(_testutils.screen.getByRole("button")).toBeInTheDocument();
            expect(_testutils.screen.getByText("High contrast text")).toBeInTheDocument();
        });
    });
    describe("Comprehensive Accessibility Tests", ()=>{
        it("passes axe accessibility tests for complex components", async ()=>{
            const ComplexComponent = ()=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleForm, {}),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleDataTable, {}),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleModal, {
                            isOpen: false,
                            onClose: ()=>{}
                        })
                    ]
                });
            const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(ComplexComponent, {}));
            await (0, _testutils.runAxeTest)(container);
        });
        it("maintains accessibility during state changes", async ()=>{
            const StatefulComponent = ()=>{
                const [isModalOpen, setIsModalOpen] = _react.default.useState(false);
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            onClick: ()=>setIsModalOpen(true),
                            children: "Open Modal"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleModal, {
                            isOpen: isModalOpen,
                            onClose: ()=>setIsModalOpen(false)
                        })
                    ]
                });
            };
            const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(StatefulComponent, {}));
            // Test initial state
            await (0, _testutils.runAxeTest)(container);
            // Open modal and test again
            _testutils.fireEvent.click(_testutils.screen.getByText("Open Modal"));
            await (0, _testutils.runAxeTest)(container);
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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