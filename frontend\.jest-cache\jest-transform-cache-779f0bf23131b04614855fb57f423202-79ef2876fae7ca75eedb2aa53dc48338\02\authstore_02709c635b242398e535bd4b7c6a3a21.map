{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\stores\\auth-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools, persist, subscribeWithSelector } from 'zustand/middleware'\nimport { immer } from 'zustand/middleware/immer'\nimport {\n  User,\n  AuthState,\n  LoginRequest,\n  RegisterRequest,\n  ChangePasswordRequest,\n  PermissionCheck,\n  ResourceType,\n  ActionType,\n} from '@/types/auth'\nimport { authApi } from '@/api/auth-api'\nimport { toast } from 'react-hot-toast'\n\ninterface AuthStore extends AuthState {\n  // Actions\n  login: (credentials: LoginRequest) => Promise<boolean>\n  logout: () => Promise<void>\n  register: (userData: RegisterRequest) => Promise<boolean>\n  refreshAuthToken: () => Promise<boolean>\n  refreshToken: () => Promise<boolean>  // Alias for refreshAuthToken\n  changePassword: (passwords: ChangePasswordRequest) => Promise<boolean>\n  updateProfile: (userData: Partial<User>) => Promise<boolean>\n  \n  // Permission checks\n  hasPermission: (check: PermissionCheck) => boolean\n  hasRole: (roleName: string) => boolean\n  canAccess: (resource: ResourceType, action: ActionType) => boolean\n  \n  // Session management\n  checkSession: () => Promise<boolean>\n  clearError: () => void\n  setLoading: (loading: boolean) => void\n  \n  // User management\n  fetchUserProfile: () => Promise<void>\n  updateLastActivity: () => void\n}\n\nexport const useAuthStore = create<AuthStore>()(\n  devtools(\n    persist(\n      subscribeWithSelector(\n        immer((set, get) => ({\n          // Initial state\n          user: null,\n          token: null,\n          refreshToken: null,\n          isAuthenticated: false,\n          isLoading: false,\n          error: null,\n\n          // Login\n          login: async (credentials: LoginRequest) => {\n            set((state) => {\n              state.isLoading = true\n              state.error = null\n            })\n\n            try {\n              const response = await authApi.login(credentials)\n              \n              if (response.success && response.data) {\n                set((state) => {\n                  state.user = response.data!.user\n                  state.token = response.data!.token\n                  state.refreshToken = response.data!.refreshToken\n                  state.isAuthenticated = true\n                  state.isLoading = false\n                  state.error = null\n                })\n\n                // Set token in API client\n                authApi.setAuthToken(response.data.token)\n                \n                toast.success('Login successful')\n                return true\n              } else {\n                throw new Error(response.message || 'Login failed')\n              }\n            } catch (error) {\n              const message = error instanceof Error ? error.message : 'Login failed'\n              set((state) => {\n                state.error = message\n                state.isLoading = false\n                state.isAuthenticated = false\n              })\n              toast.error(message)\n              return false\n            }\n          },\n\n          // Logout\n          logout: async () => {\n            try {\n              await authApi.logout()\n            } catch (error) {\n              console.error('Logout error:', error)\n            } finally {\n              set((state) => {\n                state.user = null\n                state.token = null\n                state.refreshToken = null\n                state.isAuthenticated = false\n                state.error = null\n              })\n\n              // Clear token from API client\n              authApi.clearAuthToken()\n              \n              toast.success('Logged out successfully')\n            }\n          },\n\n          // Register\n          register: async (userData: RegisterRequest) => {\n            set((state) => {\n              state.isLoading = true\n              state.error = null\n            })\n\n            try {\n              const response = await authApi.register(userData)\n\n              if (response.success && response.data) {\n                set((state) => {\n                  state.user = response.data.user\n                  state.token = response.data.token\n                  state.refreshToken = response.data.refreshToken\n                  state.isAuthenticated = true\n                  state.isLoading = false\n                })\n\n                // Store tokens in localStorage\n                localStorage.setItem('token', response.data.token)\n                localStorage.setItem('refreshToken', response.data.refreshToken)\n\n                toast.success('Registration successful. Welcome!')\n                return true\n              } else {\n                throw new Error(response.message || 'Registration failed')\n              }\n            } catch (error) {\n              const message = error instanceof Error ? error.message : 'Registration failed'\n              set((state) => {\n                state.error = message\n                state.isLoading = false\n              })\n              toast.error(message)\n              return false\n            }\n          },\n\n          // Refresh token\n          refreshAuthToken: async () => {\n            const { refreshToken } = get()\n            if (!refreshToken) return false\n\n            try {\n              const response = await authApi.refreshToken({ refreshToken })\n              \n              if (response.success && response.data) {\n                set((state) => {\n                  state.token = response.data!.token\n                  state.refreshToken = response.data!.refreshToken\n                  state.error = null\n                })\n\n                // Update token in API client\n                authApi.setAuthToken(response.data.token)\n                \n                return true\n              } else {\n                // Refresh failed, logout user\n                get().logout()\n                return false\n              }\n            } catch (error) {\n              console.error('Token refresh failed:', error)\n              get().logout()\n              return false\n            }\n          },\n\n          // Alias for refreshAuthToken (for compatibility with tests)\n          refreshToken: async () => {\n            return get().refreshAuthToken()\n          },\n\n          // Change password\n          changePassword: async (passwords: ChangePasswordRequest) => {\n            set((state) => {\n              state.isLoading = true\n              state.error = null\n            })\n\n            try {\n              const response = await authApi.changePassword(passwords)\n              \n              if (response.success) {\n                set((state) => {\n                  state.isLoading = false\n                })\n                \n                toast.success('Password changed successfully')\n                return true\n              } else {\n                throw new Error(response.message || 'Password change failed')\n              }\n            } catch (error) {\n              const message = error instanceof Error ? error.message : 'Password change failed'\n              set((state) => {\n                state.error = message\n                state.isLoading = false\n              })\n              toast.error(message)\n              return false\n            }\n          },\n\n          // Update profile\n          updateProfile: async (userData: Partial<User>) => {\n            set((state) => {\n              state.isLoading = true\n              state.error = null\n            })\n\n            try {\n              const response = await authApi.updateProfile(userData)\n              \n              if (response.success && response.data) {\n                set((state) => {\n                  state.user = response.data!\n                  state.isLoading = false\n                })\n                \n                toast.success('Profile updated successfully')\n                return true\n              } else {\n                throw new Error(response.message || 'Profile update failed')\n              }\n            } catch (error) {\n              const message = error instanceof Error ? error.message : 'Profile update failed'\n              set((state) => {\n                state.error = message\n                state.isLoading = false\n              })\n              toast.error(message)\n              return false\n            }\n          },\n\n          // Permission checks\n          hasPermission: (check: PermissionCheck) => {\n            const { user } = get()\n            if (!user || !user.permissions) return false\n\n            return user.permissions.some(permission => \n              permission.resource === check.resource && \n              permission.action === check.action\n            )\n          },\n\n          hasRole: (roleName: string) => {\n            const { user } = get()\n            if (!user || !user.roles) return false\n\n            return user.roles.some(role => role.name === roleName)\n          },\n\n          canAccess: (resource: ResourceType, action: ActionType) => {\n            return get().hasPermission({ resource, action })\n          },\n\n          // Session management\n          checkSession: async () => {\n            const { token, refreshToken } = get()\n            \n            if (!token) {\n              set((state) => {\n                state.isAuthenticated = false\n              })\n              return false\n            }\n\n            try {\n              // Check if token is still valid\n              const isValid = await authApi.validateToken()\n              \n              if (isValid) {\n                set((state) => {\n                  state.isAuthenticated = true\n                })\n                return true\n              } else if (refreshToken) {\n                // Try to refresh token\n                return await get().refreshAuthToken()\n              } else {\n                // No valid token or refresh token\n                get().logout()\n                return false\n              }\n            } catch (error) {\n              console.error('Session check failed:', error)\n              get().logout()\n              return false\n            }\n          },\n\n          // Fetch user profile\n          fetchUserProfile: async () => {\n            try {\n              const user = await authApi.getUserProfile()\n              set((state) => {\n                state.user = user\n              })\n            } catch (error) {\n              console.error('Failed to fetch user profile:', error)\n            }\n          },\n\n          // Update last activity\n          updateLastActivity: () => {\n            const { user } = get()\n            if (user) {\n              set((state) => {\n                if (state.user) {\n                  state.user.lastLogin = new Date().toISOString()\n                }\n              })\n            }\n          },\n\n          // Clear error\n          clearError: () => {\n            set((state) => {\n              state.error = null\n            })\n          },\n\n          // Set loading\n          setLoading: (loading: boolean) => {\n            set((state) => {\n              state.isLoading = loading\n            })\n          },\n        }))\n      ),\n      {\n        name: 'auth-store',\n        partialize: (state) => ({\n          user: state.user,\n          token: state.token,\n          refreshToken: state.refreshToken,\n          isAuthenticated: state.isAuthenticated,\n        }),\n      }\n    ),\n    {\n      name: 'auth-store',\n    }\n  )\n)\n\n// Selectors\nexport const useAuth = () => useAuthStore((state) => ({\n  user: state.user,\n  isAuthenticated: state.isAuthenticated,\n  isLoading: state.isLoading,\n  error: state.error,\n}))\n\nexport const usePermissions = () => useAuthStore((state) => ({\n  hasPermission: state.hasPermission,\n  hasRole: state.hasRole,\n  canAccess: state.canAccess,\n}))\n\n// Auto-refresh token before expiration\nlet refreshInterval: NodeJS.Timeout | null = null\n\nuseAuthStore.subscribe(\n  (state) => state.token,\n  (token) => {\n    if (refreshInterval) {\n      clearInterval(refreshInterval)\n      refreshInterval = null\n    }\n\n    if (token) {\n      // Refresh token every 50 minutes (assuming 60-minute expiration)\n      refreshInterval = setInterval(() => {\n        useAuthStore.getState().refreshAuthToken()\n      }, 50 * 60 * 1000)\n    }\n  }\n)\n"], "names": ["useAuth", "useAuthStore", "usePermissions", "create", "devtools", "persist", "subscribeWithSelector", "immer", "set", "get", "user", "token", "refreshToken", "isAuthenticated", "isLoading", "error", "login", "credentials", "state", "response", "authApi", "success", "data", "setAuthToken", "toast", "Error", "message", "logout", "console", "clearAuthToken", "register", "userData", "localStorage", "setItem", "refreshAuthToken", "changePassword", "passwords", "updateProfile", "hasPermission", "check", "permissions", "some", "permission", "resource", "action", "hasRole", "<PERSON><PERSON><PERSON>", "roles", "role", "name", "canAccess", "checkSession", "<PERSON><PERSON><PERSON><PERSON>", "validateToken", "fetchUserProfile", "getUserProfile", "updateLastActivity", "lastLogin", "Date", "toISOString", "clearError", "setLoading", "loading", "partialize", "refreshInterval", "subscribe", "clearInterval", "setInterval", "getState"], "mappings": ";;;;;;;;;;;IA+WaA,OAAO;eAAPA;;IAtUAC,YAAY;eAAZA;;IA6UAC,cAAc;eAAdA;;;yBAtXU;4BACkC;uBACnC;yBAWE;+BACF;AA2Bf,MAAMD,eAAeE,IAAAA,eAAM,IAChCC,IAAAA,oBAAQ,EACNC,IAAAA,mBAAO,EACLC,IAAAA,iCAAqB,EACnBC,IAAAA,YAAK,EAAC,CAACC,KAAKC,MAAS,CAAA;QACnB,gBAAgB;QAChBC,MAAM;QACNC,OAAO;QACPC,cAAc;QACdC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QAEP,QAAQ;QACRC,OAAO,OAAOC;YACZT,IAAI,CAACU;gBACHA,MAAMJ,SAAS,GAAG;gBAClBI,MAAMH,KAAK,GAAG;YAChB;YAEA,IAAI;gBACF,MAAMI,WAAW,MAAMC,gBAAO,CAACJ,KAAK,CAACC;gBAErC,IAAIE,SAASE,OAAO,IAAIF,SAASG,IAAI,EAAE;oBACrCd,IAAI,CAACU;wBACHA,MAAMR,IAAI,GAAGS,SAASG,IAAI,CAAEZ,IAAI;wBAChCQ,MAAMP,KAAK,GAAGQ,SAASG,IAAI,CAAEX,KAAK;wBAClCO,MAAMN,YAAY,GAAGO,SAASG,IAAI,CAAEV,YAAY;wBAChDM,MAAML,eAAe,GAAG;wBACxBK,MAAMJ,SAAS,GAAG;wBAClBI,MAAMH,KAAK,GAAG;oBAChB;oBAEA,0BAA0B;oBAC1BK,gBAAO,CAACG,YAAY,CAACJ,SAASG,IAAI,CAACX,KAAK;oBAExCa,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAII,MAAMN,SAASO,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOX,OAAO;gBACd,MAAMW,UAAUX,iBAAiBU,QAAQV,MAAMW,OAAO,GAAG;gBACzDlB,IAAI,CAACU;oBACHA,MAAMH,KAAK,GAAGW;oBACdR,MAAMJ,SAAS,GAAG;oBAClBI,MAAML,eAAe,GAAG;gBAC1B;gBACAW,oBAAK,CAACT,KAAK,CAACW;gBACZ,OAAO;YACT;QACF;QAEA,SAAS;QACTC,QAAQ;YACN,IAAI;gBACF,MAAMP,gBAAO,CAACO,MAAM;YACtB,EAAE,OAAOZ,OAAO;gBACda,QAAQb,KAAK,CAAC,iBAAiBA;YACjC,SAAU;gBACRP,IAAI,CAACU;oBACHA,MAAMR,IAAI,GAAG;oBACbQ,MAAMP,KAAK,GAAG;oBACdO,MAAMN,YAAY,GAAG;oBACrBM,MAAML,eAAe,GAAG;oBACxBK,MAAMH,KAAK,GAAG;gBAChB;gBAEA,8BAA8B;gBAC9BK,gBAAO,CAACS,cAAc;gBAEtBL,oBAAK,CAACH,OAAO,CAAC;YAChB;QACF;QAEA,WAAW;QACXS,UAAU,OAAOC;YACfvB,IAAI,CAACU;gBACHA,MAAMJ,SAAS,GAAG;gBAClBI,MAAMH,KAAK,GAAG;YAChB;YAEA,IAAI;gBACF,MAAMI,WAAW,MAAMC,gBAAO,CAACU,QAAQ,CAACC;gBAExC,IAAIZ,SAASE,OAAO,IAAIF,SAASG,IAAI,EAAE;oBACrCd,IAAI,CAACU;wBACHA,MAAMR,IAAI,GAAGS,SAASG,IAAI,CAACZ,IAAI;wBAC/BQ,MAAMP,KAAK,GAAGQ,SAASG,IAAI,CAACX,KAAK;wBACjCO,MAAMN,YAAY,GAAGO,SAASG,IAAI,CAACV,YAAY;wBAC/CM,MAAML,eAAe,GAAG;wBACxBK,MAAMJ,SAAS,GAAG;oBACpB;oBAEA,+BAA+B;oBAC/BkB,aAAaC,OAAO,CAAC,SAASd,SAASG,IAAI,CAACX,KAAK;oBACjDqB,aAAaC,OAAO,CAAC,gBAAgBd,SAASG,IAAI,CAACV,YAAY;oBAE/DY,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAII,MAAMN,SAASO,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOX,OAAO;gBACd,MAAMW,UAAUX,iBAAiBU,QAAQV,MAAMW,OAAO,GAAG;gBACzDlB,IAAI,CAACU;oBACHA,MAAMH,KAAK,GAAGW;oBACdR,MAAMJ,SAAS,GAAG;gBACpB;gBACAU,oBAAK,CAACT,KAAK,CAACW;gBACZ,OAAO;YACT;QACF;QAEA,gBAAgB;QAChBQ,kBAAkB;YAChB,MAAM,EAAEtB,YAAY,EAAE,GAAGH;YACzB,IAAI,CAACG,cAAc,OAAO;YAE1B,IAAI;gBACF,MAAMO,WAAW,MAAMC,gBAAO,CAACR,YAAY,CAAC;oBAAEA;gBAAa;gBAE3D,IAAIO,SAASE,OAAO,IAAIF,SAASG,IAAI,EAAE;oBACrCd,IAAI,CAACU;wBACHA,MAAMP,KAAK,GAAGQ,SAASG,IAAI,CAAEX,KAAK;wBAClCO,MAAMN,YAAY,GAAGO,SAASG,IAAI,CAAEV,YAAY;wBAChDM,MAAMH,KAAK,GAAG;oBAChB;oBAEA,6BAA6B;oBAC7BK,gBAAO,CAACG,YAAY,CAACJ,SAASG,IAAI,CAACX,KAAK;oBAExC,OAAO;gBACT,OAAO;oBACL,8BAA8B;oBAC9BF,MAAMkB,MAAM;oBACZ,OAAO;gBACT;YACF,EAAE,OAAOZ,OAAO;gBACda,QAAQb,KAAK,CAAC,yBAAyBA;gBACvCN,MAAMkB,MAAM;gBACZ,OAAO;YACT;QACF;QAEA,4DAA4D;QAC5Df,cAAc;YACZ,OAAOH,MAAMyB,gBAAgB;QAC/B;QAEA,kBAAkB;QAClBC,gBAAgB,OAAOC;YACrB5B,IAAI,CAACU;gBACHA,MAAMJ,SAAS,GAAG;gBAClBI,MAAMH,KAAK,GAAG;YAChB;YAEA,IAAI;gBACF,MAAMI,WAAW,MAAMC,gBAAO,CAACe,cAAc,CAACC;gBAE9C,IAAIjB,SAASE,OAAO,EAAE;oBACpBb,IAAI,CAACU;wBACHA,MAAMJ,SAAS,GAAG;oBACpB;oBAEAU,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAII,MAAMN,SAASO,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOX,OAAO;gBACd,MAAMW,UAAUX,iBAAiBU,QAAQV,MAAMW,OAAO,GAAG;gBACzDlB,IAAI,CAACU;oBACHA,MAAMH,KAAK,GAAGW;oBACdR,MAAMJ,SAAS,GAAG;gBACpB;gBACAU,oBAAK,CAACT,KAAK,CAACW;gBACZ,OAAO;YACT;QACF;QAEA,iBAAiB;QACjBW,eAAe,OAAON;YACpBvB,IAAI,CAACU;gBACHA,MAAMJ,SAAS,GAAG;gBAClBI,MAAMH,KAAK,GAAG;YAChB;YAEA,IAAI;gBACF,MAAMI,WAAW,MAAMC,gBAAO,CAACiB,aAAa,CAACN;gBAE7C,IAAIZ,SAASE,OAAO,IAAIF,SAASG,IAAI,EAAE;oBACrCd,IAAI,CAACU;wBACHA,MAAMR,IAAI,GAAGS,SAASG,IAAI;wBAC1BJ,MAAMJ,SAAS,GAAG;oBACpB;oBAEAU,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAII,MAAMN,SAASO,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOX,OAAO;gBACd,MAAMW,UAAUX,iBAAiBU,QAAQV,MAAMW,OAAO,GAAG;gBACzDlB,IAAI,CAACU;oBACHA,MAAMH,KAAK,GAAGW;oBACdR,MAAMJ,SAAS,GAAG;gBACpB;gBACAU,oBAAK,CAACT,KAAK,CAACW;gBACZ,OAAO;YACT;QACF;QAEA,oBAAoB;QACpBY,eAAe,CAACC;YACd,MAAM,EAAE7B,IAAI,EAAE,GAAGD;YACjB,IAAI,CAACC,QAAQ,CAACA,KAAK8B,WAAW,EAAE,OAAO;YAEvC,OAAO9B,KAAK8B,WAAW,CAACC,IAAI,CAACC,CAAAA,aAC3BA,WAAWC,QAAQ,KAAKJ,MAAMI,QAAQ,IACtCD,WAAWE,MAAM,KAAKL,MAAMK,MAAM;QAEtC;QAEAC,SAAS,CAACC;YACR,MAAM,EAAEpC,IAAI,EAAE,GAAGD;YACjB,IAAI,CAACC,QAAQ,CAACA,KAAKqC,KAAK,EAAE,OAAO;YAEjC,OAAOrC,KAAKqC,KAAK,CAACN,IAAI,CAACO,CAAAA,OAAQA,KAAKC,IAAI,KAAKH;QAC/C;QAEAI,WAAW,CAACP,UAAwBC;YAClC,OAAOnC,MAAM6B,aAAa,CAAC;gBAAEK;gBAAUC;YAAO;QAChD;QAEA,qBAAqB;QACrBO,cAAc;YACZ,MAAM,EAAExC,KAAK,EAAEC,YAAY,EAAE,GAAGH;YAEhC,IAAI,CAACE,OAAO;gBACVH,IAAI,CAACU;oBACHA,MAAML,eAAe,GAAG;gBAC1B;gBACA,OAAO;YACT;YAEA,IAAI;gBACF,gCAAgC;gBAChC,MAAMuC,UAAU,MAAMhC,gBAAO,CAACiC,aAAa;gBAE3C,IAAID,SAAS;oBACX5C,IAAI,CAACU;wBACHA,MAAML,eAAe,GAAG;oBAC1B;oBACA,OAAO;gBACT,OAAO,IAAID,cAAc;oBACvB,uBAAuB;oBACvB,OAAO,MAAMH,MAAMyB,gBAAgB;gBACrC,OAAO;oBACL,kCAAkC;oBAClCzB,MAAMkB,MAAM;oBACZ,OAAO;gBACT;YACF,EAAE,OAAOZ,OAAO;gBACda,QAAQb,KAAK,CAAC,yBAAyBA;gBACvCN,MAAMkB,MAAM;gBACZ,OAAO;YACT;QACF;QAEA,qBAAqB;QACrB2B,kBAAkB;YAChB,IAAI;gBACF,MAAM5C,OAAO,MAAMU,gBAAO,CAACmC,cAAc;gBACzC/C,IAAI,CAACU;oBACHA,MAAMR,IAAI,GAAGA;gBACf;YACF,EAAE,OAAOK,OAAO;gBACda,QAAQb,KAAK,CAAC,iCAAiCA;YACjD;QACF;QAEA,uBAAuB;QACvByC,oBAAoB;YAClB,MAAM,EAAE9C,IAAI,EAAE,GAAGD;YACjB,IAAIC,MAAM;gBACRF,IAAI,CAACU;oBACH,IAAIA,MAAMR,IAAI,EAAE;wBACdQ,MAAMR,IAAI,CAAC+C,SAAS,GAAG,IAAIC,OAAOC,WAAW;oBAC/C;gBACF;YACF;QACF;QAEA,cAAc;QACdC,YAAY;YACVpD,IAAI,CAACU;gBACHA,MAAMH,KAAK,GAAG;YAChB;QACF;QAEA,cAAc;QACd8C,YAAY,CAACC;YACXtD,IAAI,CAACU;gBACHA,MAAMJ,SAAS,GAAGgD;YACpB;QACF;IACF,CAAA,KAEF;IACEb,MAAM;IACNc,YAAY,CAAC7C,QAAW,CAAA;YACtBR,MAAMQ,MAAMR,IAAI;YAChBC,OAAOO,MAAMP,KAAK;YAClBC,cAAcM,MAAMN,YAAY;YAChCC,iBAAiBK,MAAML,eAAe;QACxC,CAAA;AACF,IAEF;IACEoC,MAAM;AACR;AAKG,MAAMjD,UAAU,IAAMC,aAAa,CAACiB,QAAW,CAAA;YACpDR,MAAMQ,MAAMR,IAAI;YAChBG,iBAAiBK,MAAML,eAAe;YACtCC,WAAWI,MAAMJ,SAAS;YAC1BC,OAAOG,MAAMH,KAAK;QACpB,CAAA;AAEO,MAAMb,iBAAiB,IAAMD,aAAa,CAACiB,QAAW,CAAA;YAC3DoB,eAAepB,MAAMoB,aAAa;YAClCO,SAAS3B,MAAM2B,OAAO;YACtBK,WAAWhC,MAAMgC,SAAS;QAC5B,CAAA;AAEA,uCAAuC;AACvC,IAAIc,kBAAyC;AAE7C/D,aAAagE,SAAS,CACpB,CAAC/C,QAAUA,MAAMP,KAAK,EACtB,CAACA;IACC,IAAIqD,iBAAiB;QACnBE,cAAcF;QACdA,kBAAkB;IACpB;IAEA,IAAIrD,OAAO;QACT,iEAAiE;QACjEqD,kBAAkBG,YAAY;YAC5BlE,aAAamE,QAAQ,GAAGlC,gBAAgB;QAC1C,GAAG,KAAK,KAAK;IACf;AACF"}