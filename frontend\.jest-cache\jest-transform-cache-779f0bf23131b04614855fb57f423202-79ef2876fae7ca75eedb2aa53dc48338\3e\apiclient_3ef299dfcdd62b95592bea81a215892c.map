{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\lib\\api-client.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { toast } from 'react-hot-toast';\n\n// Types\ninterface APIError {\n  message: string;\n  status: number;\n  code?: string;\n}\n\ninterface APIResponse<T = any> {\n  success: boolean;\n  message: string;\n  data?: T;\n}\n\nclass APIClient {\n  private client: AxiosInstance;\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';\n    \n    this.client = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor\n    this.client.interceptors.request.use(\n      (config) => {\n        // Add auth token if available\n        const token = this.getAuthToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n\n        // Add request timestamp for debugging\n        config.metadata = { startTime: new Date() };\n        \n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor\n    this.client.interceptors.response.use(\n      (response: AxiosResponse) => {\n        // Log response time in development\n        if (process.env.NODE_ENV === 'development') {\n          const endTime = new Date();\n          const startTime = response.config.metadata?.startTime;\n          if (startTime) {\n            const duration = endTime.getTime() - startTime.getTime();\n            console.log(`API ${response.config.method?.toUpperCase()} ${response.config.url}: ${duration}ms`);\n          }\n        }\n\n        return response;\n      },\n      (error) => {\n        this.handleError(error);\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  private getAuthToken(): string | null {\n    // Get token from localStorage or cookie\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('auth_token');\n    }\n    return null;\n  }\n\n  private handleError(error: any) {\n    let message = 'An unexpected error occurred';\n    let status = 500;\n\n    if (error.response) {\n      // Server responded with error status\n      status = error.response.status;\n      message = error.response.data?.message || error.response.statusText;\n\n      switch (status) {\n        case 401:\n          message = 'Authentication required. Please log in.';\n          this.handleAuthError();\n          break;\n        case 403:\n          message = 'You do not have permission to perform this action.';\n          break;\n        case 404:\n          message = 'The requested resource was not found.';\n          break;\n        case 422:\n          message = 'Invalid data provided. Please check your input.';\n          break;\n        case 429:\n          message = 'Too many requests. Please try again later.';\n          break;\n        case 500:\n          message = 'Server error. Please try again later.';\n          break;\n        case 503:\n          message = 'Service temporarily unavailable. Please try again later.';\n          break;\n      }\n    } else if (error.request) {\n      // Network error\n      message = 'Network error. Please check your connection.';\n      status = 0;\n    }\n\n    // Show error toast in production, log in development\n    if (process.env.NODE_ENV === 'production') {\n      toast.error(message);\n    } else {\n      console.error('API Error:', error);\n      toast.error(`${message} (${status})`);\n    }\n  }\n\n  private handleAuthError() {\n    // Clear auth token and redirect to login\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token');\n      // Redirect to login page\n      window.location.href = '/login';\n    }\n  }\n\n  // Generic request methods\n  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.client.get<T>(url, config);\n    return response.data;\n  }\n\n  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.client.post<T>(url, data, config);\n    return response.data;\n  }\n\n  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.client.put<T>(url, data, config);\n    return response.data;\n  }\n\n  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.client.patch<T>(url, data, config);\n    return response.data;\n  }\n\n  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.client.delete<T>(url, config);\n    return response.data;\n  }\n\n  // File upload method\n  async uploadFile<T = any>(\n    url: string,\n    file: File,\n    onProgress?: (progress: number) => void\n  ): Promise<T> {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    const config: AxiosRequestConfig = {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      onUploadProgress: (progressEvent) => {\n        if (onProgress && progressEvent.total) {\n          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n          onProgress(progress);\n        }\n      },\n    };\n\n    const response = await this.client.post<T>(url, formData, config);\n    return response.data;\n  }\n\n  // Batch request method\n  async batch<T = any>(requests: Array<() => Promise<any>>): Promise<T[]> {\n    try {\n      const results = await Promise.allSettled(requests.map(req => req()));\n      \n      return results.map((result, index) => {\n        if (result.status === 'fulfilled') {\n          return result.value;\n        } else {\n          console.error(`Batch request ${index} failed:`, result.reason);\n          throw result.reason;\n        }\n      });\n    } catch (error) {\n      console.error('Batch request failed:', error);\n      throw error;\n    }\n  }\n\n  // Health check method\n  async healthCheck(): Promise<boolean> {\n    try {\n      await this.get('/actuator/health');\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  // Set auth token\n  setAuthToken(token: string) {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('auth_token', token);\n    }\n  }\n\n  // Clear auth token\n  clearAuthToken() {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token');\n    }\n  }\n\n  // Get base URL\n  getBaseURL(): string {\n    return this.baseURL;\n  }\n\n  // Cancel all pending requests\n  cancelAllRequests() {\n    // Implementation would depend on tracking active requests\n    // For now, we'll create a new axios instance\n    this.client = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n    this.setupInterceptors();\n  }\n}\n\n// Create singleton instance\nconst apiClient = new APIClient();\n\nexport default apiClient;\n\n// Export types for use in other files\nexport type { APIError, APIResponse };\n\n// Utility function for handling API responses\nexport const handleAPIResponse = <T>(response: APIResponse<T>): T => {\n  if (!response.success) {\n    throw new Error(response.message || 'API request failed');\n  }\n  return response.data as T;\n};\n\n// Utility function for creating query strings\nexport const createQueryString = (params: Record<string, any>): string => {\n  const searchParams = new URLSearchParams();\n  \n  Object.entries(params).forEach(([key, value]) => {\n    if (value !== undefined && value !== null && value !== '') {\n      if (Array.isArray(value)) {\n        value.forEach(item => searchParams.append(key, String(item)));\n      } else {\n        searchParams.append(key, String(value));\n      }\n    }\n  });\n  \n  return searchParams.toString();\n};\n\n// Utility function for retrying failed requests\nexport const retryRequest = async <T>(\n  requestFn: () => Promise<T>,\n  maxRetries: number = 3,\n  delay: number = 1000\n): Promise<T> => {\n  let lastError: Error;\n  \n  for (let i = 0; i <= maxRetries; i++) {\n    try {\n      return await requestFn();\n    } catch (error) {\n      lastError = error as Error;\n      \n      if (i === maxRetries) {\n        break;\n      }\n      \n      // Wait before retrying\n      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));\n    }\n  }\n  \n  throw lastError!;\n};\n"], "names": ["createQueryString", "handleAPIResponse", "retryRequest", "APIClient", "constructor", "baseURL", "process", "env", "NEXT_PUBLIC_API_URL", "client", "axios", "create", "timeout", "headers", "setupInterceptors", "interceptors", "request", "use", "config", "token", "getAuthToken", "Authorization", "metadata", "startTime", "Date", "error", "Promise", "reject", "response", "NODE_ENV", "endTime", "duration", "getTime", "console", "log", "method", "toUpperCase", "url", "handleError", "window", "localStorage", "getItem", "message", "status", "data", "statusText", "handleAuthError", "toast", "removeItem", "location", "href", "get", "post", "put", "patch", "delete", "uploadFile", "file", "onProgress", "formData", "FormData", "append", "onUploadProgress", "progressEvent", "total", "progress", "Math", "round", "loaded", "batch", "requests", "results", "allSettled", "map", "req", "result", "index", "value", "reason", "healthCheck", "setAuthToken", "setItem", "clearAuthToken", "getBaseURL", "cancelAllRequests", "apiClient", "success", "Error", "params", "searchParams", "URLSearchParams", "Object", "entries", "for<PERSON>ach", "key", "undefined", "Array", "isArray", "item", "String", "toString", "requestFn", "maxRetries", "delay", "lastError", "i", "resolve", "setTimeout", "pow"], "mappings": ";;;;;;;;;;;IAgRaA,iBAAiB;eAAjBA;;IAdb,OAAyB;eAAzB;;IAMaC,iBAAiB;eAAjBA;;IAyBAC,YAAY;eAAZA;;;8DAjS2D;+BAClD;;;;;;AAetB,MAAMC;IAIJC,aAAc;QACZ,IAAI,CAACC,OAAO,GAAGC,QAAQC,GAAG,CAACC,mBAAmB,IAAI;QAElD,IAAI,CAACC,MAAM,GAAGC,cAAK,CAACC,MAAM,CAAC;YACzBN,SAAS,IAAI,CAACA,OAAO;YACrBO,SAAS;YACTC,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAACC,iBAAiB;IACxB;IAEQA,oBAAoB;QAC1B,sBAAsB;QACtB,IAAI,CAACL,MAAM,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CAClC,CAACC;YACC,8BAA8B;YAC9B,MAAMC,QAAQ,IAAI,CAACC,YAAY;YAC/B,IAAID,OAAO;gBACTD,OAAOL,OAAO,CAACQ,aAAa,GAAG,CAAC,OAAO,EAAEF,MAAM,CAAC;YAClD;YAEA,sCAAsC;YACtCD,OAAOI,QAAQ,GAAG;gBAAEC,WAAW,IAAIC;YAAO;YAE1C,OAAON;QACT,GACA,CAACO;YACC,OAAOC,QAAQC,MAAM,CAACF;QACxB;QAGF,uBAAuB;QACvB,IAAI,CAAChB,MAAM,CAACM,YAAY,CAACa,QAAQ,CAACX,GAAG,CACnC,CAACW;YACC,mCAAmC;YACnC,IAAItB,QAAQC,GAAG,CAACsB,QAAQ,KAAK,eAAe;gBAC1C,MAAMC,UAAU,IAAIN;gBACpB,MAAMD,YAAYK,SAASV,MAAM,CAACI,QAAQ,EAAEC;gBAC5C,IAAIA,WAAW;oBACb,MAAMQ,WAAWD,QAAQE,OAAO,KAAKT,UAAUS,OAAO;oBACtDC,QAAQC,GAAG,CAAC,CAAC,IAAI,EAAEN,SAASV,MAAM,CAACiB,MAAM,EAAEC,cAAc,CAAC,EAAER,SAASV,MAAM,CAACmB,GAAG,CAAC,EAAE,EAAEN,SAAS,EAAE,CAAC;gBAClG;YACF;YAEA,OAAOH;QACT,GACA,CAACH;YACC,IAAI,CAACa,WAAW,CAACb;YACjB,OAAOC,QAAQC,MAAM,CAACF;QACxB;IAEJ;IAEQL,eAA8B;QACpC,wCAAwC;QACxC,IAAI,OAAOmB,WAAW,aAAa;YACjC,OAAOC,aAAaC,OAAO,CAAC;QAC9B;QACA,OAAO;IACT;IAEQH,YAAYb,KAAU,EAAE;QAC9B,IAAIiB,UAAU;QACd,IAAIC,SAAS;QAEb,IAAIlB,MAAMG,QAAQ,EAAE;YAClB,qCAAqC;YACrCe,SAASlB,MAAMG,QAAQ,CAACe,MAAM;YAC9BD,UAAUjB,MAAMG,QAAQ,CAACgB,IAAI,EAAEF,WAAWjB,MAAMG,QAAQ,CAACiB,UAAU;YAEnE,OAAQF;gBACN,KAAK;oBACHD,UAAU;oBACV,IAAI,CAACI,eAAe;oBACpB;gBACF,KAAK;oBACHJ,UAAU;oBACV;gBACF,KAAK;oBACHA,UAAU;oBACV;gBACF,KAAK;oBACHA,UAAU;oBACV;gBACF,KAAK;oBACHA,UAAU;oBACV;gBACF,KAAK;oBACHA,UAAU;oBACV;gBACF,KAAK;oBACHA,UAAU;oBACV;YACJ;QACF,OAAO,IAAIjB,MAAMT,OAAO,EAAE;YACxB,gBAAgB;YAChB0B,UAAU;YACVC,SAAS;QACX;QAEA,qDAAqD;QACrD,IAAIrC,QAAQC,GAAG,CAACsB,QAAQ,KAAK,cAAc;YACzCkB,oBAAK,CAACtB,KAAK,CAACiB;QACd,OAAO;YACLT,QAAQR,KAAK,CAAC,cAAcA;YAC5BsB,oBAAK,CAACtB,KAAK,CAAC,CAAC,EAAEiB,QAAQ,EAAE,EAAEC,OAAO,CAAC,CAAC;QACtC;IACF;IAEQG,kBAAkB;QACxB,yCAAyC;QACzC,IAAI,OAAOP,WAAW,aAAa;YACjCC,aAAaQ,UAAU,CAAC;YACxB,yBAAyB;YACzBT,OAAOU,QAAQ,CAACC,IAAI,GAAG;QACzB;IACF;IAEA,0BAA0B;IAC1B,MAAMC,IAAad,GAAW,EAAEnB,MAA2B,EAAc;QACvE,MAAMU,WAAW,MAAM,IAAI,CAACnB,MAAM,CAAC0C,GAAG,CAAId,KAAKnB;QAC/C,OAAOU,SAASgB,IAAI;IACtB;IAEA,MAAMQ,KAAcf,GAAW,EAAEO,IAAU,EAAE1B,MAA2B,EAAc;QACpF,MAAMU,WAAW,MAAM,IAAI,CAACnB,MAAM,CAAC2C,IAAI,CAAIf,KAAKO,MAAM1B;QACtD,OAAOU,SAASgB,IAAI;IACtB;IAEA,MAAMS,IAAahB,GAAW,EAAEO,IAAU,EAAE1B,MAA2B,EAAc;QACnF,MAAMU,WAAW,MAAM,IAAI,CAACnB,MAAM,CAAC4C,GAAG,CAAIhB,KAAKO,MAAM1B;QACrD,OAAOU,SAASgB,IAAI;IACtB;IAEA,MAAMU,MAAejB,GAAW,EAAEO,IAAU,EAAE1B,MAA2B,EAAc;QACrF,MAAMU,WAAW,MAAM,IAAI,CAACnB,MAAM,CAAC6C,KAAK,CAAIjB,KAAKO,MAAM1B;QACvD,OAAOU,SAASgB,IAAI;IACtB;IAEA,MAAMW,OAAgBlB,GAAW,EAAEnB,MAA2B,EAAc;QAC1E,MAAMU,WAAW,MAAM,IAAI,CAACnB,MAAM,CAAC8C,MAAM,CAAIlB,KAAKnB;QAClD,OAAOU,SAASgB,IAAI;IACtB;IAEA,qBAAqB;IACrB,MAAMY,WACJnB,GAAW,EACXoB,IAAU,EACVC,UAAuC,EAC3B;QACZ,MAAMC,WAAW,IAAIC;QACrBD,SAASE,MAAM,CAAC,QAAQJ;QAExB,MAAMvC,SAA6B;YACjCL,SAAS;gBACP,gBAAgB;YAClB;YACAiD,kBAAkB,CAACC;gBACjB,IAAIL,cAAcK,cAAcC,KAAK,EAAE;oBACrC,MAAMC,WAAWC,KAAKC,KAAK,CAAC,AAACJ,cAAcK,MAAM,GAAG,MAAOL,cAAcC,KAAK;oBAC9EN,WAAWO;gBACb;YACF;QACF;QAEA,MAAMrC,WAAW,MAAM,IAAI,CAACnB,MAAM,CAAC2C,IAAI,CAAIf,KAAKsB,UAAUzC;QAC1D,OAAOU,SAASgB,IAAI;IACtB;IAEA,uBAAuB;IACvB,MAAMyB,MAAeC,QAAmC,EAAgB;QACtE,IAAI;YACF,MAAMC,UAAU,MAAM7C,QAAQ8C,UAAU,CAACF,SAASG,GAAG,CAACC,CAAAA,MAAOA;YAE7D,OAAOH,QAAQE,GAAG,CAAC,CAACE,QAAQC;gBAC1B,IAAID,OAAOhC,MAAM,KAAK,aAAa;oBACjC,OAAOgC,OAAOE,KAAK;gBACrB,OAAO;oBACL5C,QAAQR,KAAK,CAAC,CAAC,cAAc,EAAEmD,MAAM,QAAQ,CAAC,EAAED,OAAOG,MAAM;oBAC7D,MAAMH,OAAOG,MAAM;gBACrB;YACF;QACF,EAAE,OAAOrD,OAAO;YACdQ,QAAQR,KAAK,CAAC,yBAAyBA;YACvC,MAAMA;QACR;IACF;IAEA,sBAAsB;IACtB,MAAMsD,cAAgC;QACpC,IAAI;YACF,MAAM,IAAI,CAAC5B,GAAG,CAAC;YACf,OAAO;QACT,EAAE,OAAO1B,OAAO;YACd,OAAO;QACT;IACF;IAEA,iBAAiB;IACjBuD,aAAa7D,KAAa,EAAE;QAC1B,IAAI,OAAOoB,WAAW,aAAa;YACjCC,aAAayC,OAAO,CAAC,cAAc9D;QACrC;IACF;IAEA,mBAAmB;IACnB+D,iBAAiB;QACf,IAAI,OAAO3C,WAAW,aAAa;YACjCC,aAAaQ,UAAU,CAAC;QAC1B;IACF;IAEA,eAAe;IACfmC,aAAqB;QACnB,OAAO,IAAI,CAAC9E,OAAO;IACrB;IAEA,8BAA8B;IAC9B+E,oBAAoB;QAClB,0DAA0D;QAC1D,6CAA6C;QAC7C,IAAI,CAAC3E,MAAM,GAAGC,cAAK,CAACC,MAAM,CAAC;YACzBN,SAAS,IAAI,CAACA,OAAO;YACrBO,SAAS;YACTC,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,IAAI,CAACC,iBAAiB;IACxB;AACF;AAEA,4BAA4B;AAC5B,MAAMuE,YAAY,IAAIlF;MAEtB,WAAekF;AAMR,MAAMpF,oBAAoB,CAAI2B;IACnC,IAAI,CAACA,SAAS0D,OAAO,EAAE;QACrB,MAAM,IAAIC,MAAM3D,SAASc,OAAO,IAAI;IACtC;IACA,OAAOd,SAASgB,IAAI;AACtB;AAGO,MAAM5C,oBAAoB,CAACwF;IAChC,MAAMC,eAAe,IAAIC;IAEzBC,OAAOC,OAAO,CAACJ,QAAQK,OAAO,CAAC,CAAC,CAACC,KAAKjB,MAAM;QAC1C,IAAIA,UAAUkB,aAAalB,UAAU,QAAQA,UAAU,IAAI;YACzD,IAAImB,MAAMC,OAAO,CAACpB,QAAQ;gBACxBA,MAAMgB,OAAO,CAACK,CAAAA,OAAQT,aAAa5B,MAAM,CAACiC,KAAKK,OAAOD;YACxD,OAAO;gBACLT,aAAa5B,MAAM,CAACiC,KAAKK,OAAOtB;YAClC;QACF;IACF;IAEA,OAAOY,aAAaW,QAAQ;AAC9B;AAGO,MAAMlG,eAAe,OAC1BmG,WACAC,aAAqB,CAAC,EACtBC,QAAgB,IAAI;IAEpB,IAAIC;IAEJ,IAAK,IAAIC,IAAI,GAAGA,KAAKH,YAAYG,IAAK;QACpC,IAAI;YACF,OAAO,MAAMJ;QACf,EAAE,OAAO5E,OAAO;YACd+E,YAAY/E;YAEZ,IAAIgF,MAAMH,YAAY;gBACpB;YACF;YAEA,uBAAuB;YACvB,MAAM,IAAI5E,QAAQgF,CAAAA,UAAWC,WAAWD,SAASH,QAAQrC,KAAK0C,GAAG,CAAC,GAAGH;QACvE;IACF;IAEA,MAAMD;AACR"}