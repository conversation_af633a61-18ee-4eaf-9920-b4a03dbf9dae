7f67059c7a5c1b3239557b8f58249087
"use strict";
// Mock the dashboard store
jest.mock("@/stores/dashboard-store");
// Mock framer-motion
jest.mock("framer-motion", ()=>({
        motion: {
            div: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    ...props,
                    children: children
                }),
            li: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("li", {
                    ...props,
                    children: children
                })
        },
        AnimatePresence: ({ children })=>children
    }));
// Mock animated components
jest.mock("@/components/ui/animated-alert", ()=>({
        AnimatedAlert: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "animated-alert",
                ...props,
                children: children
            }),
        RealtimeAlerts: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "realtime-alerts-container",
                children: children
            })
    }));
jest.mock("@/components/ui/animated-components", ()=>({
        StaggerContainer: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "stagger-container",
                children: children
            }),
        StaggerItem: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "stagger-item",
                children: children
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../../lib/test-utils");
const _realtimealerts = require("../realtime-alerts");
const _dashboardstore = require("../../../../stores/dashboard-store");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockUseDashboardStore = _dashboardstore.useDashboardStore;
describe("RealtimeAlerts Component", ()=>{
    const mockAlerts = [
        (0, _testutils.createMockAlert)({
            id: 1,
            type: "ERROR",
            title: "Critical Battery",
            message: "UAV-001 battery level is critically low (5%)",
            severity: "HIGH",
            acknowledged: false,
            timestamp: "2024-01-01T10:00:00Z"
        }),
        (0, _testutils.createMockAlert)({
            id: 2,
            type: "WARNING",
            title: "Maintenance Required",
            message: "UAV-002 requires scheduled maintenance",
            severity: "MEDIUM",
            acknowledged: false,
            timestamp: "2024-01-01T09:30:00Z"
        }),
        (0, _testutils.createMockAlert)({
            id: 3,
            type: "INFO",
            title: "Flight Completed",
            message: "UAV-003 has successfully completed mission Alpha",
            severity: "LOW",
            acknowledged: true,
            timestamp: "2024-01-01T09:00:00Z"
        })
    ];
    const mockAcknowledgeAlert = jest.fn();
    const mockRemoveAlert = jest.fn();
    const mockClearAlerts = jest.fn();
    const mockToggleAlerts = jest.fn();
    beforeEach(()=>{
        jest.clearAllMocks();
        mockUseDashboardStore.mockReturnValue({
            alerts: mockAlerts,
            showAlerts: true,
            acknowledgeAlert: mockAcknowledgeAlert,
            removeAlert: mockRemoveAlert,
            clearAlerts: mockClearAlerts,
            toggleAlerts: mockToggleAlerts,
            getAlertCounts: jest.fn(()=>({
                    total: 3,
                    error: 1,
                    warning: 1,
                    info: 1,
                    high: 1,
                    medium: 1,
                    low: 1
                })),
            getUnacknowledgedAlerts: jest.fn(()=>mockAlerts.filter((a)=>!a.acknowledged)),
            // Other store properties
            metrics: null,
            flightActivity: null,
            batteryStats: null,
            hibernatePodMetrics: null,
            chartData: null,
            recentLocationUpdates: [],
            isConnected: true,
            lastUpdate: null,
            connectionError: null,
            selectedTimeRange: "24h",
            autoRefresh: true,
            refreshInterval: 30,
            updateMetrics: jest.fn(),
            updateFlightActivity: jest.fn(),
            updateBatteryStats: jest.fn(),
            updateHibernatePodMetrics: jest.fn(),
            updateChartData: jest.fn(),
            addAlert: jest.fn(),
            addLocationUpdate: jest.fn(),
            setConnectionStatus: jest.fn(),
            clearConnectionError: jest.fn(),
            setTimeRange: jest.fn(),
            toggleAutoRefresh: jest.fn(),
            setRefreshInterval: jest.fn(),
            resetData: jest.fn()
        });
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText("Real-time Alerts")).toBeInTheDocument();
        expect(_testutils.screen.getByText("System notifications and warnings")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("stagger-container")).toBeInTheDocument();
    });
    it("displays all alerts", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText("Critical Battery")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Maintenance Required")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Flight Completed")).toBeInTheDocument();
    });
    it("shows alert count in header", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText("3")).toBeInTheDocument() // Total alert count
        ;
    });
    it("displays different alert types with correct styling", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const errorAlert = _testutils.screen.getByText("Critical Battery").closest('[data-testid="stagger-item"]');
        const warningAlert = _testutils.screen.getByText("Maintenance Required").closest('[data-testid="stagger-item"]');
        const infoAlert = _testutils.screen.getByText("Flight Completed").closest('[data-testid="stagger-item"]');
        expect(errorAlert).toBeInTheDocument();
        expect(warningAlert).toBeInTheDocument();
        expect(infoAlert).toBeInTheDocument();
    });
    it("handles alert acknowledgment", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const acknowledgeButton = _testutils.screen.getAllByRole("button", {
            name: /acknowledge/i
        })[0];
        _testutils.fireEvent.click(acknowledgeButton);
        expect(mockAcknowledgeAlert).toHaveBeenCalledWith(1);
    });
    it("handles alert removal", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const removeButton = _testutils.screen.getAllByRole("button", {
            name: /remove|dismiss/i
        })[0];
        _testutils.fireEvent.click(removeButton);
        expect(mockRemoveAlert).toHaveBeenCalledWith(1);
    });
    it("handles clear all alerts", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const clearAllButton = _testutils.screen.getByRole("button", {
            name: /clear all/i
        });
        _testutils.fireEvent.click(clearAllButton);
        expect(mockClearAlerts).toHaveBeenCalled();
    });
    it("toggles alerts visibility", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const toggleButton = _testutils.screen.getByRole("button", {
            name: /toggle alerts/i
        });
        _testutils.fireEvent.click(toggleButton);
        expect(mockToggleAlerts).toHaveBeenCalled();
    });
    it("hides alerts when showAlerts is false", ()=>{
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            showAlerts: false
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.queryByText("Critical Battery")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByText("Maintenance Required")).not.toBeInTheDocument();
    });
    it("displays empty state when no alerts", ()=>{
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            alerts: []
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText(/no alerts/i)).toBeInTheDocument();
        expect(_testutils.screen.getByText(/all systems are running normally/i)).toBeInTheDocument();
    });
    it("filters alerts by type", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const errorFilter = _testutils.screen.getByRole("button", {
            name: /error/i
        });
        _testutils.fireEvent.click(errorFilter);
        expect(_testutils.screen.getByText("Critical Battery")).toBeInTheDocument();
        expect(_testutils.screen.queryByText("Maintenance Required")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByText("Flight Completed")).not.toBeInTheDocument();
    });
    it("filters alerts by severity", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const highSeverityFilter = _testutils.screen.getByRole("button", {
            name: /high/i
        });
        _testutils.fireEvent.click(highSeverityFilter);
        expect(_testutils.screen.getByText("Critical Battery")).toBeInTheDocument();
        expect(_testutils.screen.queryByText("Maintenance Required")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByText("Flight Completed")).not.toBeInTheDocument();
    });
    it("shows only unacknowledged alerts when filter is applied", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const unacknowledgedFilter = _testutils.screen.getByRole("button", {
            name: /unacknowledged/i
        });
        _testutils.fireEvent.click(unacknowledgedFilter);
        expect(_testutils.screen.getByText("Critical Battery")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Maintenance Required")).toBeInTheDocument();
        expect(_testutils.screen.queryByText("Flight Completed")).not.toBeInTheDocument();
    });
    it("displays alert timestamps", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        // Timestamps would be formatted and displayed
        expect(_testutils.screen.getByText(/10:00/)).toBeInTheDocument();
        expect(_testutils.screen.getByText(/09:30/)).toBeInTheDocument();
        expect(_testutils.screen.getByText(/09:00/)).toBeInTheDocument();
    });
    it("shows alert severity badges", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText("HIGH")).toBeInTheDocument();
        expect(_testutils.screen.getByText("MEDIUM")).toBeInTheDocument();
        expect(_testutils.screen.getByText("LOW")).toBeInTheDocument();
    });
    it("handles real-time alert updates", async ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const newAlert = (0, _testutils.createMockAlert)({
            id: 4,
            type: "ERROR",
            title: "Connection Lost",
            message: "Lost connection to UAV-004",
            severity: "HIGH",
            acknowledged: false,
            timestamp: new Date().toISOString()
        });
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            alerts: [
                ...mockAlerts,
                newAlert
            ]
        });
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Connection Lost")).toBeInTheDocument();
        });
    });
    it("sorts alerts by timestamp (newest first)", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const alertTitles = _testutils.screen.getAllByRole("heading", {
            level: 4
        });
        expect(alertTitles[0]).toHaveTextContent("Critical Battery") // 10:00
        ;
        expect(alertTitles[1]).toHaveTextContent("Maintenance Required") // 09:30
        ;
        expect(alertTitles[2]).toHaveTextContent("Flight Completed") // 09:00
        ;
    });
    it("limits displayed alerts to maximum count", ()=>{
        const manyAlerts = Array.from({
            length: 25
        }, (_, i)=>(0, _testutils.createMockAlert)({
                id: i + 1,
                title: `Alert ${i + 1}`,
                timestamp: new Date(Date.now() - i * 60000).toISOString()
            }));
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            alerts: manyAlerts
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {
            maxAlerts: 20
        }));
        const alertItems = _testutils.screen.getAllByTestId("stagger-item");
        expect(alertItems).toHaveLength(20);
    });
    it("shows load more button when there are more alerts", ()=>{
        const manyAlerts = Array.from({
            length: 25
        }, (_, i)=>(0, _testutils.createMockAlert)({
                id: i + 1,
                title: `Alert ${i + 1}`
            }));
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            alerts: manyAlerts
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {
            maxAlerts: 20
        }));
        expect(_testutils.screen.getByRole("button", {
            name: /load more/i
        })).toBeInTheDocument();
    });
    it("handles alert search", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search alerts/i);
        _testutils.fireEvent.change(searchInput, {
            target: {
                value: "battery"
            }
        });
        expect(_testutils.screen.getByText("Critical Battery")).toBeInTheDocument();
        expect(_testutils.screen.queryByText("Maintenance Required")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByText("Flight Completed")).not.toBeInTheDocument();
    });
    it("supports keyboard navigation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const firstAlert = _testutils.screen.getByText("Critical Battery").closest('[data-testid="stagger-item"]');
        const acknowledgeButton = _testutils.screen.getAllByRole("button", {
            name: /acknowledge/i
        })[0];
        acknowledgeButton.focus();
        expect(acknowledgeButton).toHaveFocus();
        _testutils.fireEvent.keyDown(acknowledgeButton, {
            key: "Enter"
        });
        expect(mockAcknowledgeAlert).toHaveBeenCalledWith(1);
    });
    it("shows connection status indicator", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByTestId("connection-indicator")).toBeInTheDocument();
        expect(_testutils.screen.getByText(/connected/i)).toBeInTheDocument();
    });
    it("handles disconnected state", ()=>{
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            isConnected: false,
            connectionError: "Connection lost"
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText(/disconnected/i)).toBeInTheDocument();
        expect(_testutils.screen.getByText("Connection lost")).toBeInTheDocument();
    });
    it("auto-refreshes alerts when connected", ()=>{
        jest.useFakeTimers();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {
            autoRefresh: true,
            refreshInterval: 5000
        }));
        // Fast-forward time
        jest.advanceTimersByTime(5000);
        // Auto-refresh would trigger store updates
        expect(_testutils.screen.getByTestId("stagger-container")).toBeInTheDocument();
        jest.useRealTimers();
    });
});

//# sourceMappingURL=data:application/json;base64,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