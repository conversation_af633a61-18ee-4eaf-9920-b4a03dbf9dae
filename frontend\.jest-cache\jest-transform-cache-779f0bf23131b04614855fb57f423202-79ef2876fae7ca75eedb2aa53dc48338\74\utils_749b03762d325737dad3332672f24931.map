{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\lib\\utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Date formatting utilities\nexport function formatDate(date: string | Date, format: 'short' | 'long' | 'time' = 'short'): string {\n  const d = new Date(date);\n  \n  if (isNaN(d.getTime())) {\n    return 'Invalid Date';\n  }\n  \n  switch (format) {\n    case 'short':\n      return d.toLocaleDateString();\n    case 'long':\n      return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n      });\n    case 'time':\n      return d.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n      });\n    default:\n      return d.toLocaleDateString();\n  }\n}\n\nexport function formatDateTime(date: string | Date): string {\n  const d = new Date(date);\n  \n  if (isNaN(d.getTime())) {\n    return 'Invalid Date';\n  }\n  \n  return d.toLocaleString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\nexport function formatRelativeTime(date: string | Date): string {\n  const d = new Date(date);\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n  \n  if (diffInSeconds < 60) {\n    return 'Just now';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n  } else {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days} day${days > 1 ? 's' : ''} ago`;\n  }\n}\n\n// Number formatting utilities\nexport function formatNumber(num: number, decimals: number = 0): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(num);\n}\n\nexport function formatPercentage(num: number, decimals: number = 1): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'percent',\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(num / 100);\n}\n\nexport function formatBytes(bytes: number, decimals: number = 2): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  \n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n}\n\n// String utilities\nexport function truncateString(str: string, length: number): string {\n  if (str.length <= length) return str;\n  return str.slice(0, length) + '...';\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n}\n\nexport function camelToTitle(str: string): string {\n  return str\n    .replace(/([A-Z])/g, ' $1')\n    .replace(/^./, (str) => str.toUpperCase())\n    .trim();\n}\n\n// Status utilities\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    AUTHORIZED: 'text-green-600 bg-green-100 border-green-200',\n    UNAUTHORIZED: 'text-red-600 bg-red-100 border-red-200',\n    HIBERNATING: 'text-purple-600 bg-purple-100 border-purple-200',\n    CHARGING: 'text-yellow-600 bg-yellow-100 border-yellow-200',\n    MAINTENANCE: 'text-orange-600 bg-orange-100 border-orange-200',\n    EMERGENCY: 'text-red-800 bg-red-200 border-red-300',\n    READY: 'text-blue-600 bg-blue-100 border-blue-200',\n    IN_FLIGHT: 'text-indigo-600 bg-indigo-100 border-indigo-200',\n    OUT_OF_SERVICE: 'text-gray-600 bg-gray-100 border-gray-200',\n  };\n\n  return statusColors[status] || 'text-gray-600 bg-gray-100 border-gray-200';\n}\n\nexport function getStatusVariant(status: string): 'default' | 'secondary' | 'destructive' | 'success' | 'warning' | 'info' {\n  const statusVariants: Record<string, 'default' | 'secondary' | 'destructive' | 'success' | 'warning' | 'info'> = {\n    AUTHORIZED: 'success',\n    UNAUTHORIZED: 'destructive',\n    HIBERNATING: 'secondary',\n    CHARGING: 'warning',\n    MAINTENANCE: 'warning',\n    EMERGENCY: 'destructive',\n    READY: 'info',\n    IN_FLIGHT: 'info',\n    OUT_OF_SERVICE: 'secondary',\n  };\n\n  return statusVariants[status] || 'secondary';\n}\n\nexport function getBatteryColor(percentage: number): string {\n  if (percentage > 50) return 'text-green-600';\n  if (percentage > 20) return 'text-yellow-600';\n  return 'text-red-600';\n}\n\nexport function getBatteryIcon(percentage: number): string {\n  if (percentage > 75) return 'battery-full';\n  if (percentage > 50) return 'battery-three-quarters';\n  if (percentage > 25) return 'battery-half';\n  if (percentage > 10) return 'battery-quarter';\n  return 'battery-empty';\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function isValidRFID(rfid: string): boolean {\n  // RFID should be alphanumeric with hyphens and underscores, 3-50 characters\n  const rfidRegex = /^[A-Za-z0-9-_]{3,50}$/;\n  return rfidRegex.test(rfid);\n}\n\n// Array utilities\nexport function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {\n  return array.reduce((groups, item) => {\n    const group = String(item[key]);\n    groups[group] = groups[group] || [];\n    groups[group].push(item);\n    return groups;\n  }, {} as Record<string, T[]>);\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key];\n    const bVal = b[key];\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n    return 0;\n  });\n}\n\n// Debounce utility\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Local storage utilities\nexport function getFromStorage<T>(key: string, defaultValue: T): T {\n  if (typeof window === 'undefined') return defaultValue;\n  \n  try {\n    const item = localStorage.getItem(key);\n    return item ? JSON.parse(item) : defaultValue;\n  } catch (error) {\n    console.error(`Error reading from localStorage key \"${key}\":`, error);\n    return defaultValue;\n  }\n}\n\nexport function setToStorage<T>(key: string, value: T): void {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    localStorage.setItem(key, JSON.stringify(value));\n  } catch (error) {\n    console.error(`Error writing to localStorage key \"${key}\":`, error);\n  }\n}\n\nexport function removeFromStorage(key: string): void {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    localStorage.removeItem(key);\n  } catch (error) {\n    console.error(`Error removing from localStorage key \"${key}\":`, error);\n  }\n}\n\n// URL utilities\nexport function createSearchParams(params: Record<string, any>): URLSearchParams {\n  const searchParams = new URLSearchParams();\n  \n  Object.entries(params).forEach(([key, value]) => {\n    if (value !== undefined && value !== null && value !== '') {\n      if (Array.isArray(value)) {\n        value.forEach(item => searchParams.append(key, String(item)));\n      } else {\n        searchParams.append(key, String(value));\n      }\n    }\n  });\n  \n  return searchParams;\n}\n\n// Error handling utilities\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) return error.message;\n  if (typeof error === 'string') return error;\n  return 'An unknown error occurred';\n}\n\n// Color utilities for charts\nexport const chartColors = {\n  primary: '#3498db',\n  secondary: '#2c3e50',\n  success: '#2ecc71',\n  warning: '#f39c12',\n  danger: '#e74c3c',\n  info: '#17a2b8',\n  light: '#f8f9fa',\n  dark: '#343a40',\n};\n\nexport function generateChartColors(count: number): string[] {\n  const baseColors = Object.values(chartColors);\n  const colors: string[] = [];\n  \n  for (let i = 0; i < count; i++) {\n    colors.push(baseColors[i % baseColors.length]);\n  }\n  \n  return colors;\n}\n"], "names": ["camelToTitle", "capitalizeFirst", "chartColors", "cn", "createSearchParams", "debounce", "formatBytes", "formatDate", "formatDateTime", "formatNumber", "formatPercentage", "formatRelativeTime", "generateChartColors", "getBatteryColor", "getBatteryIcon", "getErrorMessage", "getFromStorage", "getStatusColor", "getStatusVariant", "groupBy", "isValidEmail", "isValidRFID", "removeFromStorage", "setToStorage", "sortBy", "truncateString", "inputs", "twMerge", "clsx", "date", "format", "d", "Date", "isNaN", "getTime", "toLocaleDateString", "year", "month", "day", "toLocaleTimeString", "hour", "minute", "toLocaleString", "now", "diffInSeconds", "Math", "floor", "minutes", "hours", "days", "num", "decimals", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "style", "bytes", "k", "dm", "sizes", "i", "log", "parseFloat", "pow", "toFixed", "str", "length", "slice", "char<PERSON>t", "toUpperCase", "toLowerCase", "replace", "trim", "status", "statusColors", "AUTHORIZED", "UNAUTHORIZED", "HIBERNATING", "CHARGING", "MAINTENANCE", "EMERGENCY", "READY", "IN_FLIGHT", "OUT_OF_SERVICE", "statusVariants", "percentage", "email", "emailRegex", "test", "rfid", "rfidRegex", "array", "key", "reduce", "groups", "item", "group", "String", "push", "direction", "sort", "a", "b", "aVal", "bVal", "func", "wait", "timeout", "args", "clearTimeout", "setTimeout", "defaultValue", "window", "localStorage", "getItem", "JSON", "parse", "error", "console", "value", "setItem", "stringify", "removeItem", "params", "searchParams", "URLSearchParams", "Object", "entries", "for<PERSON>ach", "undefined", "Array", "isArray", "append", "Error", "message", "primary", "secondary", "success", "warning", "danger", "info", "light", "dark", "count", "baseColors", "values", "colors"], "mappings": ";;;;;;;;;;;IA2GgBA,YAAY;eAAZA;;IAJAC,eAAe;eAAfA;;IAkKHC,WAAW;eAAXA;;IAtQGC,EAAE;eAAFA;;IA8OAC,kBAAkB;eAAlBA;;IA9CAC,QAAQ;eAARA;;IA9GAC,WAAW;eAAXA;;IA7EAC,UAAU;eAAVA;;IA0BAC,cAAc;eAAdA;;IAoCAC,YAAY;eAAZA;;IAOAC,gBAAgB;eAAhBA;;IA3BAC,kBAAkB;eAAlBA;;IAkOAC,mBAAmB;eAAnBA;;IAjIAC,eAAe;eAAfA;;IAMAC,cAAc;eAAdA;;IAyGAC,eAAe;eAAfA;;IAlDAC,cAAc;eAAdA;;IA7FAC,cAAc;eAAdA;;IAgBAC,gBAAgB;eAAhBA;;IA2CAC,OAAO;eAAPA;;IAZAC,YAAY;eAAZA;;IAKAC,WAAW;eAAXA;;IA+DAC,iBAAiB;eAAjBA;;IAVAC,YAAY;eAAZA;;IArCAC,MAAM;eAANA;;IArFAC,cAAc;eAAdA;;;sBAlGsB;+BACd;AAEjB,SAAStB,GAAG,GAAGuB,MAAoB;IACxC,OAAOC,IAAAA,sBAAO,EAACC,IAAAA,UAAI,EAACF;AACtB;AAGO,SAASnB,WAAWsB,IAAmB,EAAEC,SAAoC,OAAO;IACzF,MAAMC,IAAI,IAAIC,KAAKH;IAEnB,IAAII,MAAMF,EAAEG,OAAO,KAAK;QACtB,OAAO;IACT;IAEA,OAAQJ;QACN,KAAK;YACH,OAAOC,EAAEI,kBAAkB;QAC7B,KAAK;YACH,OAAOJ,EAAEI,kBAAkB,CAAC,SAAS;gBACnCC,MAAM;gBACNC,OAAO;gBACPC,KAAK;YACP;QACF,KAAK;YACH,OAAOP,EAAEQ,kBAAkB,CAAC,SAAS;gBACnCC,MAAM;gBACNC,QAAQ;YACV;QACF;YACE,OAAOV,EAAEI,kBAAkB;IAC/B;AACF;AAEO,SAAS3B,eAAeqB,IAAmB;IAChD,MAAME,IAAI,IAAIC,KAAKH;IAEnB,IAAII,MAAMF,EAAEG,OAAO,KAAK;QACtB,OAAO;IACT;IAEA,OAAOH,EAAEW,cAAc,CAAC,SAAS;QAC/BN,MAAM;QACNC,OAAO;QACPC,KAAK;QACLE,MAAM;QACNC,QAAQ;IACV;AACF;AAEO,SAAS9B,mBAAmBkB,IAAmB;IACpD,MAAME,IAAI,IAAIC,KAAKH;IACnB,MAAMc,MAAM,IAAIX;IAChB,MAAMY,gBAAgBC,KAAKC,KAAK,CAAC,AAACH,CAAAA,IAAIT,OAAO,KAAKH,EAAEG,OAAO,EAAC,IAAK;IAEjE,IAAIU,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAIA,gBAAgB,MAAM;QAC/B,MAAMG,UAAUF,KAAKC,KAAK,CAACF,gBAAgB;QAC3C,OAAO,CAAC,EAAEG,QAAQ,OAAO,EAAEA,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;IACzD,OAAO,IAAIH,gBAAgB,OAAO;QAChC,MAAMI,QAAQH,KAAKC,KAAK,CAACF,gBAAgB;QACzC,OAAO,CAAC,EAAEI,MAAM,KAAK,EAAEA,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO;QACL,MAAMC,OAAOJ,KAAKC,KAAK,CAACF,gBAAgB;QACxC,OAAO,CAAC,EAAEK,KAAK,IAAI,EAAEA,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;IAChD;AACF;AAGO,SAASxC,aAAayC,GAAW,EAAEC,WAAmB,CAAC;IAC5D,OAAO,IAAIC,KAAKC,YAAY,CAAC,SAAS;QACpCC,uBAAuBH;QACvBI,uBAAuBJ;IACzB,GAAGrB,MAAM,CAACoB;AACZ;AAEO,SAASxC,iBAAiBwC,GAAW,EAAEC,WAAmB,CAAC;IAChE,OAAO,IAAIC,KAAKC,YAAY,CAAC,SAAS;QACpCG,OAAO;QACPF,uBAAuBH;QACvBI,uBAAuBJ;IACzB,GAAGrB,MAAM,CAACoB,MAAM;AAClB;AAEO,SAAS5C,YAAYmD,KAAa,EAAEN,WAAmB,CAAC;IAC7D,IAAIM,UAAU,GAAG,OAAO;IAExB,MAAMC,IAAI;IACV,MAAMC,KAAKR,WAAW,IAAI,IAAIA;IAC9B,MAAMS,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAE/C,MAAMC,IAAIhB,KAAKC,KAAK,CAACD,KAAKiB,GAAG,CAACL,SAASZ,KAAKiB,GAAG,CAACJ;IAEhD,OAAOK,WAAW,AAACN,CAAAA,QAAQZ,KAAKmB,GAAG,CAACN,GAAGG,EAAC,EAAGI,OAAO,CAACN,OAAO,MAAMC,KAAK,CAACC,EAAE;AAC1E;AAGO,SAASpC,eAAeyC,GAAW,EAAEC,MAAc;IACxD,IAAID,IAAIC,MAAM,IAAIA,QAAQ,OAAOD;IACjC,OAAOA,IAAIE,KAAK,CAAC,GAAGD,UAAU;AAChC;AAEO,SAASlE,gBAAgBiE,GAAW;IACzC,OAAOA,IAAIG,MAAM,CAAC,GAAGC,WAAW,KAAKJ,IAAIE,KAAK,CAAC,GAAGG,WAAW;AAC/D;AAEO,SAASvE,aAAakE,GAAW;IACtC,OAAOA,IACJM,OAAO,CAAC,YAAY,OACpBA,OAAO,CAAC,MAAM,CAACN,MAAQA,IAAII,WAAW,IACtCG,IAAI;AACT;AAGO,SAASxD,eAAeyD,MAAc;IAC3C,MAAMC,eAAuC;QAC3CC,YAAY;QACZC,cAAc;QACdC,aAAa;QACbC,UAAU;QACVC,aAAa;QACbC,WAAW;QACXC,OAAO;QACPC,WAAW;QACXC,gBAAgB;IAClB;IAEA,OAAOT,YAAY,CAACD,OAAO,IAAI;AACjC;AAEO,SAASxD,iBAAiBwD,MAAc;IAC7C,MAAMW,iBAA2G;QAC/GT,YAAY;QACZC,cAAc;QACdC,aAAa;QACbC,UAAU;QACVC,aAAa;QACbC,WAAW;QACXC,OAAO;QACPC,WAAW;QACXC,gBAAgB;IAClB;IAEA,OAAOC,cAAc,CAACX,OAAO,IAAI;AACnC;AAEO,SAAS7D,gBAAgByE,UAAkB;IAChD,IAAIA,aAAa,IAAI,OAAO;IAC5B,IAAIA,aAAa,IAAI,OAAO;IAC5B,OAAO;AACT;AAEO,SAASxE,eAAewE,UAAkB;IAC/C,IAAIA,aAAa,IAAI,OAAO;IAC5B,IAAIA,aAAa,IAAI,OAAO;IAC5B,IAAIA,aAAa,IAAI,OAAO;IAC5B,IAAIA,aAAa,IAAI,OAAO;IAC5B,OAAO;AACT;AAGO,SAASlE,aAAamE,KAAa;IACxC,MAAMC,aAAa;IACnB,OAAOA,WAAWC,IAAI,CAACF;AACzB;AAEO,SAASlE,YAAYqE,IAAY;IACtC,4EAA4E;IAC5E,MAAMC,YAAY;IAClB,OAAOA,UAAUF,IAAI,CAACC;AACxB;AAGO,SAASvE,QAAWyE,KAAU,EAAEC,GAAY;IACjD,OAAOD,MAAME,MAAM,CAAC,CAACC,QAAQC;QAC3B,MAAMC,QAAQC,OAAOF,IAAI,CAACH,IAAI;QAC9BE,MAAM,CAACE,MAAM,GAAGF,MAAM,CAACE,MAAM,IAAI,EAAE;QACnCF,MAAM,CAACE,MAAM,CAACE,IAAI,CAACH;QACnB,OAAOD;IACT,GAAG,CAAC;AACN;AAEO,SAASvE,OAAUoE,KAAU,EAAEC,GAAY,EAAEO,YAA4B,KAAK;IACnF,OAAO;WAAIR;KAAM,CAACS,IAAI,CAAC,CAACC,GAAGC;QACzB,MAAMC,OAAOF,CAAC,CAACT,IAAI;QACnB,MAAMY,OAAOF,CAAC,CAACV,IAAI;QAEnB,IAAIW,OAAOC,MAAM,OAAOL,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAII,OAAOC,MAAM,OAAOL,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS/F,SACdqG,IAAO,EACPC,IAAY;IAEZ,IAAIC;IAEJ,OAAO,CAAC,GAAGC;QACTC,aAAaF;QACbA,UAAUG,WAAW,IAAML,QAAQG,OAAOF;IAC5C;AACF;AAGO,SAAS3F,eAAkB6E,GAAW,EAAEmB,YAAe;IAC5D,IAAI,OAAOC,WAAW,aAAa,OAAOD;IAE1C,IAAI;QACF,MAAMhB,OAAOkB,aAAaC,OAAO,CAACtB;QAClC,OAAOG,OAAOoB,KAAKC,KAAK,CAACrB,QAAQgB;IACnC,EAAE,OAAOM,OAAO;QACdC,QAAQD,KAAK,CAAC,CAAC,qCAAqC,EAAEzB,IAAI,EAAE,CAAC,EAAEyB;QAC/D,OAAON;IACT;AACF;AAEO,SAASzF,aAAgBsE,GAAW,EAAE2B,KAAQ;IACnD,IAAI,OAAOP,WAAW,aAAa;IAEnC,IAAI;QACFC,aAAaO,OAAO,CAAC5B,KAAKuB,KAAKM,SAAS,CAACF;IAC3C,EAAE,OAAOF,OAAO;QACdC,QAAQD,KAAK,CAAC,CAAC,mCAAmC,EAAEzB,IAAI,EAAE,CAAC,EAAEyB;IAC/D;AACF;AAEO,SAAShG,kBAAkBuE,GAAW;IAC3C,IAAI,OAAOoB,WAAW,aAAa;IAEnC,IAAI;QACFC,aAAaS,UAAU,CAAC9B;IAC1B,EAAE,OAAOyB,OAAO;QACdC,QAAQD,KAAK,CAAC,CAAC,sCAAsC,EAAEzB,IAAI,EAAE,CAAC,EAAEyB;IAClE;AACF;AAGO,SAASlH,mBAAmBwH,MAA2B;IAC5D,MAAMC,eAAe,IAAIC;IAEzBC,OAAOC,OAAO,CAACJ,QAAQK,OAAO,CAAC,CAAC,CAACpC,KAAK2B,MAAM;QAC1C,IAAIA,UAAUU,aAAaV,UAAU,QAAQA,UAAU,IAAI;YACzD,IAAIW,MAAMC,OAAO,CAACZ,QAAQ;gBACxBA,MAAMS,OAAO,CAACjC,CAAAA,OAAQ6B,aAAaQ,MAAM,CAACxC,KAAKK,OAAOF;YACxD,OAAO;gBACL6B,aAAaQ,MAAM,CAACxC,KAAKK,OAAOsB;YAClC;QACF;IACF;IAEA,OAAOK;AACT;AAGO,SAAS9G,gBAAgBuG,KAAc;IAC5C,IAAIA,iBAAiBgB,OAAO,OAAOhB,MAAMiB,OAAO;IAChD,IAAI,OAAOjB,UAAU,UAAU,OAAOA;IACtC,OAAO;AACT;AAGO,MAAMpH,cAAc;IACzBsI,SAAS;IACTC,WAAW;IACXC,SAAS;IACTC,SAAS;IACTC,QAAQ;IACRC,MAAM;IACNC,OAAO;IACPC,MAAM;AACR;AAEO,SAASnI,oBAAoBoI,KAAa;IAC/C,MAAMC,aAAalB,OAAOmB,MAAM,CAAChJ;IACjC,MAAMiJ,SAAmB,EAAE;IAE3B,IAAK,IAAItF,IAAI,GAAGA,IAAImF,OAAOnF,IAAK;QAC9BsF,OAAOhD,IAAI,CAAC8C,UAAU,CAACpF,IAAIoF,WAAW9E,MAAM,CAAC;IAC/C;IAEA,OAAOgF;AACT"}