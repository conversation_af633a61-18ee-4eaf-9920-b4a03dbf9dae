{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\layout\\header.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport {\n  Bell,\n  Menu,\n  Search,\n  Settings,\n  User,\n  LogOut,\n  Shield,\n  Moon,\n  Sun,\n  Wifi,\n  WifiOff,\n  Activity,\n} from 'lucide-react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'\nimport { MainNav } from './main-nav'\nimport { useConnectionStatus, useUnacknowledgedAlerts } from '@/stores/dashboard-store'\nimport { cn } from '@/lib/utils'\n\ninterface HeaderProps {\n  onMenuClick?: () => void\n}\n\nexport function Header({ onMenuClick }: HeaderProps) {\n  const pathname = usePathname()\n  const [searchQuery, setSearchQuery] = useState('')\n  const [isDarkMode, setIsDarkMode] = useState(false)\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n  \n  const connectionStatus = useConnectionStatus()\n  const unacknowledgedAlerts = useUnacknowledgedAlerts()\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (searchQuery.trim()) {\n      // Navigate to search results or filter current page\n      console.log('Searching for:', searchQuery)\n    }\n  }\n\n  const toggleDarkMode = () => {\n    setIsDarkMode(!isDarkMode)\n    document.documentElement.classList.toggle('dark')\n  }\n\n  const getPageTitle = () => {\n    const pathSegments = pathname.split('/').filter(Boolean)\n    if (pathSegments.length === 0) return 'Dashboard'\n    \n    const pageMap: Record<string, string> = {\n      dashboard: 'Dashboard',\n      uavs: 'UAV Management',\n      map: 'Map View',\n      'hibernate-pod': 'Hibernate Pod',\n      'docking-stations': 'Docking Stations',\n      battery: 'Battery Monitor',\n      'flight-logs': 'Flight Logs',\n      analytics: 'Analytics',\n      alerts: 'Alerts',\n      regions: 'Regions',\n      users: 'Users',\n      settings: 'Settings',\n    }\n    \n    return pageMap[pathSegments[0]] || 'UAV Management System'\n  }\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-16 items-center justify-between px-4\">\n        {/* Left section */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Mobile menu trigger */}\n          <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>\n            <SheetTrigger asChild>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"md:hidden\"\n                onClick={() => setMobileMenuOpen(true)}\n              >\n                <Menu className=\"h-5 w-5\" />\n                <span className=\"sr-only\">Toggle menu</span>\n              </Button>\n            </SheetTrigger>\n            <SheetContent side=\"left\" className=\"w-80\">\n              <div className=\"flex flex-col h-full\">\n                <div className=\"flex items-center space-x-2 pb-4 border-b\">\n                  <Shield className=\"h-6 w-6 text-primary\" />\n                  <span className=\"font-orbitron font-bold text-lg\">UAV Control</span>\n                </div>\n                <div className=\"flex-1 py-4\">\n                  <MainNav onNavigate={() => setMobileMenuOpen(false)} />\n                </div>\n              </div>\n            </SheetContent>\n          </Sheet>\n\n          {/* Logo and title */}\n          <Link href=\"/dashboard\" className=\"flex items-center space-x-2\">\n            <Shield className=\"h-8 w-8 text-primary\" />\n            <div className=\"hidden sm:block\">\n              <h1 className=\"font-orbitron font-bold text-xl text-primary\">\n                UAV Control\n              </h1>\n              <p className=\"text-xs text-muted-foreground\">\n                Management System\n              </p>\n            </div>\n          </Link>\n\n          {/* Page title */}\n          <div className=\"hidden lg:block\">\n            <h2 className=\"text-lg font-semibold text-foreground\">\n              {getPageTitle()}\n            </h2>\n          </div>\n        </div>\n\n        {/* Center section - Search */}\n        <div className=\"flex-1 max-w-md mx-4\">\n          <form onSubmit={handleSearch} className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              type=\"search\"\n              placeholder=\"Search UAVs, regions, or logs...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10 pr-4\"\n            />\n          </form>\n        </div>\n\n        {/* Right section */}\n        <div className=\"flex items-center space-x-2\">\n          {/* Connection status */}\n          <div className=\"hidden sm:flex items-center space-x-2\">\n            {connectionStatus.isConnected ? (\n              <div className=\"flex items-center space-x-1 text-green-600\">\n                <Wifi className=\"h-4 w-4\" />\n                <span className=\"text-xs font-medium\">Connected</span>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-1 text-red-600\">\n                <WifiOff className=\"h-4 w-4\" />\n                <span className=\"text-xs font-medium\">Disconnected</span>\n              </div>\n            )}\n          </div>\n\n          {/* System status indicator */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n            <Activity className={cn(\n              \"h-5 w-5\",\n              connectionStatus.isConnected ? \"text-green-600\" : \"text-red-600\"\n            )} />\n            <span className=\"sr-only\">System status</span>\n          </Button>\n\n          {/* Notifications */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n                <Bell className=\"h-5 w-5\" />\n                {unacknowledgedAlerts.length > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs\"\n                  >\n                    {unacknowledgedAlerts.length > 9 ? '9+' : unacknowledgedAlerts.length}\n                  </Badge>\n                )}\n                <span className=\"sr-only\">Notifications</span>\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\" className=\"w-80\">\n              <DropdownMenuLabel>Notifications</DropdownMenuLabel>\n              <DropdownMenuSeparator />\n              {unacknowledgedAlerts.length > 0 ? (\n                <>\n                  {unacknowledgedAlerts.slice(0, 5).map((alert) => (\n                    <DropdownMenuItem key={alert.id} className=\"flex flex-col items-start p-3\">\n                      <div className=\"flex items-center space-x-2 w-full\">\n                        <Badge variant={alert.type === 'CRITICAL' ? 'destructive' : 'secondary'}>\n                          {alert.type}\n                        </Badge>\n                        <span className=\"text-xs text-muted-foreground\">\n                          {new Date(alert.timestamp).toLocaleTimeString()}\n                        </span>\n                      </div>\n                      <p className=\"text-sm font-medium mt-1\">{alert.title}</p>\n                      <p className=\"text-xs text-muted-foreground mt-1\">{alert.message}</p>\n                    </DropdownMenuItem>\n                  ))}\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/alerts\" className=\"w-full text-center\">\n                      View all alerts\n                    </Link>\n                  </DropdownMenuItem>\n                </>\n              ) : (\n                <DropdownMenuItem disabled>\n                  No new notifications\n                </DropdownMenuItem>\n              )}\n            </DropdownMenuContent>\n          </DropdownMenu>\n\n          {/* Dark mode toggle */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={toggleDarkMode}\n          >\n            {isDarkMode ? (\n              <Sun className=\"h-5 w-5\" />\n            ) : (\n              <Moon className=\"h-5 w-5\" />\n            )}\n            <span className=\"sr-only\">Toggle dark mode</span>\n          </Button>\n\n          {/* User menu */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"icon\">\n                <User className=\"h-5 w-5\" />\n                <span className=\"sr-only\">User menu</span>\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\">\n              <DropdownMenuLabel>My Account</DropdownMenuLabel>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem asChild>\n                <Link href=\"/profile\">\n                  <User className=\"mr-2 h-4 w-4\" />\n                  Profile\n                </Link>\n              </DropdownMenuItem>\n              <DropdownMenuItem asChild>\n                <Link href=\"/settings\">\n                  <Settings className=\"mr-2 h-4 w-4\" />\n                  Settings\n                </Link>\n              </DropdownMenuItem>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem>\n                <LogOut className=\"mr-2 h-4 w-4\" />\n                Log out\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": ["Header", "onMenuClick", "pathname", "usePathname", "searchQuery", "setSearch<PERSON>uery", "useState", "isDarkMode", "setIsDarkMode", "mobileMenuOpen", "setMobileMenuOpen", "connectionStatus", "useConnectionStatus", "unacknowledged<PERSON><PERSON><PERSON>", "useUnacknowledgedAlerts", "handleSearch", "e", "preventDefault", "trim", "console", "log", "toggleDarkMode", "document", "documentElement", "classList", "toggle", "getPageTitle", "pathSegments", "split", "filter", "Boolean", "length", "pageMap", "dashboard", "uavs", "map", "battery", "analytics", "alerts", "regions", "users", "settings", "header", "className", "div", "Sheet", "open", "onOpenChange", "Sheet<PERSON><PERSON>ger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "size", "onClick", "<PERSON><PERSON>", "span", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "side", "Shield", "MainNav", "onNavigate", "Link", "href", "h1", "p", "h2", "form", "onSubmit", "Search", "Input", "type", "placeholder", "value", "onChange", "target", "isConnected", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Activity", "cn", "DropdownMenu", "DropdownMenuTrigger", "Bell", "Badge", "DropdownMenuContent", "align", "DropdownMenuLabel", "DropdownMenuSeparator", "slice", "alert", "DropdownMenuItem", "Date", "timestamp", "toLocaleTimeString", "title", "message", "id", "disabled", "Sun", "Moon", "User", "Settings", "LogOut"], "mappings": "AAAA;;;;;+BAuCgBA;;;eAAAA;;;;+DArCgB;6DACf;4BACW;6BAcrB;wBACgB;uBACD;uBACA;8BAQf;uBAC2C;yBAC1B;gCACqC;uBAC1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMZ,SAASA,OAAO,EAAEC,WAAW,EAAe;IACjD,MAAMC,WAAWC,IAAAA,uBAAW;IAC5B,MAAM,CAACC,aAAaC,eAAe,GAAGC,IAAAA,eAAQ,EAAC;IAC/C,MAAM,CAACC,YAAYC,cAAc,GAAGF,IAAAA,eAAQ,EAAC;IAC7C,MAAM,CAACG,gBAAgBC,kBAAkB,GAAGJ,IAAAA,eAAQ,EAAC;IAErD,MAAMK,mBAAmBC,IAAAA,mCAAmB;IAC5C,MAAMC,uBAAuBC,IAAAA,uCAAuB;IAEpD,MAAMC,eAAe,CAACC;QACpBA,EAAEC,cAAc;QAChB,IAAIb,YAAYc,IAAI,IAAI;YACtB,oDAAoD;YACpDC,QAAQC,GAAG,CAAC,kBAAkBhB;QAChC;IACF;IAEA,MAAMiB,iBAAiB;QACrBb,cAAc,CAACD;QACfe,SAASC,eAAe,CAACC,SAAS,CAACC,MAAM,CAAC;IAC5C;IAEA,MAAMC,eAAe;QACnB,MAAMC,eAAezB,SAAS0B,KAAK,CAAC,KAAKC,MAAM,CAACC;QAChD,IAAIH,aAAaI,MAAM,KAAK,GAAG,OAAO;QAEtC,MAAMC,UAAkC;YACtCC,WAAW;YACXC,MAAM;YACNC,KAAK;YACL,iBAAiB;YACjB,oBAAoB;YACpBC,SAAS;YACT,eAAe;YACfC,WAAW;YACXC,QAAQ;YACRC,SAAS;YACTC,OAAO;YACPC,UAAU;QACZ;QAEA,OAAOT,OAAO,CAACL,YAAY,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,qBACE,qBAACe;QAAOC,WAAU;kBAChB,cAAA,sBAACC;YAAID,WAAU;;8BAEb,sBAACC;oBAAID,WAAU;;sCAEb,sBAACE,YAAK;4BAACC,MAAMrC;4BAAgBsC,cAAcrC;;8CACzC,qBAACsC,mBAAY;oCAACC,OAAO;8CACnB,cAAA,sBAACC,cAAM;wCACLC,SAAQ;wCACRC,MAAK;wCACLT,WAAU;wCACVU,SAAS,IAAM3C,kBAAkB;;0DAEjC,qBAAC4C,iBAAI;gDAACX,WAAU;;0DAChB,qBAACY;gDAAKZ,WAAU;0DAAU;;;;;8CAG9B,qBAACa,mBAAY;oCAACC,MAAK;oCAAOd,WAAU;8CAClC,cAAA,sBAACC;wCAAID,WAAU;;0DACb,sBAACC;gDAAID,WAAU;;kEACb,qBAACe,mBAAM;wDAACf,WAAU;;kEAClB,qBAACY;wDAAKZ,WAAU;kEAAkC;;;;0DAEpD,qBAACC;gDAAID,WAAU;0DACb,cAAA,qBAACgB,gBAAO;oDAACC,YAAY,IAAMlD,kBAAkB;;;;;;;;sCAOrD,sBAACmD,aAAI;4BAACC,MAAK;4BAAanB,WAAU;;8CAChC,qBAACe,mBAAM;oCAACf,WAAU;;8CAClB,sBAACC;oCAAID,WAAU;;sDACb,qBAACoB;4CAAGpB,WAAU;sDAA+C;;sDAG7D,qBAACqB;4CAAErB,WAAU;sDAAgC;;;;;;sCAOjD,qBAACC;4BAAID,WAAU;sCACb,cAAA,qBAACsB;gCAAGtB,WAAU;0CACXjB;;;;;8BAMP,qBAACkB;oBAAID,WAAU;8BACb,cAAA,sBAACuB;wBAAKC,UAAUpD;wBAAc4B,WAAU;;0CACtC,qBAACyB,mBAAM;gCAACzB,WAAU;;0CAClB,qBAAC0B,YAAK;gCACJC,MAAK;gCACLC,aAAY;gCACZC,OAAOpE;gCACPqE,UAAU,CAACzD,IAAMX,eAAeW,EAAE0D,MAAM,CAACF,KAAK;gCAC9C7B,WAAU;;;;;8BAMhB,sBAACC;oBAAID,WAAU;;sCAEb,qBAACC;4BAAID,WAAU;sCACZhC,iBAAiBgE,WAAW,iBAC3B,sBAAC/B;gCAAID,WAAU;;kDACb,qBAACiC,iBAAI;wCAACjC,WAAU;;kDAChB,qBAACY;wCAAKZ,WAAU;kDAAsB;;;+CAGxC,sBAACC;gCAAID,WAAU;;kDACb,qBAACkC,oBAAO;wCAAClC,WAAU;;kDACnB,qBAACY;wCAAKZ,WAAU;kDAAsB;;;;;sCAM5C,sBAACO,cAAM;4BAACC,SAAQ;4BAAQC,MAAK;4BAAOT,WAAU;;8CAC5C,qBAACmC,qBAAQ;oCAACnC,WAAWoC,IAAAA,SAAE,EACrB,WACApE,iBAAiBgE,WAAW,GAAG,mBAAmB;;8CAEpD,qBAACpB;oCAAKZ,WAAU;8CAAU;;;;sCAI5B,sBAACqC,0BAAY;;8CACX,qBAACC,iCAAmB;oCAAChC,OAAO;8CAC1B,cAAA,sBAACC,cAAM;wCAACC,SAAQ;wCAAQC,MAAK;wCAAOT,WAAU;;0DAC5C,qBAACuC,iBAAI;gDAACvC,WAAU;;4CACf9B,qBAAqBkB,MAAM,GAAG,mBAC7B,qBAACoD,YAAK;gDACJhC,SAAQ;gDACRR,WAAU;0DAET9B,qBAAqBkB,MAAM,GAAG,IAAI,OAAOlB,qBAAqBkB,MAAM;;0DAGzE,qBAACwB;gDAAKZ,WAAU;0DAAU;;;;;8CAG9B,sBAACyC,iCAAmB;oCAACC,OAAM;oCAAM1C,WAAU;;sDACzC,qBAAC2C,+BAAiB;sDAAC;;sDACnB,qBAACC,mCAAqB;wCACrB1E,qBAAqBkB,MAAM,GAAG,kBAC7B;;gDACGlB,qBAAqB2E,KAAK,CAAC,GAAG,GAAGrD,GAAG,CAAC,CAACsD,sBACrC,sBAACC,8BAAgB;wDAAgB/C,WAAU;;0EACzC,sBAACC;gEAAID,WAAU;;kFACb,qBAACwC,YAAK;wEAAChC,SAASsC,MAAMnB,IAAI,KAAK,aAAa,gBAAgB;kFACzDmB,MAAMnB,IAAI;;kFAEb,qBAACf;wEAAKZ,WAAU;kFACb,IAAIgD,KAAKF,MAAMG,SAAS,EAAEC,kBAAkB;;;;0EAGjD,qBAAC7B;gEAAErB,WAAU;0EAA4B8C,MAAMK,KAAK;;0EACpD,qBAAC9B;gEAAErB,WAAU;0EAAsC8C,MAAMM,OAAO;;;uDAV3CN,MAAMO,EAAE;8DAajC,qBAACT,mCAAqB;8DACtB,qBAACG,8BAAgB;oDAACzC,OAAO;8DACvB,cAAA,qBAACY,aAAI;wDAACC,MAAK;wDAAUnB,WAAU;kEAAqB;;;;2DAMxD,qBAAC+C,8BAAgB;4CAACO,QAAQ;sDAAC;;;;;;sCAQjC,sBAAC/C,cAAM;4BACLC,SAAQ;4BACRC,MAAK;4BACLC,SAAShC;;gCAERd,2BACC,qBAAC2F,gBAAG;oCAACvD,WAAU;mDAEf,qBAACwD,iBAAI;oCAACxD,WAAU;;8CAElB,qBAACY;oCAAKZ,WAAU;8CAAU;;;;sCAI5B,sBAACqC,0BAAY;;8CACX,qBAACC,iCAAmB;oCAAChC,OAAO;8CAC1B,cAAA,sBAACC,cAAM;wCAACC,SAAQ;wCAAQC,MAAK;;0DAC3B,qBAACgD,iBAAI;gDAACzD,WAAU;;0DAChB,qBAACY;gDAAKZ,WAAU;0DAAU;;;;;8CAG9B,sBAACyC,iCAAmB;oCAACC,OAAM;;sDACzB,qBAACC,+BAAiB;sDAAC;;sDACnB,qBAACC,mCAAqB;sDACtB,qBAACG,8BAAgB;4CAACzC,OAAO;sDACvB,cAAA,sBAACY,aAAI;gDAACC,MAAK;;kEACT,qBAACsC,iBAAI;wDAACzD,WAAU;;oDAAiB;;;;sDAIrC,qBAAC+C,8BAAgB;4CAACzC,OAAO;sDACvB,cAAA,sBAACY,aAAI;gDAACC,MAAK;;kEACT,qBAACuC,qBAAQ;wDAAC1D,WAAU;;oDAAiB;;;;sDAIzC,qBAAC4C,mCAAqB;sDACtB,sBAACG,8BAAgB;;8DACf,qBAACY,mBAAM;oDAAC3D,WAAU;;gDAAiB;;;;;;;;;;;;AASnD"}