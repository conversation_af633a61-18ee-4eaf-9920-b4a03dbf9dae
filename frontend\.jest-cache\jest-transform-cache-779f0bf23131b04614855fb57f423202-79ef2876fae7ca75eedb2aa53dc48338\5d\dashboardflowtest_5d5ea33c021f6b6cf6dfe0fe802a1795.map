{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\__tests__\\integration\\dashboard-flow.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { useDashboardStore } from '@/stores/dashboard-store'\nimport { createMockAlert, createMockUAV } from '@/lib/test-utils'\n\n// Mock WebSocket for real-time updates\nconst mockWebSocket = {\n  send: jest.fn(),\n  close: jest.fn(),\n  addEventListener: jest.fn(),\n  removeEventListener: jest.fn(),\n  readyState: WebSocket.OPEN,\n}\n\n// Mock global WebSocket\nglobal.WebSocket = jest.fn(() => mockWebSocket) as any\n\n// Integration test component for dashboard\nconst DashboardTestComponent = () => {\n  const {\n    metrics,\n    alerts,\n    recentLocationUpdates,\n    isConnected,\n    connectionError,\n    showAlerts,\n    autoRefresh,\n    selectedTimeRange,\n    fetchDashboardData,\n    addAlert,\n    acknowledgeAlert,\n    removeAlert,\n    clearAlerts,\n    addLocationUpdate,\n    setConnectionStatus,\n    setTimeRange,\n    setAutoRefresh,\n    setShowAlerts,\n    updateLastUpdate,\n  } = useDashboardStore()\n\n  const [wsConnected, setWsConnected] = React.useState(false)\n\n  React.useEffect(() => {\n    // Simulate initial data fetch\n    fetchDashboardData()\n    \n    // Simulate WebSocket connection\n    const ws = new WebSocket('ws://localhost:8080/ws')\n    \n    ws.addEventListener('open', () => {\n      setWsConnected(true)\n      setConnectionStatus(true)\n    })\n    \n    ws.addEventListener('message', (event) => {\n      const data = JSON.parse(event.data)\n      \n      if (data.type === 'alert') {\n        addAlert(data.payload)\n      } else if (data.type === 'location_update') {\n        addLocationUpdate(data.payload)\n      }\n    })\n    \n    ws.addEventListener('error', () => {\n      setConnectionStatus(false, 'WebSocket connection failed')\n    })\n    \n    return () => {\n      ws.close()\n      setConnectionStatus(false)\n    }\n  }, [])\n\n  const handleTimeRangeChange = (range: string) => {\n    setTimeRange(range)\n    fetchDashboardData()\n  }\n\n  const handleToggleAutoRefresh = () => {\n    setAutoRefresh(!autoRefresh)\n  }\n\n  const handleToggleAlerts = () => {\n    setShowAlerts(!showAlerts)\n  }\n\n  const handleAcknowledgeAlert = (alertId: string) => {\n    acknowledgeAlert(alertId)\n  }\n\n  const handleRemoveAlert = (alertId: string) => {\n    removeAlert(alertId)\n  }\n\n  const handleClearAllAlerts = () => {\n    clearAlerts()\n  }\n\n  return (\n    <div>\n      <h1>Dashboard</h1>\n      \n      {/* Connection Status */}\n      <div data-testid=\"connection-status\">\n        Status: {isConnected ? 'Connected' : 'Disconnected'}\n        {connectionError && <span> - {connectionError}</span>}\n        {wsConnected && <span> (WebSocket Active)</span>}\n      </div>\n\n      {/* Controls */}\n      <div data-testid=\"dashboard-controls\">\n        <select \n          value={selectedTimeRange} \n          onChange={(e) => handleTimeRangeChange(e.target.value)}\n          data-testid=\"time-range-select\"\n        >\n          <option value=\"1h\">Last Hour</option>\n          <option value=\"24h\">Last 24 Hours</option>\n          <option value=\"7d\">Last 7 Days</option>\n          <option value=\"30d\">Last 30 Days</option>\n        </select>\n        \n        <button onClick={handleToggleAutoRefresh} data-testid=\"auto-refresh-toggle\">\n          Auto Refresh: {autoRefresh ? 'ON' : 'OFF'}\n        </button>\n        \n        <button onClick={handleToggleAlerts} data-testid=\"alerts-toggle\">\n          Alerts: {showAlerts ? 'VISIBLE' : 'HIDDEN'}\n        </button>\n        \n        <button onClick={() => fetchDashboardData()} data-testid=\"refresh-button\">\n          Refresh Data\n        </button>\n      </div>\n\n      {/* Metrics */}\n      {metrics && (\n        <div data-testid=\"dashboard-metrics\">\n          <h2>System Metrics</h2>\n          <div>Total UAVs: {metrics.totalUAVs}</div>\n          <div>Authorized: {metrics.authorizedUAVs}</div>\n          <div>Unauthorized: {metrics.unauthorizedUAVs}</div>\n          <div>Active Flights: {metrics.activeFlights}</div>\n          <div>Hibernating: {metrics.hibernatingUAVs}</div>\n        </div>\n      )}\n\n      {/* Alerts */}\n      {showAlerts && (\n        <div data-testid=\"alerts-section\">\n          <h2>Alerts ({alerts.length})</h2>\n          \n          <div>\n            <button onClick={handleClearAllAlerts} data-testid=\"clear-all-alerts\">\n              Clear All\n            </button>\n          </div>\n          \n          {alerts.length === 0 ? (\n            <p>No alerts</p>\n          ) : (\n            <div data-testid=\"alerts-list\">\n              {alerts.map(alert => (\n                <div key={alert.id} data-testid={`alert-${alert.id}`}>\n                  <h4>{alert.title}</h4>\n                  <p>{alert.message}</p>\n                  <p>Type: {alert.type}</p>\n                  <p>Severity: {alert.severity}</p>\n                  <p>Acknowledged: {alert.acknowledged ? 'Yes' : 'No'}</p>\n                  \n                  <div>\n                    {!alert.acknowledged && (\n                      <button \n                        onClick={() => handleAcknowledgeAlert(alert.id)}\n                        data-testid={`acknowledge-${alert.id}`}\n                      >\n                        Acknowledge\n                      </button>\n                    )}\n                    <button \n                      onClick={() => handleRemoveAlert(alert.id)}\n                      data-testid={`remove-${alert.id}`}\n                    >\n                      Remove\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Location Updates */}\n      <div data-testid=\"location-updates\">\n        <h2>Recent Location Updates ({recentLocationUpdates.length})</h2>\n        {recentLocationUpdates.slice(0, 5).map((update, index) => (\n          <div key={index} data-testid={`location-update-${index}`}>\n            UAV {update.rfidTag}: {update.latitude}, {update.longitude}\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\ndescribe('Dashboard Flow Integration Tests', () => {\n  beforeEach(() => {\n    jest.clearAllMocks()\n    \n    // Reset dashboard store\n    useDashboardStore.setState({\n      metrics: null,\n      flightActivity: null,\n      batteryStats: null,\n      hibernatePodMetrics: null,\n      chartData: null,\n      alerts: [],\n      recentLocationUpdates: [],\n      isConnected: false,\n      lastUpdate: null,\n      connectionError: null,\n      selectedTimeRange: '24h',\n      autoRefresh: true,\n      refreshInterval: 30,\n      showAlerts: true,\n    })\n\n    // Mock fetch for dashboard data\n    global.fetch = jest.fn().mockResolvedValue({\n      ok: true,\n      json: jest.fn().mockResolvedValue({\n        metrics: {\n          totalUAVs: 10,\n          authorizedUAVs: 8,\n          unauthorizedUAVs: 2,\n          activeFlights: 3,\n          hibernatingUAVs: 2,\n          lowBatteryCount: 1,\n          chargingCount: 2,\n          maintenanceCount: 1,\n          emergencyCount: 0,\n        },\n      }),\n    })\n  })\n\n  it('loads dashboard data on mount', async () => {\n    render(<DashboardTestComponent />)\n\n    // Should show initial state\n    expect(screen.getByText('Status: Disconnected')).toBeInTheDocument()\n\n    // Wait for data to load\n    await waitFor(() => {\n      expect(screen.getByText('Total UAVs: 10')).toBeInTheDocument()\n      expect(screen.getByText('Authorized: 8')).toBeInTheDocument()\n      expect(screen.getByText('Active Flights: 3')).toBeInTheDocument()\n    })\n\n    // Should show WebSocket connection\n    expect(screen.getByText('Status: Connected (WebSocket Active)')).toBeInTheDocument()\n  })\n\n  it('handles time range changes', async () => {\n    render(<DashboardTestComponent />)\n\n    // Wait for initial load\n    await waitFor(() => {\n      expect(screen.getByTestId('dashboard-metrics')).toBeInTheDocument()\n    })\n\n    // Change time range\n    const timeRangeSelect = screen.getByTestId('time-range-select')\n    fireEvent.change(timeRangeSelect, { target: { value: '7d' } })\n\n    // Should update selected time range\n    expect(timeRangeSelect).toHaveValue('7d')\n\n    // Should trigger data refresh\n    await waitFor(() => {\n      expect(global.fetch).toHaveBeenCalledTimes(2) // Initial + after time range change\n    })\n  })\n\n  it('toggles auto refresh', () => {\n    render(<DashboardTestComponent />)\n\n    const autoRefreshToggle = screen.getByTestId('auto-refresh-toggle')\n    \n    // Initially ON\n    expect(screen.getByText('Auto Refresh: ON')).toBeInTheDocument()\n\n    // Toggle OFF\n    fireEvent.click(autoRefreshToggle)\n    expect(screen.getByText('Auto Refresh: OFF')).toBeInTheDocument()\n\n    // Toggle back ON\n    fireEvent.click(autoRefreshToggle)\n    expect(screen.getByText('Auto Refresh: ON')).toBeInTheDocument()\n  })\n\n  it('toggles alerts visibility', () => {\n    render(<DashboardTestComponent />)\n\n    const alertsToggle = screen.getByTestId('alerts-toggle')\n    \n    // Initially visible\n    expect(screen.getByText('Alerts: VISIBLE')).toBeInTheDocument()\n    expect(screen.getByTestId('alerts-section')).toBeInTheDocument()\n\n    // Toggle hidden\n    fireEvent.click(alertsToggle)\n    expect(screen.getByText('Alerts: HIDDEN')).toBeInTheDocument()\n    expect(screen.queryByTestId('alerts-section')).not.toBeInTheDocument()\n  })\n\n  it('handles real-time alert updates', async () => {\n    render(<DashboardTestComponent />)\n\n    // Wait for WebSocket connection\n    await waitFor(() => {\n      expect(screen.getByText(/WebSocket Active/)).toBeInTheDocument()\n    })\n\n    // Simulate receiving an alert via WebSocket\n    const mockAlert = createMockAlert({\n      id: '1',\n      title: 'Low Battery',\n      message: 'UAV-001 battery is low',\n      type: 'WARNING',\n      severity: 'MEDIUM',\n    })\n\n    // Simulate WebSocket message\n    const messageEvent = new MessageEvent('message', {\n      data: JSON.stringify({\n        type: 'alert',\n        payload: mockAlert,\n      }),\n    })\n\n    // Trigger the message handler\n    const { addAlert } = useDashboardStore.getState()\n    addAlert(mockAlert)\n\n    // Should display the new alert\n    await waitFor(() => {\n      expect(screen.getByText('Alerts (1)')).toBeInTheDocument()\n      expect(screen.getByText('Low Battery')).toBeInTheDocument()\n      expect(screen.getByText('UAV-001 battery is low')).toBeInTheDocument()\n    })\n  })\n\n  it('handles alert acknowledgment', async () => {\n    const mockAlert = createMockAlert({\n      id: '1',\n      title: 'Test Alert',\n      acknowledged: false,\n    })\n\n    // Add alert to store\n    useDashboardStore.getState().addAlert(mockAlert)\n\n    render(<DashboardTestComponent />)\n\n    // Should show unacknowledged alert\n    await waitFor(() => {\n      expect(screen.getByText('Acknowledged: No')).toBeInTheDocument()\n      expect(screen.getByTestId('acknowledge-1')).toBeInTheDocument()\n    })\n\n    // Acknowledge alert\n    fireEvent.click(screen.getByTestId('acknowledge-1'))\n\n    // Should update acknowledgment status\n    await waitFor(() => {\n      expect(screen.getByText('Acknowledged: Yes')).toBeInTheDocument()\n      expect(screen.queryByTestId('acknowledge-1')).not.toBeInTheDocument()\n    })\n  })\n\n  it('handles alert removal', async () => {\n    const mockAlert = createMockAlert({\n      id: '1',\n      title: 'Test Alert',\n    })\n\n    // Add alert to store\n    useDashboardStore.getState().addAlert(mockAlert)\n\n    render(<DashboardTestComponent />)\n\n    // Should show alert\n    await waitFor(() => {\n      expect(screen.getByText('Alerts (1)')).toBeInTheDocument()\n      expect(screen.getByTestId('alert-1')).toBeInTheDocument()\n    })\n\n    // Remove alert\n    fireEvent.click(screen.getByTestId('remove-1'))\n\n    // Should remove alert\n    await waitFor(() => {\n      expect(screen.getByText('Alerts (0)')).toBeInTheDocument()\n      expect(screen.queryByTestId('alert-1')).not.toBeInTheDocument()\n      expect(screen.getByText('No alerts')).toBeInTheDocument()\n    })\n  })\n\n  it('clears all alerts', async () => {\n    // Add multiple alerts\n    const alerts = [\n      createMockAlert({ id: '1', title: 'Alert 1' }),\n      createMockAlert({ id: '2', title: 'Alert 2' }),\n      createMockAlert({ id: '3', title: 'Alert 3' }),\n    ]\n\n    alerts.forEach(alert => {\n      useDashboardStore.getState().addAlert(alert)\n    })\n\n    render(<DashboardTestComponent />)\n\n    // Should show all alerts\n    await waitFor(() => {\n      expect(screen.getByText('Alerts (3)')).toBeInTheDocument()\n    })\n\n    // Clear all alerts\n    fireEvent.click(screen.getByTestId('clear-all-alerts'))\n\n    // Should remove all alerts\n    await waitFor(() => {\n      expect(screen.getByText('Alerts (0)')).toBeInTheDocument()\n      expect(screen.getByText('No alerts')).toBeInTheDocument()\n    })\n  })\n\n  it('handles real-time location updates', async () => {\n    render(<DashboardTestComponent />)\n\n    // Wait for WebSocket connection\n    await waitFor(() => {\n      expect(screen.getByText(/WebSocket Active/)).toBeInTheDocument()\n    })\n\n    // Simulate location update\n    const locationUpdate = {\n      uavId: 1,\n      rfidTag: 'UAV-001',\n      latitude: 40.7128,\n      longitude: -74.0060,\n      altitude: 100,\n      timestamp: new Date().toISOString(),\n      speed: 25,\n      heading: 180,\n    }\n\n    // Add location update to store\n    useDashboardStore.getState().addLocationUpdate(locationUpdate)\n\n    // Should display location update\n    await waitFor(() => {\n      expect(screen.getByText('Recent Location Updates (1)')).toBeInTheDocument()\n      expect(screen.getByText('UAV UAV-001: 40.7128, -74.0060')).toBeInTheDocument()\n    })\n  })\n\n  it('handles connection errors', async () => {\n    render(<DashboardTestComponent />)\n\n    // Simulate connection error\n    useDashboardStore.getState().setConnectionStatus(false, 'Network timeout')\n\n    // Should show error status\n    await waitFor(() => {\n      expect(screen.getByText('Status: Disconnected - Network timeout')).toBeInTheDocument()\n    })\n  })\n\n  it('refreshes data manually', async () => {\n    render(<DashboardTestComponent />)\n\n    // Wait for initial load\n    await waitFor(() => {\n      expect(screen.getByTestId('dashboard-metrics')).toBeInTheDocument()\n    })\n\n    // Clear previous fetch calls\n    ;(global.fetch as jest.Mock).mockClear()\n\n    // Click refresh\n    fireEvent.click(screen.getByTestId('refresh-button'))\n\n    // Should trigger data fetch\n    await waitFor(() => {\n      expect(global.fetch).toHaveBeenCalled()\n    })\n  })\n})\n"], "names": ["mockWebSocket", "send", "jest", "fn", "close", "addEventListener", "removeEventListener", "readyState", "WebSocket", "OPEN", "global", "DashboardTestComponent", "metrics", "alerts", "recentLocationUpdates", "isConnected", "connectionError", "show<PERSON><PERSON><PERSON>", "autoRefresh", "selectedTimeRange", "fetchDashboardData", "add<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "addLocationUpdate", "setConnectionStatus", "setTimeRange", "setAutoRefresh", "setShow<PERSON>ler<PERSON>", "updateLastUpdate", "useDashboardStore", "wsConnected", "setWsConnected", "React", "useState", "useEffect", "ws", "event", "data", "JSON", "parse", "type", "payload", "handleTimeRangeChange", "range", "handleToggleAutoRefresh", "handleToggleAlerts", "handleAcknowledgeAlert", "alertId", "handleRemoveAlert", "handleClearAllAlerts", "div", "h1", "data-testid", "span", "select", "value", "onChange", "e", "target", "option", "button", "onClick", "h2", "totalUAVs", "authorizedUAVs", "unauthorizedUAVs", "activeFlights", "hibernatingUAVs", "length", "p", "map", "alert", "id", "h4", "title", "message", "severity", "acknowledged", "slice", "update", "index", "rfidTag", "latitude", "longitude", "describe", "beforeEach", "clearAllMocks", "setState", "flightActivity", "batteryStats", "hibernatePodMetrics", "chartData", "lastUpdate", "refreshInterval", "fetch", "mockResolvedValue", "ok", "json", "lowBatteryCount", "chargingCount", "maintenanceCount", "emergencyCount", "it", "render", "expect", "screen", "getByText", "toBeInTheDocument", "waitFor", "getByTestId", "timeRangeSelect", "fireEvent", "change", "toHaveValue", "toHaveBeenCalledTimes", "autoRefreshToggle", "click", "alertsToggle", "queryByTestId", "not", "<PERSON><PERSON><PERSON><PERSON>", "createMockAlert", "messageEvent", "MessageEvent", "stringify", "getState", "for<PERSON>ach", "locationUpdate", "uavId", "altitude", "timestamp", "Date", "toISOString", "speed", "heading", "mockClear", "toHaveBeenCalled"], "mappings": ";;;;;8DAAkB;2BACiC;gCACjB;;;;;;AAGlC,uCAAuC;AACvC,MAAMA,gBAAgB;IACpBC,MAAMC,KAAKC,EAAE;IACbC,OAAOF,KAAKC,EAAE;IACdE,kBAAkBH,KAAKC,EAAE;IACzBG,qBAAqBJ,KAAKC,EAAE;IAC5BI,YAAYC,UAAUC,IAAI;AAC5B;AAEA,wBAAwB;AACxBC,OAAOF,SAAS,GAAGN,KAAKC,EAAE,CAAC,IAAMH;AAEjC,2CAA2C;AAC3C,MAAMW,yBAAyB;IAC7B,MAAM,EACJC,OAAO,EACPC,MAAM,EACNC,qBAAqB,EACrBC,WAAW,EACXC,eAAe,EACfC,UAAU,EACVC,WAAW,EACXC,iBAAiB,EACjBC,kBAAkB,EAClBC,QAAQ,EACRC,gBAAgB,EAChBC,WAAW,EACXC,WAAW,EACXC,iBAAiB,EACjBC,mBAAmB,EACnBC,YAAY,EACZC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EACjB,GAAGC,IAAAA,iCAAiB;IAErB,MAAM,CAACC,aAAaC,eAAe,GAAGC,cAAK,CAACC,QAAQ,CAAC;IAErDD,cAAK,CAACE,SAAS,CAAC;QACd,8BAA8B;QAC9BhB;QAEA,gCAAgC;QAChC,MAAMiB,KAAK,IAAI7B,UAAU;QAEzB6B,GAAGhC,gBAAgB,CAAC,QAAQ;YAC1B4B,eAAe;YACfP,oBAAoB;QACtB;QAEAW,GAAGhC,gBAAgB,CAAC,WAAW,CAACiC;YAC9B,MAAMC,OAAOC,KAAKC,KAAK,CAACH,MAAMC,IAAI;YAElC,IAAIA,KAAKG,IAAI,KAAK,SAAS;gBACzBrB,SAASkB,KAAKI,OAAO;YACvB,OAAO,IAAIJ,KAAKG,IAAI,KAAK,mBAAmB;gBAC1CjB,kBAAkBc,KAAKI,OAAO;YAChC;QACF;QAEAN,GAAGhC,gBAAgB,CAAC,SAAS;YAC3BqB,oBAAoB,OAAO;QAC7B;QAEA,OAAO;YACLW,GAAGjC,KAAK;YACRsB,oBAAoB;QACtB;IACF,GAAG,EAAE;IAEL,MAAMkB,wBAAwB,CAACC;QAC7BlB,aAAakB;QACbzB;IACF;IAEA,MAAM0B,0BAA0B;QAC9BlB,eAAe,CAACV;IAClB;IAEA,MAAM6B,qBAAqB;QACzBlB,cAAc,CAACZ;IACjB;IAEA,MAAM+B,yBAAyB,CAACC;QAC9B3B,iBAAiB2B;IACnB;IAEA,MAAMC,oBAAoB,CAACD;QACzB1B,YAAY0B;IACd;IAEA,MAAME,uBAAuB;QAC3B3B;IACF;IAEA,qBACE,sBAAC4B;;0BACC,qBAACC;0BAAG;;0BAGJ,sBAACD;gBAAIE,eAAY;;oBAAoB;oBAC1BvC,cAAc,cAAc;oBACpCC,iCAAmB,sBAACuC;;4BAAK;4BAAIvC;;;oBAC7BgB,6BAAe,qBAACuB;kCAAK;;;;0BAIxB,sBAACH;gBAAIE,eAAY;;kCACf,sBAACE;wBACCC,OAAOtC;wBACPuC,UAAU,CAACC,IAAMf,sBAAsBe,EAAEC,MAAM,CAACH,KAAK;wBACrDH,eAAY;;0CAEZ,qBAACO;gCAAOJ,OAAM;0CAAK;;0CACnB,qBAACI;gCAAOJ,OAAM;0CAAM;;0CACpB,qBAACI;gCAAOJ,OAAM;0CAAK;;0CACnB,qBAACI;gCAAOJ,OAAM;0CAAM;;;;kCAGtB,sBAACK;wBAAOC,SAASjB;wBAAyBQ,eAAY;;4BAAsB;4BAC3DpC,cAAc,OAAO;;;kCAGtC,sBAAC4C;wBAAOC,SAAShB;wBAAoBO,eAAY;;4BAAgB;4BACtDrC,aAAa,YAAY;;;kCAGpC,qBAAC6C;wBAAOC,SAAS,IAAM3C;wBAAsBkC,eAAY;kCAAiB;;;;YAM3E1C,yBACC,sBAACwC;gBAAIE,eAAY;;kCACf,qBAACU;kCAAG;;kCACJ,sBAACZ;;4BAAI;4BAAaxC,QAAQqD,SAAS;;;kCACnC,sBAACb;;4BAAI;4BAAaxC,QAAQsD,cAAc;;;kCACxC,sBAACd;;4BAAI;4BAAexC,QAAQuD,gBAAgB;;;kCAC5C,sBAACf;;4BAAI;4BAAiBxC,QAAQwD,aAAa;;;kCAC3C,sBAAChB;;4BAAI;4BAAcxC,QAAQyD,eAAe;;;;;YAK7CpD,4BACC,sBAACmC;gBAAIE,eAAY;;kCACf,sBAACU;;4BAAG;4BAASnD,OAAOyD,MAAM;4BAAC;;;kCAE3B,qBAAClB;kCACC,cAAA,qBAACU;4BAAOC,SAASZ;4BAAsBG,eAAY;sCAAmB;;;oBAKvEzC,OAAOyD,MAAM,KAAK,kBACjB,qBAACC;kCAAE;uCAEH,qBAACnB;wBAAIE,eAAY;kCACdzC,OAAO2D,GAAG,CAACC,CAAAA,sBACV,sBAACrB;gCAAmBE,eAAa,CAAC,MAAM,EAAEmB,MAAMC,EAAE,CAAC,CAAC;;kDAClD,qBAACC;kDAAIF,MAAMG,KAAK;;kDAChB,qBAACL;kDAAGE,MAAMI,OAAO;;kDACjB,sBAACN;;4CAAE;4CAAOE,MAAM/B,IAAI;;;kDACpB,sBAAC6B;;4CAAE;4CAAWE,MAAMK,QAAQ;;;kDAC5B,sBAACP;;4CAAE;4CAAeE,MAAMM,YAAY,GAAG,QAAQ;;;kDAE/C,sBAAC3B;;4CACE,CAACqB,MAAMM,YAAY,kBAClB,qBAACjB;gDACCC,SAAS,IAAMf,uBAAuByB,MAAMC,EAAE;gDAC9CpB,eAAa,CAAC,YAAY,EAAEmB,MAAMC,EAAE,CAAC,CAAC;0DACvC;;0DAIH,qBAACZ;gDACCC,SAAS,IAAMb,kBAAkBuB,MAAMC,EAAE;gDACzCpB,eAAa,CAAC,OAAO,EAAEmB,MAAMC,EAAE,CAAC,CAAC;0DAClC;;;;;+BAnBKD,MAAMC,EAAE;;;;0BA+B5B,sBAACtB;gBAAIE,eAAY;;kCACf,sBAACU;;4BAAG;4BAA0BlD,sBAAsBwD,MAAM;4BAAC;;;oBAC1DxD,sBAAsBkE,KAAK,CAAC,GAAG,GAAGR,GAAG,CAAC,CAACS,QAAQC,sBAC9C,sBAAC9B;4BAAgBE,eAAa,CAAC,gBAAgB,EAAE4B,MAAM,CAAC;;gCAAE;gCACnDD,OAAOE,OAAO;gCAAC;gCAAGF,OAAOG,QAAQ;gCAAC;gCAAGH,OAAOI,SAAS;;2BADlDH;;;;;AAOpB;AAEAI,SAAS,oCAAoC;IAC3CC,WAAW;QACTrF,KAAKsF,aAAa;QAElB,wBAAwB;QACxBzD,iCAAiB,CAAC0D,QAAQ,CAAC;YACzB7E,SAAS;YACT8E,gBAAgB;YAChBC,cAAc;YACdC,qBAAqB;YACrBC,WAAW;YACXhF,QAAQ,EAAE;YACVC,uBAAuB,EAAE;YACzBC,aAAa;YACb+E,YAAY;YACZ9E,iBAAiB;YACjBG,mBAAmB;YACnBD,aAAa;YACb6E,iBAAiB;YACjB9E,YAAY;QACd;QAEA,gCAAgC;QAChCP,OAAOsF,KAAK,GAAG9F,KAAKC,EAAE,GAAG8F,iBAAiB,CAAC;YACzCC,IAAI;YACJC,MAAMjG,KAAKC,EAAE,GAAG8F,iBAAiB,CAAC;gBAChCrF,SAAS;oBACPqD,WAAW;oBACXC,gBAAgB;oBAChBC,kBAAkB;oBAClBC,eAAe;oBACfC,iBAAiB;oBACjB+B,iBAAiB;oBACjBC,eAAe;oBACfC,kBAAkB;oBAClBC,gBAAgB;gBAClB;YACF;QACF;IACF;IAEAC,GAAG,iCAAiC;QAClCC,IAAAA,iBAAM,gBAAC,qBAAC9F;QAER,4BAA4B;QAC5B+F,OAAOC,iBAAM,CAACC,SAAS,CAAC,yBAAyBC,iBAAiB;QAElE,wBAAwB;QACxB,MAAMC,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;YAC5DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC3DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;QACjE;QAEA,mCAAmC;QACnCH,OAAOC,iBAAM,CAACC,SAAS,CAAC,yCAAyCC,iBAAiB;IACpF;IAEAL,GAAG,8BAA8B;QAC/BC,IAAAA,iBAAM,gBAAC,qBAAC9F;QAER,wBAAwB;QACxB,MAAMmG,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACI,WAAW,CAAC,sBAAsBF,iBAAiB;QACnE;QAEA,oBAAoB;QACpB,MAAMG,kBAAkBL,iBAAM,CAACI,WAAW,CAAC;QAC3CE,oBAAS,CAACC,MAAM,CAACF,iBAAiB;YAAEpD,QAAQ;gBAAEH,OAAO;YAAK;QAAE;QAE5D,oCAAoC;QACpCiD,OAAOM,iBAAiBG,WAAW,CAAC;QAEpC,8BAA8B;QAC9B,MAAML,IAAAA,kBAAO,EAAC;YACZJ,OAAOhG,OAAOsF,KAAK,EAAEoB,qBAAqB,CAAC,GAAG,oCAAoC;;QACpF;IACF;IAEAZ,GAAG,wBAAwB;QACzBC,IAAAA,iBAAM,gBAAC,qBAAC9F;QAER,MAAM0G,oBAAoBV,iBAAM,CAACI,WAAW,CAAC;QAE7C,eAAe;QACfL,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;QAE9D,aAAa;QACbI,oBAAS,CAACK,KAAK,CAACD;QAChBX,OAAOC,iBAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;QAE/D,iBAAiB;QACjBI,oBAAS,CAACK,KAAK,CAACD;QAChBX,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;IAChE;IAEAL,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAAC9F;QAER,MAAM4G,eAAeZ,iBAAM,CAACI,WAAW,CAAC;QAExC,oBAAoB;QACpBL,OAAOC,iBAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;QAC7DH,OAAOC,iBAAM,CAACI,WAAW,CAAC,mBAAmBF,iBAAiB;QAE9D,gBAAgB;QAChBI,oBAAS,CAACK,KAAK,CAACC;QAChBb,OAAOC,iBAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACa,aAAa,CAAC,mBAAmBC,GAAG,CAACZ,iBAAiB;IACtE;IAEAL,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBAAC,qBAAC9F;QAER,gCAAgC;QAChC,MAAMmG,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;QAChE;QAEA,4CAA4C;QAC5C,MAAMa,YAAYC,IAAAA,0BAAe,EAAC;YAChCjD,IAAI;YACJE,OAAO;YACPC,SAAS;YACTnC,MAAM;YACNoC,UAAU;QACZ;QAEA,6BAA6B;QAC7B,MAAM8C,eAAe,IAAIC,aAAa,WAAW;YAC/CtF,MAAMC,KAAKsF,SAAS,CAAC;gBACnBpF,MAAM;gBACNC,SAAS+E;YACX;QACF;QAEA,8BAA8B;QAC9B,MAAM,EAAErG,QAAQ,EAAE,GAAGU,iCAAiB,CAACgG,QAAQ;QAC/C1G,SAASqG;QAET,+BAA+B;QAC/B,MAAMZ,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,eAAeC,iBAAiB;YACxDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,gBAAgBC,iBAAiB;YACzDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,2BAA2BC,iBAAiB;QACtE;IACF;IAEAL,GAAG,gCAAgC;QACjC,MAAMkB,YAAYC,IAAAA,0BAAe,EAAC;YAChCjD,IAAI;YACJE,OAAO;YACPG,cAAc;QAChB;QAEA,qBAAqB;QACrBhD,iCAAiB,CAACgG,QAAQ,GAAG1G,QAAQ,CAACqG;QAEtCjB,IAAAA,iBAAM,gBAAC,qBAAC9F;QAER,mCAAmC;QACnC,MAAMmG,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;YAC9DH,OAAOC,iBAAM,CAACI,WAAW,CAAC,kBAAkBF,iBAAiB;QAC/D;QAEA,oBAAoB;QACpBI,oBAAS,CAACK,KAAK,CAACX,iBAAM,CAACI,WAAW,CAAC;QAEnC,sCAAsC;QACtC,MAAMD,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;YAC/DH,OAAOC,iBAAM,CAACa,aAAa,CAAC,kBAAkBC,GAAG,CAACZ,iBAAiB;QACrE;IACF;IAEAL,GAAG,yBAAyB;QAC1B,MAAMkB,YAAYC,IAAAA,0BAAe,EAAC;YAChCjD,IAAI;YACJE,OAAO;QACT;QAEA,qBAAqB;QACrB7C,iCAAiB,CAACgG,QAAQ,GAAG1G,QAAQ,CAACqG;QAEtCjB,IAAAA,iBAAM,gBAAC,qBAAC9F;QAER,oBAAoB;QACpB,MAAMmG,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,eAAeC,iBAAiB;YACxDH,OAAOC,iBAAM,CAACI,WAAW,CAAC,YAAYF,iBAAiB;QACzD;QAEA,eAAe;QACfI,oBAAS,CAACK,KAAK,CAACX,iBAAM,CAACI,WAAW,CAAC;QAEnC,sBAAsB;QACtB,MAAMD,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,eAAeC,iBAAiB;YACxDH,OAAOC,iBAAM,CAACa,aAAa,CAAC,YAAYC,GAAG,CAACZ,iBAAiB;YAC7DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,cAAcC,iBAAiB;QACzD;IACF;IAEAL,GAAG,qBAAqB;QACtB,sBAAsB;QACtB,MAAM3F,SAAS;YACb8G,IAAAA,0BAAe,EAAC;gBAAEjD,IAAI;gBAAKE,OAAO;YAAU;YAC5C+C,IAAAA,0BAAe,EAAC;gBAAEjD,IAAI;gBAAKE,OAAO;YAAU;YAC5C+C,IAAAA,0BAAe,EAAC;gBAAEjD,IAAI;gBAAKE,OAAO;YAAU;SAC7C;QAED/D,OAAOmH,OAAO,CAACvD,CAAAA;YACb1C,iCAAiB,CAACgG,QAAQ,GAAG1G,QAAQ,CAACoD;QACxC;QAEAgC,IAAAA,iBAAM,gBAAC,qBAAC9F;QAER,yBAAyB;QACzB,MAAMmG,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,eAAeC,iBAAiB;QAC1D;QAEA,mBAAmB;QACnBI,oBAAS,CAACK,KAAK,CAACX,iBAAM,CAACI,WAAW,CAAC;QAEnC,2BAA2B;QAC3B,MAAMD,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,eAAeC,iBAAiB;YACxDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,cAAcC,iBAAiB;QACzD;IACF;IAEAL,GAAG,sCAAsC;QACvCC,IAAAA,iBAAM,gBAAC,qBAAC9F;QAER,gCAAgC;QAChC,MAAMmG,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;QAChE;QAEA,2BAA2B;QAC3B,MAAMoB,iBAAiB;YACrBC,OAAO;YACP/C,SAAS;YACTC,UAAU;YACVC,WAAW,CAAC;YACZ8C,UAAU;YACVC,WAAW,IAAIC,OAAOC,WAAW;YACjCC,OAAO;YACPC,SAAS;QACX;QAEA,+BAA+B;QAC/BzG,iCAAiB,CAACgG,QAAQ,GAAGtG,iBAAiB,CAACwG;QAE/C,iCAAiC;QACjC,MAAMnB,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,gCAAgCC,iBAAiB;YACzEH,OAAOC,iBAAM,CAACC,SAAS,CAAC,mCAAmCC,iBAAiB;QAC9E;IACF;IAEAL,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAAC9F;QAER,4BAA4B;QAC5BoB,iCAAiB,CAACgG,QAAQ,GAAGrG,mBAAmB,CAAC,OAAO;QAExD,2BAA2B;QAC3B,MAAMoF,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,2CAA2CC,iBAAiB;QACtF;IACF;IAEAL,GAAG,2BAA2B;QAC5BC,IAAAA,iBAAM,gBAAC,qBAAC9F;QAER,wBAAwB;QACxB,MAAMmG,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACI,WAAW,CAAC,sBAAsBF,iBAAiB;QACnE;QAGEnG,OAAOsF,KAAK,CAAeyC,SAAS;QAEtC,gBAAgB;QAChBxB,oBAAS,CAACK,KAAK,CAACX,iBAAM,CAACI,WAAW,CAAC;QAEnC,4BAA4B;QAC5B,MAAMD,IAAAA,kBAAO,EAAC;YACZJ,OAAOhG,OAAOsF,KAAK,EAAE0C,gBAAgB;QACvC;IACF;AACF"}