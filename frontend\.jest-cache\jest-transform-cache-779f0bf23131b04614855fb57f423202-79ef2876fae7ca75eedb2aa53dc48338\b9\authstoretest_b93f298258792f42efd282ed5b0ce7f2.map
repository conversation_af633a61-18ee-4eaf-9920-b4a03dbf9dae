{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\stores\\__tests__\\auth-store.test.ts"], "sourcesContent": ["import { renderHook, act } from '@testing-library/react'\nimport { useAuthStore } from '../auth-store'\nimport { authApi } from '@/api/auth-api'\nimport { createMockUser, mockApiResponse, mockApiError } from '@/lib/test-utils'\nimport { LoginRequest, RegisterRequest, ChangePasswordRequest } from '@/types/auth'\n\n// Mock the API\njest.mock('@/api/auth-api')\nconst mockedAuthApi = authApi as jest.Mocked<typeof authApi>\n\n// Mock react-hot-toast\njest.mock('react-hot-toast', () => ({\n  toast: {\n    success: jest.fn(),\n    error: jest.fn(),\n    loading: jest.fn(),\n    dismiss: jest.fn(),\n  },\n}))\n\ndescribe('Auth Store', () => {\n  beforeEach(() => {\n    // Reset store state\n    useAuthStore.setState({\n      user: null,\n      token: null,\n      refreshToken: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n    })\n    \n    // Clear all mocks\n    jest.clearAllMocks()\n    \n    // Clear localStorage\n    localStorage.clear()\n  })\n\n  describe('login', () => {\n    it('should login successfully', async () => {\n      const mockUser = createMockUser()\n      const loginData: LoginRequest = {\n        username: 'testuser',\n        password: 'password123',\n        rememberMe: false,\n      }\n      \n      const mockResponse = {\n        user: mockUser,\n        token: 'mock-token',\n        refreshToken: 'mock-refresh-token',\n        expiresIn: 3600,\n      }\n      \n      mockedAuthApi.login.mockResolvedValue(mockResponse)\n\n      const { result } = renderHook(() => useAuthStore())\n\n      await act(async () => {\n        const success = await result.current.login(loginData)\n        expect(success).toBe(true)\n      })\n\n      expect(result.current.user).toEqual(mockUser)\n      expect(result.current.token).toBe('mock-token')\n      expect(result.current.refreshToken).toBe('mock-refresh-token')\n      expect(result.current.isAuthenticated).toBe(true)\n      expect(result.current.isLoading).toBe(false)\n      expect(result.current.error).toBeNull()\n      expect(mockedAuthApi.login).toHaveBeenCalledWith(loginData)\n    })\n\n    it('should handle login failure', async () => {\n      const loginData: LoginRequest = {\n        username: 'testuser',\n        password: 'wrongpassword',\n        rememberMe: false,\n      }\n      \n      mockedAuthApi.login.mockRejectedValue(new Error('Invalid credentials'))\n\n      const { result } = renderHook(() => useAuthStore())\n\n      await act(async () => {\n        const success = await result.current.login(loginData)\n        expect(success).toBe(false)\n      })\n\n      expect(result.current.user).toBeNull()\n      expect(result.current.token).toBeNull()\n      expect(result.current.isAuthenticated).toBe(false)\n      expect(result.current.isLoading).toBe(false)\n      expect(result.current.error).toBe('Invalid credentials')\n    })\n\n    it('should set loading state during login', async () => {\n      const loginData: LoginRequest = {\n        username: 'testuser',\n        password: 'password123',\n        rememberMe: false,\n      }\n      \n      let resolveLogin: (value: any) => void\n      const loginPromise = new Promise(resolve => {\n        resolveLogin = resolve\n      })\n      \n      mockedAuthApi.login.mockReturnValue(loginPromise as any)\n\n      const { result } = renderHook(() => useAuthStore())\n\n      // Start login\n      act(() => {\n        result.current.login(loginData)\n      })\n\n      // Check loading state\n      expect(result.current.isLoading).toBe(true)\n\n      // Resolve login\n      await act(async () => {\n        resolveLogin!({\n          user: createMockUser(),\n          token: 'mock-token',\n          refreshToken: 'mock-refresh-token',\n          expiresIn: 3600,\n        })\n        await loginPromise\n      })\n\n      expect(result.current.isLoading).toBe(false)\n    })\n  })\n\n  describe('logout', () => {\n    it('should logout successfully', async () => {\n      // Set initial authenticated state\n      const mockUser = createMockUser()\n      useAuthStore.setState({\n        user: mockUser,\n        token: 'mock-token',\n        refreshToken: 'mock-refresh-token',\n        isAuthenticated: true,\n      })\n\n      mockedAuthApi.logout.mockResolvedValue()\n\n      const { result } = renderHook(() => useAuthStore())\n\n      await act(async () => {\n        await result.current.logout()\n      })\n\n      expect(result.current.user).toBeNull()\n      expect(result.current.token).toBeNull()\n      expect(result.current.refreshToken).toBeNull()\n      expect(result.current.isAuthenticated).toBe(false)\n      expect(result.current.error).toBeNull()\n      expect(mockedAuthApi.logout).toHaveBeenCalled()\n    })\n\n    it('should handle logout failure gracefully', async () => {\n      // Set initial authenticated state\n      const mockUser = createMockUser()\n      useAuthStore.setState({\n        user: mockUser,\n        token: 'mock-token',\n        refreshToken: 'mock-refresh-token',\n        isAuthenticated: true,\n      })\n\n      mockedAuthApi.logout.mockRejectedValue(new Error('Logout failed'))\n\n      const { result } = renderHook(() => useAuthStore())\n\n      await act(async () => {\n        await result.current.logout()\n      })\n\n      // Should still clear local state even if API call fails\n      expect(result.current.user).toBeNull()\n      expect(result.current.token).toBeNull()\n      expect(result.current.refreshToken).toBeNull()\n      expect(result.current.isAuthenticated).toBe(false)\n    })\n  })\n\n  describe('register', () => {\n    it('should register successfully', async () => {\n      const registerData: RegisterRequest = {\n        username: 'newuser',\n        email: '<EMAIL>',\n        password: 'password123',\n        firstName: 'New',\n        lastName: 'User',\n      }\n      \n      const mockUser = createMockUser({\n        username: 'newuser',\n        email: '<EMAIL>',\n        firstName: 'New',\n        lastName: 'User',\n      })\n      \n      const mockResponse = {\n        user: mockUser,\n        token: 'mock-token',\n        refreshToken: 'mock-refresh-token',\n        expiresIn: 3600,\n      }\n      \n      mockedAuthApi.register.mockResolvedValue(mockResponse)\n\n      const { result } = renderHook(() => useAuthStore())\n\n      await act(async () => {\n        const success = await result.current.register(registerData)\n        expect(success).toBe(true)\n      })\n\n      expect(result.current.user).toEqual(mockUser)\n      expect(result.current.token).toBe('mock-token')\n      expect(result.current.isAuthenticated).toBe(true)\n      expect(mockedAuthApi.register).toHaveBeenCalledWith(registerData)\n    })\n\n    it('should handle registration failure', async () => {\n      const registerData: RegisterRequest = {\n        username: 'existinguser',\n        email: '<EMAIL>',\n        password: 'password123',\n        firstName: 'Existing',\n        lastName: 'User',\n      }\n      \n      mockedAuthApi.register.mockRejectedValue(new Error('Username already exists'))\n\n      const { result } = renderHook(() => useAuthStore())\n\n      await act(async () => {\n        const success = await result.current.register(registerData)\n        expect(success).toBe(false)\n      })\n\n      expect(result.current.user).toBeNull()\n      expect(result.current.isAuthenticated).toBe(false)\n      expect(result.current.error).toBe('Username already exists')\n    })\n  })\n\n  describe('refreshToken', () => {\n    it('should refresh token successfully', async () => {\n      const mockUser = createMockUser()\n      useAuthStore.setState({\n        user: mockUser,\n        token: 'old-token',\n        refreshToken: 'refresh-token',\n        isAuthenticated: true,\n      })\n\n      const mockResponse = {\n        token: 'new-token',\n        refreshToken: 'new-refresh-token',\n        expiresIn: 3600,\n      }\n      \n      mockedAuthApi.refreshToken.mockResolvedValue(mockResponse)\n\n      const { result } = renderHook(() => useAuthStore())\n\n      await act(async () => {\n        const success = await result.current.refreshToken()\n        expect(success).toBe(true)\n      })\n\n      expect(result.current.token).toBe('new-token')\n      expect(result.current.refreshToken).toBe('new-refresh-token')\n      expect(result.current.isAuthenticated).toBe(true)\n      expect(mockedAuthApi.refreshToken).toHaveBeenCalledWith({\n        refreshToken: 'refresh-token',\n      })\n    })\n\n    it('should handle refresh token failure', async () => {\n      useAuthStore.setState({\n        user: createMockUser(),\n        token: 'old-token',\n        refreshToken: 'invalid-refresh-token',\n        isAuthenticated: true,\n      })\n\n      mockedAuthApi.refreshToken.mockRejectedValue(new Error('Invalid refresh token'))\n\n      const { result } = renderHook(() => useAuthStore())\n\n      await act(async () => {\n        const success = await result.current.refreshToken()\n        expect(success).toBe(false)\n      })\n\n      // Should logout user when refresh fails\n      expect(result.current.user).toBeNull()\n      expect(result.current.token).toBeNull()\n      expect(result.current.refreshToken).toBeNull()\n      expect(result.current.isAuthenticated).toBe(false)\n    })\n  })\n\n  describe('permission checks', () => {\n    it('should check permissions correctly', () => {\n      const mockUser = createMockUser({\n        permissions: [\n          { id: 1, name: 'UAV_READ', resource: 'UAV', action: 'READ', description: 'Read UAV data' },\n          { id: 2, name: 'UAV_WRITE', resource: 'UAV', action: 'WRITE', description: 'Write UAV data' },\n        ],\n      })\n      \n      useAuthStore.setState({\n        user: mockUser,\n        isAuthenticated: true,\n      })\n\n      const { result } = renderHook(() => useAuthStore())\n\n      expect(result.current.hasPermission({ permission: 'UAV_READ' })).toBe(true)\n      expect(result.current.hasPermission({ permission: 'UAV_DELETE' })).toBe(false)\n      expect(result.current.canAccess('UAV', 'READ')).toBe(true)\n      expect(result.current.canAccess('UAV', 'DELETE')).toBe(false)\n    })\n\n    it('should check roles correctly', () => {\n      const mockUser = createMockUser({\n        roles: [\n          { id: 1, name: 'ADMIN', description: 'Administrator', permissions: [] },\n          { id: 2, name: 'USER', description: 'Regular user', permissions: [] },\n        ],\n      })\n      \n      useAuthStore.setState({\n        user: mockUser,\n        isAuthenticated: true,\n      })\n\n      const { result } = renderHook(() => useAuthStore())\n\n      expect(result.current.hasRole('ADMIN')).toBe(true)\n      expect(result.current.hasRole('USER')).toBe(true)\n      expect(result.current.hasRole('SUPER_ADMIN')).toBe(false)\n    })\n\n    it('should return false for unauthenticated users', () => {\n      useAuthStore.setState({\n        user: null,\n        isAuthenticated: false,\n      })\n\n      const { result } = renderHook(() => useAuthStore())\n\n      expect(result.current.hasPermission({ permission: 'UAV_READ' })).toBe(false)\n      expect(result.current.hasRole('ADMIN')).toBe(false)\n      expect(result.current.canAccess('UAV', 'READ')).toBe(false)\n    })\n  })\n\n  describe('utility functions', () => {\n    it('should clear error', () => {\n      useAuthStore.setState({ error: 'Some error' })\n\n      const { result } = renderHook(() => useAuthStore())\n\n      act(() => {\n        result.current.clearError()\n      })\n\n      expect(result.current.error).toBeNull()\n    })\n\n    it('should set loading state', () => {\n      const { result } = renderHook(() => useAuthStore())\n\n      act(() => {\n        result.current.setLoading(true)\n      })\n\n      expect(result.current.isLoading).toBe(true)\n\n      act(() => {\n        result.current.setLoading(false)\n      })\n\n      expect(result.current.isLoading).toBe(false)\n    })\n\n    it('should update last activity', () => {\n      const mockUser = createMockUser()\n      useAuthStore.setState({\n        user: mockUser,\n        isAuthenticated: true,\n      })\n\n      const { result } = renderHook(() => useAuthStore())\n\n      const beforeUpdate = result.current.user?.lastActivity\n\n      act(() => {\n        result.current.updateLastActivity()\n      })\n\n      const afterUpdate = result.current.user?.lastActivity\n      expect(afterUpdate).not.toBe(beforeUpdate)\n    })\n  })\n})\n"], "names": ["jest", "mock", "toast", "success", "fn", "error", "loading", "dismiss", "mockedAuth<PERSON>pi", "authApi", "describe", "beforeEach", "useAuthStore", "setState", "user", "token", "refreshToken", "isAuthenticated", "isLoading", "clearAllMocks", "localStorage", "clear", "it", "mockUser", "createMockUser", "loginData", "username", "password", "rememberMe", "mockResponse", "expiresIn", "login", "mockResolvedValue", "result", "renderHook", "act", "current", "expect", "toBe", "toEqual", "toBeNull", "toHaveBeenCalledWith", "mockRejectedValue", "Error", "resolveLogin", "loginPromise", "Promise", "resolve", "mockReturnValue", "logout", "toHaveBeenCalled", "registerData", "email", "firstName", "lastName", "register", "permissions", "id", "name", "resource", "action", "description", "hasPermission", "permission", "canAccess", "roles", "hasRole", "clearError", "setLoading", "beforeUpdate", "lastActivity", "updateLastActivity", "afterUpdate", "not"], "mappings": ";AAMA,eAAe;AACfA,KAAKC,IAAI,CAAC;AAGV,uBAAuB;AACvBD,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC,OAAO;YACLC,SAASH,KAAKI,EAAE;YAChBC,OAAOL,KAAKI,EAAE;YACdE,SAASN,KAAKI,EAAE;YAChBG,SAASP,KAAKI,EAAE;QAClB;IACF,CAAA;;;;uBAlBgC;2BACH;yBACL;2BACsC;AAK9D,MAAMI,gBAAgBC,gBAAO;AAY7BC,SAAS,cAAc;IACrBC,WAAW;QACT,oBAAoB;QACpBC,uBAAY,CAACC,QAAQ,CAAC;YACpBC,MAAM;YACNC,OAAO;YACPC,cAAc;YACdC,iBAAiB;YACjBC,WAAW;YACXb,OAAO;QACT;QAEA,kBAAkB;QAClBL,KAAKmB,aAAa;QAElB,qBAAqB;QACrBC,aAAaC,KAAK;IACpB;IAEAX,SAAS,SAAS;QAChBY,GAAG,6BAA6B;YAC9B,MAAMC,WAAWC,IAAAA,yBAAc;YAC/B,MAAMC,YAA0B;gBAC9BC,UAAU;gBACVC,UAAU;gBACVC,YAAY;YACd;YAEA,MAAMC,eAAe;gBACnBf,MAAMS;gBACNR,OAAO;gBACPC,cAAc;gBACdc,WAAW;YACb;YAEAtB,cAAcuB,KAAK,CAACC,iBAAiB,CAACH;YAEtC,MAAM,EAAEI,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhD,MAAMuB,IAAAA,UAAG,EAAC;gBACR,MAAMhC,UAAU,MAAM8B,OAAOG,OAAO,CAACL,KAAK,CAACN;gBAC3CY,OAAOlC,SAASmC,IAAI,CAAC;YACvB;YAEAD,OAAOJ,OAAOG,OAAO,CAACtB,IAAI,EAAEyB,OAAO,CAAChB;YACpCc,OAAOJ,OAAOG,OAAO,CAACrB,KAAK,EAAEuB,IAAI,CAAC;YAClCD,OAAOJ,OAAOG,OAAO,CAACpB,YAAY,EAAEsB,IAAI,CAAC;YACzCD,OAAOJ,OAAOG,OAAO,CAACnB,eAAe,EAAEqB,IAAI,CAAC;YAC5CD,OAAOJ,OAAOG,OAAO,CAAClB,SAAS,EAAEoB,IAAI,CAAC;YACtCD,OAAOJ,OAAOG,OAAO,CAAC/B,KAAK,EAAEmC,QAAQ;YACrCH,OAAO7B,cAAcuB,KAAK,EAAEU,oBAAoB,CAAChB;QACnD;QAEAH,GAAG,+BAA+B;YAChC,MAAMG,YAA0B;gBAC9BC,UAAU;gBACVC,UAAU;gBACVC,YAAY;YACd;YAEApB,cAAcuB,KAAK,CAACW,iBAAiB,CAAC,IAAIC,MAAM;YAEhD,MAAM,EAAEV,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhD,MAAMuB,IAAAA,UAAG,EAAC;gBACR,MAAMhC,UAAU,MAAM8B,OAAOG,OAAO,CAACL,KAAK,CAACN;gBAC3CY,OAAOlC,SAASmC,IAAI,CAAC;YACvB;YAEAD,OAAOJ,OAAOG,OAAO,CAACtB,IAAI,EAAE0B,QAAQ;YACpCH,OAAOJ,OAAOG,OAAO,CAACrB,KAAK,EAAEyB,QAAQ;YACrCH,OAAOJ,OAAOG,OAAO,CAACnB,eAAe,EAAEqB,IAAI,CAAC;YAC5CD,OAAOJ,OAAOG,OAAO,CAAClB,SAAS,EAAEoB,IAAI,CAAC;YACtCD,OAAOJ,OAAOG,OAAO,CAAC/B,KAAK,EAAEiC,IAAI,CAAC;QACpC;QAEAhB,GAAG,yCAAyC;YAC1C,MAAMG,YAA0B;gBAC9BC,UAAU;gBACVC,UAAU;gBACVC,YAAY;YACd;YAEA,IAAIgB;YACJ,MAAMC,eAAe,IAAIC,QAAQC,CAAAA;gBAC/BH,eAAeG;YACjB;YAEAvC,cAAcuB,KAAK,CAACiB,eAAe,CAACH;YAEpC,MAAM,EAAEZ,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhD,cAAc;YACduB,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACL,KAAK,CAACN;YACvB;YAEA,sBAAsB;YACtBY,OAAOJ,OAAOG,OAAO,CAAClB,SAAS,EAAEoB,IAAI,CAAC;YAEtC,gBAAgB;YAChB,MAAMH,IAAAA,UAAG,EAAC;gBACRS,aAAc;oBACZ9B,MAAMU,IAAAA,yBAAc;oBACpBT,OAAO;oBACPC,cAAc;oBACdc,WAAW;gBACb;gBACA,MAAMe;YACR;YAEAR,OAAOJ,OAAOG,OAAO,CAAClB,SAAS,EAAEoB,IAAI,CAAC;QACxC;IACF;IAEA5B,SAAS,UAAU;QACjBY,GAAG,8BAA8B;YAC/B,kCAAkC;YAClC,MAAMC,WAAWC,IAAAA,yBAAc;YAC/BZ,uBAAY,CAACC,QAAQ,CAAC;gBACpBC,MAAMS;gBACNR,OAAO;gBACPC,cAAc;gBACdC,iBAAiB;YACnB;YAEAT,cAAcyC,MAAM,CAACjB,iBAAiB;YAEtC,MAAM,EAAEC,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhD,MAAMuB,IAAAA,UAAG,EAAC;gBACR,MAAMF,OAAOG,OAAO,CAACa,MAAM;YAC7B;YAEAZ,OAAOJ,OAAOG,OAAO,CAACtB,IAAI,EAAE0B,QAAQ;YACpCH,OAAOJ,OAAOG,OAAO,CAACrB,KAAK,EAAEyB,QAAQ;YACrCH,OAAOJ,OAAOG,OAAO,CAACpB,YAAY,EAAEwB,QAAQ;YAC5CH,OAAOJ,OAAOG,OAAO,CAACnB,eAAe,EAAEqB,IAAI,CAAC;YAC5CD,OAAOJ,OAAOG,OAAO,CAAC/B,KAAK,EAAEmC,QAAQ;YACrCH,OAAO7B,cAAcyC,MAAM,EAAEC,gBAAgB;QAC/C;QAEA5B,GAAG,2CAA2C;YAC5C,kCAAkC;YAClC,MAAMC,WAAWC,IAAAA,yBAAc;YAC/BZ,uBAAY,CAACC,QAAQ,CAAC;gBACpBC,MAAMS;gBACNR,OAAO;gBACPC,cAAc;gBACdC,iBAAiB;YACnB;YAEAT,cAAcyC,MAAM,CAACP,iBAAiB,CAAC,IAAIC,MAAM;YAEjD,MAAM,EAAEV,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhD,MAAMuB,IAAAA,UAAG,EAAC;gBACR,MAAMF,OAAOG,OAAO,CAACa,MAAM;YAC7B;YAEA,wDAAwD;YACxDZ,OAAOJ,OAAOG,OAAO,CAACtB,IAAI,EAAE0B,QAAQ;YACpCH,OAAOJ,OAAOG,OAAO,CAACrB,KAAK,EAAEyB,QAAQ;YACrCH,OAAOJ,OAAOG,OAAO,CAACpB,YAAY,EAAEwB,QAAQ;YAC5CH,OAAOJ,OAAOG,OAAO,CAACnB,eAAe,EAAEqB,IAAI,CAAC;QAC9C;IACF;IAEA5B,SAAS,YAAY;QACnBY,GAAG,gCAAgC;YACjC,MAAM6B,eAAgC;gBACpCzB,UAAU;gBACV0B,OAAO;gBACPzB,UAAU;gBACV0B,WAAW;gBACXC,UAAU;YACZ;YAEA,MAAM/B,WAAWC,IAAAA,yBAAc,EAAC;gBAC9BE,UAAU;gBACV0B,OAAO;gBACPC,WAAW;gBACXC,UAAU;YACZ;YAEA,MAAMzB,eAAe;gBACnBf,MAAMS;gBACNR,OAAO;gBACPC,cAAc;gBACdc,WAAW;YACb;YAEAtB,cAAc+C,QAAQ,CAACvB,iBAAiB,CAACH;YAEzC,MAAM,EAAEI,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhD,MAAMuB,IAAAA,UAAG,EAAC;gBACR,MAAMhC,UAAU,MAAM8B,OAAOG,OAAO,CAACmB,QAAQ,CAACJ;gBAC9Cd,OAAOlC,SAASmC,IAAI,CAAC;YACvB;YAEAD,OAAOJ,OAAOG,OAAO,CAACtB,IAAI,EAAEyB,OAAO,CAAChB;YACpCc,OAAOJ,OAAOG,OAAO,CAACrB,KAAK,EAAEuB,IAAI,CAAC;YAClCD,OAAOJ,OAAOG,OAAO,CAACnB,eAAe,EAAEqB,IAAI,CAAC;YAC5CD,OAAO7B,cAAc+C,QAAQ,EAAEd,oBAAoB,CAACU;QACtD;QAEA7B,GAAG,sCAAsC;YACvC,MAAM6B,eAAgC;gBACpCzB,UAAU;gBACV0B,OAAO;gBACPzB,UAAU;gBACV0B,WAAW;gBACXC,UAAU;YACZ;YAEA9C,cAAc+C,QAAQ,CAACb,iBAAiB,CAAC,IAAIC,MAAM;YAEnD,MAAM,EAAEV,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhD,MAAMuB,IAAAA,UAAG,EAAC;gBACR,MAAMhC,UAAU,MAAM8B,OAAOG,OAAO,CAACmB,QAAQ,CAACJ;gBAC9Cd,OAAOlC,SAASmC,IAAI,CAAC;YACvB;YAEAD,OAAOJ,OAAOG,OAAO,CAACtB,IAAI,EAAE0B,QAAQ;YACpCH,OAAOJ,OAAOG,OAAO,CAACnB,eAAe,EAAEqB,IAAI,CAAC;YAC5CD,OAAOJ,OAAOG,OAAO,CAAC/B,KAAK,EAAEiC,IAAI,CAAC;QACpC;IACF;IAEA5B,SAAS,gBAAgB;QACvBY,GAAG,qCAAqC;YACtC,MAAMC,WAAWC,IAAAA,yBAAc;YAC/BZ,uBAAY,CAACC,QAAQ,CAAC;gBACpBC,MAAMS;gBACNR,OAAO;gBACPC,cAAc;gBACdC,iBAAiB;YACnB;YAEA,MAAMY,eAAe;gBACnBd,OAAO;gBACPC,cAAc;gBACdc,WAAW;YACb;YAEAtB,cAAcQ,YAAY,CAACgB,iBAAiB,CAACH;YAE7C,MAAM,EAAEI,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhD,MAAMuB,IAAAA,UAAG,EAAC;gBACR,MAAMhC,UAAU,MAAM8B,OAAOG,OAAO,CAACpB,YAAY;gBACjDqB,OAAOlC,SAASmC,IAAI,CAAC;YACvB;YAEAD,OAAOJ,OAAOG,OAAO,CAACrB,KAAK,EAAEuB,IAAI,CAAC;YAClCD,OAAOJ,OAAOG,OAAO,CAACpB,YAAY,EAAEsB,IAAI,CAAC;YACzCD,OAAOJ,OAAOG,OAAO,CAACnB,eAAe,EAAEqB,IAAI,CAAC;YAC5CD,OAAO7B,cAAcQ,YAAY,EAAEyB,oBAAoB,CAAC;gBACtDzB,cAAc;YAChB;QACF;QAEAM,GAAG,uCAAuC;YACxCV,uBAAY,CAACC,QAAQ,CAAC;gBACpBC,MAAMU,IAAAA,yBAAc;gBACpBT,OAAO;gBACPC,cAAc;gBACdC,iBAAiB;YACnB;YAEAT,cAAcQ,YAAY,CAAC0B,iBAAiB,CAAC,IAAIC,MAAM;YAEvD,MAAM,EAAEV,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhD,MAAMuB,IAAAA,UAAG,EAAC;gBACR,MAAMhC,UAAU,MAAM8B,OAAOG,OAAO,CAACpB,YAAY;gBACjDqB,OAAOlC,SAASmC,IAAI,CAAC;YACvB;YAEA,wCAAwC;YACxCD,OAAOJ,OAAOG,OAAO,CAACtB,IAAI,EAAE0B,QAAQ;YACpCH,OAAOJ,OAAOG,OAAO,CAACrB,KAAK,EAAEyB,QAAQ;YACrCH,OAAOJ,OAAOG,OAAO,CAACpB,YAAY,EAAEwB,QAAQ;YAC5CH,OAAOJ,OAAOG,OAAO,CAACnB,eAAe,EAAEqB,IAAI,CAAC;QAC9C;IACF;IAEA5B,SAAS,qBAAqB;QAC5BY,GAAG,sCAAsC;YACvC,MAAMC,WAAWC,IAAAA,yBAAc,EAAC;gBAC9BgC,aAAa;oBACX;wBAAEC,IAAI;wBAAGC,MAAM;wBAAYC,UAAU;wBAAOC,QAAQ;wBAAQC,aAAa;oBAAgB;oBACzF;wBAAEJ,IAAI;wBAAGC,MAAM;wBAAaC,UAAU;wBAAOC,QAAQ;wBAASC,aAAa;oBAAiB;iBAC7F;YACH;YAEAjD,uBAAY,CAACC,QAAQ,CAAC;gBACpBC,MAAMS;gBACNN,iBAAiB;YACnB;YAEA,MAAM,EAAEgB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhDyB,OAAOJ,OAAOG,OAAO,CAAC0B,aAAa,CAAC;gBAAEC,YAAY;YAAW,IAAIzB,IAAI,CAAC;YACtED,OAAOJ,OAAOG,OAAO,CAAC0B,aAAa,CAAC;gBAAEC,YAAY;YAAa,IAAIzB,IAAI,CAAC;YACxED,OAAOJ,OAAOG,OAAO,CAAC4B,SAAS,CAAC,OAAO,SAAS1B,IAAI,CAAC;YACrDD,OAAOJ,OAAOG,OAAO,CAAC4B,SAAS,CAAC,OAAO,WAAW1B,IAAI,CAAC;QACzD;QAEAhB,GAAG,gCAAgC;YACjC,MAAMC,WAAWC,IAAAA,yBAAc,EAAC;gBAC9ByC,OAAO;oBACL;wBAAER,IAAI;wBAAGC,MAAM;wBAASG,aAAa;wBAAiBL,aAAa,EAAE;oBAAC;oBACtE;wBAAEC,IAAI;wBAAGC,MAAM;wBAAQG,aAAa;wBAAgBL,aAAa,EAAE;oBAAC;iBACrE;YACH;YAEA5C,uBAAY,CAACC,QAAQ,CAAC;gBACpBC,MAAMS;gBACNN,iBAAiB;YACnB;YAEA,MAAM,EAAEgB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhDyB,OAAOJ,OAAOG,OAAO,CAAC8B,OAAO,CAAC,UAAU5B,IAAI,CAAC;YAC7CD,OAAOJ,OAAOG,OAAO,CAAC8B,OAAO,CAAC,SAAS5B,IAAI,CAAC;YAC5CD,OAAOJ,OAAOG,OAAO,CAAC8B,OAAO,CAAC,gBAAgB5B,IAAI,CAAC;QACrD;QAEAhB,GAAG,iDAAiD;YAClDV,uBAAY,CAACC,QAAQ,CAAC;gBACpBC,MAAM;gBACNG,iBAAiB;YACnB;YAEA,MAAM,EAAEgB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhDyB,OAAOJ,OAAOG,OAAO,CAAC0B,aAAa,CAAC;gBAAEC,YAAY;YAAW,IAAIzB,IAAI,CAAC;YACtED,OAAOJ,OAAOG,OAAO,CAAC8B,OAAO,CAAC,UAAU5B,IAAI,CAAC;YAC7CD,OAAOJ,OAAOG,OAAO,CAAC4B,SAAS,CAAC,OAAO,SAAS1B,IAAI,CAAC;QACvD;IACF;IAEA5B,SAAS,qBAAqB;QAC5BY,GAAG,sBAAsB;YACvBV,uBAAY,CAACC,QAAQ,CAAC;gBAAER,OAAO;YAAa;YAE5C,MAAM,EAAE4B,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhDuB,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAAC+B,UAAU;YAC3B;YAEA9B,OAAOJ,OAAOG,OAAO,CAAC/B,KAAK,EAAEmC,QAAQ;QACvC;QAEAlB,GAAG,4BAA4B;YAC7B,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhDuB,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACgC,UAAU,CAAC;YAC5B;YAEA/B,OAAOJ,OAAOG,OAAO,CAAClB,SAAS,EAAEoB,IAAI,CAAC;YAEtCH,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACgC,UAAU,CAAC;YAC5B;YAEA/B,OAAOJ,OAAOG,OAAO,CAAClB,SAAS,EAAEoB,IAAI,CAAC;QACxC;QAEAhB,GAAG,+BAA+B;YAChC,MAAMC,WAAWC,IAAAA,yBAAc;YAC/BZ,uBAAY,CAACC,QAAQ,CAAC;gBACpBC,MAAMS;gBACNN,iBAAiB;YACnB;YAEA,MAAM,EAAEgB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMtB,IAAAA,uBAAY;YAEhD,MAAMyD,eAAepC,OAAOG,OAAO,CAACtB,IAAI,EAAEwD;YAE1CnC,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACmC,kBAAkB;YACnC;YAEA,MAAMC,cAAcvC,OAAOG,OAAO,CAACtB,IAAI,EAAEwD;YACzCjC,OAAOmC,aAAaC,GAAG,CAACnC,IAAI,CAAC+B;QAC/B;IACF;AACF"}