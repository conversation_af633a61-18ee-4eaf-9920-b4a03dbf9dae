{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\layout\\main-nav.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\nimport {\n  LayoutDashboard,\n  Plane,\n  Map,\n  Settings,\n  BarChart3,\n  Shield,\n  Battery,\n  MapPin,\n  AlertTriangle,\n  Users,\n  FileText,\n  Home,\n} from 'lucide-react'\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: LayoutDashboard,\n    description: 'System overview and real-time monitoring',\n  },\n  {\n    name: 'UAV Management',\n    href: '/uavs',\n    icon: Plane,\n    description: 'Manage UAV fleet and operations',\n  },\n  {\n    name: 'Map View',\n    href: '/map',\n    icon: Map,\n    description: 'Real-time UAV tracking and geofences',\n  },\n  {\n    name: 'Hibernate Pod',\n    href: '/hibernate-pod',\n    icon: Home,\n    description: 'Manage UAV hibernation and storage',\n  },\n  {\n    name: 'Docking Stations',\n    href: '/docking-stations',\n    icon: MapPin,\n    description: 'Manage docking station network',\n  },\n  {\n    name: 'Battery Monitor',\n    href: '/battery',\n    icon: Battery,\n    description: 'Monitor UAV battery status and health',\n  },\n  {\n    name: 'Flight Logs',\n    href: '/flight-logs',\n    icon: FileText,\n    description: 'View flight history and logs',\n  },\n  {\n    name: 'Analytics',\n    href: '/analytics',\n    icon: BarChart3,\n    description: 'Performance metrics and reports',\n  },\n  {\n    name: 'Alerts',\n    href: '/alerts',\n    icon: AlertTriangle,\n    description: 'System alerts and notifications',\n  },\n  {\n    name: 'Regions',\n    href: '/regions',\n    icon: Shield,\n    description: 'Manage operational regions',\n  },\n  {\n    name: 'Users',\n    href: '/users',\n    icon: Users,\n    description: 'User management and permissions',\n  },\n  {\n    name: 'Settings',\n    href: '/settings',\n    icon: Settings,\n    description: 'System configuration and preferences',\n  },\n]\n\ninterface MainNavProps {\n  className?: string\n  onNavigate?: () => void\n}\n\nexport function MainNav({ className, onNavigate }: MainNavProps) {\n  const pathname = usePathname()\n\n  return (\n    <nav className={cn('flex flex-col space-y-1', className)}>\n      {navigation.map((item) => {\n        const isActive = pathname === item.href || pathname.startsWith(item.href + '/')\n        const Icon = item.icon\n\n        return (\n          <Link\n            key={item.name}\n            href={item.href}\n            onClick={onNavigate}\n            className={cn(\n              'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',\n              'hover:bg-accent hover:text-accent-foreground',\n              'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n              isActive\n                ? 'bg-primary text-primary-foreground shadow-sm'\n                : 'text-muted-foreground'\n            )}\n            title={item.description}\n          >\n            <Icon\n              className={cn(\n                'mr-3 h-5 w-5 flex-shrink-0 transition-colors',\n                isActive\n                  ? 'text-primary-foreground'\n                  : 'text-muted-foreground group-hover:text-accent-foreground'\n              )}\n            />\n            <span className=\"truncate\">{item.name}</span>\n            {isActive && (\n              <div className=\"ml-auto h-2 w-2 rounded-full bg-primary-foreground\" />\n            )}\n          </Link>\n        )\n      })}\n    </nav>\n  )\n}\n\n// Mobile navigation component\nexport function MobileNav({ className, onNavigate }: MainNavProps) {\n  const pathname = usePathname()\n\n  return (\n    <nav className={cn('grid grid-cols-2 gap-2 p-4', className)}>\n      {navigation.slice(0, 8).map((item) => {\n        const isActive = pathname === item.href || pathname.startsWith(item.href + '/')\n        const Icon = item.icon\n\n        return (\n          <motion.div\n            key={item.name}\n            whileHover={{ scale: 1.05, y: -2 }}\n            whileTap={{ scale: 0.95 }}\n            transition={{ duration: 0.2 }}\n          >\n            <Link\n              href={item.href}\n              onClick={onNavigate}\n              className={cn(\n                'flex flex-col items-center justify-center p-3 rounded-lg transition-colors',\n                'hover:bg-accent hover:text-accent-foreground',\n                'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n                isActive\n                  ? 'bg-primary text-primary-foreground'\n                  : 'text-muted-foreground'\n              )}\n            >\n              <motion.div\n                animate={isActive ? { scale: 1.1 } : { scale: 1 }}\n                transition={{ duration: 0.2 }}\n              >\n                <Icon className=\"h-6 w-6 mb-1\" />\n              </motion.div>\n              <span className=\"text-xs font-medium text-center leading-tight\">\n                {item.name}\n              </span>\n            </Link>\n          </motion.div>\n        )\n      })}\n    </nav>\n  )\n}\n\n// Quick access navigation for frequently used items\nexport function QuickNav({ className }: { className?: string }) {\n  const pathname = usePathname()\n  const quickItems = navigation.slice(0, 4) // Dashboard, UAVs, Map, Hibernate Pod\n\n  return (\n    <nav className={cn('flex space-x-1', className)}>\n      {quickItems.map((item) => {\n        const isActive = pathname === item.href || pathname.startsWith(item.href + '/')\n        const Icon = item.icon\n\n        return (\n          <motion.div\n            key={item.name}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            transition={{ duration: 0.2 }}\n          >\n            <Link\n              href={item.href}\n              className={cn(\n                'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',\n                'hover:bg-accent hover:text-accent-foreground',\n                'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n                isActive\n                  ? 'bg-primary text-primary-foreground'\n                  : 'text-muted-foreground'\n              )}\n              title={item.description}\n            >\n              <motion.div\n                animate={isActive ? { rotate: 360 } : { rotate: 0 }}\n                transition={{ duration: 0.3 }}\n              >\n                <Icon className=\"h-4 w-4 mr-2\" />\n              </motion.div>\n              <span className=\"hidden sm:inline\">{item.name}</span>\n            </Link>\n          </motion.div>\n        )\n      })}\n    </nav>\n  )\n}\n"], "names": ["MainNav", "MobileNav", "QuickNav", "navigation", "name", "href", "icon", "LayoutDashboard", "description", "Plane", "Map", "Home", "MapPin", "Battery", "FileText", "BarChart3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shield", "Users", "Settings", "className", "onNavigate", "pathname", "usePathname", "nav", "cn", "map", "item", "isActive", "startsWith", "Icon", "Link", "onClick", "title", "span", "div", "slice", "motion", "whileHover", "scale", "y", "whileTap", "transition", "duration", "animate", "quickItems", "rotate"], "mappings": "AAAA;;;;;;;;;;;;IAsGgBA,OAAO;eAAPA;;IA4CAC,SAAS;eAATA;;IA8CAC,QAAQ;eAARA;;;;8DA9LE;6DACD;4BACW;8BACL;uBACJ;6BAcZ;;;;;;AAEP,MAAMC,aAAa;IACjB;QACEC,MAAM;QACNC,MAAM;QACNC,MAAMC,4BAAe;QACrBC,aAAa;IACf;IACA;QACEJ,MAAM;QACNC,MAAM;QACNC,MAAMG,kBAAK;QACXD,aAAa;IACf;IACA;QACEJ,MAAM;QACNC,MAAM;QACNC,MAAMI,gBAAG;QACTF,aAAa;IACf;IACA;QACEJ,MAAM;QACNC,MAAM;QACNC,MAAMK,iBAAI;QACVH,aAAa;IACf;IACA;QACEJ,MAAM;QACNC,MAAM;QACNC,MAAMM,mBAAM;QACZJ,aAAa;IACf;IACA;QACEJ,MAAM;QACNC,MAAM;QACNC,MAAMO,oBAAO;QACbL,aAAa;IACf;IACA;QACEJ,MAAM;QACNC,MAAM;QACNC,MAAMQ,qBAAQ;QACdN,aAAa;IACf;IACA;QACEJ,MAAM;QACNC,MAAM;QACNC,MAAMS,sBAAS;QACfP,aAAa;IACf;IACA;QACEJ,MAAM;QACNC,MAAM;QACNC,MAAMU,0BAAa;QACnBR,aAAa;IACf;IACA;QACEJ,MAAM;QACNC,MAAM;QACNC,MAAMW,mBAAM;QACZT,aAAa;IACf;IACA;QACEJ,MAAM;QACNC,MAAM;QACNC,MAAMY,kBAAK;QACXV,aAAa;IACf;IACA;QACEJ,MAAM;QACNC,MAAM;QACNC,MAAMa,qBAAQ;QACdX,aAAa;IACf;CACD;AAOM,SAASR,QAAQ,EAAEoB,SAAS,EAAEC,UAAU,EAAgB;IAC7D,MAAMC,WAAWC,IAAAA,uBAAW;IAE5B,qBACE,qBAACC;QAAIJ,WAAWK,IAAAA,SAAE,EAAC,2BAA2BL;kBAC3CjB,WAAWuB,GAAG,CAAC,CAACC;YACf,MAAMC,WAAWN,aAAaK,KAAKtB,IAAI,IAAIiB,SAASO,UAAU,CAACF,KAAKtB,IAAI,GAAG;YAC3E,MAAMyB,OAAOH,KAAKrB,IAAI;YAEtB,qBACE,sBAACyB,aAAI;gBAEH1B,MAAMsB,KAAKtB,IAAI;gBACf2B,SAASX;gBACTD,WAAWK,IAAAA,SAAE,EACX,sFACA,gDACA,uEACAG,WACI,iDACA;gBAENK,OAAON,KAAKnB,WAAW;;kCAEvB,qBAACsB;wBACCV,WAAWK,IAAAA,SAAE,EACX,gDACAG,WACI,4BACA;;kCAGR,qBAACM;wBAAKd,WAAU;kCAAYO,KAAKvB,IAAI;;oBACpCwB,0BACC,qBAACO;wBAAIf,WAAU;;;eAvBZO,KAAKvB,IAAI;QA2BpB;;AAGN;AAGO,SAASH,UAAU,EAAEmB,SAAS,EAAEC,UAAU,EAAgB;IAC/D,MAAMC,WAAWC,IAAAA,uBAAW;IAE5B,qBACE,qBAACC;QAAIJ,WAAWK,IAAAA,SAAE,EAAC,8BAA8BL;kBAC9CjB,WAAWiC,KAAK,CAAC,GAAG,GAAGV,GAAG,CAAC,CAACC;YAC3B,MAAMC,WAAWN,aAAaK,KAAKtB,IAAI,IAAIiB,SAASO,UAAU,CAACF,KAAKtB,IAAI,GAAG;YAC3E,MAAMyB,OAAOH,KAAKrB,IAAI;YAEtB,qBACE,qBAAC+B,oBAAM,CAACF,GAAG;gBAETG,YAAY;oBAAEC,OAAO;oBAAMC,GAAG,CAAC;gBAAE;gBACjCC,UAAU;oBAAEF,OAAO;gBAAK;gBACxBG,YAAY;oBAAEC,UAAU;gBAAI;0BAE5B,cAAA,sBAACZ,aAAI;oBACH1B,MAAMsB,KAAKtB,IAAI;oBACf2B,SAASX;oBACTD,WAAWK,IAAAA,SAAE,EACX,8EACA,gDACA,uEACAG,WACI,uCACA;;sCAGN,qBAACS,oBAAM,CAACF,GAAG;4BACTS,SAAShB,WAAW;gCAAEW,OAAO;4BAAI,IAAI;gCAAEA,OAAO;4BAAE;4BAChDG,YAAY;gCAAEC,UAAU;4BAAI;sCAE5B,cAAA,qBAACb;gCAAKV,WAAU;;;sCAElB,qBAACc;4BAAKd,WAAU;sCACbO,KAAKvB,IAAI;;;;eAxBTuB,KAAKvB,IAAI;QA6BpB;;AAGN;AAGO,SAASF,SAAS,EAAEkB,SAAS,EAA0B;IAC5D,MAAME,WAAWC,IAAAA,uBAAW;IAC5B,MAAMsB,aAAa1C,WAAWiC,KAAK,CAAC,GAAG,GAAG,sCAAsC;;IAEhF,qBACE,qBAACZ;QAAIJ,WAAWK,IAAAA,SAAE,EAAC,kBAAkBL;kBAClCyB,WAAWnB,GAAG,CAAC,CAACC;YACf,MAAMC,WAAWN,aAAaK,KAAKtB,IAAI,IAAIiB,SAASO,UAAU,CAACF,KAAKtB,IAAI,GAAG;YAC3E,MAAMyB,OAAOH,KAAKrB,IAAI;YAEtB,qBACE,qBAAC+B,oBAAM,CAACF,GAAG;gBAETG,YAAY;oBAAEC,OAAO;gBAAK;gBAC1BE,UAAU;oBAAEF,OAAO;gBAAK;gBACxBG,YAAY;oBAAEC,UAAU;gBAAI;0BAE5B,cAAA,sBAACZ,aAAI;oBACH1B,MAAMsB,KAAKtB,IAAI;oBACfe,WAAWK,IAAAA,SAAE,EACX,gFACA,gDACA,uEACAG,WACI,uCACA;oBAENK,OAAON,KAAKnB,WAAW;;sCAEvB,qBAAC6B,oBAAM,CAACF,GAAG;4BACTS,SAAShB,WAAW;gCAAEkB,QAAQ;4BAAI,IAAI;gCAAEA,QAAQ;4BAAE;4BAClDJ,YAAY;gCAAEC,UAAU;4BAAI;sCAE5B,cAAA,qBAACb;gCAAKV,WAAU;;;sCAElB,qBAACc;4BAAKd,WAAU;sCAAoBO,KAAKvB,IAAI;;;;eAvB1CuB,KAAKvB,IAAI;QA2BpB;;AAGN"}