{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\__tests__\\animated-button.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { AnimatedButton, FloatingActionButton, AnimatedIconButton, ProgressButton } from '../animated-button'\nimport { mockPrefersReducedMotion, mockFramerMotion } from '@/lib/test-utils'\nimport { Loader2, Plus, Download } from 'lucide-react'\n\n// Mock framer-motion for testing\nmockFramerMotion()\n\ndescribe('AnimatedButton Component', () => {\n  beforeEach(() => {\n    jest.clearAllMocks()\n  })\n\n  it('renders correctly', () => {\n    render(<AnimatedButton>Animated Button</AnimatedButton>)\n    expect(screen.getByRole('button', { name: /animated button/i })).toBeInTheDocument()\n  })\n\n  it('handles click events', () => {\n    const handleClick = jest.fn()\n    render(<AnimatedButton onClick={handleClick}>Click me</AnimatedButton>)\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).toHaveBeenCalledTimes(1)\n  })\n\n  it('shows loading state', () => {\n    render(<AnimatedButton loading>Loading Button</AnimatedButton>)\n    const button = screen.getByRole('button')\n\n    expect(button).toBeDisabled()\n\n    // Check for loading spinner (it's a div with specific styling)\n    const spinner = button.querySelector('.border-t-transparent')\n    expect(spinner).toBeInTheDocument()\n  })\n\n  it('is disabled when disabled prop is true', () => {\n    render(<AnimatedButton disabled>Disabled Button</AnimatedButton>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toBeDisabled()\n  })\n\n  it('does not trigger click when disabled', () => {\n    const handleClick = jest.fn()\n    render(<AnimatedButton disabled onClick={handleClick}>Disabled</AnimatedButton>)\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).not.toHaveBeenCalled()\n  })\n\n  it('does not trigger click when loading', () => {\n    const handleClick = jest.fn()\n    render(<AnimatedButton loading onClick={handleClick}>Loading</AnimatedButton>)\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).not.toHaveBeenCalled()\n  })\n\n  it('applies custom className', () => {\n    render(<AnimatedButton className=\"custom-class\">Custom</AnimatedButton>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toHaveClass('custom-class')\n  })\n\n  it('handles ripple effect when enabled', () => {\n    render(<AnimatedButton ripple>Ripple Button</AnimatedButton>)\n    const button = screen.getByRole('button')\n    \n    fireEvent.click(button)\n    // Ripple effect should be triggered (tested through animation state)\n    expect(button).toBeInTheDocument()\n  })\n\n  it('respects prefers-reduced-motion', () => {\n    mockPrefersReducedMotion(true)\n    render(<AnimatedButton>Reduced Motion</AnimatedButton>)\n    \n    const button = screen.getByRole('button')\n    expect(button).toBeInTheDocument()\n    // Animation should be disabled when prefers-reduced-motion is set\n  })\n\n  it('supports glow effect', () => {\n    render(<AnimatedButton glow>Glow Button</AnimatedButton>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toBeInTheDocument()\n    // Glow effect should be applied through CSS classes\n  })\n\n  it('supports magnetic effect', () => {\n    render(<AnimatedButton magnetic>Magnetic Button</AnimatedButton>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toBeInTheDocument()\n    // Magnetic effect should be applied through motion properties\n  })\n})\n\ndescribe('FloatingActionButton Component', () => {\n  it('renders correctly', () => {\n    render(\n      <FloatingActionButton>\n        <Plus className=\"h-6 w-6\" />\n      </FloatingActionButton>\n    )\n    \n    const button = screen.getByRole('button')\n    expect(button).toBeInTheDocument()\n    expect(button).toHaveClass('rounded-full', 'w-14', 'h-14')\n  })\n\n  it('applies correct position classes', () => {\n    const { rerender } = render(\n      <FloatingActionButton position=\"bottom-right\">\n        <Plus className=\"h-6 w-6\" />\n      </FloatingActionButton>\n    )\n    \n    let container = document.querySelector('.fixed')\n    expect(container).toHaveClass('bottom-6', 'right-6')\n    \n    rerender(\n      <FloatingActionButton position=\"bottom-left\">\n        <Plus className=\"h-6 w-6\" />\n      </FloatingActionButton>\n    )\n    \n    container = document.querySelector('.fixed')\n    expect(container).toHaveClass('bottom-6', 'left-6')\n  })\n\n  it('handles click events', () => {\n    const handleClick = jest.fn()\n    render(\n      <FloatingActionButton onClick={handleClick}>\n        <Plus className=\"h-6 w-6\" />\n      </FloatingActionButton>\n    )\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).toHaveBeenCalledTimes(1)\n  })\n\n  it('handles hover interactions', async () => {\n    render(\n      <FloatingActionButton>\n        <Plus className=\"h-6 w-6\" />\n      </FloatingActionButton>\n    )\n\n    const button = screen.getByRole('button')\n    expect(button).toBeInTheDocument()\n\n    // Test that the button can be hovered\n    fireEvent.mouseEnter(button)\n    fireEvent.mouseLeave(button)\n  })\n})\n\ndescribe('AnimatedIconButton Component', () => {\n  it('renders correctly', () => {\n    render(\n      <AnimatedIconButton icon={<Plus />} aria-label=\"Add item\" />\n    )\n\n    const button = screen.getByRole('button', { name: /add item/i })\n    expect(button).toBeInTheDocument()\n  })\n\n  it('applies size classes correctly', () => {\n    const { rerender } = render(\n      <AnimatedIconButton icon={<Plus />} size=\"sm\" aria-label=\"Small button\" />\n    )\n\n    let button = screen.getByRole('button')\n    expect(button).toHaveClass('h-8', 'w-8')\n\n    rerender(\n      <AnimatedIconButton icon={<Plus />} size=\"lg\" aria-label=\"Large button\" />\n    )\n\n    button = screen.getByRole('button')\n    expect(button).toHaveClass('h-12', 'w-12')\n  })\n\n  it('applies variant styles correctly', () => {\n    const { rerender } = render(\n      <AnimatedIconButton icon={<Plus />} variant=\"ghost\" aria-label=\"Ghost button\" />\n    )\n\n    let button = screen.getByRole('button')\n    expect(button).toHaveClass('hover:bg-accent')\n\n    rerender(\n      <AnimatedIconButton icon={<Plus />} variant=\"outline\" aria-label=\"Outline button\" />\n    )\n\n    button = screen.getByRole('button')\n    expect(button).toHaveClass('border')\n  })\n\n  it('handles click events', () => {\n    const handleClick = jest.fn()\n    render(\n      <AnimatedIconButton\n        icon={<Plus />}\n        onClick={handleClick}\n        aria-label=\"Clickable button\"\n      />\n    )\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).toHaveBeenCalledTimes(1)\n  })\n\n  it('is disabled when disabled prop is true', () => {\n    render(\n      <AnimatedIconButton \n        icon={<Plus />}\n        disabled \n        aria-label=\"Disabled button\" \n      />\n    )\n    \n    const button = screen.getByRole('button')\n    expect(button).toBeDisabled()\n  })\n})\n\ndescribe('ProgressButton Component', () => {\n  it('renders correctly', () => {\n    render(<ProgressButton progress={0}>Download</ProgressButton>)\n    expect(screen.getByRole('button', { name: /download/i })).toBeInTheDocument()\n  })\n\n  it('shows progress bar when showProgress is true', () => {\n    render(<ProgressButton progress={50} showProgress={true}>Downloading...</ProgressButton>)\n\n    const button = screen.getByRole('button')\n    expect(button).toBeInTheDocument()\n\n    // Check for progress bar element (it's a div with specific styling)\n    const progressBar = button.querySelector('.bg-white\\\\/20')\n    expect(progressBar).toBeInTheDocument()\n  })\n\n  it('shows completion state', () => {\n    render(<ProgressButton progress={100} showProgress={true}>Completed</ProgressButton>)\n\n    const button = screen.getByRole('button')\n    expect(button).toBeInTheDocument()\n    expect(button).toHaveTextContent('Completed')\n  })\n\n  it('handles click events when not in progress', () => {\n    const handleClick = jest.fn()\n    render(<ProgressButton progress={0} onClick={handleClick}>Start</ProgressButton>)\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).toHaveBeenCalledTimes(1)\n  })\n\n  it('handles click events normally', () => {\n    const handleClick = jest.fn()\n    render(<ProgressButton progress={50} onClick={handleClick}>In Progress</ProgressButton>)\n\n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).toHaveBeenCalledTimes(1)\n  })\n\n  it('shows custom content', () => {\n    render(\n      <ProgressButton progress={0} showProgress={true}>\n        Download File\n      </ProgressButton>\n    )\n\n    expect(screen.getByText('Download File')).toBeInTheDocument()\n  })\n\n  it('applies correct progress width', () => {\n    render(<ProgressButton progress={75} showProgress={true}>Progress</ProgressButton>)\n\n    const button = screen.getByRole('button')\n    const progressBar = button.querySelector('.bg-white\\\\/20')\n    expect(progressBar).toBeInTheDocument()\n  })\n\n  it('handles progress animation', async () => {\n    const { rerender } = render(<ProgressButton progress={0} showProgress={true}>Start</ProgressButton>)\n\n    rerender(<ProgressButton progress={50} showProgress={true}>Progress</ProgressButton>)\n\n    const button = screen.getByRole('button')\n    const progressBar = button.querySelector('.bg-white\\\\/20')\n    expect(progressBar).toBeInTheDocument()\n  })\n\n  it('resets to initial state when progress is 0', () => {\n    const { rerender } = render(<ProgressButton progress={100} showProgress={true}>Done</ProgressButton>)\n\n    rerender(<ProgressButton progress={0} showProgress={false}>Start Again</ProgressButton>)\n\n    const button = screen.getByRole('button')\n    expect(button).not.toBeDisabled()\n    expect(button).toHaveTextContent('Start Again')\n  })\n})\n"], "names": ["mockFramerMotion", "describe", "beforeEach", "jest", "clearAllMocks", "it", "render", "AnimatedButton", "expect", "screen", "getByRole", "name", "toBeInTheDocument", "handleClick", "fn", "onClick", "fireEvent", "click", "toHaveBeenCalledTimes", "loading", "button", "toBeDisabled", "spinner", "querySelector", "disabled", "not", "toHaveBeenCalled", "className", "toHaveClass", "ripple", "mockPrefersReducedMotion", "glow", "magnetic", "FloatingActionButton", "Plus", "rerender", "position", "container", "document", "mouseEnter", "mouseLeave", "AnimatedIconButton", "icon", "aria-label", "size", "variant", "ProgressButton", "progress", "showProgress", "progressBar", "toHaveTextContent", "getByText"], "mappings": ";;;;;8DAAkB;2BACiC;gCACsC;6BAEjD;;;;;;AAExC,iCAAiC;AACjCA,IAAAA,2BAAgB;AAEhBC,SAAS,4BAA4B;IACnCC,WAAW;QACTC,KAAKC,aAAa;IACpB;IAEAC,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;sBAAC;;QACvBC,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAmB,IAAIC,iBAAiB;IACpF;IAEAP,GAAG,wBAAwB;QACzB,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACQ,SAASF;sBAAa;;QAE7CG,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaK,qBAAqB,CAAC;IAC5C;IAEAb,GAAG,uBAAuB;QACxBC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACY,OAAO;sBAAC;;QAC/B,MAAMC,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOY,QAAQC,YAAY;QAE3B,+DAA+D;QAC/D,MAAMC,UAAUF,OAAOG,aAAa,CAAC;QACrCf,OAAOc,SAASV,iBAAiB;IACnC;IAEAP,GAAG,0CAA0C;QAC3CC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACiB,QAAQ;sBAAC;;QAChC,MAAMJ,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOY,QAAQC,YAAY;IAC7B;IAEAhB,GAAG,wCAAwC;QACzC,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACiB,QAAQ;YAACT,SAASF;sBAAa;;QAEtDG,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaY,GAAG,CAACC,gBAAgB;IAC1C;IAEArB,GAAG,uCAAuC;QACxC,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACY,OAAO;YAACJ,SAASF;sBAAa;;QAErDG,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaY,GAAG,CAACC,gBAAgB;IAC1C;IAEArB,GAAG,4BAA4B;QAC7BC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACoB,WAAU;sBAAe;;QAChD,MAAMP,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOY,QAAQQ,WAAW,CAAC;IAC7B;IAEAvB,GAAG,sCAAsC;QACvCC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACsB,MAAM;sBAAC;;QAC9B,MAAMT,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCM,oBAAS,CAACC,KAAK,CAACG;QAChB,qEAAqE;QACrEZ,OAAOY,QAAQR,iBAAiB;IAClC;IAEAP,GAAG,mCAAmC;QACpCyB,IAAAA,mCAAwB,EAAC;QACzBxB,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;sBAAC;;QAEvB,MAAMa,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQR,iBAAiB;IAChC,kEAAkE;IACpE;IAEAP,GAAG,wBAAwB;QACzBC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACwB,IAAI;sBAAC;;QAC5B,MAAMX,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOY,QAAQR,iBAAiB;IAChC,oDAAoD;IACtD;IAEAP,GAAG,4BAA4B;QAC7BC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACyB,QAAQ;sBAAC;;QAChC,MAAMZ,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOY,QAAQR,iBAAiB;IAChC,8DAA8D;IAChE;AACF;AAEAX,SAAS,kCAAkC;IACzCI,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBACJ,qBAAC2B,oCAAoB;sBACnB,cAAA,qBAACC,iBAAI;gBAACP,WAAU;;;QAIpB,MAAMP,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQR,iBAAiB;QAChCJ,OAAOY,QAAQQ,WAAW,CAAC,gBAAgB,QAAQ;IACrD;IAEAvB,GAAG,oCAAoC;QACrC,MAAM,EAAE8B,QAAQ,EAAE,GAAG7B,IAAAA,iBAAM,gBACzB,qBAAC2B,oCAAoB;YAACG,UAAS;sBAC7B,cAAA,qBAACF,iBAAI;gBAACP,WAAU;;;QAIpB,IAAIU,YAAYC,SAASf,aAAa,CAAC;QACvCf,OAAO6B,WAAWT,WAAW,CAAC,YAAY;QAE1CO,uBACE,qBAACF,oCAAoB;YAACG,UAAS;sBAC7B,cAAA,qBAACF,iBAAI;gBAACP,WAAU;;;QAIpBU,YAAYC,SAASf,aAAa,CAAC;QACnCf,OAAO6B,WAAWT,WAAW,CAAC,YAAY;IAC5C;IAEAvB,GAAG,wBAAwB;QACzB,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBACJ,qBAAC2B,oCAAoB;YAAClB,SAASF;sBAC7B,cAAA,qBAACqB,iBAAI;gBAACP,WAAU;;;QAIpBX,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaK,qBAAqB,CAAC;IAC5C;IAEAb,GAAG,8BAA8B;QAC/BC,IAAAA,iBAAM,gBACJ,qBAAC2B,oCAAoB;sBACnB,cAAA,qBAACC,iBAAI;gBAACP,WAAU;;;QAIpB,MAAMP,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQR,iBAAiB;QAEhC,sCAAsC;QACtCI,oBAAS,CAACuB,UAAU,CAACnB;QACrBJ,oBAAS,CAACwB,UAAU,CAACpB;IACvB;AACF;AAEAnB,SAAS,gCAAgC;IACvCI,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBACJ,qBAACmC,kCAAkB;YAACC,oBAAM,qBAACR,iBAAI;YAAKS,cAAW;;QAGjD,MAAMvB,SAASX,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAY;QAC9DH,OAAOY,QAAQR,iBAAiB;IAClC;IAEAP,GAAG,kCAAkC;QACnC,MAAM,EAAE8B,QAAQ,EAAE,GAAG7B,IAAAA,iBAAM,gBACzB,qBAACmC,kCAAkB;YAACC,oBAAM,qBAACR,iBAAI;YAAKU,MAAK;YAAKD,cAAW;;QAG3D,IAAIvB,SAASX,iBAAM,CAACC,SAAS,CAAC;QAC9BF,OAAOY,QAAQQ,WAAW,CAAC,OAAO;QAElCO,uBACE,qBAACM,kCAAkB;YAACC,oBAAM,qBAACR,iBAAI;YAAKU,MAAK;YAAKD,cAAW;;QAG3DvB,SAASX,iBAAM,CAACC,SAAS,CAAC;QAC1BF,OAAOY,QAAQQ,WAAW,CAAC,QAAQ;IACrC;IAEAvB,GAAG,oCAAoC;QACrC,MAAM,EAAE8B,QAAQ,EAAE,GAAG7B,IAAAA,iBAAM,gBACzB,qBAACmC,kCAAkB;YAACC,oBAAM,qBAACR,iBAAI;YAAKW,SAAQ;YAAQF,cAAW;;QAGjE,IAAIvB,SAASX,iBAAM,CAACC,SAAS,CAAC;QAC9BF,OAAOY,QAAQQ,WAAW,CAAC;QAE3BO,uBACE,qBAACM,kCAAkB;YAACC,oBAAM,qBAACR,iBAAI;YAAKW,SAAQ;YAAUF,cAAW;;QAGnEvB,SAASX,iBAAM,CAACC,SAAS,CAAC;QAC1BF,OAAOY,QAAQQ,WAAW,CAAC;IAC7B;IAEAvB,GAAG,wBAAwB;QACzB,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBACJ,qBAACmC,kCAAkB;YACjBC,oBAAM,qBAACR,iBAAI;YACXnB,SAASF;YACT8B,cAAW;;QAIf3B,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaK,qBAAqB,CAAC;IAC5C;IAEAb,GAAG,0CAA0C;QAC3CC,IAAAA,iBAAM,gBACJ,qBAACmC,kCAAkB;YACjBC,oBAAM,qBAACR,iBAAI;YACXV,QAAQ;YACRmB,cAAW;;QAIf,MAAMvB,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQC,YAAY;IAC7B;AACF;AAEApB,SAAS,4BAA4B;IACnCI,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAACwC,8BAAc;YAACC,UAAU;sBAAG;;QACpCvC,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAY,IAAIC,iBAAiB;IAC7E;IAEAP,GAAG,gDAAgD;QACjDC,IAAAA,iBAAM,gBAAC,qBAACwC,8BAAc;YAACC,UAAU;YAAIC,cAAc;sBAAM;;QAEzD,MAAM5B,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQR,iBAAiB;QAEhC,oEAAoE;QACpE,MAAMqC,cAAc7B,OAAOG,aAAa,CAAC;QACzCf,OAAOyC,aAAarC,iBAAiB;IACvC;IAEAP,GAAG,0BAA0B;QAC3BC,IAAAA,iBAAM,gBAAC,qBAACwC,8BAAc;YAACC,UAAU;YAAKC,cAAc;sBAAM;;QAE1D,MAAM5B,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQR,iBAAiB;QAChCJ,OAAOY,QAAQ8B,iBAAiB,CAAC;IACnC;IAEA7C,GAAG,6CAA6C;QAC9C,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBAAC,qBAACwC,8BAAc;YAACC,UAAU;YAAGhC,SAASF;sBAAa;;QAE1DG,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaK,qBAAqB,CAAC;IAC5C;IAEAb,GAAG,iCAAiC;QAClC,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBAAC,qBAACwC,8BAAc;YAACC,UAAU;YAAIhC,SAASF;sBAAa;;QAE3DG,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaK,qBAAqB,CAAC;IAC5C;IAEAb,GAAG,wBAAwB;QACzBC,IAAAA,iBAAM,gBACJ,qBAACwC,8BAAc;YAACC,UAAU;YAAGC,cAAc;sBAAM;;QAKnDxC,OAAOC,iBAAM,CAAC0C,SAAS,CAAC,kBAAkBvC,iBAAiB;IAC7D;IAEAP,GAAG,kCAAkC;QACnCC,IAAAA,iBAAM,gBAAC,qBAACwC,8BAAc;YAACC,UAAU;YAAIC,cAAc;sBAAM;;QAEzD,MAAM5B,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChC,MAAMuC,cAAc7B,OAAOG,aAAa,CAAC;QACzCf,OAAOyC,aAAarC,iBAAiB;IACvC;IAEAP,GAAG,8BAA8B;QAC/B,MAAM,EAAE8B,QAAQ,EAAE,GAAG7B,IAAAA,iBAAM,gBAAC,qBAACwC,8BAAc;YAACC,UAAU;YAAGC,cAAc;sBAAM;;QAE7Eb,uBAAS,qBAACW,8BAAc;YAACC,UAAU;YAAIC,cAAc;sBAAM;;QAE3D,MAAM5B,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChC,MAAMuC,cAAc7B,OAAOG,aAAa,CAAC;QACzCf,OAAOyC,aAAarC,iBAAiB;IACvC;IAEAP,GAAG,8CAA8C;QAC/C,MAAM,EAAE8B,QAAQ,EAAE,GAAG7B,IAAAA,iBAAM,gBAAC,qBAACwC,8BAAc;YAACC,UAAU;YAAKC,cAAc;sBAAM;;QAE/Eb,uBAAS,qBAACW,8BAAc;YAACC,UAAU;YAAGC,cAAc;sBAAO;;QAE3D,MAAM5B,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQK,GAAG,CAACJ,YAAY;QAC/Bb,OAAOY,QAAQ8B,iBAAiB,CAAC;IACnC;AACF"}