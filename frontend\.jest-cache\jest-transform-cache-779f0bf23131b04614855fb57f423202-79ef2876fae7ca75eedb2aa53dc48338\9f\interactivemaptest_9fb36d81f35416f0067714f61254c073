b9e0a187a1ae9708f533ca2d1fb3950d
"use strict";
// Mock react-leaflet components (already mocked in jest.setup.js)
// Mock framer-motion
jest.mock("framer-motion", ()=>({
        motion: {
            div: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    ...props,
                    children: children
                })
        },
        AnimatePresence: ({ children })=>children
    }));
// Mock the animated map components
jest.mock("../animated-map-components", ()=>({
        AnimatedUAVMarker: ({ uav, onSelect })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `uav-marker-${uav.id}`,
                onClick: ()=>onSelect(uav),
                children: [
                    "UAV Marker: ",
                    uav.rfidTag
                ]
            }),
        AnimatedGeofence: ({ region })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `geofence-${region.id}`,
                children: [
                    "Geofence: ",
                    region.name
                ]
            }),
        AnimatedFlightPath: ({ path })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "flight-path",
                children: "Flight Path"
            }),
        AnimatedDockingStation: ({ station, onSelect })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `docking-station-${station.id}`,
                onClick: ()=>onSelect(station),
                children: [
                    "Docking Station: ",
                    station.name
                ]
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../../lib/test-utils");
const _interactivemap = require("../interactive-map");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
describe("InteractiveMap Component", ()=>{
    const mockUAVs = [
        (0, _testutils.createMockUAV)({
            id: 1,
            rfidTag: "UAV-001",
            status: "AUTHORIZED",
            operationalStatus: "ACTIVE",
            location: {
                latitude: 40.7128,
                longitude: -74.0060
            }
        }),
        (0, _testutils.createMockUAV)({
            id: 2,
            rfidTag: "UAV-002",
            status: "AUTHORIZED",
            operationalStatus: "READY",
            location: {
                latitude: 40.7589,
                longitude: -73.9851
            }
        })
    ];
    const mockDockingStations = [
        (0, _testutils.createMockDockingStation)({
            id: 1,
            name: "Station Alpha",
            location: {
                latitude: 40.7505,
                longitude: -73.9934
            },
            status: "AVAILABLE"
        })
    ];
    const mockRegions = [
        {
            id: 1,
            name: "Zone A",
            description: "Authorized zone A",
            coordinates: [
                {
                    latitude: 40.7000,
                    longitude: -74.0200
                },
                {
                    latitude: 40.7200,
                    longitude: -74.0200
                },
                {
                    latitude: 40.7200,
                    longitude: -73.9800
                },
                {
                    latitude: 40.7000,
                    longitude: -73.9800
                }
            ],
            isActive: true
        }
    ];
    const defaultProps = {
        uavs: mockUAVs,
        dockingStations: mockDockingStations,
        regions: mockRegions,
        center: {
            latitude: 40.7128,
            longitude: -74.0060
        },
        zoom: 12,
        onUAVSelect: jest.fn(),
        onStationSelect: jest.fn(),
        onMapClick: jest.fn()
    };
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("tile-layer")).toBeInTheDocument();
    });
    it("renders UAV markers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("uav-marker-2")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Marker: UAV-001")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Marker: UAV-002")).toBeInTheDocument();
    });
    it("renders docking station markers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("docking-station-1")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Docking Station: Station Alpha")).toBeInTheDocument();
    });
    it("renders geofences for regions", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("geofence-1")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Geofence: Zone A")).toBeInTheDocument();
    });
    it("handles UAV selection", ()=>{
        const onUAVSelect = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            onUAVSelect: onUAVSelect
        }));
        const uavMarker = _testutils.screen.getByTestId("uav-marker-1");
        _testutils.fireEvent.click(uavMarker);
        expect(onUAVSelect).toHaveBeenCalledWith(mockUAVs[0]);
    });
    it("handles docking station selection", ()=>{
        const onStationSelect = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            onStationSelect: onStationSelect
        }));
        const stationMarker = _testutils.screen.getByTestId("docking-station-1");
        _testutils.fireEvent.click(stationMarker);
        expect(onStationSelect).toHaveBeenCalledWith(mockDockingStations[0]);
    });
    it("highlights selected UAV", ()=>{
        const selectedUAV = mockUAVs[0];
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            selectedUAV: selectedUAV
        }));
        const selectedMarker = _testutils.screen.getByTestId("uav-marker-1");
        expect(selectedMarker).toBeInTheDocument();
    // The selected state would be passed to the AnimatedUAVMarker component
    });
    it("shows flight paths when enabled", ()=>{
        const flightPaths = [
            {
                id: 1,
                uavId: 1,
                coordinates: [
                    {
                        latitude: 40.7128,
                        longitude: -74.0060
                    },
                    {
                        latitude: 40.7589,
                        longitude: -73.9851
                    }
                ],
                timestamp: new Date().toISOString()
            }
        ];
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            flightPaths: flightPaths,
            showFlightPaths: true
        }));
        expect(_testutils.screen.getByTestId("flight-path")).toBeInTheDocument();
    });
    it("filters UAVs by status", ()=>{
        const filteredProps = {
            ...defaultProps,
            uavs: mockUAVs.filter((uav)=>uav.operationalStatus === "ACTIVE")
        };
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...filteredProps
        }));
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-2")).not.toBeInTheDocument();
    });
    it("updates map center when prop changes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps
        }));
        const newCenter = {
            latitude: 41.8781,
            longitude: -87.6298
        };
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            center: newCenter
        }));
        // Map center update would be handled by the MapContainer component
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    });
    it("updates zoom level when prop changes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            zoom: 15
        }));
        // Zoom update would be handled by the MapContainer component
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    });
    it("handles empty UAV list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            uavs: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-1")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-2")).not.toBeInTheDocument();
    });
    it("handles empty docking stations list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            dockingStations: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("docking-station-1")).not.toBeInTheDocument();
    });
    it("handles empty regions list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            regions: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("geofence-1")).not.toBeInTheDocument();
    });
    it("shows loading state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            loading: true
        }));
        expect(_testutils.screen.getByTestId("map-loading")).toBeInTheDocument();
    });
    it("shows error state", ()=>{
        const errorMessage = "Failed to load map data";
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            error: errorMessage
        }));
        expect(_testutils.screen.getByText(errorMessage)).toBeInTheDocument();
        expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
    });
    it("supports different map layers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            mapLayer: "satellite"
        }));
        // Different tile layer would be rendered
        expect(_testutils.screen.getByTestId("tile-layer")).toBeInTheDocument();
    });
    it("handles real-time updates", async ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps
        }));
        const updatedUAVs = [
            ...mockUAVs,
            (0, _testutils.createMockUAV)({
                id: 3,
                rfidTag: "UAV-003",
                location: {
                    latitude: 40.7300,
                    longitude: -74.0000
                }
            })
        ];
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            uavs: updatedUAVs
        }));
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByTestId("uav-marker-3")).toBeInTheDocument();
        });
    });
    it("handles UAV location updates", ()=>{
        const updatedUAVs = mockUAVs.map((uav)=>uav.id === 1 ? {
                ...uav,
                location: {
                    latitude: 40.7200,
                    longitude: -74.0100
                }
            } : uav);
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            uavs: updatedUAVs
        }));
        // Updated location would be reflected in the marker position
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
    });
    it("supports clustering for many UAVs", ()=>{
        const manyUAVs = Array.from({
            length: 50
        }, (_, i)=>(0, _testutils.createMockUAV)({
                id: i + 1,
                rfidTag: `UAV-${(i + 1).toString().padStart(3, "0")}`,
                location: {
                    latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
                    longitude: -74.0060 + (Math.random() - 0.5) * 0.1
                }
            }));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            uavs: manyUAVs,
            enableClustering: true
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    // Clustering would be handled by the map library
    });
    it("handles map interaction events", ()=>{
        const onMapClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            onMapClick: onMapClick
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        _testutils.fireEvent.click(mapContainer);
        // Map click would be handled by the MapContainer component
        expect(mapContainer).toBeInTheDocument();
    });
    it("supports custom map controls", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            showZoomControl: true,
            showScaleControl: true,
            showFullscreenControl: true
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    // Custom controls would be rendered as part of the map
    });
    it("handles responsive design", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            className: "h-96 w-full"
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        expect(mapContainer.parentElement).toHaveClass("h-96", "w-full");
    });
    it("supports accessibility features", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.InteractiveMap, {
            ...defaultProps,
            "aria-label": "UAV tracking map",
            role: "application"
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        expect(mapContainer).toHaveAttribute("aria-label", "UAV tracking map");
        expect(mapContainer).toHaveAttribute("role", "application");
    });
});

//# sourceMappingURL=data:application/json;base64,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