677c6bf8edf4edcba0ba74e896beb8ac
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    HibernatePodApi: function() {
        return HibernatePodApi;
    },
    RegionApi: function() {
        return RegionApi;
    },
    UAVApi: function() {
        return UAVApi;
    },
    // Export default as UAV API for convenience
    default: function() {
        return _default;
    },
    hibernatePodApi: function() {
        return hibernatePodApi;
    },
    regionApi: function() {
        return regionApi;
    },
    uavApi: function() {
        return uavApi;
    }
});
const _apiclient = /*#__PURE__*/ _interop_require_wildcard(require("../lib/api-client"));
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
class UAVApi {
    // Get all UAVs with optional filtering and pagination
    async getUAVs(filter, pagination) {
        const params = {
            ...filter,
            ...pagination
        };
        const queryString = (0, _apiclient.createQueryString)(params);
        const url = queryString ? `${this.basePath}/all?${queryString}` : `${this.basePath}/all`;
        return _apiclient.default.get(url);
    }
    // Get UAV by ID
    async getUAVById(id) {
        return _apiclient.default.get(`${this.basePath}/${id}`);
    }
    // Create new UAV
    async createUAV(uav) {
        return _apiclient.default.post(`${this.basePath}`, uav);
    }
    // Update existing UAV
    async updateUAV(id, uav) {
        return _apiclient.default.put(`${this.basePath}/${id}`, uav);
    }
    // Delete UAV
    async deleteUAV(id) {
        return _apiclient.default.delete(`${this.basePath}/${id}`);
    }
    // Update UAV status
    async updateUAVStatus(id) {
        return _apiclient.default.put(`${this.basePath}/${id}/status`);
    }
    // Validate RFID tag uniqueness
    async validateRfidTag(rfidTag, excludeId) {
        const params = excludeId ? {
            excludeId
        } : {};
        const queryString = (0, _apiclient.createQueryString)(params);
        const url = queryString ? `${this.basePath}/validate-rfid/${encodeURIComponent(rfidTag)}?${queryString}` : `${this.basePath}/validate-rfid/${encodeURIComponent(rfidTag)}`;
        return _apiclient.default.get(url);
    }
    // Add region to UAV
    async addRegionToUAV(uavId, regionId) {
        return _apiclient.default.post(`${this.basePath}/${uavId}/regions/${regionId}`);
    }
    // Remove region from UAV
    async removeRegionFromUAV(uavId, regionId) {
        return _apiclient.default.delete(`${this.basePath}/${uavId}/regions/${regionId}`);
    }
    // Get available regions for UAV (not already assigned)
    async getAvailableRegionsForUAV(uavId) {
        return _apiclient.default.get(`${this.basePath}/${uavId}/available-regions`);
    }
    // Get system statistics
    async getSystemStatistics() {
        return _apiclient.default.get(`${this.basePath}/statistics`);
    }
    // Search UAVs
    async searchUAVs(query) {
        const params = {
            search: query
        };
        const queryString = (0, _apiclient.createQueryString)(params);
        return _apiclient.default.get(`${this.basePath}/search?${queryString}`);
    }
    // Get UAVs by status
    async getUAVsByStatus(status) {
        const params = {
            status
        };
        const queryString = (0, _apiclient.createQueryString)(params);
        return _apiclient.default.get(`${this.basePath}/all?${queryString}`);
    }
    // Get UAVs by region
    async getUAVsByRegion(regionId) {
        const params = {
            regionId
        };
        const queryString = (0, _apiclient.createQueryString)(params);
        return _apiclient.default.get(`${this.basePath}/all?${queryString}`);
    }
    // Bulk operations
    async bulkUpdateStatus(uavIds, status) {
        return _apiclient.default.post(`${this.basePath}/bulk/status`, {
            uavIds,
            status
        });
    }
    async bulkDelete(uavIds) {
        return _apiclient.default.post(`${this.basePath}/bulk/delete`, {
            uavIds
        });
    }
    // Export UAVs data
    async exportUAVs(format = "csv") {
        const response = await _apiclient.default.get(`${this.basePath}/export?format=${format}`, {
            responseType: "blob"
        });
        return response;
    }
    // Import UAVs data
    async importUAVs(file, onProgress) {
        return _apiclient.default.uploadFile(`${this.basePath}/import`, file, onProgress);
    }
    constructor(){
        this.basePath = "/api/uav";
    }
}
class HibernatePodApi {
    // Get hibernate pod status
    async getStatus() {
        return _apiclient.default.get(`${this.basePath}/status`);
    }
    // Add UAV to hibernate pod
    async addUAV(uavId) {
        return _apiclient.default.post(`${this.basePath}/add`, {
            uavId
        });
    }
    // Remove UAV from hibernate pod
    async removeUAV(uavId) {
        return _apiclient.default.post(`${this.basePath}/remove`, {
            uavId
        });
    }
    // Get UAVs in hibernate pod
    async getUAVsInPod() {
        return _apiclient.default.get(`${this.basePath}/uavs`);
    }
    // Update hibernate pod capacity
    async updateCapacity(maxCapacity) {
        return _apiclient.default.put(`${this.basePath}/capacity`, {
            maxCapacity
        });
    }
    constructor(){
        this.basePath = "/api/hibernate-pod";
    }
}
class RegionApi {
    // Get all regions
    async getRegions() {
        return _apiclient.default.get(this.basePath);
    }
    // Get region by ID
    async getRegionById(id) {
        return _apiclient.default.get(`${this.basePath}/${id}`);
    }
    // Create new region
    async createRegion(region) {
        return _apiclient.default.post(this.basePath, region);
    }
    // Update region
    async updateRegion(id, region) {
        return _apiclient.default.put(`${this.basePath}/${id}`, region);
    }
    // Delete region
    async deleteRegion(id) {
        return _apiclient.default.delete(`${this.basePath}/${id}`);
    }
    // Get UAVs in region
    async getUAVsInRegion(regionId) {
        return _apiclient.default.get(`${this.basePath}/${regionId}/uavs`);
    }
    constructor(){
        this.basePath = "/api/regions";
    }
}
const uavApi = new UAVApi();
const hibernatePodApi = new HibernatePodApi();
const regionApi = new RegionApi();
const _default = uavApi;

//# sourceMappingURL=data:application/json;base64,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