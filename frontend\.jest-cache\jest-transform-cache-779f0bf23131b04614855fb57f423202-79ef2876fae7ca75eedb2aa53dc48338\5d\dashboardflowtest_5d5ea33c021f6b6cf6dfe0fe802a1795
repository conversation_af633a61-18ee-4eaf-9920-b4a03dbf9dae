b9a37f68414e94b21f1b4ba77bc7760c
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../lib/test-utils");
const _dashboardstore = require("../../stores/dashboard-store");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock WebSocket for real-time updates
const mockWebSocket = {
    send: jest.fn(),
    close: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    readyState: WebSocket.OPEN
};
// Mock global WebSocket
global.WebSocket = jest.fn(()=>mockWebSocket);
// Integration test component for dashboard
const DashboardTestComponent = ()=>{
    const { metrics, alerts, recentLocationUpdates, isConnected, connectionError, showAlerts, autoRefresh, selectedTimeRange, fetchDashboardData, addAlert, acknowledgeAlert, removeAlert, clearAlerts, addLocationUpdate, setConnectionStatus, setTimeRange, setAutoRefresh, setShowAlerts, updateLastUpdate } = (0, _dashboardstore.useDashboardStore)();
    const [wsConnected, setWsConnected] = _react.default.useState(false);
    _react.default.useEffect(()=>{
        // Simulate initial data fetch
        fetchDashboardData();
        // Simulate WebSocket connection
        const ws = new WebSocket("ws://localhost:8080/ws");
        ws.addEventListener("open", ()=>{
            setWsConnected(true);
            setConnectionStatus(true);
        });
        ws.addEventListener("message", (event)=>{
            const data = JSON.parse(event.data);
            if (data.type === "alert") {
                addAlert(data.payload);
            } else if (data.type === "location_update") {
                addLocationUpdate(data.payload);
            }
        });
        ws.addEventListener("error", ()=>{
            setConnectionStatus(false, "WebSocket connection failed");
        });
        return ()=>{
            ws.close();
            setConnectionStatus(false);
        };
    }, []);
    const handleTimeRangeChange = (range)=>{
        setTimeRange(range);
        fetchDashboardData();
    };
    const handleToggleAutoRefresh = ()=>{
        setAutoRefresh(!autoRefresh);
    };
    const handleToggleAlerts = ()=>{
        setShowAlerts(!showAlerts);
    };
    const handleAcknowledgeAlert = (alertId)=>{
        acknowledgeAlert(alertId);
    };
    const handleRemoveAlert = (alertId)=>{
        removeAlert(alertId);
    };
    const handleClearAllAlerts = ()=>{
        clearAlerts();
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                children: "Dashboard"
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": "connection-status",
                children: [
                    "Status: ",
                    isConnected ? "Connected" : "Disconnected",
                    connectionError && /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                        children: [
                            " - ",
                            connectionError
                        ]
                    }),
                    wsConnected && /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                        children: " (WebSocket Active)"
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": "dashboard-controls",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("select", {
                        value: selectedTimeRange,
                        onChange: (e)=>handleTimeRangeChange(e.target.value),
                        "data-testid": "time-range-select",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                value: "1h",
                                children: "Last Hour"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                value: "24h",
                                children: "Last 24 Hours"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                value: "7d",
                                children: "Last 7 Days"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                value: "30d",
                                children: "Last 30 Days"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                        onClick: handleToggleAutoRefresh,
                        "data-testid": "auto-refresh-toggle",
                        children: [
                            "Auto Refresh: ",
                            autoRefresh ? "ON" : "OFF"
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                        onClick: handleToggleAlerts,
                        "data-testid": "alerts-toggle",
                        children: [
                            "Alerts: ",
                            showAlerts ? "VISIBLE" : "HIDDEN"
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        onClick: ()=>fetchDashboardData(),
                        "data-testid": "refresh-button",
                        children: "Refresh Data"
                    })
                ]
            }),
            metrics && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": "dashboard-metrics",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                        children: "System Metrics"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            "Total UAVs: ",
                            metrics.totalUAVs
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            "Authorized: ",
                            metrics.authorizedUAVs
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            "Unauthorized: ",
                            metrics.unauthorizedUAVs
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            "Active Flights: ",
                            metrics.activeFlights
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            "Hibernating: ",
                            metrics.hibernatingUAVs
                        ]
                    })
                ]
            }),
            showAlerts && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": "alerts-section",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("h2", {
                        children: [
                            "Alerts (",
                            alerts.length,
                            ")"
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            onClick: handleClearAllAlerts,
                            "data-testid": "clear-all-alerts",
                            children: "Clear All"
                        })
                    }),
                    alerts.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                        children: "No alerts"
                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        "data-testid": "alerts-list",
                        children: alerts.map((alert)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                "data-testid": `alert-${alert.id}`,
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                        children: alert.title
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                        children: alert.message
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                        children: [
                                            "Type: ",
                                            alert.type
                                        ]
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                        children: [
                                            "Severity: ",
                                            alert.severity
                                        ]
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                        children: [
                                            "Acknowledged: ",
                                            alert.acknowledged ? "Yes" : "No"
                                        ]
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        children: [
                                            !alert.acknowledged && /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                                onClick: ()=>handleAcknowledgeAlert(alert.id),
                                                "data-testid": `acknowledge-${alert.id}`,
                                                children: "Acknowledge"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                                onClick: ()=>handleRemoveAlert(alert.id),
                                                "data-testid": `remove-${alert.id}`,
                                                children: "Remove"
                                            })
                                        ]
                                    })
                                ]
                            }, alert.id))
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": "location-updates",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("h2", {
                        children: [
                            "Recent Location Updates (",
                            recentLocationUpdates.length,
                            ")"
                        ]
                    }),
                    recentLocationUpdates.slice(0, 5).map((update, index)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            "data-testid": `location-update-${index}`,
                            children: [
                                "UAV ",
                                update.rfidTag,
                                ": ",
                                update.latitude,
                                ", ",
                                update.longitude
                            ]
                        }, index))
                ]
            })
        ]
    });
};
describe("Dashboard Flow Integration Tests", ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
        // Reset dashboard store
        _dashboardstore.useDashboardStore.setState({
            metrics: null,
            flightActivity: null,
            batteryStats: null,
            hibernatePodMetrics: null,
            chartData: null,
            alerts: [],
            recentLocationUpdates: [],
            isConnected: false,
            lastUpdate: null,
            connectionError: null,
            selectedTimeRange: "24h",
            autoRefresh: true,
            refreshInterval: 30,
            showAlerts: true
        });
        // Mock fetch for dashboard data
        global.fetch = jest.fn().mockResolvedValue({
            ok: true,
            json: jest.fn().mockResolvedValue({
                metrics: {
                    totalUAVs: 10,
                    authorizedUAVs: 8,
                    unauthorizedUAVs: 2,
                    activeFlights: 3,
                    hibernatingUAVs: 2,
                    lowBatteryCount: 1,
                    chargingCount: 2,
                    maintenanceCount: 1,
                    emergencyCount: 0
                }
            })
        });
    });
    it("loads dashboard data on mount", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DashboardTestComponent, {}));
        // Should show initial state
        expect(_testutils.screen.getByText("Status: Disconnected")).toBeInTheDocument();
        // Wait for data to load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Total UAVs: 10")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Authorized: 8")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Active Flights: 3")).toBeInTheDocument();
        });
        // Should show WebSocket connection
        expect(_testutils.screen.getByText("Status: Connected (WebSocket Active)")).toBeInTheDocument();
    });
    it("handles time range changes", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DashboardTestComponent, {}));
        // Wait for initial load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByTestId("dashboard-metrics")).toBeInTheDocument();
        });
        // Change time range
        const timeRangeSelect = _testutils.screen.getByTestId("time-range-select");
        _testutils.fireEvent.change(timeRangeSelect, {
            target: {
                value: "7d"
            }
        });
        // Should update selected time range
        expect(timeRangeSelect).toHaveValue("7d");
        // Should trigger data refresh
        await (0, _testutils.waitFor)(()=>{
            expect(global.fetch).toHaveBeenCalledTimes(2) // Initial + after time range change
            ;
        });
    });
    it("toggles auto refresh", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DashboardTestComponent, {}));
        const autoRefreshToggle = _testutils.screen.getByTestId("auto-refresh-toggle");
        // Initially ON
        expect(_testutils.screen.getByText("Auto Refresh: ON")).toBeInTheDocument();
        // Toggle OFF
        _testutils.fireEvent.click(autoRefreshToggle);
        expect(_testutils.screen.getByText("Auto Refresh: OFF")).toBeInTheDocument();
        // Toggle back ON
        _testutils.fireEvent.click(autoRefreshToggle);
        expect(_testutils.screen.getByText("Auto Refresh: ON")).toBeInTheDocument();
    });
    it("toggles alerts visibility", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DashboardTestComponent, {}));
        const alertsToggle = _testutils.screen.getByTestId("alerts-toggle");
        // Initially visible
        expect(_testutils.screen.getByText("Alerts: VISIBLE")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("alerts-section")).toBeInTheDocument();
        // Toggle hidden
        _testutils.fireEvent.click(alertsToggle);
        expect(_testutils.screen.getByText("Alerts: HIDDEN")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("alerts-section")).not.toBeInTheDocument();
    });
    it("handles real-time alert updates", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DashboardTestComponent, {}));
        // Wait for WebSocket connection
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText(/WebSocket Active/)).toBeInTheDocument();
        });
        // Simulate receiving an alert via WebSocket
        const mockAlert = (0, _testutils.createMockAlert)({
            id: "1",
            title: "Low Battery",
            message: "UAV-001 battery is low",
            type: "WARNING",
            severity: "MEDIUM"
        });
        // Simulate WebSocket message
        const messageEvent = new MessageEvent("message", {
            data: JSON.stringify({
                type: "alert",
                payload: mockAlert
            })
        });
        // Trigger the message handler
        const { addAlert } = _dashboardstore.useDashboardStore.getState();
        addAlert(mockAlert);
        // Should display the new alert
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Alerts (1)")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Low Battery")).toBeInTheDocument();
            expect(_testutils.screen.getByText("UAV-001 battery is low")).toBeInTheDocument();
        });
    });
    it("handles alert acknowledgment", async ()=>{
        const mockAlert = (0, _testutils.createMockAlert)({
            id: "1",
            title: "Test Alert",
            acknowledged: false
        });
        // Add alert to store
        _dashboardstore.useDashboardStore.getState().addAlert(mockAlert);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DashboardTestComponent, {}));
        // Should show unacknowledged alert
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Acknowledged: No")).toBeInTheDocument();
            expect(_testutils.screen.getByTestId("acknowledge-1")).toBeInTheDocument();
        });
        // Acknowledge alert
        _testutils.fireEvent.click(_testutils.screen.getByTestId("acknowledge-1"));
        // Should update acknowledgment status
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Acknowledged: Yes")).toBeInTheDocument();
            expect(_testutils.screen.queryByTestId("acknowledge-1")).not.toBeInTheDocument();
        });
    });
    it("handles alert removal", async ()=>{
        const mockAlert = (0, _testutils.createMockAlert)({
            id: "1",
            title: "Test Alert"
        });
        // Add alert to store
        _dashboardstore.useDashboardStore.getState().addAlert(mockAlert);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DashboardTestComponent, {}));
        // Should show alert
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Alerts (1)")).toBeInTheDocument();
            expect(_testutils.screen.getByTestId("alert-1")).toBeInTheDocument();
        });
        // Remove alert
        _testutils.fireEvent.click(_testutils.screen.getByTestId("remove-1"));
        // Should remove alert
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Alerts (0)")).toBeInTheDocument();
            expect(_testutils.screen.queryByTestId("alert-1")).not.toBeInTheDocument();
            expect(_testutils.screen.getByText("No alerts")).toBeInTheDocument();
        });
    });
    it("clears all alerts", async ()=>{
        // Add multiple alerts
        const alerts = [
            (0, _testutils.createMockAlert)({
                id: "1",
                title: "Alert 1"
            }),
            (0, _testutils.createMockAlert)({
                id: "2",
                title: "Alert 2"
            }),
            (0, _testutils.createMockAlert)({
                id: "3",
                title: "Alert 3"
            })
        ];
        alerts.forEach((alert)=>{
            _dashboardstore.useDashboardStore.getState().addAlert(alert);
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DashboardTestComponent, {}));
        // Should show all alerts
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Alerts (3)")).toBeInTheDocument();
        });
        // Clear all alerts
        _testutils.fireEvent.click(_testutils.screen.getByTestId("clear-all-alerts"));
        // Should remove all alerts
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Alerts (0)")).toBeInTheDocument();
            expect(_testutils.screen.getByText("No alerts")).toBeInTheDocument();
        });
    });
    it("handles real-time location updates", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DashboardTestComponent, {}));
        // Wait for WebSocket connection
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText(/WebSocket Active/)).toBeInTheDocument();
        });
        // Simulate location update
        const locationUpdate = {
            uavId: 1,
            rfidTag: "UAV-001",
            latitude: 40.7128,
            longitude: -74.0060,
            altitude: 100,
            timestamp: new Date().toISOString(),
            speed: 25,
            heading: 180
        };
        // Add location update to store
        _dashboardstore.useDashboardStore.getState().addLocationUpdate(locationUpdate);
        // Should display location update
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Recent Location Updates (1)")).toBeInTheDocument();
            expect(_testutils.screen.getByText("UAV UAV-001: 40.7128, -74.0060")).toBeInTheDocument();
        });
    });
    it("handles connection errors", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DashboardTestComponent, {}));
        // Simulate connection error
        _dashboardstore.useDashboardStore.getState().setConnectionStatus(false, "Network timeout");
        // Should show error status
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Status: Disconnected - Network timeout")).toBeInTheDocument();
        });
    });
    it("refreshes data manually", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DashboardTestComponent, {}));
        // Wait for initial load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByTestId("dashboard-metrics")).toBeInTheDocument();
        });
        global.fetch.mockClear();
        // Click refresh
        _testutils.fireEvent.click(_testutils.screen.getByTestId("refresh-button"));
        // Should trigger data fetch
        await (0, _testutils.waitFor)(()=>{
            expect(global.fetch).toHaveBeenCalled();
        });
    });
});

//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFxEYUNodWFuZ0JhY2tlbmRcXGZyb250ZW5kXFxzcmNcXF9fdGVzdHNfX1xcaW50ZWdyYXRpb25cXGRhc2hib2FyZC1mbG93LnRlc3QudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHJlbmRlciwgc2NyZWVuLCBmaXJlRXZlbnQsIHdhaXRGb3IgfSBmcm9tICdAL2xpYi90ZXN0LXV0aWxzJ1xuaW1wb3J0IHsgdXNlRGFzaGJvYXJkU3RvcmUgfSBmcm9tICdAL3N0b3Jlcy9kYXNoYm9hcmQtc3RvcmUnXG5pbXBvcnQgeyBjcmVhdGVNb2NrQWxlcnQsIGNyZWF0ZU1vY2tVQVYgfSBmcm9tICdAL2xpYi90ZXN0LXV0aWxzJ1xuXG4vLyBNb2NrIFdlYlNvY2tldCBmb3IgcmVhbC10aW1lIHVwZGF0ZXNcbmNvbnN0IG1vY2tXZWJTb2NrZXQgPSB7XG4gIHNlbmQ6IGplc3QuZm4oKSxcbiAgY2xvc2U6IGplc3QuZm4oKSxcbiAgYWRkRXZlbnRMaXN0ZW5lcjogamVzdC5mbigpLFxuICByZW1vdmVFdmVudExpc3RlbmVyOiBqZXN0LmZuKCksXG4gIHJlYWR5U3RhdGU6IFdlYlNvY2tldC5PUEVOLFxufVxuXG4vLyBNb2NrIGdsb2JhbCBXZWJTb2NrZXRcbmdsb2JhbC5XZWJTb2NrZXQgPSBqZXN0LmZuKCgpID0+IG1vY2tXZWJTb2NrZXQpIGFzIGFueVxuXG4vLyBJbnRlZ3JhdGlvbiB0ZXN0IGNvbXBvbmVudCBmb3IgZGFzaGJvYXJkXG5jb25zdCBEYXNoYm9hcmRUZXN0Q29tcG9uZW50ID0gKCkgPT4ge1xuICBjb25zdCB7XG4gICAgbWV0cmljcyxcbiAgICBhbGVydHMsXG4gICAgcmVjZW50TG9jYXRpb25VcGRhdGVzLFxuICAgIGlzQ29ubmVjdGVkLFxuICAgIGNvbm5lY3Rpb25FcnJvcixcbiAgICBzaG93QWxlcnRzLFxuICAgIGF1dG9SZWZyZXNoLFxuICAgIHNlbGVjdGVkVGltZVJhbmdlLFxuICAgIGZldGNoRGFzaGJvYXJkRGF0YSxcbiAgICBhZGRBbGVydCxcbiAgICBhY2tub3dsZWRnZUFsZXJ0LFxuICAgIHJlbW92ZUFsZXJ0LFxuICAgIGNsZWFyQWxlcnRzLFxuICAgIGFkZExvY2F0aW9uVXBkYXRlLFxuICAgIHNldENvbm5lY3Rpb25TdGF0dXMsXG4gICAgc2V0VGltZVJhbmdlLFxuICAgIHNldEF1dG9SZWZyZXNoLFxuICAgIHNldFNob3dBbGVydHMsXG4gICAgdXBkYXRlTGFzdFVwZGF0ZSxcbiAgfSA9IHVzZURhc2hib2FyZFN0b3JlKClcblxuICBjb25zdCBbd3NDb25uZWN0ZWQsIHNldFdzQ29ubmVjdGVkXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKVxuXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gU2ltdWxhdGUgaW5pdGlhbCBkYXRhIGZldGNoXG4gICAgZmV0Y2hEYXNoYm9hcmREYXRhKClcbiAgICBcbiAgICAvLyBTaW11bGF0ZSBXZWJTb2NrZXQgY29ubmVjdGlvblxuICAgIGNvbnN0IHdzID0gbmV3IFdlYlNvY2tldCgnd3M6Ly9sb2NhbGhvc3Q6ODA4MC93cycpXG4gICAgXG4gICAgd3MuYWRkRXZlbnRMaXN0ZW5lcignb3BlbicsICgpID0+IHtcbiAgICAgIHNldFdzQ29ubmVjdGVkKHRydWUpXG4gICAgICBzZXRDb25uZWN0aW9uU3RhdHVzKHRydWUpXG4gICAgfSlcbiAgICBcbiAgICB3cy5hZGRFdmVudExpc3RlbmVyKCdtZXNzYWdlJywgKGV2ZW50KSA9PiB7XG4gICAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZShldmVudC5kYXRhKVxuICAgICAgXG4gICAgICBpZiAoZGF0YS50eXBlID09PSAnYWxlcnQnKSB7XG4gICAgICAgIGFkZEFsZXJ0KGRhdGEucGF5bG9hZClcbiAgICAgIH0gZWxzZSBpZiAoZGF0YS50eXBlID09PSAnbG9jYXRpb25fdXBkYXRlJykge1xuICAgICAgICBhZGRMb2NhdGlvblVwZGF0ZShkYXRhLnBheWxvYWQpXG4gICAgICB9XG4gICAgfSlcbiAgICBcbiAgICB3cy5hZGRFdmVudExpc3RlbmVyKCdlcnJvcicsICgpID0+IHtcbiAgICAgIHNldENvbm5lY3Rpb25TdGF0dXMoZmFsc2UsICdXZWJTb2NrZXQgY29ubmVjdGlvbiBmYWlsZWQnKVxuICAgIH0pXG4gICAgXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHdzLmNsb3NlKClcbiAgICAgIHNldENvbm5lY3Rpb25TdGF0dXMoZmFsc2UpXG4gICAgfVxuICB9LCBbXSlcblxuICBjb25zdCBoYW5kbGVUaW1lUmFuZ2VDaGFuZ2UgPSAocmFuZ2U6IHN0cmluZykgPT4ge1xuICAgIHNldFRpbWVSYW5nZShyYW5nZSlcbiAgICBmZXRjaERhc2hib2FyZERhdGEoKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlVG9nZ2xlQXV0b1JlZnJlc2ggPSAoKSA9PiB7XG4gICAgc2V0QXV0b1JlZnJlc2goIWF1dG9SZWZyZXNoKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlVG9nZ2xlQWxlcnRzID0gKCkgPT4ge1xuICAgIHNldFNob3dBbGVydHMoIXNob3dBbGVydHMpXG4gIH1cblxuICBjb25zdCBoYW5kbGVBY2tub3dsZWRnZUFsZXJ0ID0gKGFsZXJ0SWQ6IHN0cmluZykgPT4ge1xuICAgIGFja25vd2xlZGdlQWxlcnQoYWxlcnRJZClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVJlbW92ZUFsZXJ0ID0gKGFsZXJ0SWQ6IHN0cmluZykgPT4ge1xuICAgIHJlbW92ZUFsZXJ0KGFsZXJ0SWQpXG4gIH1cblxuICBjb25zdCBoYW5kbGVDbGVhckFsbEFsZXJ0cyA9ICgpID0+IHtcbiAgICBjbGVhckFsZXJ0cygpXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXY+XG4gICAgICA8aDE+RGFzaGJvYXJkPC9oMT5cbiAgICAgIFxuICAgICAgey8qIENvbm5lY3Rpb24gU3RhdHVzICovfVxuICAgICAgPGRpdiBkYXRhLXRlc3RpZD1cImNvbm5lY3Rpb24tc3RhdHVzXCI+XG4gICAgICAgIFN0YXR1czoge2lzQ29ubmVjdGVkID8gJ0Nvbm5lY3RlZCcgOiAnRGlzY29ubmVjdGVkJ31cbiAgICAgICAge2Nvbm5lY3Rpb25FcnJvciAmJiA8c3Bhbj4gLSB7Y29ubmVjdGlvbkVycm9yfTwvc3Bhbj59XG4gICAgICAgIHt3c0Nvbm5lY3RlZCAmJiA8c3Bhbj4gKFdlYlNvY2tldCBBY3RpdmUpPC9zcGFuPn1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQ29udHJvbHMgKi99XG4gICAgICA8ZGl2IGRhdGEtdGVzdGlkPVwiZGFzaGJvYXJkLWNvbnRyb2xzXCI+XG4gICAgICAgIDxzZWxlY3QgXG4gICAgICAgICAgdmFsdWU9e3NlbGVjdGVkVGltZVJhbmdlfSBcbiAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVRpbWVSYW5nZUNoYW5nZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgZGF0YS10ZXN0aWQ9XCJ0aW1lLXJhbmdlLXNlbGVjdFwiXG4gICAgICAgID5cbiAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiMWhcIj5MYXN0IEhvdXI8L29wdGlvbj5cbiAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiMjRoXCI+TGFzdCAyNCBIb3Vyczwvb3B0aW9uPlxuICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCI3ZFwiPkxhc3QgNyBEYXlzPC9vcHRpb24+XG4gICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjMwZFwiPkxhc3QgMzAgRGF5czwvb3B0aW9uPlxuICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgXG4gICAgICAgIDxidXR0b24gb25DbGljaz17aGFuZGxlVG9nZ2xlQXV0b1JlZnJlc2h9IGRhdGEtdGVzdGlkPVwiYXV0by1yZWZyZXNoLXRvZ2dsZVwiPlxuICAgICAgICAgIEF1dG8gUmVmcmVzaDoge2F1dG9SZWZyZXNoID8gJ09OJyA6ICdPRkYnfVxuICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgXG4gICAgICAgIDxidXR0b24gb25DbGljaz17aGFuZGxlVG9nZ2xlQWxlcnRzfSBkYXRhLXRlc3RpZD1cImFsZXJ0cy10b2dnbGVcIj5cbiAgICAgICAgICBBbGVydHM6IHtzaG93QWxlcnRzID8gJ1ZJU0lCTEUnIDogJ0hJRERFTid9XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgICBcbiAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBmZXRjaERhc2hib2FyZERhdGEoKX0gZGF0YS10ZXN0aWQ9XCJyZWZyZXNoLWJ1dHRvblwiPlxuICAgICAgICAgIFJlZnJlc2ggRGF0YVxuICAgICAgICA8L2J1dHRvbj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTWV0cmljcyAqL31cbiAgICAgIHttZXRyaWNzICYmIChcbiAgICAgICAgPGRpdiBkYXRhLXRlc3RpZD1cImRhc2hib2FyZC1tZXRyaWNzXCI+XG4gICAgICAgICAgPGgyPlN5c3RlbSBNZXRyaWNzPC9oMj5cbiAgICAgICAgICA8ZGl2PlRvdGFsIFVBVnM6IHttZXRyaWNzLnRvdGFsVUFWc308L2Rpdj5cbiAgICAgICAgICA8ZGl2PkF1dGhvcml6ZWQ6IHttZXRyaWNzLmF1dGhvcml6ZWRVQVZzfTwvZGl2PlxuICAgICAgICAgIDxkaXY+VW5hdXRob3JpemVkOiB7bWV0cmljcy51bmF1dGhvcml6ZWRVQVZzfTwvZGl2PlxuICAgICAgICAgIDxkaXY+QWN0aXZlIEZsaWdodHM6IHttZXRyaWNzLmFjdGl2ZUZsaWdodHN9PC9kaXY+XG4gICAgICAgICAgPGRpdj5IaWJlcm5hdGluZzoge21ldHJpY3MuaGliZXJuYXRpbmdVQVZzfTwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBBbGVydHMgKi99XG4gICAgICB7c2hvd0FsZXJ0cyAmJiAoXG4gICAgICAgIDxkaXYgZGF0YS10ZXN0aWQ9XCJhbGVydHMtc2VjdGlvblwiPlxuICAgICAgICAgIDxoMj5BbGVydHMgKHthbGVydHMubGVuZ3RofSk8L2gyPlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUNsZWFyQWxsQWxlcnRzfSBkYXRhLXRlc3RpZD1cImNsZWFyLWFsbC1hbGVydHNcIj5cbiAgICAgICAgICAgICAgQ2xlYXIgQWxsXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICB7YWxlcnRzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgIDxwPk5vIGFsZXJ0czwvcD5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPGRpdiBkYXRhLXRlc3RpZD1cImFsZXJ0cy1saXN0XCI+XG4gICAgICAgICAgICAgIHthbGVydHMubWFwKGFsZXJ0ID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17YWxlcnQuaWR9IGRhdGEtdGVzdGlkPXtgYWxlcnQtJHthbGVydC5pZH1gfT5cbiAgICAgICAgICAgICAgICAgIDxoND57YWxlcnQudGl0bGV9PC9oND5cbiAgICAgICAgICAgICAgICAgIDxwPnthbGVydC5tZXNzYWdlfTwvcD5cbiAgICAgICAgICAgICAgICAgIDxwPlR5cGU6IHthbGVydC50eXBlfTwvcD5cbiAgICAgICAgICAgICAgICAgIDxwPlNldmVyaXR5OiB7YWxlcnQuc2V2ZXJpdHl9PC9wPlxuICAgICAgICAgICAgICAgICAgPHA+QWNrbm93bGVkZ2VkOiB7YWxlcnQuYWNrbm93bGVkZ2VkID8gJ1llcycgOiAnTm8nfTwvcD5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgeyFhbGVydC5hY2tub3dsZWRnZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBY2tub3dsZWRnZUFsZXJ0KGFsZXJ0LmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEtdGVzdGlkPXtgYWNrbm93bGVkZ2UtJHthbGVydC5pZH1gfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIEFja25vd2xlZGdlXG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUmVtb3ZlQWxlcnQoYWxlcnQuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgIGRhdGEtdGVzdGlkPXtgcmVtb3ZlLSR7YWxlcnQuaWR9YH1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIFJlbW92ZVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIExvY2F0aW9uIFVwZGF0ZXMgKi99XG4gICAgICA8ZGl2IGRhdGEtdGVzdGlkPVwibG9jYXRpb24tdXBkYXRlc1wiPlxuICAgICAgICA8aDI+UmVjZW50IExvY2F0aW9uIFVwZGF0ZXMgKHtyZWNlbnRMb2NhdGlvblVwZGF0ZXMubGVuZ3RofSk8L2gyPlxuICAgICAgICB7cmVjZW50TG9jYXRpb25VcGRhdGVzLnNsaWNlKDAsIDUpLm1hcCgodXBkYXRlLCBpbmRleCkgPT4gKFxuICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gZGF0YS10ZXN0aWQ9e2Bsb2NhdGlvbi11cGRhdGUtJHtpbmRleH1gfT5cbiAgICAgICAgICAgIFVBViB7dXBkYXRlLnJmaWRUYWd9OiB7dXBkYXRlLmxhdGl0dWRlfSwge3VwZGF0ZS5sb25naXR1ZGV9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZGVzY3JpYmUoJ0Rhc2hib2FyZCBGbG93IEludGVncmF0aW9uIFRlc3RzJywgKCkgPT4ge1xuICBiZWZvcmVFYWNoKCgpID0+IHtcbiAgICBqZXN0LmNsZWFyQWxsTW9ja3MoKVxuICAgIFxuICAgIC8vIFJlc2V0IGRhc2hib2FyZCBzdG9yZVxuICAgIHVzZURhc2hib2FyZFN0b3JlLnNldFN0YXRlKHtcbiAgICAgIG1ldHJpY3M6IG51bGwsXG4gICAgICBmbGlnaHRBY3Rpdml0eTogbnVsbCxcbiAgICAgIGJhdHRlcnlTdGF0czogbnVsbCxcbiAgICAgIGhpYmVybmF0ZVBvZE1ldHJpY3M6IG51bGwsXG4gICAgICBjaGFydERhdGE6IG51bGwsXG4gICAgICBhbGVydHM6IFtdLFxuICAgICAgcmVjZW50TG9jYXRpb25VcGRhdGVzOiBbXSxcbiAgICAgIGlzQ29ubmVjdGVkOiBmYWxzZSxcbiAgICAgIGxhc3RVcGRhdGU6IG51bGwsXG4gICAgICBjb25uZWN0aW9uRXJyb3I6IG51bGwsXG4gICAgICBzZWxlY3RlZFRpbWVSYW5nZTogJzI0aCcsXG4gICAgICBhdXRvUmVmcmVzaDogdHJ1ZSxcbiAgICAgIHJlZnJlc2hJbnRlcnZhbDogMzAsXG4gICAgICBzaG93QWxlcnRzOiB0cnVlLFxuICAgIH0pXG5cbiAgICAvLyBNb2NrIGZldGNoIGZvciBkYXNoYm9hcmQgZGF0YVxuICAgIGdsb2JhbC5mZXRjaCA9IGplc3QuZm4oKS5tb2NrUmVzb2x2ZWRWYWx1ZSh7XG4gICAgICBvazogdHJ1ZSxcbiAgICAgIGpzb246IGplc3QuZm4oKS5tb2NrUmVzb2x2ZWRWYWx1ZSh7XG4gICAgICAgIG1ldHJpY3M6IHtcbiAgICAgICAgICB0b3RhbFVBVnM6IDEwLFxuICAgICAgICAgIGF1dGhvcml6ZWRVQVZzOiA4LFxuICAgICAgICAgIHVuYXV0aG9yaXplZFVBVnM6IDIsXG4gICAgICAgICAgYWN0aXZlRmxpZ2h0czogMyxcbiAgICAgICAgICBoaWJlcm5hdGluZ1VBVnM6IDIsXG4gICAgICAgICAgbG93QmF0dGVyeUNvdW50OiAxLFxuICAgICAgICAgIGNoYXJnaW5nQ291bnQ6IDIsXG4gICAgICAgICAgbWFpbnRlbmFuY2VDb3VudDogMSxcbiAgICAgICAgICBlbWVyZ2VuY3lDb3VudDogMCxcbiAgICAgICAgfSxcbiAgICAgIH0pLFxuICAgIH0pXG4gIH0pXG5cbiAgaXQoJ2xvYWRzIGRhc2hib2FyZCBkYXRhIG9uIG1vdW50JywgYXN5bmMgKCkgPT4ge1xuICAgIHJlbmRlcig8RGFzaGJvYXJkVGVzdENvbXBvbmVudCAvPilcblxuICAgIC8vIFNob3VsZCBzaG93IGluaXRpYWwgc3RhdGVcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnU3RhdHVzOiBEaXNjb25uZWN0ZWQnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuXG4gICAgLy8gV2FpdCBmb3IgZGF0YSB0byBsb2FkXG4gICAgYXdhaXQgd2FpdEZvcigoKSA9PiB7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnVG90YWwgVUFWczogMTAnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0F1dGhvcml6ZWQ6IDgnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0FjdGl2ZSBGbGlnaHRzOiAzJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICB9KVxuXG4gICAgLy8gU2hvdWxkIHNob3cgV2ViU29ja2V0IGNvbm5lY3Rpb25cbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnU3RhdHVzOiBDb25uZWN0ZWQgKFdlYlNvY2tldCBBY3RpdmUpJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcblxuICBpdCgnaGFuZGxlcyB0aW1lIHJhbmdlIGNoYW5nZXMnLCBhc3luYyAoKSA9PiB7XG4gICAgcmVuZGVyKDxEYXNoYm9hcmRUZXN0Q29tcG9uZW50IC8+KVxuXG4gICAgLy8gV2FpdCBmb3IgaW5pdGlhbCBsb2FkXG4gICAgYXdhaXQgd2FpdEZvcigoKSA9PiB7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdkYXNoYm9hcmQtbWV0cmljcycpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgfSlcblxuICAgIC8vIENoYW5nZSB0aW1lIHJhbmdlXG4gICAgY29uc3QgdGltZVJhbmdlU2VsZWN0ID0gc2NyZWVuLmdldEJ5VGVzdElkKCd0aW1lLXJhbmdlLXNlbGVjdCcpXG4gICAgZmlyZUV2ZW50LmNoYW5nZSh0aW1lUmFuZ2VTZWxlY3QsIHsgdGFyZ2V0OiB7IHZhbHVlOiAnN2QnIH0gfSlcblxuICAgIC8vIFNob3VsZCB1cGRhdGUgc2VsZWN0ZWQgdGltZSByYW5nZVxuICAgIGV4cGVjdCh0aW1lUmFuZ2VTZWxlY3QpLnRvSGF2ZVZhbHVlKCc3ZCcpXG5cbiAgICAvLyBTaG91bGQgdHJpZ2dlciBkYXRhIHJlZnJlc2hcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChnbG9iYWwuZmV0Y2gpLnRvSGF2ZUJlZW5DYWxsZWRUaW1lcygyKSAvLyBJbml0aWFsICsgYWZ0ZXIgdGltZSByYW5nZSBjaGFuZ2VcbiAgICB9KVxuICB9KVxuXG4gIGl0KCd0b2dnbGVzIGF1dG8gcmVmcmVzaCcsICgpID0+IHtcbiAgICByZW5kZXIoPERhc2hib2FyZFRlc3RDb21wb25lbnQgLz4pXG5cbiAgICBjb25zdCBhdXRvUmVmcmVzaFRvZ2dsZSA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnYXV0by1yZWZyZXNoLXRvZ2dsZScpXG4gICAgXG4gICAgLy8gSW5pdGlhbGx5IE9OXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0F1dG8gUmVmcmVzaDogT04nKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuXG4gICAgLy8gVG9nZ2xlIE9GRlxuICAgIGZpcmVFdmVudC5jbGljayhhdXRvUmVmcmVzaFRvZ2dsZSlcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnQXV0byBSZWZyZXNoOiBPRkYnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuXG4gICAgLy8gVG9nZ2xlIGJhY2sgT05cbiAgICBmaXJlRXZlbnQuY2xpY2soYXV0b1JlZnJlc2hUb2dnbGUpXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0F1dG8gUmVmcmVzaDogT04nKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCd0b2dnbGVzIGFsZXJ0cyB2aXNpYmlsaXR5JywgKCkgPT4ge1xuICAgIHJlbmRlcig8RGFzaGJvYXJkVGVzdENvbXBvbmVudCAvPilcblxuICAgIGNvbnN0IGFsZXJ0c1RvZ2dsZSA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnYWxlcnRzLXRvZ2dsZScpXG4gICAgXG4gICAgLy8gSW5pdGlhbGx5IHZpc2libGVcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnQWxlcnRzOiBWSVNJQkxFJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdhbGVydHMtc2VjdGlvbicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG5cbiAgICAvLyBUb2dnbGUgaGlkZGVuXG4gICAgZmlyZUV2ZW50LmNsaWNrKGFsZXJ0c1RvZ2dsZSlcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnQWxlcnRzOiBISURERU4nKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIGV4cGVjdChzY3JlZW4ucXVlcnlCeVRlc3RJZCgnYWxlcnRzLXNlY3Rpb24nKSkubm90LnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcblxuICBpdCgnaGFuZGxlcyByZWFsLXRpbWUgYWxlcnQgdXBkYXRlcycsIGFzeW5jICgpID0+IHtcbiAgICByZW5kZXIoPERhc2hib2FyZFRlc3RDb21wb25lbnQgLz4pXG5cbiAgICAvLyBXYWl0IGZvciBXZWJTb2NrZXQgY29ubmVjdGlvblxuICAgIGF3YWl0IHdhaXRGb3IoKCkgPT4ge1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoL1dlYlNvY2tldCBBY3RpdmUvKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG5cbiAgICAvLyBTaW11bGF0ZSByZWNlaXZpbmcgYW4gYWxlcnQgdmlhIFdlYlNvY2tldFxuICAgIGNvbnN0IG1vY2tBbGVydCA9IGNyZWF0ZU1vY2tBbGVydCh7XG4gICAgICBpZDogJzEnLFxuICAgICAgdGl0bGU6ICdMb3cgQmF0dGVyeScsXG4gICAgICBtZXNzYWdlOiAnVUFWLTAwMSBiYXR0ZXJ5IGlzIGxvdycsXG4gICAgICB0eXBlOiAnV0FSTklORycsXG4gICAgICBzZXZlcml0eTogJ01FRElVTScsXG4gICAgfSlcblxuICAgIC8vIFNpbXVsYXRlIFdlYlNvY2tldCBtZXNzYWdlXG4gICAgY29uc3QgbWVzc2FnZUV2ZW50ID0gbmV3IE1lc3NhZ2VFdmVudCgnbWVzc2FnZScsIHtcbiAgICAgIGRhdGE6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgdHlwZTogJ2FsZXJ0JyxcbiAgICAgICAgcGF5bG9hZDogbW9ja0FsZXJ0LFxuICAgICAgfSksXG4gICAgfSlcblxuICAgIC8vIFRyaWdnZXIgdGhlIG1lc3NhZ2UgaGFuZGxlclxuICAgIGNvbnN0IHsgYWRkQWxlcnQgfSA9IHVzZURhc2hib2FyZFN0b3JlLmdldFN0YXRlKClcbiAgICBhZGRBbGVydChtb2NrQWxlcnQpXG5cbiAgICAvLyBTaG91bGQgZGlzcGxheSB0aGUgbmV3IGFsZXJ0XG4gICAgYXdhaXQgd2FpdEZvcigoKSA9PiB7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnQWxlcnRzICgxKScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnTG93IEJhdHRlcnknKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ1VBVi0wMDEgYmF0dGVyeSBpcyBsb3cnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG4gIH0pXG5cbiAgaXQoJ2hhbmRsZXMgYWxlcnQgYWNrbm93bGVkZ21lbnQnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgbW9ja0FsZXJ0ID0gY3JlYXRlTW9ja0FsZXJ0KHtcbiAgICAgIGlkOiAnMScsXG4gICAgICB0aXRsZTogJ1Rlc3QgQWxlcnQnLFxuICAgICAgYWNrbm93bGVkZ2VkOiBmYWxzZSxcbiAgICB9KVxuXG4gICAgLy8gQWRkIGFsZXJ0IHRvIHN0b3JlXG4gICAgdXNlRGFzaGJvYXJkU3RvcmUuZ2V0U3RhdGUoKS5hZGRBbGVydChtb2NrQWxlcnQpXG5cbiAgICByZW5kZXIoPERhc2hib2FyZFRlc3RDb21wb25lbnQgLz4pXG5cbiAgICAvLyBTaG91bGQgc2hvdyB1bmFja25vd2xlZGdlZCBhbGVydFxuICAgIGF3YWl0IHdhaXRGb3IoKCkgPT4ge1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0Fja25vd2xlZGdlZDogTm8nKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnYWNrbm93bGVkZ2UtMScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgfSlcblxuICAgIC8vIEFja25vd2xlZGdlIGFsZXJ0XG4gICAgZmlyZUV2ZW50LmNsaWNrKHNjcmVlbi5nZXRCeVRlc3RJZCgnYWNrbm93bGVkZ2UtMScpKVxuXG4gICAgLy8gU2hvdWxkIHVwZGF0ZSBhY2tub3dsZWRnbWVudCBzdGF0dXNcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdBY2tub3dsZWRnZWQ6IFllcycpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgICBleHBlY3Qoc2NyZWVuLnF1ZXJ5QnlUZXN0SWQoJ2Fja25vd2xlZGdlLTEnKSkubm90LnRvQmVJblRoZURvY3VtZW50KClcbiAgICB9KVxuICB9KVxuXG4gIGl0KCdoYW5kbGVzIGFsZXJ0IHJlbW92YWwnLCBhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgbW9ja0FsZXJ0ID0gY3JlYXRlTW9ja0FsZXJ0KHtcbiAgICAgIGlkOiAnMScsXG4gICAgICB0aXRsZTogJ1Rlc3QgQWxlcnQnLFxuICAgIH0pXG5cbiAgICAvLyBBZGQgYWxlcnQgdG8gc3RvcmVcbiAgICB1c2VEYXNoYm9hcmRTdG9yZS5nZXRTdGF0ZSgpLmFkZEFsZXJ0KG1vY2tBbGVydClcblxuICAgIHJlbmRlcig8RGFzaGJvYXJkVGVzdENvbXBvbmVudCAvPilcblxuICAgIC8vIFNob3VsZCBzaG93IGFsZXJ0XG4gICAgYXdhaXQgd2FpdEZvcigoKSA9PiB7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnQWxlcnRzICgxKScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdhbGVydC0xJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICB9KVxuXG4gICAgLy8gUmVtb3ZlIGFsZXJ0XG4gICAgZmlyZUV2ZW50LmNsaWNrKHNjcmVlbi5nZXRCeVRlc3RJZCgncmVtb3ZlLTEnKSlcblxuICAgIC8vIFNob3VsZCByZW1vdmUgYWxlcnRcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdBbGVydHMgKDApJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICAgIGV4cGVjdChzY3JlZW4ucXVlcnlCeVRlc3RJZCgnYWxlcnQtMScpKS5ub3QudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ05vIGFsZXJ0cycpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgfSlcbiAgfSlcblxuICBpdCgnY2xlYXJzIGFsbCBhbGVydHMnLCBhc3luYyAoKSA9PiB7XG4gICAgLy8gQWRkIG11bHRpcGxlIGFsZXJ0c1xuICAgIGNvbnN0IGFsZXJ0cyA9IFtcbiAgICAgIGNyZWF0ZU1vY2tBbGVydCh7IGlkOiAnMScsIHRpdGxlOiAnQWxlcnQgMScgfSksXG4gICAgICBjcmVhdGVNb2NrQWxlcnQoeyBpZDogJzInLCB0aXRsZTogJ0FsZXJ0IDInIH0pLFxuICAgICAgY3JlYXRlTW9ja0FsZXJ0KHsgaWQ6ICczJywgdGl0bGU6ICdBbGVydCAzJyB9KSxcbiAgICBdXG5cbiAgICBhbGVydHMuZm9yRWFjaChhbGVydCA9PiB7XG4gICAgICB1c2VEYXNoYm9hcmRTdG9yZS5nZXRTdGF0ZSgpLmFkZEFsZXJ0KGFsZXJ0KVxuICAgIH0pXG5cbiAgICByZW5kZXIoPERhc2hib2FyZFRlc3RDb21wb25lbnQgLz4pXG5cbiAgICAvLyBTaG91bGQgc2hvdyBhbGwgYWxlcnRzXG4gICAgYXdhaXQgd2FpdEZvcigoKSA9PiB7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnQWxlcnRzICgzKScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgfSlcblxuICAgIC8vIENsZWFyIGFsbCBhbGVydHNcbiAgICBmaXJlRXZlbnQuY2xpY2soc2NyZWVuLmdldEJ5VGVzdElkKCdjbGVhci1hbGwtYWxlcnRzJykpXG5cbiAgICAvLyBTaG91bGQgcmVtb3ZlIGFsbCBhbGVydHNcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdBbGVydHMgKDApJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdObyBhbGVydHMnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG4gIH0pXG5cbiAgaXQoJ2hhbmRsZXMgcmVhbC10aW1lIGxvY2F0aW9uIHVwZGF0ZXMnLCBhc3luYyAoKSA9PiB7XG4gICAgcmVuZGVyKDxEYXNoYm9hcmRUZXN0Q29tcG9uZW50IC8+KVxuXG4gICAgLy8gV2FpdCBmb3IgV2ViU29ja2V0IGNvbm5lY3Rpb25cbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KC9XZWJTb2NrZXQgQWN0aXZlLykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICB9KVxuXG4gICAgLy8gU2ltdWxhdGUgbG9jYXRpb24gdXBkYXRlXG4gICAgY29uc3QgbG9jYXRpb25VcGRhdGUgPSB7XG4gICAgICB1YXZJZDogMSxcbiAgICAgIHJmaWRUYWc6ICdVQVYtMDAxJyxcbiAgICAgIGxhdGl0dWRlOiA0MC43MTI4LFxuICAgICAgbG9uZ2l0dWRlOiAtNzQuMDA2MCxcbiAgICAgIGFsdGl0dWRlOiAxMDAsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIHNwZWVkOiAyNSxcbiAgICAgIGhlYWRpbmc6IDE4MCxcbiAgICB9XG5cbiAgICAvLyBBZGQgbG9jYXRpb24gdXBkYXRlIHRvIHN0b3JlXG4gICAgdXNlRGFzaGJvYXJkU3RvcmUuZ2V0U3RhdGUoKS5hZGRMb2NhdGlvblVwZGF0ZShsb2NhdGlvblVwZGF0ZSlcblxuICAgIC8vIFNob3VsZCBkaXNwbGF5IGxvY2F0aW9uIHVwZGF0ZVxuICAgIGF3YWl0IHdhaXRGb3IoKCkgPT4ge1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ1JlY2VudCBMb2NhdGlvbiBVcGRhdGVzICgxKScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnVUFWIFVBVi0wMDE6IDQwLjcxMjgsIC03NC4wMDYwJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICB9KVxuICB9KVxuXG4gIGl0KCdoYW5kbGVzIGNvbm5lY3Rpb24gZXJyb3JzJywgYXN5bmMgKCkgPT4ge1xuICAgIHJlbmRlcig8RGFzaGJvYXJkVGVzdENvbXBvbmVudCAvPilcblxuICAgIC8vIFNpbXVsYXRlIGNvbm5lY3Rpb24gZXJyb3JcbiAgICB1c2VEYXNoYm9hcmRTdG9yZS5nZXRTdGF0ZSgpLnNldENvbm5lY3Rpb25TdGF0dXMoZmFsc2UsICdOZXR3b3JrIHRpbWVvdXQnKVxuXG4gICAgLy8gU2hvdWxkIHNob3cgZXJyb3Igc3RhdHVzXG4gICAgYXdhaXQgd2FpdEZvcigoKSA9PiB7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnU3RhdHVzOiBEaXNjb25uZWN0ZWQgLSBOZXR3b3JrIHRpbWVvdXQnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG4gIH0pXG5cbiAgaXQoJ3JlZnJlc2hlcyBkYXRhIG1hbnVhbGx5JywgYXN5bmMgKCkgPT4ge1xuICAgIHJlbmRlcig8RGFzaGJvYXJkVGVzdENvbXBvbmVudCAvPilcblxuICAgIC8vIFdhaXQgZm9yIGluaXRpYWwgbG9hZFxuICAgIGF3YWl0IHdhaXRGb3IoKCkgPT4ge1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnZGFzaGJvYXJkLW1ldHJpY3MnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG5cbiAgICAvLyBDbGVhciBwcmV2aW91cyBmZXRjaCBjYWxsc1xuICAgIDsoZ2xvYmFsLmZldGNoIGFzIGplc3QuTW9jaykubW9ja0NsZWFyKClcblxuICAgIC8vIENsaWNrIHJlZnJlc2hcbiAgICBmaXJlRXZlbnQuY2xpY2soc2NyZWVuLmdldEJ5VGVzdElkKCdyZWZyZXNoLWJ1dHRvbicpKVxuXG4gICAgLy8gU2hvdWxkIHRyaWdnZXIgZGF0YSBmZXRjaFxuICAgIGF3YWl0IHdhaXRGb3IoKCkgPT4ge1xuICAgICAgZXhwZWN0KGdsb2JhbC5mZXRjaCkudG9IYXZlQmVlbkNhbGxlZCgpXG4gICAgfSlcbiAgfSlcbn0pXG4iXSwibmFtZXMiOlsibW9ja1dlYlNvY2tldCIsInNlbmQiLCJqZXN0IiwiZm4iLCJjbG9zZSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwicmVhZHlTdGF0ZSIsIldlYlNvY2tldCIsIk9QRU4iLCJnbG9iYWwiLCJEYXNoYm9hcmRUZXN0Q29tcG9uZW50IiwibWV0cmljcyIsImFsZXJ0cyIsInJlY2VudExvY2F0aW9uVXBkYXRlcyIsImlzQ29ubmVjdGVkIiwiY29ubmVjdGlvbkVycm9yIiwic2hvd0FsZXJ0cyIsImF1dG9SZWZyZXNoIiwic2VsZWN0ZWRUaW1lUmFuZ2UiLCJmZXRjaERhc2hib2FyZERhdGEiLCJhZGRBbGVydCIsImFja25vd2xlZGdlQWxlcnQiLCJyZW1vdmVBbGVydCIsImNsZWFyQWxlcnRzIiwiYWRkTG9jYXRpb25VcGRhdGUiLCJzZXRDb25uZWN0aW9uU3RhdHVzIiwic2V0VGltZVJhbmdlIiwic2V0QXV0b1JlZnJlc2giLCJzZXRTaG93QWxlcnRzIiwidXBkYXRlTGFzdFVwZGF0ZSIsInVzZURhc2hib2FyZFN0b3JlIiwid3NDb25uZWN0ZWQiLCJzZXRXc0Nvbm5lY3RlZCIsIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ3cyIsImV2ZW50IiwiZGF0YSIsIkpTT04iLCJwYXJzZSIsInR5cGUiLCJwYXlsb2FkIiwiaGFuZGxlVGltZVJhbmdlQ2hhbmdlIiwicmFuZ2UiLCJoYW5kbGVUb2dnbGVBdXRvUmVmcmVzaCIsImhhbmRsZVRvZ2dsZUFsZXJ0cyIsImhhbmRsZUFja25vd2xlZGdlQWxlcnQiLCJhbGVydElkIiwiaGFuZGxlUmVtb3ZlQWxlcnQiLCJoYW5kbGVDbGVhckFsbEFsZXJ0cyIsImRpdiIsImgxIiwiZGF0YS10ZXN0aWQiLCJzcGFuIiwic2VsZWN0IiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvcHRpb24iLCJidXR0b24iLCJvbkNsaWNrIiwiaDIiLCJ0b3RhbFVBVnMiLCJhdXRob3JpemVkVUFWcyIsInVuYXV0aG9yaXplZFVBVnMiLCJhY3RpdmVGbGlnaHRzIiwiaGliZXJuYXRpbmdVQVZzIiwibGVuZ3RoIiwicCIsIm1hcCIsImFsZXJ0IiwiaWQiLCJoNCIsInRpdGxlIiwibWVzc2FnZSIsInNldmVyaXR5IiwiYWNrbm93bGVkZ2VkIiwic2xpY2UiLCJ1cGRhdGUiLCJpbmRleCIsInJmaWRUYWciLCJsYXRpdHVkZSIsImxvbmdpdHVkZSIsImRlc2NyaWJlIiwiYmVmb3JlRWFjaCIsImNsZWFyQWxsTW9ja3MiLCJzZXRTdGF0ZSIsImZsaWdodEFjdGl2aXR5IiwiYmF0dGVyeVN0YXRzIiwiaGliZXJuYXRlUG9kTWV0cmljcyIsImNoYXJ0RGF0YSIsImxhc3RVcGRhdGUiLCJyZWZyZXNoSW50ZXJ2YWwiLCJmZXRjaCIsIm1vY2tSZXNvbHZlZFZhbHVlIiwib2siLCJqc29uIiwibG93QmF0dGVyeUNvdW50IiwiY2hhcmdpbmdDb3VudCIsIm1haW50ZW5hbmNlQ291bnQiLCJlbWVyZ2VuY3lDb3VudCIsIml0IiwicmVuZGVyIiwiZXhwZWN0Iiwic2NyZWVuIiwiZ2V0QnlUZXh0IiwidG9CZUluVGhlRG9jdW1lbnQiLCJ3YWl0Rm9yIiwiZ2V0QnlUZXN0SWQiLCJ0aW1lUmFuZ2VTZWxlY3QiLCJmaXJlRXZlbnQiLCJjaGFuZ2UiLCJ0b0hhdmVWYWx1ZSIsInRvSGF2ZUJlZW5DYWxsZWRUaW1lcyIsImF1dG9SZWZyZXNoVG9nZ2xlIiwiY2xpY2siLCJhbGVydHNUb2dnbGUiLCJxdWVyeUJ5VGVzdElkIiwibm90IiwibW9ja0FsZXJ0IiwiY3JlYXRlTW9ja0FsZXJ0IiwibWVzc2FnZUV2ZW50IiwiTWVzc2FnZUV2ZW50Iiwic3RyaW5naWZ5IiwiZ2V0U3RhdGUiLCJmb3JFYWNoIiwibG9jYXRpb25VcGRhdGUiLCJ1YXZJZCIsImFsdGl0dWRlIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BlZWQiLCJoZWFkaW5nIiwibW9ja0NsZWFyIiwidG9IYXZlQmVlbkNhbGxlZCJdLCJtYXBwaW5ncyI6Ijs7Ozs7OERBQWtCOzJCQUNpQztnQ0FDakI7Ozs7OztBQUdsQyx1Q0FBdUM7QUFDdkMsTUFBTUEsZ0JBQWdCO0lBQ3BCQyxNQUFNQyxLQUFLQyxFQUFFO0lBQ2JDLE9BQU9GLEtBQUtDLEVBQUU7SUFDZEUsa0JBQWtCSCxLQUFLQyxFQUFFO0lBQ3pCRyxxQkFBcUJKLEtBQUtDLEVBQUU7SUFDNUJJLFlBQVlDLFVBQVVDLElBQUk7QUFDNUI7QUFFQSx3QkFBd0I7QUFDeEJDLE9BQU9GLFNBQVMsR0FBR04sS0FBS0MsRUFBRSxDQUFDLElBQU1IO0FBRWpDLDJDQUEyQztBQUMzQyxNQUFNVyx5QkFBeUI7SUFDN0IsTUFBTSxFQUNKQyxPQUFPLEVBQ1BDLE1BQU0sRUFDTkMscUJBQXFCLEVBQ3JCQyxXQUFXLEVBQ1hDLGVBQWUsRUFDZkMsVUFBVSxFQUNWQyxXQUFXLEVBQ1hDLGlCQUFpQixFQUNqQkMsa0JBQWtCLEVBQ2xCQyxRQUFRLEVBQ1JDLGdCQUFnQixFQUNoQkMsV0FBVyxFQUNYQyxXQUFXLEVBQ1hDLGlCQUFpQixFQUNqQkMsbUJBQW1CLEVBQ25CQyxZQUFZLEVBQ1pDLGNBQWMsRUFDZEMsYUFBYSxFQUNiQyxnQkFBZ0IsRUFDakIsR0FBR0MsSUFBQUEsaUNBQWlCO0lBRXJCLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHQyxjQUFLLENBQUNDLFFBQVEsQ0FBQztJQUVyREQsY0FBSyxDQUFDRSxTQUFTLENBQUM7UUFDZCw4QkFBOEI7UUFDOUJoQjtRQUVBLGdDQUFnQztRQUNoQyxNQUFNaUIsS0FBSyxJQUFJN0IsVUFBVTtRQUV6QjZCLEdBQUdoQyxnQkFBZ0IsQ0FBQyxRQUFRO1lBQzFCNEIsZUFBZTtZQUNmUCxvQkFBb0I7UUFDdEI7UUFFQVcsR0FBR2hDLGdCQUFnQixDQUFDLFdBQVcsQ0FBQ2lDO1lBQzlCLE1BQU1DLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ0gsTUFBTUMsSUFBSTtZQUVsQyxJQUFJQSxLQUFLRyxJQUFJLEtBQUssU0FBUztnQkFDekJyQixTQUFTa0IsS0FBS0ksT0FBTztZQUN2QixPQUFPLElBQUlKLEtBQUtHLElBQUksS0FBSyxtQkFBbUI7Z0JBQzFDakIsa0JBQWtCYyxLQUFLSSxPQUFPO1lBQ2hDO1FBQ0Y7UUFFQU4sR0FBR2hDLGdCQUFnQixDQUFDLFNBQVM7WUFDM0JxQixvQkFBb0IsT0FBTztRQUM3QjtRQUVBLE9BQU87WUFDTFcsR0FBR2pDLEtBQUs7WUFDUnNCLG9CQUFvQjtRQUN0QjtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1rQix3QkFBd0IsQ0FBQ0M7UUFDN0JsQixhQUFha0I7UUFDYnpCO0lBQ0Y7SUFFQSxNQUFNMEIsMEJBQTBCO1FBQzlCbEIsZUFBZSxDQUFDVjtJQUNsQjtJQUVBLE1BQU02QixxQkFBcUI7UUFDekJsQixjQUFjLENBQUNaO0lBQ2pCO0lBRUEsTUFBTStCLHlCQUF5QixDQUFDQztRQUM5QjNCLGlCQUFpQjJCO0lBQ25CO0lBRUEsTUFBTUMsb0JBQW9CLENBQUNEO1FBQ3pCMUIsWUFBWTBCO0lBQ2Q7SUFFQSxNQUFNRSx1QkFBdUI7UUFDM0IzQjtJQUNGO0lBRUEscUJBQ0Usc0JBQUM0Qjs7MEJBQ0MscUJBQUNDOzBCQUFHOzswQkFHSixzQkFBQ0Q7Z0JBQUlFLGVBQVk7O29CQUFvQjtvQkFDMUJ2QyxjQUFjLGNBQWM7b0JBQ3BDQyxpQ0FBbUIsc0JBQUN1Qzs7NEJBQUs7NEJBQUl2Qzs7O29CQUM3QmdCLDZCQUFlLHFCQUFDdUI7a0NBQUs7Ozs7MEJBSXhCLHNCQUFDSDtnQkFBSUUsZUFBWTs7a0NBQ2Ysc0JBQUNFO3dCQUNDQyxPQUFPdEM7d0JBQ1B1QyxVQUFVLENBQUNDLElBQU1mLHNCQUFzQmUsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dCQUNyREgsZUFBWTs7MENBRVoscUJBQUNPO2dDQUFPSixPQUFNOzBDQUFLOzswQ0FDbkIscUJBQUNJO2dDQUFPSixPQUFNOzBDQUFNOzswQ0FDcEIscUJBQUNJO2dDQUFPSixPQUFNOzBDQUFLOzswQ0FDbkIscUJBQUNJO2dDQUFPSixPQUFNOzBDQUFNOzs7O2tDQUd0QixzQkFBQ0s7d0JBQU9DLFNBQVNqQjt3QkFBeUJRLGVBQVk7OzRCQUFzQjs0QkFDM0RwQyxjQUFjLE9BQU87OztrQ0FHdEMsc0JBQUM0Qzt3QkFBT0MsU0FBU2hCO3dCQUFvQk8sZUFBWTs7NEJBQWdCOzRCQUN0RHJDLGFBQWEsWUFBWTs7O2tDQUdwQyxxQkFBQzZDO3dCQUFPQyxTQUFTLElBQU0zQzt3QkFBc0JrQyxlQUFZO2tDQUFpQjs7OztZQU0zRTFDLHlCQUNDLHNCQUFDd0M7Z0JBQUlFLGVBQVk7O2tDQUNmLHFCQUFDVTtrQ0FBRzs7a0NBQ0osc0JBQUNaOzs0QkFBSTs0QkFBYXhDLFFBQVFxRCxTQUFTOzs7a0NBQ25DLHNCQUFDYjs7NEJBQUk7NEJBQWF4QyxRQUFRc0QsY0FBYzs7O2tDQUN4QyxzQkFBQ2Q7OzRCQUFJOzRCQUFleEMsUUFBUXVELGdCQUFnQjs7O2tDQUM1QyxzQkFBQ2Y7OzRCQUFJOzRCQUFpQnhDLFFBQVF3RCxhQUFhOzs7a0NBQzNDLHNCQUFDaEI7OzRCQUFJOzRCQUFjeEMsUUFBUXlELGVBQWU7Ozs7O1lBSzdDcEQsNEJBQ0Msc0JBQUNtQztnQkFBSUUsZUFBWTs7a0NBQ2Ysc0JBQUNVOzs0QkFBRzs0QkFBU25ELE9BQU95RCxNQUFNOzRCQUFDOzs7a0NBRTNCLHFCQUFDbEI7a0NBQ0MsY0FBQSxxQkFBQ1U7NEJBQU9DLFNBQVNaOzRCQUFzQkcsZUFBWTtzQ0FBbUI7OztvQkFLdkV6QyxPQUFPeUQsTUFBTSxLQUFLLGtCQUNqQixxQkFBQ0M7a0NBQUU7dUNBRUgscUJBQUNuQjt3QkFBSUUsZUFBWTtrQ0FDZHpDLE9BQU8yRCxHQUFHLENBQUNDLENBQUFBLHNCQUNWLHNCQUFDckI7Z0NBQW1CRSxlQUFhLENBQUMsTUFBTSxFQUFFbUIsTUFBTUMsRUFBRSxDQUFDLENBQUM7O2tEQUNsRCxxQkFBQ0M7a0RBQUlGLE1BQU1HLEtBQUs7O2tEQUNoQixxQkFBQ0w7a0RBQUdFLE1BQU1JLE9BQU87O2tEQUNqQixzQkFBQ047OzRDQUFFOzRDQUFPRSxNQUFNL0IsSUFBSTs7O2tEQUNwQixzQkFBQzZCOzs0Q0FBRTs0Q0FBV0UsTUFBTUssUUFBUTs7O2tEQUM1QixzQkFBQ1A7OzRDQUFFOzRDQUFlRSxNQUFNTSxZQUFZLEdBQUcsUUFBUTs7O2tEQUUvQyxzQkFBQzNCOzs0Q0FDRSxDQUFDcUIsTUFBTU0sWUFBWSxrQkFDbEIscUJBQUNqQjtnREFDQ0MsU0FBUyxJQUFNZix1QkFBdUJ5QixNQUFNQyxFQUFFO2dEQUM5Q3BCLGVBQWEsQ0FBQyxZQUFZLEVBQUVtQixNQUFNQyxFQUFFLENBQUMsQ0FBQzswREFDdkM7OzBEQUlILHFCQUFDWjtnREFDQ0MsU0FBUyxJQUFNYixrQkFBa0J1QixNQUFNQyxFQUFFO2dEQUN6Q3BCLGVBQWEsQ0FBQyxPQUFPLEVBQUVtQixNQUFNQyxFQUFFLENBQUMsQ0FBQzswREFDbEM7Ozs7OytCQW5CS0QsTUFBTUMsRUFBRTs7OzswQkErQjVCLHNCQUFDdEI7Z0JBQUlFLGVBQVk7O2tDQUNmLHNCQUFDVTs7NEJBQUc7NEJBQTBCbEQsc0JBQXNCd0QsTUFBTTs0QkFBQzs7O29CQUMxRHhELHNCQUFzQmtFLEtBQUssQ0FBQyxHQUFHLEdBQUdSLEdBQUcsQ0FBQyxDQUFDUyxRQUFRQyxzQkFDOUMsc0JBQUM5Qjs0QkFBZ0JFLGVBQWEsQ0FBQyxnQkFBZ0IsRUFBRTRCLE1BQU0sQ0FBQzs7Z0NBQUU7Z0NBQ25ERCxPQUFPRSxPQUFPO2dDQUFDO2dDQUFHRixPQUFPRyxRQUFRO2dDQUFDO2dDQUFHSCxPQUFPSSxTQUFTOzsyQkFEbERIOzs7OztBQU9wQjtBQUVBSSxTQUFTLG9DQUFvQztJQUMzQ0MsV0FBVztRQUNUckYsS0FBS3NGLGFBQWE7UUFFbEIsd0JBQXdCO1FBQ3hCekQsaUNBQWlCLENBQUMwRCxRQUFRLENBQUM7WUFDekI3RSxTQUFTO1lBQ1Q4RSxnQkFBZ0I7WUFDaEJDLGNBQWM7WUFDZEMscUJBQXFCO1lBQ3JCQyxXQUFXO1lBQ1hoRixRQUFRLEVBQUU7WUFDVkMsdUJBQXVCLEVBQUU7WUFDekJDLGFBQWE7WUFDYitFLFlBQVk7WUFDWjlFLGlCQUFpQjtZQUNqQkcsbUJBQW1CO1lBQ25CRCxhQUFhO1lBQ2I2RSxpQkFBaUI7WUFDakI5RSxZQUFZO1FBQ2Q7UUFFQSxnQ0FBZ0M7UUFDaENQLE9BQU9zRixLQUFLLEdBQUc5RixLQUFLQyxFQUFFLEdBQUc4RixpQkFBaUIsQ0FBQztZQUN6Q0MsSUFBSTtZQUNKQyxNQUFNakcsS0FBS0MsRUFBRSxHQUFHOEYsaUJBQWlCLENBQUM7Z0JBQ2hDckYsU0FBUztvQkFDUHFELFdBQVc7b0JBQ1hDLGdCQUFnQjtvQkFDaEJDLGtCQUFrQjtvQkFDbEJDLGVBQWU7b0JBQ2ZDLGlCQUFpQjtvQkFDakIrQixpQkFBaUI7b0JBQ2pCQyxlQUFlO29CQUNmQyxrQkFBa0I7b0JBQ2xCQyxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7UUFDRjtJQUNGO0lBRUFDLEdBQUcsaUNBQWlDO1FBQ2xDQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQzlGO1FBRVIsNEJBQTRCO1FBQzVCK0YsT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLHlCQUF5QkMsaUJBQWlCO1FBRWxFLHdCQUF3QjtRQUN4QixNQUFNQyxJQUFBQSxrQkFBTyxFQUFDO1lBQ1pKLE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyxtQkFBbUJDLGlCQUFpQjtZQUM1REgsT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLGtCQUFrQkMsaUJBQWlCO1lBQzNESCxPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsc0JBQXNCQyxpQkFBaUI7UUFDakU7UUFFQSxtQ0FBbUM7UUFDbkNILE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyx5Q0FBeUNDLGlCQUFpQjtJQUNwRjtJQUVBTCxHQUFHLDhCQUE4QjtRQUMvQkMsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUM5RjtRQUVSLHdCQUF3QjtRQUN4QixNQUFNbUcsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDSSxXQUFXLENBQUMsc0JBQXNCRixpQkFBaUI7UUFDbkU7UUFFQSxvQkFBb0I7UUFDcEIsTUFBTUcsa0JBQWtCTCxpQkFBTSxDQUFDSSxXQUFXLENBQUM7UUFDM0NFLG9CQUFTLENBQUNDLE1BQU0sQ0FBQ0YsaUJBQWlCO1lBQUVwRCxRQUFRO2dCQUFFSCxPQUFPO1lBQUs7UUFBRTtRQUU1RCxvQ0FBb0M7UUFDcENpRCxPQUFPTSxpQkFBaUJHLFdBQVcsQ0FBQztRQUVwQyw4QkFBOEI7UUFDOUIsTUFBTUwsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPaEcsT0FBT3NGLEtBQUssRUFBRW9CLHFCQUFxQixDQUFDLEdBQUcsb0NBQW9DOztRQUNwRjtJQUNGO0lBRUFaLEdBQUcsd0JBQXdCO1FBQ3pCQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQzlGO1FBRVIsTUFBTTBHLG9CQUFvQlYsaUJBQU0sQ0FBQ0ksV0FBVyxDQUFDO1FBRTdDLGVBQWU7UUFDZkwsT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLHFCQUFxQkMsaUJBQWlCO1FBRTlELGFBQWE7UUFDYkksb0JBQVMsQ0FBQ0ssS0FBSyxDQUFDRDtRQUNoQlgsT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLHNCQUFzQkMsaUJBQWlCO1FBRS9ELGlCQUFpQjtRQUNqQkksb0JBQVMsQ0FBQ0ssS0FBSyxDQUFDRDtRQUNoQlgsT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLHFCQUFxQkMsaUJBQWlCO0lBQ2hFO0lBRUFMLEdBQUcsNkJBQTZCO1FBQzlCQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQzlGO1FBRVIsTUFBTTRHLGVBQWVaLGlCQUFNLENBQUNJLFdBQVcsQ0FBQztRQUV4QyxvQkFBb0I7UUFDcEJMLE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyxvQkFBb0JDLGlCQUFpQjtRQUM3REgsT0FBT0MsaUJBQU0sQ0FBQ0ksV0FBVyxDQUFDLG1CQUFtQkYsaUJBQWlCO1FBRTlELGdCQUFnQjtRQUNoQkksb0JBQVMsQ0FBQ0ssS0FBSyxDQUFDQztRQUNoQmIsT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLG1CQUFtQkMsaUJBQWlCO1FBQzVESCxPQUFPQyxpQkFBTSxDQUFDYSxhQUFhLENBQUMsbUJBQW1CQyxHQUFHLENBQUNaLGlCQUFpQjtJQUN0RTtJQUVBTCxHQUFHLG1DQUFtQztRQUNwQ0MsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUM5RjtRQUVSLGdDQUFnQztRQUNoQyxNQUFNbUcsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMscUJBQXFCQyxpQkFBaUI7UUFDaEU7UUFFQSw0Q0FBNEM7UUFDNUMsTUFBTWEsWUFBWUMsSUFBQUEsMEJBQWUsRUFBQztZQUNoQ2pELElBQUk7WUFDSkUsT0FBTztZQUNQQyxTQUFTO1lBQ1RuQyxNQUFNO1lBQ05vQyxVQUFVO1FBQ1o7UUFFQSw2QkFBNkI7UUFDN0IsTUFBTThDLGVBQWUsSUFBSUMsYUFBYSxXQUFXO1lBQy9DdEYsTUFBTUMsS0FBS3NGLFNBQVMsQ0FBQztnQkFDbkJwRixNQUFNO2dCQUNOQyxTQUFTK0U7WUFDWDtRQUNGO1FBRUEsOEJBQThCO1FBQzlCLE1BQU0sRUFBRXJHLFFBQVEsRUFBRSxHQUFHVSxpQ0FBaUIsQ0FBQ2dHLFFBQVE7UUFDL0MxRyxTQUFTcUc7UUFFVCwrQkFBK0I7UUFDL0IsTUFBTVosSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsZUFBZUMsaUJBQWlCO1lBQ3hESCxPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsZ0JBQWdCQyxpQkFBaUI7WUFDekRILE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQywyQkFBMkJDLGlCQUFpQjtRQUN0RTtJQUNGO0lBRUFMLEdBQUcsZ0NBQWdDO1FBQ2pDLE1BQU1rQixZQUFZQyxJQUFBQSwwQkFBZSxFQUFDO1lBQ2hDakQsSUFBSTtZQUNKRSxPQUFPO1lBQ1BHLGNBQWM7UUFDaEI7UUFFQSxxQkFBcUI7UUFDckJoRCxpQ0FBaUIsQ0FBQ2dHLFFBQVEsR0FBRzFHLFFBQVEsQ0FBQ3FHO1FBRXRDakIsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUM5RjtRQUVSLG1DQUFtQztRQUNuQyxNQUFNbUcsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMscUJBQXFCQyxpQkFBaUI7WUFDOURILE9BQU9DLGlCQUFNLENBQUNJLFdBQVcsQ0FBQyxrQkFBa0JGLGlCQUFpQjtRQUMvRDtRQUVBLG9CQUFvQjtRQUNwQkksb0JBQVMsQ0FBQ0ssS0FBSyxDQUFDWCxpQkFBTSxDQUFDSSxXQUFXLENBQUM7UUFFbkMsc0NBQXNDO1FBQ3RDLE1BQU1ELElBQUFBLGtCQUFPLEVBQUM7WUFDWkosT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLHNCQUFzQkMsaUJBQWlCO1lBQy9ESCxPQUFPQyxpQkFBTSxDQUFDYSxhQUFhLENBQUMsa0JBQWtCQyxHQUFHLENBQUNaLGlCQUFpQjtRQUNyRTtJQUNGO0lBRUFMLEdBQUcseUJBQXlCO1FBQzFCLE1BQU1rQixZQUFZQyxJQUFBQSwwQkFBZSxFQUFDO1lBQ2hDakQsSUFBSTtZQUNKRSxPQUFPO1FBQ1Q7UUFFQSxxQkFBcUI7UUFDckI3QyxpQ0FBaUIsQ0FBQ2dHLFFBQVEsR0FBRzFHLFFBQVEsQ0FBQ3FHO1FBRXRDakIsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUM5RjtRQUVSLG9CQUFvQjtRQUNwQixNQUFNbUcsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsZUFBZUMsaUJBQWlCO1lBQ3hESCxPQUFPQyxpQkFBTSxDQUFDSSxXQUFXLENBQUMsWUFBWUYsaUJBQWlCO1FBQ3pEO1FBRUEsZUFBZTtRQUNmSSxvQkFBUyxDQUFDSyxLQUFLLENBQUNYLGlCQUFNLENBQUNJLFdBQVcsQ0FBQztRQUVuQyxzQkFBc0I7UUFDdEIsTUFBTUQsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsZUFBZUMsaUJBQWlCO1lBQ3hESCxPQUFPQyxpQkFBTSxDQUFDYSxhQUFhLENBQUMsWUFBWUMsR0FBRyxDQUFDWixpQkFBaUI7WUFDN0RILE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyxjQUFjQyxpQkFBaUI7UUFDekQ7SUFDRjtJQUVBTCxHQUFHLHFCQUFxQjtRQUN0QixzQkFBc0I7UUFDdEIsTUFBTTNGLFNBQVM7WUFDYjhHLElBQUFBLDBCQUFlLEVBQUM7Z0JBQUVqRCxJQUFJO2dCQUFLRSxPQUFPO1lBQVU7WUFDNUMrQyxJQUFBQSwwQkFBZSxFQUFDO2dCQUFFakQsSUFBSTtnQkFBS0UsT0FBTztZQUFVO1lBQzVDK0MsSUFBQUEsMEJBQWUsRUFBQztnQkFBRWpELElBQUk7Z0JBQUtFLE9BQU87WUFBVTtTQUM3QztRQUVEL0QsT0FBT21ILE9BQU8sQ0FBQ3ZELENBQUFBO1lBQ2IxQyxpQ0FBaUIsQ0FBQ2dHLFFBQVEsR0FBRzFHLFFBQVEsQ0FBQ29EO1FBQ3hDO1FBRUFnQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQzlGO1FBRVIseUJBQXlCO1FBQ3pCLE1BQU1tRyxJQUFBQSxrQkFBTyxFQUFDO1lBQ1pKLE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyxlQUFlQyxpQkFBaUI7UUFDMUQ7UUFFQSxtQkFBbUI7UUFDbkJJLG9CQUFTLENBQUNLLEtBQUssQ0FBQ1gsaUJBQU0sQ0FBQ0ksV0FBVyxDQUFDO1FBRW5DLDJCQUEyQjtRQUMzQixNQUFNRCxJQUFBQSxrQkFBTyxFQUFDO1lBQ1pKLE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyxlQUFlQyxpQkFBaUI7WUFDeERILE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyxjQUFjQyxpQkFBaUI7UUFDekQ7SUFDRjtJQUVBTCxHQUFHLHNDQUFzQztRQUN2Q0MsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUM5RjtRQUVSLGdDQUFnQztRQUNoQyxNQUFNbUcsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMscUJBQXFCQyxpQkFBaUI7UUFDaEU7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTW9CLGlCQUFpQjtZQUNyQkMsT0FBTztZQUNQL0MsU0FBUztZQUNUQyxVQUFVO1lBQ1ZDLFdBQVcsQ0FBQztZQUNaOEMsVUFBVTtZQUNWQyxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDakNDLE9BQU87WUFDUEMsU0FBUztRQUNYO1FBRUEsK0JBQStCO1FBQy9CekcsaUNBQWlCLENBQUNnRyxRQUFRLEdBQUd0RyxpQkFBaUIsQ0FBQ3dHO1FBRS9DLGlDQUFpQztRQUNqQyxNQUFNbkIsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsZ0NBQWdDQyxpQkFBaUI7WUFDekVILE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyxtQ0FBbUNDLGlCQUFpQjtRQUM5RTtJQUNGO0lBRUFMLEdBQUcsNkJBQTZCO1FBQzlCQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQzlGO1FBRVIsNEJBQTRCO1FBQzVCb0IsaUNBQWlCLENBQUNnRyxRQUFRLEdBQUdyRyxtQkFBbUIsQ0FBQyxPQUFPO1FBRXhELDJCQUEyQjtRQUMzQixNQUFNb0YsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsMkNBQTJDQyxpQkFBaUI7UUFDdEY7SUFDRjtJQUVBTCxHQUFHLDJCQUEyQjtRQUM1QkMsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUM5RjtRQUVSLHdCQUF3QjtRQUN4QixNQUFNbUcsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDSSxXQUFXLENBQUMsc0JBQXNCRixpQkFBaUI7UUFDbkU7UUFHRW5HLE9BQU9zRixLQUFLLENBQWV5QyxTQUFTO1FBRXRDLGdCQUFnQjtRQUNoQnhCLG9CQUFTLENBQUNLLEtBQUssQ0FBQ1gsaUJBQU0sQ0FBQ0ksV0FBVyxDQUFDO1FBRW5DLDRCQUE0QjtRQUM1QixNQUFNRCxJQUFBQSxrQkFBTyxFQUFDO1lBQ1pKLE9BQU9oRyxPQUFPc0YsS0FBSyxFQUFFMEMsZ0JBQWdCO1FBQ3ZDO0lBQ0Y7QUFDRiJ9