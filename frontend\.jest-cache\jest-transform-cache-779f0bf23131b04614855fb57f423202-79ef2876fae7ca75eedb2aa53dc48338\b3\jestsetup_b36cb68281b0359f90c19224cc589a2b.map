{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\jest.setup.js"], "sourcesContent": ["import '@testing-library/jest-dom'\nimport { TextEncoder, TextDecoder } from 'util'\n\n// Polyfill for TextEncoder/TextDecoder\nglobal.TextEncoder = TextEncoder\nglobal.TextDecoder = TextDecoder\n\n// Mock Next.js router\njest.mock('next/navigation', () => ({\n  useRouter() {\n    return {\n      push: jest.fn(),\n      replace: jest.fn(),\n      prefetch: jest.fn(),\n      back: jest.fn(),\n      forward: jest.fn(),\n      refresh: jest.fn(),\n    }\n  },\n  usePathname() {\n    return '/dashboard'\n  },\n  useSearchParams() {\n    return new URLSearchParams()\n  },\n}))\n\n// Mock Next.js dynamic imports\njest.mock('next/dynamic', () => (func) => {\n  const DynamicComponent = (props) => {\n    const Component = func()\n    return <Component {...props} />\n  }\n  DynamicComponent.displayName = 'DynamicComponent'\n  return DynamicComponent\n})\n\n// Mock Leaflet\njest.mock('leaflet', () => ({\n  map: jest.fn(() => ({\n    setView: jest.fn(),\n    on: jest.fn(),\n    off: jest.fn(),\n    remove: jest.fn(),\n  })),\n  tileLayer: jest.fn(() => ({\n    addTo: jest.fn(),\n  })),\n  marker: jest.fn(() => ({\n    addTo: jest.fn(),\n    bindPopup: jest.fn(),\n    setLatLng: jest.fn(),\n  })),\n  icon: jest.fn(),\n  divIcon: jest.fn(),\n  Icon: {\n    Default: {\n      prototype: {},\n      mergeOptions: jest.fn(),\n    },\n  },\n}))\n\n// Mock react-leaflet\njest.mock('react-leaflet', () => ({\n  MapContainer: ({ children }) => <div data-testid=\"map-container\">{children}</div>,\n  TileLayer: () => <div data-testid=\"tile-layer\" />,\n  Marker: ({ children }) => <div data-testid=\"marker\">{children}</div>,\n  Popup: ({ children }) => <div data-testid=\"popup\">{children}</div>,\n  Circle: ({ children }) => <div data-testid=\"circle\">{children}</div>,\n  useMap: () => ({\n    setView: jest.fn(),\n    on: jest.fn(),\n    off: jest.fn(),\n  }),\n}))\n\n// Mock Recharts\njest.mock('recharts', () => ({\n  ResponsiveContainer: ({ children }) => <div data-testid=\"responsive-container\">{children}</div>,\n  LineChart: ({ children }) => <div data-testid=\"line-chart\">{children}</div>,\n  AreaChart: ({ children }) => <div data-testid=\"area-chart\">{children}</div>,\n  BarChart: ({ children }) => <div data-testid=\"bar-chart\">{children}</div>,\n  PieChart: ({ children }) => <div data-testid=\"pie-chart\">{children}</div>,\n  Line: () => <div data-testid=\"line\" />,\n  Area: () => <div data-testid=\"area\" />,\n  Bar: () => <div data-testid=\"bar\" />,\n  Pie: () => <div data-testid=\"pie\" />,\n  Cell: () => <div data-testid=\"cell\" />,\n  XAxis: () => <div data-testid=\"x-axis\" />,\n  YAxis: () => <div data-testid=\"y-axis\" />,\n  CartesianGrid: () => <div data-testid=\"cartesian-grid\" />,\n  Tooltip: () => <div data-testid=\"tooltip\" />,\n  Legend: () => <div data-testid=\"legend\" />,\n}))\n\n// Mock Socket.IO\njest.mock('socket.io-client', () => ({\n  io: jest.fn(() => ({\n    on: jest.fn(),\n    off: jest.fn(),\n    emit: jest.fn(),\n    connect: jest.fn(),\n    disconnect: jest.fn(),\n    connected: true,\n  })),\n}))\n\n// Mock react-hot-toast\njest.mock('react-hot-toast', () => ({\n  toast: {\n    success: jest.fn(),\n    error: jest.fn(),\n    loading: jest.fn(),\n    dismiss: jest.fn(),\n  },\n  Toaster: () => <div data-testid=\"toaster\" />,\n}))\n\n// Mock IntersectionObserver\nglobal.IntersectionObserver = class IntersectionObserver {\n  constructor() {}\n  disconnect() {}\n  observe() {}\n  unobserve() {}\n}\n\n// Mock ResizeObserver\nglobal.ResizeObserver = class ResizeObserver {\n  constructor() {}\n  disconnect() {}\n  observe() {}\n  unobserve() {}\n}\n\n// Mock matchMedia\nObject.defineProperty(window, 'matchMedia', {\n  writable: true,\n  value: jest.fn().mockImplementation(query => ({\n    matches: false,\n    media: query,\n    onchange: null,\n    addListener: jest.fn(), // deprecated\n    removeListener: jest.fn(), // deprecated\n    addEventListener: jest.fn(),\n    removeEventListener: jest.fn(),\n    dispatchEvent: jest.fn(),\n  })),\n})\n\n// Mock localStorage\nconst localStorageMock = {\n  getItem: jest.fn(),\n  setItem: jest.fn(),\n  removeItem: jest.fn(),\n  clear: jest.fn(),\n}\nglobal.localStorage = localStorageMock\n\n// Mock sessionStorage\nconst sessionStorageMock = {\n  getItem: jest.fn(),\n  setItem: jest.fn(),\n  removeItem: jest.fn(),\n  clear: jest.fn(),\n}\nglobal.sessionStorage = sessionStorageMock\n\n// Suppress console errors during tests\nconst originalError = console.error\nbeforeAll(() => {\n  console.error = (...args) => {\n    if (\n      typeof args[0] === 'string' &&\n      args[0].includes('Warning: ReactDOM.render is no longer supported')\n    ) {\n      return\n    }\n    originalError.call(console, ...args)\n  }\n})\n\nafterAll(() => {\n  console.error = originalError\n})\n"], "names": ["jest", "mock", "useRouter", "push", "fn", "replace", "prefetch", "back", "forward", "refresh", "usePathname", "useSearchParams", "URLSearchParams", "func", "DynamicComponent", "props", "Component", "displayName", "map", "<PERSON><PERSON><PERSON><PERSON>", "on", "off", "remove", "<PERSON><PERSON><PERSON>er", "addTo", "marker", "bindPopup", "setLatLng", "icon", "divIcon", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "mergeOptions", "MapContainer", "children", "div", "data-testid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "Circle", "useMap", "ResponsiveContainer", "Line<PERSON>hart", "AreaChart", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Line", "Area", "Bar", "Pie", "Cell", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "io", "emit", "connect", "disconnect", "connected", "toast", "success", "error", "loading", "dismiss", "Toaster", "global", "TextEncoder", "TextDecoder", "IntersectionObserver", "constructor", "observe", "unobserve", "ResizeObserver", "Object", "defineProperty", "window", "writable", "value", "mockImplementation", "query", "matches", "media", "onchange", "addListener", "removeListener", "addEventListener", "removeEventListener", "dispatchEvent", "localStorageMock", "getItem", "setItem", "removeItem", "clear", "localStorage", "sessionStorageMock", "sessionStorage", "originalError", "console", "beforeAll", "args", "includes", "call", "afterAll"], "mappings": ";AAOA,sBAAsB;AACtBA,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC;YACE,OAAO;gBACLC,MAAMH,KAAKI,EAAE;gBACbC,SAASL,KAAKI,EAAE;gBAChBE,UAAUN,KAAKI,EAAE;gBACjBG,MAAMP,KAAKI,EAAE;gBACbI,SAASR,KAAKI,EAAE;gBAChBK,SAAST,KAAKI,EAAE;YAClB;QACF;QACAM;YACE,OAAO;QACT;QACAC;YACE,OAAO,IAAIC;QACb;IACF,CAAA;AAEA,+BAA+B;AAC/BZ,KAAKC,IAAI,CAAC,gBAAgB,IAAM,CAACY;QAC/B,MAAMC,mBAAmB,CAACC;YACxB,MAAMC,YAAYH;YAClB,qBAAO,qBAACG;gBAAW,GAAGD,KAAK;;QAC7B;QACAD,iBAAiBG,WAAW,GAAG;QAC/B,OAAOH;IACT;AAEA,eAAe;AACfd,KAAKC,IAAI,CAAC,WAAW,IAAO,CAAA;QAC1BiB,KAAKlB,KAAKI,EAAE,CAAC,IAAO,CAAA;gBAClBe,SAASnB,KAAKI,EAAE;gBAChBgB,IAAIpB,KAAKI,EAAE;gBACXiB,KAAKrB,KAAKI,EAAE;gBACZkB,QAAQtB,KAAKI,EAAE;YACjB,CAAA;QACAmB,WAAWvB,KAAKI,EAAE,CAAC,IAAO,CAAA;gBACxBoB,OAAOxB,KAAKI,EAAE;YAChB,CAAA;QACAqB,QAAQzB,KAAKI,EAAE,CAAC,IAAO,CAAA;gBACrBoB,OAAOxB,KAAKI,EAAE;gBACdsB,WAAW1B,KAAKI,EAAE;gBAClBuB,WAAW3B,KAAKI,EAAE;YACpB,CAAA;QACAwB,MAAM5B,KAAKI,EAAE;QACbyB,SAAS7B,KAAKI,EAAE;QAChB0B,MAAM;YACJC,SAAS;gBACPC,WAAW,CAAC;gBACZC,cAAcjC,KAAKI,EAAE;YACvB;QACF;IACF,CAAA;AAEA,qBAAqB;AACrBJ,KAAKC,IAAI,CAAC,iBAAiB,IAAO,CAAA;QAChCiC,cAAc,CAAC,EAAEC,QAAQ,EAAE,iBAAK,qBAACC;gBAAIC,eAAY;0BAAiBF;;QAClEG,WAAW,kBAAM,qBAACF;gBAAIC,eAAY;;QAClCE,QAAQ,CAAC,EAAEJ,QAAQ,EAAE,iBAAK,qBAACC;gBAAIC,eAAY;0BAAUF;;QACrDK,OAAO,CAAC,EAAEL,QAAQ,EAAE,iBAAK,qBAACC;gBAAIC,eAAY;0BAASF;;QACnDM,QAAQ,CAAC,EAAEN,QAAQ,EAAE,iBAAK,qBAACC;gBAAIC,eAAY;0BAAUF;;QACrDO,QAAQ,IAAO,CAAA;gBACbvB,SAASnB,KAAKI,EAAE;gBAChBgB,IAAIpB,KAAKI,EAAE;gBACXiB,KAAKrB,KAAKI,EAAE;YACd,CAAA;IACF,CAAA;AAEA,gBAAgB;AAChBJ,KAAKC,IAAI,CAAC,YAAY,IAAO,CAAA;QAC3B0C,qBAAqB,CAAC,EAAER,QAAQ,EAAE,iBAAK,qBAACC;gBAAIC,eAAY;0BAAwBF;;QAChFS,WAAW,CAAC,EAAET,QAAQ,EAAE,iBAAK,qBAACC;gBAAIC,eAAY;0BAAcF;;QAC5DU,WAAW,CAAC,EAAEV,QAAQ,EAAE,iBAAK,qBAACC;gBAAIC,eAAY;0BAAcF;;QAC5DW,UAAU,CAAC,EAAEX,QAAQ,EAAE,iBAAK,qBAACC;gBAAIC,eAAY;0BAAaF;;QAC1DY,UAAU,CAAC,EAAEZ,QAAQ,EAAE,iBAAK,qBAACC;gBAAIC,eAAY;0BAAaF;;QAC1Da,MAAM,kBAAM,qBAACZ;gBAAIC,eAAY;;QAC7BY,MAAM,kBAAM,qBAACb;gBAAIC,eAAY;;QAC7Ba,KAAK,kBAAM,qBAACd;gBAAIC,eAAY;;QAC5Bc,KAAK,kBAAM,qBAACf;gBAAIC,eAAY;;QAC5Be,MAAM,kBAAM,qBAAChB;gBAAIC,eAAY;;QAC7BgB,OAAO,kBAAM,qBAACjB;gBAAIC,eAAY;;QAC9BiB,OAAO,kBAAM,qBAAClB;gBAAIC,eAAY;;QAC9BkB,eAAe,kBAAM,qBAACnB;gBAAIC,eAAY;;QACtCmB,SAAS,kBAAM,qBAACpB;gBAAIC,eAAY;;QAChCoB,QAAQ,kBAAM,qBAACrB;gBAAIC,eAAY;;IACjC,CAAA;AAEA,iBAAiB;AACjBrC,KAAKC,IAAI,CAAC,oBAAoB,IAAO,CAAA;QACnCyD,IAAI1D,KAAKI,EAAE,CAAC,IAAO,CAAA;gBACjBgB,IAAIpB,KAAKI,EAAE;gBACXiB,KAAKrB,KAAKI,EAAE;gBACZuD,MAAM3D,KAAKI,EAAE;gBACbwD,SAAS5D,KAAKI,EAAE;gBAChByD,YAAY7D,KAAKI,EAAE;gBACnB0D,WAAW;YACb,CAAA;IACF,CAAA;AAEA,uBAAuB;AACvB9D,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClC8D,OAAO;YACLC,SAAShE,KAAKI,EAAE;YAChB6D,OAAOjE,KAAKI,EAAE;YACd8D,SAASlE,KAAKI,EAAE;YAChB+D,SAASnE,KAAKI,EAAE;QAClB;QACAgE,SAAS,kBAAM,qBAAChC;gBAAIC,eAAY;;IAClC,CAAA;;;;;QArHO;sBACkC;AAEzC,uCAAuC;AACvCgC,OAAOC,WAAW,GAAGA,iBAAW;AAChCD,OAAOE,WAAW,GAAGA,iBAAW;AAkHhC,4BAA4B;AAC5BF,OAAOG,oBAAoB,GAAG,MAAMA;IAClCC,aAAc,CAAC;IACfZ,aAAa,CAAC;IACda,UAAU,CAAC;IACXC,YAAY,CAAC;AACf;AAEA,sBAAsB;AACtBN,OAAOO,cAAc,GAAG,MAAMA;IAC5BH,aAAc,CAAC;IACfZ,aAAa,CAAC;IACda,UAAU,CAAC;IACXC,YAAY,CAAC;AACf;AAEA,kBAAkB;AAClBE,OAAOC,cAAc,CAACC,QAAQ,cAAc;IAC1CC,UAAU;IACVC,OAAOjF,KAAKI,EAAE,GAAG8E,kBAAkB,CAACC,CAAAA,QAAU,CAAA;YAC5CC,SAAS;YACTC,OAAOF;YACPG,UAAU;YACVC,aAAavF,KAAKI,EAAE;YACpBoF,gBAAgBxF,KAAKI,EAAE;YACvBqF,kBAAkBzF,KAAKI,EAAE;YACzBsF,qBAAqB1F,KAAKI,EAAE;YAC5BuF,eAAe3F,KAAKI,EAAE;QACxB,CAAA;AACF;AAEA,oBAAoB;AACpB,MAAMwF,mBAAmB;IACvBC,SAAS7F,KAAKI,EAAE;IAChB0F,SAAS9F,KAAKI,EAAE;IAChB2F,YAAY/F,KAAKI,EAAE;IACnB4F,OAAOhG,KAAKI,EAAE;AAChB;AACAiE,OAAO4B,YAAY,GAAGL;AAEtB,sBAAsB;AACtB,MAAMM,qBAAqB;IACzBL,SAAS7F,KAAKI,EAAE;IAChB0F,SAAS9F,KAAKI,EAAE;IAChB2F,YAAY/F,KAAKI,EAAE;IACnB4F,OAAOhG,KAAKI,EAAE;AAChB;AACAiE,OAAO8B,cAAc,GAAGD;AAExB,uCAAuC;AACvC,MAAME,gBAAgBC,QAAQpC,KAAK;AACnCqC,UAAU;IACRD,QAAQpC,KAAK,GAAG,CAAC,GAAGsC;QAClB,IACE,OAAOA,IAAI,CAAC,EAAE,KAAK,YACnBA,IAAI,CAAC,EAAE,CAACC,QAAQ,CAAC,oDACjB;YACA;QACF;QACAJ,cAAcK,IAAI,CAACJ,YAAYE;IACjC;AACF;AAEAG,SAAS;IACPL,QAAQpC,KAAK,GAAGmC;AAClB"}