{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\layout\\__tests__\\header.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { Header } from '../header'\nimport { useDashboardStore } from '@/stores/dashboard-store'\nimport { usePathname } from 'next/navigation'\nimport { mockFramerMotion, runAxeTest } from '@/lib/test-utils'\n\n// Mock Next.js navigation\njest.mock('next/navigation', () => ({\n  usePathname: jest.fn(),\n}))\n\n// Mock the dashboard store\njest.mock('@/stores/dashboard-store', () => ({\n  useDashboardStore: jest.fn(),\n  useConnectionStatus: jest.fn(),\n  useUnacknowledgedAlerts: jest.fn(),\n}))\n\n// Mock the main nav component\njest.mock('../main-nav', () => ({\n  MainNav: ({ onNavigate }: { onNavigate?: () => void }) => (\n    <div data-testid=\"main-nav\">\n      <button onClick={onNavigate} data-testid=\"nav-item\">\n        Dashboard\n      </button>\n    </div>\n  ),\n}))\n\n// Mock UI components\njest.mock('@/components/ui/sheet', () => ({\n  Sheet: ({ children, open, onOpenChange }: any) => (\n    <div data-testid=\"sheet\" data-open={open}>\n      <div onClick={() => onOpenChange?.(false)}>{children}</div>\n    </div>\n  ),\n  SheetContent: ({ children }: any) => (\n    <div data-testid=\"sheet-content\">{children}</div>\n  ),\n  SheetTrigger: ({ children }: any) => (\n    <div data-testid=\"sheet-trigger\">{children}</div>\n  ),\n}))\n\n// Mock framer-motion\nmockFramerMotion()\n\ndescribe('Header Component', () => {\n  const mockOnMenuClick = jest.fn()\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n    \n    // Mock pathname\n    ;(usePathname as jest.Mock).mockReturnValue('/dashboard')\n    \n    // Mock dashboard store hooks\n    ;(useDashboardStore as jest.Mock).mockReturnValue({\n      alerts: [],\n      isConnected: true,\n    })\n  })\n\n  it('renders correctly', () => {\n    render(<Header onMenuClick={mockOnMenuClick} />)\n\n    expect(screen.getByRole('banner')).toBeInTheDocument()\n    expect(screen.getByText('UAV Control')).toBeInTheDocument()\n    expect(screen.getByText('Management System')).toBeInTheDocument()\n  })\n\n  it('displays logo and branding', () => {\n    render(<Header />)\n\n    expect(screen.getByText('UAV Control')).toBeInTheDocument()\n    expect(screen.getByText('Management System')).toBeInTheDocument()\n    \n    // Check for Shield icon (logo)\n    const logoLink = screen.getByRole('link', { name: /uav control/i })\n    expect(logoLink).toHaveAttribute('href', '/dashboard')\n  })\n\n  it('shows mobile menu trigger', () => {\n    render(<Header />)\n\n    const menuButton = screen.getByRole('button', { name: /toggle menu/i })\n    expect(menuButton).toBeInTheDocument()\n    expect(menuButton).toHaveClass('md:hidden')\n  })\n\n  it('handles mobile menu interaction', () => {\n    render(<Header />)\n\n    const menuButton = screen.getByRole('button', { name: /toggle menu/i })\n    const sheet = screen.getByTestId('sheet')\n\n    // Initially closed\n    expect(sheet).toHaveAttribute('data-open', 'false')\n\n    // Open mobile menu\n    fireEvent.click(menuButton)\n    expect(sheet).toHaveAttribute('data-open', 'true')\n  })\n\n  it('displays search functionality', () => {\n    render(<Header />)\n\n    const searchInput = screen.getByPlaceholderText(/search/i)\n    expect(searchInput).toBeInTheDocument()\n  })\n\n  it('handles search form submission', () => {\n    const consoleSpy = jest.spyOn(console, 'log').mockImplementation()\n    render(<Header />)\n\n    const searchInput = screen.getByPlaceholderText(/search/i)\n    const searchForm = searchInput.closest('form')\n\n    fireEvent.change(searchInput, { target: { value: 'UAV-001' } })\n    fireEvent.submit(searchForm!)\n\n    expect(consoleSpy).toHaveBeenCalledWith('Searching for:', 'UAV-001')\n    consoleSpy.mockRestore()\n  })\n\n  it('does not search with empty query', () => {\n    const consoleSpy = jest.spyOn(console, 'log').mockImplementation()\n    render(<Header />)\n\n    const searchInput = screen.getByPlaceholderText(/search/i)\n    const searchForm = searchInput.closest('form')\n\n    fireEvent.submit(searchForm!)\n\n    expect(consoleSpy).not.toHaveBeenCalled()\n    consoleSpy.mockRestore()\n  })\n\n  it('displays connection status', () => {\n    render(<Header />)\n\n    // Should show connected status by default\n    const connectionIndicator = screen.getByTestId('connection-status')\n    expect(connectionIndicator).toBeInTheDocument()\n  })\n\n  it('shows disconnected state', () => {\n    ;(useDashboardStore as jest.Mock).mockReturnValue({\n      alerts: [],\n      isConnected: false,\n    })\n\n    render(<Header />)\n\n    const connectionIndicator = screen.getByTestId('connection-status')\n    expect(connectionIndicator).toHaveClass('text-destructive')\n  })\n\n  it('displays notification badge with alert count', () => {\n    ;(useDashboardStore as jest.Mock).mockReturnValue({\n      alerts: [\n        { id: '1', type: 'ERROR', acknowledged: false },\n        { id: '2', type: 'WARNING', acknowledged: false },\n      ],\n      isConnected: true,\n    })\n\n    render(<Header />)\n\n    const notificationButton = screen.getByRole('button', { name: /notifications/i })\n    expect(notificationButton).toBeInTheDocument()\n    \n    const badge = screen.getByText('2')\n    expect(badge).toBeInTheDocument()\n  })\n\n  it('handles dark mode toggle', () => {\n    render(<Header />)\n\n    const darkModeButton = screen.getByRole('button', { name: /toggle theme/i })\n    expect(darkModeButton).toBeInTheDocument()\n\n    fireEvent.click(darkModeButton)\n    // Dark mode toggle functionality would be tested here\n  })\n\n  it('displays user menu', () => {\n    render(<Header />)\n\n    const userMenuButton = screen.getByRole('button', { name: /user menu/i })\n    expect(userMenuButton).toBeInTheDocument()\n  })\n\n  it('shows page title based on pathname', () => {\n    ;(usePathname as jest.Mock).mockReturnValue('/uavs')\n    render(<Header />)\n\n    expect(screen.getByText('UAV Management')).toBeInTheDocument()\n  })\n\n  it('handles different pathnames correctly', () => {\n    const pathTitleMap = [\n      ['/dashboard', 'Dashboard'],\n      ['/uavs', 'UAV Management'],\n      ['/map', 'Map View'],\n      ['/hibernate-pod', 'Hibernate Pod'],\n      ['/battery', 'Battery Monitor'],\n    ]\n\n    pathTitleMap.forEach(([path, title]) => {\n      ;(usePathname as jest.Mock).mockReturnValue(path)\n      const { unmount } = render(<Header />)\n      \n      expect(screen.getByText(title)).toBeInTheDocument()\n      unmount()\n    })\n  })\n\n  it('calls onMenuClick when provided', () => {\n    render(<Header onMenuClick={mockOnMenuClick} />)\n\n    const menuButton = screen.getByRole('button', { name: /toggle menu/i })\n    fireEvent.click(menuButton)\n\n    expect(mockOnMenuClick).toHaveBeenCalledTimes(1)\n  })\n\n  it('maintains accessibility standards', async () => {\n    const { container } = render(<Header />)\n\n    // Check for proper header role\n    expect(screen.getByRole('banner')).toBeInTheDocument()\n    \n    // Check for proper button labels\n    expect(screen.getByRole('button', { name: /toggle menu/i })).toBeInTheDocument()\n    expect(screen.getByRole('button', { name: /toggle theme/i })).toBeInTheDocument()\n    \n    // Run accessibility tests\n    await runAxeTest(container)\n  })\n\n  it('handles keyboard navigation', () => {\n    render(<Header />)\n\n    const searchInput = screen.getByPlaceholderText(/search/i)\n    const menuButton = screen.getByRole('button', { name: /toggle menu/i })\n\n    // Test tab navigation\n    searchInput.focus()\n    expect(searchInput).toHaveFocus()\n\n    menuButton.focus()\n    expect(menuButton).toHaveFocus()\n  })\n\n  it('supports responsive design', () => {\n    render(<Header />)\n\n    // Check responsive classes\n    const header = screen.getByRole('banner')\n    expect(header).toHaveClass('sticky', 'top-0', 'z-50', 'w-full')\n\n    // Mobile menu should be hidden on desktop\n    const menuButton = screen.getByRole('button', { name: /toggle menu/i })\n    expect(menuButton).toHaveClass('md:hidden')\n\n    // Logo text should be hidden on small screens\n    const logoText = screen.getByText('UAV Control').parentElement\n    expect(logoText).toHaveClass('hidden', 'sm:block')\n  })\n\n  it('closes mobile menu when navigation occurs', () => {\n    render(<Header />)\n\n    const menuButton = screen.getByRole('button', { name: /toggle menu/i })\n    const sheet = screen.getByTestId('sheet')\n\n    // Open mobile menu\n    fireEvent.click(menuButton)\n    expect(sheet).toHaveAttribute('data-open', 'true')\n\n    // Navigate (simulate clicking nav item)\n    const navItem = screen.getByTestId('nav-item')\n    fireEvent.click(navItem)\n\n    // Menu should close\n    expect(sheet).toHaveAttribute('data-open', 'false')\n  })\n\n  it('handles search input changes', () => {\n    render(<Header />)\n\n    const searchInput = screen.getByPlaceholderText(/search/i)\n    \n    fireEvent.change(searchInput, { target: { value: 'test query' } })\n    expect(searchInput).toHaveValue('test query')\n  })\n\n  it('displays proper backdrop blur effect', () => {\n    render(<Header />)\n\n    const header = screen.getByRole('banner')\n    expect(header).toHaveClass(\n      'bg-background/95',\n      'backdrop-blur',\n      'supports-[backdrop-filter]:bg-background/60'\n    )\n  })\n})\n"], "names": ["jest", "mock", "usePathname", "fn", "useDashboardStore", "useConnectionStatus", "useUnacknowledgedAlerts", "MainNav", "onNavigate", "div", "data-testid", "button", "onClick", "Sheet", "children", "open", "onOpenChange", "data-open", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sheet<PERSON><PERSON>ger", "mockFramerMotion", "describe", "mockOnMenuClick", "beforeEach", "clearAllMocks", "mockReturnValue", "alerts", "isConnected", "it", "render", "Header", "onMenuClick", "expect", "screen", "getByRole", "toBeInTheDocument", "getByText", "logoLink", "name", "toHaveAttribute", "menuButton", "toHaveClass", "sheet", "getByTestId", "fireEvent", "click", "searchInput", "getByPlaceholderText", "consoleSpy", "spyOn", "console", "mockImplementation", "searchForm", "closest", "change", "target", "value", "submit", "toHaveBeenCalledWith", "mockRestore", "not", "toHaveBeenCalled", "connectionIndicator", "id", "type", "acknowledged", "notificationButton", "badge", "darkModeButton", "userMenuButton", "pathTitleMap", "for<PERSON>ach", "path", "title", "unmount", "toHaveBeenCalledTimes", "container", "runAxeTest", "focus", "toHaveFocus", "header", "logoText", "parentElement", "navItem", "toHaveValue"], "mappings": ";AAOA,0BAA0B;AAC1BA,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC,aAAaF,KAAKG,EAAE;IACtB,CAAA;AAEA,2BAA2B;AAC3BH,KAAKC,IAAI,CAAC,4BAA4B,IAAO,CAAA;QAC3CG,mBAAmBJ,KAAKG,EAAE;QAC1BE,qBAAqBL,KAAKG,EAAE;QAC5BG,yBAAyBN,KAAKG,EAAE;IAClC,CAAA;AAEA,8BAA8B;AAC9BH,KAAKC,IAAI,CAAC,eAAe,IAAO,CAAA;QAC9BM,SAAS,CAAC,EAAEC,UAAU,EAA+B,iBACnD,qBAACC;gBAAIC,eAAY;0BACf,cAAA,qBAACC;oBAAOC,SAASJ;oBAAYE,eAAY;8BAAW;;;IAK1D,CAAA;AAEA,qBAAqB;AACrBV,KAAKC,IAAI,CAAC,yBAAyB,IAAO,CAAA;QACxCY,OAAO,CAAC,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,EAAO,iBAC3C,qBAACP;gBAAIC,eAAY;gBAAQO,aAAWF;0BAClC,cAAA,qBAACN;oBAAIG,SAAS,IAAMI,eAAe;8BAASF;;;QAGhDI,cAAc,CAAC,EAAEJ,QAAQ,EAAO,iBAC9B,qBAACL;gBAAIC,eAAY;0BAAiBI;;QAEpCK,cAAc,CAAC,EAAEL,QAAQ,EAAO,iBAC9B,qBAACL;gBAAIC,eAAY;0BAAiBI;;IAEtC,CAAA;;;;;8DA3CkB;2BACiC;wBAC5B;gCACW;4BACN;;;;;;AAyC5B,qBAAqB;AACrBM,IAAAA,2BAAgB;AAEhBC,SAAS,oBAAoB;IAC3B,MAAMC,kBAAkBtB,KAAKG,EAAE;IAE/BoB,WAAW;QACTvB,KAAKwB,aAAa;QAGhBtB,uBAAW,CAAeuB,eAAe,CAAC;QAG1CrB,iCAAiB,CAAeqB,eAAe,CAAC;YAChDC,QAAQ,EAAE;YACVC,aAAa;QACf;IACF;IAEAC,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;YAACC,aAAaT;;QAE5BU,OAAOC,iBAAM,CAACC,SAAS,CAAC,WAAWC,iBAAiB;QACpDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;QACzDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,sBAAsBD,iBAAiB;IACjE;IAEAP,GAAG,8BAA8B;QAC/BC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEdE,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;QACzDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,sBAAsBD,iBAAiB;QAE/D,+BAA+B;QAC/B,MAAME,WAAWJ,iBAAM,CAACC,SAAS,CAAC,QAAQ;YAAEI,MAAM;QAAe;QACjEN,OAAOK,UAAUE,eAAe,CAAC,QAAQ;IAC3C;IAEAX,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMU,aAAaP,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEI,MAAM;QAAe;QACrEN,OAAOQ,YAAYL,iBAAiB;QACpCH,OAAOQ,YAAYC,WAAW,CAAC;IACjC;IAEAb,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMU,aAAaP,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEI,MAAM;QAAe;QACrE,MAAMI,QAAQT,iBAAM,CAACU,WAAW,CAAC;QAEjC,mBAAmB;QACnBX,OAAOU,OAAOH,eAAe,CAAC,aAAa;QAE3C,mBAAmB;QACnBK,oBAAS,CAACC,KAAK,CAACL;QAChBR,OAAOU,OAAOH,eAAe,CAAC,aAAa;IAC7C;IAEAX,GAAG,iCAAiC;QAClCC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMgB,cAAcb,iBAAM,CAACc,oBAAoB,CAAC;QAChDf,OAAOc,aAAaX,iBAAiB;IACvC;IAEAP,GAAG,kCAAkC;QACnC,MAAMoB,aAAahD,KAAKiD,KAAK,CAACC,SAAS,OAAOC,kBAAkB;QAChEtB,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMgB,cAAcb,iBAAM,CAACc,oBAAoB,CAAC;QAChD,MAAMK,aAAaN,YAAYO,OAAO,CAAC;QAEvCT,oBAAS,CAACU,MAAM,CAACR,aAAa;YAAES,QAAQ;gBAAEC,OAAO;YAAU;QAAE;QAC7DZ,oBAAS,CAACa,MAAM,CAACL;QAEjBpB,OAAOgB,YAAYU,oBAAoB,CAAC,kBAAkB;QAC1DV,WAAWW,WAAW;IACxB;IAEA/B,GAAG,oCAAoC;QACrC,MAAMoB,aAAahD,KAAKiD,KAAK,CAACC,SAAS,OAAOC,kBAAkB;QAChEtB,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMgB,cAAcb,iBAAM,CAACc,oBAAoB,CAAC;QAChD,MAAMK,aAAaN,YAAYO,OAAO,CAAC;QAEvCT,oBAAS,CAACa,MAAM,CAACL;QAEjBpB,OAAOgB,YAAYY,GAAG,CAACC,gBAAgB;QACvCb,WAAWW,WAAW;IACxB;IAEA/B,GAAG,8BAA8B;QAC/BC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,0CAA0C;QAC1C,MAAMgC,sBAAsB7B,iBAAM,CAACU,WAAW,CAAC;QAC/CX,OAAO8B,qBAAqB3B,iBAAiB;IAC/C;IAEAP,GAAG,4BAA4B;QAC3BxB,iCAAiB,CAAeqB,eAAe,CAAC;YAChDC,QAAQ,EAAE;YACVC,aAAa;QACf;QAEAE,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMgC,sBAAsB7B,iBAAM,CAACU,WAAW,CAAC;QAC/CX,OAAO8B,qBAAqBrB,WAAW,CAAC;IAC1C;IAEAb,GAAG,gDAAgD;QAC/CxB,iCAAiB,CAAeqB,eAAe,CAAC;YAChDC,QAAQ;gBACN;oBAAEqC,IAAI;oBAAKC,MAAM;oBAASC,cAAc;gBAAM;gBAC9C;oBAAEF,IAAI;oBAAKC,MAAM;oBAAWC,cAAc;gBAAM;aACjD;YACDtC,aAAa;QACf;QAEAE,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMoC,qBAAqBjC,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEI,MAAM;QAAiB;QAC/EN,OAAOkC,oBAAoB/B,iBAAiB;QAE5C,MAAMgC,QAAQlC,iBAAM,CAACG,SAAS,CAAC;QAC/BJ,OAAOmC,OAAOhC,iBAAiB;IACjC;IAEAP,GAAG,4BAA4B;QAC7BC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMsC,iBAAiBnC,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEI,MAAM;QAAgB;QAC1EN,OAAOoC,gBAAgBjC,iBAAiB;QAExCS,oBAAS,CAACC,KAAK,CAACuB;IAChB,sDAAsD;IACxD;IAEAxC,GAAG,sBAAsB;QACvBC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMuC,iBAAiBpC,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEI,MAAM;QAAa;QACvEN,OAAOqC,gBAAgBlC,iBAAiB;IAC1C;IAEAP,GAAG,sCAAsC;QACrC1B,uBAAW,CAAeuB,eAAe,CAAC;QAC5CI,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEdE,OAAOC,iBAAM,CAACG,SAAS,CAAC,mBAAmBD,iBAAiB;IAC9D;IAEAP,GAAG,yCAAyC;QAC1C,MAAM0C,eAAe;YACnB;gBAAC;gBAAc;aAAY;YAC3B;gBAAC;gBAAS;aAAiB;YAC3B;gBAAC;gBAAQ;aAAW;YACpB;gBAAC;gBAAkB;aAAgB;YACnC;gBAAC;gBAAY;aAAkB;SAChC;QAEDA,aAAaC,OAAO,CAAC,CAAC,CAACC,MAAMC,MAAM;YAC/BvE,uBAAW,CAAeuB,eAAe,CAAC+C;YAC5C,MAAM,EAAEE,OAAO,EAAE,GAAG7C,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;YAElCE,OAAOC,iBAAM,CAACG,SAAS,CAACqC,QAAQtC,iBAAiB;YACjDuC;QACF;IACF;IAEA9C,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;YAACC,aAAaT;;QAE5B,MAAMkB,aAAaP,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEI,MAAM;QAAe;QACrEM,oBAAS,CAACC,KAAK,CAACL;QAEhBR,OAAOV,iBAAiBqD,qBAAqB,CAAC;IAChD;IAEA/C,GAAG,qCAAqC;QACtC,MAAM,EAAEgD,SAAS,EAAE,GAAG/C,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEpC,+BAA+B;QAC/BE,OAAOC,iBAAM,CAACC,SAAS,CAAC,WAAWC,iBAAiB;QAEpD,iCAAiC;QACjCH,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEI,MAAM;QAAe,IAAIH,iBAAiB;QAC9EH,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEI,MAAM;QAAgB,IAAIH,iBAAiB;QAE/E,0BAA0B;QAC1B,MAAM0C,IAAAA,qBAAU,EAACD;IACnB;IAEAhD,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMgB,cAAcb,iBAAM,CAACc,oBAAoB,CAAC;QAChD,MAAMP,aAAaP,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEI,MAAM;QAAe;QAErE,sBAAsB;QACtBQ,YAAYgC,KAAK;QACjB9C,OAAOc,aAAaiC,WAAW;QAE/BvC,WAAWsC,KAAK;QAChB9C,OAAOQ,YAAYuC,WAAW;IAChC;IAEAnD,GAAG,8BAA8B;QAC/BC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,2BAA2B;QAC3B,MAAMkD,SAAS/C,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOgD,QAAQvC,WAAW,CAAC,UAAU,SAAS,QAAQ;QAEtD,0CAA0C;QAC1C,MAAMD,aAAaP,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEI,MAAM;QAAe;QACrEN,OAAOQ,YAAYC,WAAW,CAAC;QAE/B,8CAA8C;QAC9C,MAAMwC,WAAWhD,iBAAM,CAACG,SAAS,CAAC,eAAe8C,aAAa;QAC9DlD,OAAOiD,UAAUxC,WAAW,CAAC,UAAU;IACzC;IAEAb,GAAG,6CAA6C;QAC9CC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMU,aAAaP,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEI,MAAM;QAAe;QACrE,MAAMI,QAAQT,iBAAM,CAACU,WAAW,CAAC;QAEjC,mBAAmB;QACnBC,oBAAS,CAACC,KAAK,CAACL;QAChBR,OAAOU,OAAOH,eAAe,CAAC,aAAa;QAE3C,wCAAwC;QACxC,MAAM4C,UAAUlD,iBAAM,CAACU,WAAW,CAAC;QACnCC,oBAAS,CAACC,KAAK,CAACsC;QAEhB,oBAAoB;QACpBnD,OAAOU,OAAOH,eAAe,CAAC,aAAa;IAC7C;IAEAX,GAAG,gCAAgC;QACjCC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMgB,cAAcb,iBAAM,CAACc,oBAAoB,CAAC;QAEhDH,oBAAS,CAACU,MAAM,CAACR,aAAa;YAAES,QAAQ;gBAAEC,OAAO;YAAa;QAAE;QAChExB,OAAOc,aAAasC,WAAW,CAAC;IAClC;IAEAxD,GAAG,wCAAwC;QACzCC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;QAEd,MAAMkD,SAAS/C,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOgD,QAAQvC,WAAW,CACxB,oBACA,iBACA;IAEJ;AACF"}