48ab35468e10119574f9549be0d111de
"use strict";
// Mock the API
jest.mock("@/api/uav-api");
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _react = require("@testing-library/react");
const _uavstore = require("../uav-store");
const _testutils = require("../../lib/test-utils");
const _uavapi = require("../../api/uav-api");
const mockedUavApi = _uavapi.uavApi;
describe("UAV Store", ()=>{
    beforeEach(()=>{
        // Reset store state
        _uavstore.useUAVStore.setState({
            uavs: [],
            selectedUAV: null,
            loading: false,
            error: null,
            filter: {},
            searchQuery: "",
            regions: [],
            systemStats: null,
            hibernatePodStatus: null
        });
        // Clear all mocks
        jest.clearAllMocks();
    });
    describe("fetchUAVs", ()=>{
        it("should fetch UAVs successfully", async ()=>{
            const mockUAVs = [
                (0, _testutils.createMockUAV)(),
                (0, _testutils.createMockUAV)({
                    id: 2,
                    rfidTag: "UAV-002"
                })
            ];
            mockedUavApi.getUAVs.mockResolvedValue(mockUAVs);
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            await (0, _react.act)(async ()=>{
                await result.current.fetchUAVs();
            });
            expect(result.current.uavs).toEqual(mockUAVs);
            expect(result.current.loading).toBe(false);
            expect(result.current.error).toBeNull();
            expect(mockedUavApi.getUAVs).toHaveBeenCalledTimes(1);
        });
        it("should handle fetch UAVs error", async ()=>{
            const errorMessage = "Failed to fetch UAVs";
            mockedUavApi.getUAVs.mockRejectedValue(new Error(errorMessage));
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            await (0, _react.act)(async ()=>{
                await result.current.fetchUAVs();
            });
            expect(result.current.uavs).toEqual([]);
            expect(result.current.loading).toBe(false);
            expect(result.current.error).toBe(errorMessage);
        });
        it("should set loading state during fetch", async ()=>{
            let resolvePromise;
            const promise = new Promise((resolve)=>{
                resolvePromise = resolve;
            });
            mockedUavApi.getUAVs.mockReturnValue(promise);
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            (0, _react.act)(()=>{
                result.current.fetchUAVs();
            });
            expect(result.current.loading).toBe(true);
            await (0, _react.act)(async ()=>{
                resolvePromise([]);
            });
            expect(result.current.loading).toBe(false);
        });
    });
    describe("createUAV", ()=>{
        it("should create UAV successfully", async ()=>{
            const newUAV = (0, _testutils.createMockUAV)();
            const mockResponse = (0, _testutils.mockApiResponse)(newUAV);
            mockedUavApi.createUAV.mockResolvedValue(mockResponse);
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            let success;
            await (0, _react.act)(async ()=>{
                success = await result.current.createUAV({
                    rfidTag: newUAV.rfidTag,
                    ownerName: newUAV.ownerName,
                    model: newUAV.model,
                    status: newUAV.status
                });
            });
            expect(success).toBe(true);
            expect(result.current.uavs).toContain(newUAV);
            expect(result.current.loading).toBe(false);
            expect(result.current.error).toBeNull();
        });
        it("should handle create UAV error", async ()=>{
            const errorMessage = "Failed to create UAV";
            const mockResponse = {
                success: false,
                message: errorMessage
            };
            mockedUavApi.createUAV.mockResolvedValue(mockResponse);
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            let success;
            await (0, _react.act)(async ()=>{
                success = await result.current.createUAV({
                    rfidTag: "UAV-001",
                    ownerName: "Test Owner",
                    model: "Test Model",
                    status: "AUTHORIZED"
                });
            });
            expect(success).toBe(false);
            expect(result.current.error).toBe(errorMessage);
        });
    });
    describe("updateUAV", ()=>{
        it("should update UAV successfully", async ()=>{
            const existingUAV = (0, _testutils.createMockUAV)();
            const updatedUAV = {
                ...existingUAV,
                ownerName: "Updated Owner"
            };
            const mockResponse = (0, _testutils.mockApiResponse)(updatedUAV);
            mockedUavApi.updateUAV.mockResolvedValue(mockResponse);
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            // Set initial state
            (0, _react.act)(()=>{
                _uavstore.useUAVStore.setState({
                    uavs: [
                        existingUAV
                    ]
                });
            });
            let success;
            await (0, _react.act)(async ()=>{
                success = await result.current.updateUAV(existingUAV.id, {
                    ownerName: "Updated Owner"
                });
            });
            expect(success).toBe(true);
            expect(result.current.uavs[0].ownerName).toBe("Updated Owner");
        });
    });
    describe("deleteUAV", ()=>{
        it("should delete UAV successfully", async ()=>{
            const uavToDelete = (0, _testutils.createMockUAV)();
            const mockResponse = (0, _testutils.mockApiResponse)(undefined);
            mockedUavApi.deleteUAV.mockResolvedValue(mockResponse);
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            // Set initial state
            (0, _react.act)(()=>{
                _uavstore.useUAVStore.setState({
                    uavs: [
                        uavToDelete
                    ]
                });
            });
            let success;
            await (0, _react.act)(async ()=>{
                success = await result.current.deleteUAV(uavToDelete.id);
            });
            expect(success).toBe(true);
            expect(result.current.uavs).toHaveLength(0);
        });
    });
    describe("filters and search", ()=>{
        it("should set filter correctly", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            (0, _react.act)(()=>{
                result.current.setFilter({
                    status: "AUTHORIZED"
                });
            });
            expect(result.current.filter.status).toBe("AUTHORIZED");
        });
        it("should set search query correctly", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            (0, _react.act)(()=>{
                result.current.setSearchQuery("UAV-001");
            });
            expect(result.current.searchQuery).toBe("UAV-001");
        });
        it("should clear error", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            // Set error first
            (0, _react.act)(()=>{
                _uavstore.useUAVStore.setState({
                    error: "Test error"
                });
            });
            expect(result.current.error).toBe("Test error");
            (0, _react.act)(()=>{
                result.current.clearError();
            });
            expect(result.current.error).toBeNull();
        });
    });
    describe("selectedUAV", ()=>{
        it("should set selected UAV", ()=>{
            const uav = (0, _testutils.createMockUAV)();
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            (0, _react.act)(()=>{
                result.current.setSelectedUAV(uav);
            });
            expect(result.current.selectedUAV).toEqual(uav);
        });
        it("should clear selected UAV", ()=>{
            const uav = (0, _testutils.createMockUAV)();
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            // Set UAV first
            (0, _react.act)(()=>{
                result.current.setSelectedUAV(uav);
            });
            expect(result.current.selectedUAV).toEqual(uav);
            // Clear UAV
            (0, _react.act)(()=>{
                result.current.setSelectedUAV(null);
            });
            expect(result.current.selectedUAV).toBeNull();
        });
    });
    describe("real-time updates", ()=>{
        it("should update UAV in store", ()=>{
            const existingUAV = (0, _testutils.createMockUAV)();
            const updatedUAV = {
                ...existingUAV,
                ownerName: "Updated Owner"
            };
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            // Set initial state
            (0, _react.act)(()=>{
                _uavstore.useUAVStore.setState({
                    uavs: [
                        existingUAV
                    ]
                });
            });
            // Update UAV
            (0, _react.act)(()=>{
                result.current.updateUAVInStore(updatedUAV);
            });
            expect(result.current.uavs[0].ownerName).toBe("Updated Owner");
        });
        it("should add new UAV if not exists", ()=>{
            const newUAV = (0, _testutils.createMockUAV)();
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            (0, _react.act)(()=>{
                result.current.updateUAVInStore(newUAV);
            });
            expect(result.current.uavs).toContain(newUAV);
        });
        it("should remove UAV from store", ()=>{
            const uav = (0, _testutils.createMockUAV)();
            const { result } = (0, _react.renderHook)(()=>(0, _uavstore.useUAVStore)());
            // Set initial state
            (0, _react.act)(()=>{
                _uavstore.useUAVStore.setState({
                    uavs: [
                        uav
                    ]
                });
            });
            // Remove UAV
            (0, _react.act)(()=>{
                result.current.removeUAVFromStore(uav.id);
            });
            expect(result.current.uavs).toHaveLength(0);
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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