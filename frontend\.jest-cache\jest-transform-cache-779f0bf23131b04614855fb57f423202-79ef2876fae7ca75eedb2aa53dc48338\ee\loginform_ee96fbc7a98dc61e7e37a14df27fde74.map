{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\auth\\login-form.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Shield, Eye, EyeOff, Loader2, AlertCircle } from 'lucide-react'\nimport { useAuthStore } from '@/stores/auth-store'\nimport { LoginRequest } from '@/types/auth'\n\nconst loginSchema = z.object({\n  username: z.string().min(1, 'Username is required'),\n  password: z.string().min(1, 'Password is required'),\n  rememberMe: z.boolean().optional(),\n})\n\ntype LoginFormData = z.infer<typeof loginSchema>\n\ninterface LoginFormProps {\n  redirectTo?: string\n  onSuccess?: () => void\n}\n\nexport function LoginForm({ redirectTo = '/dashboard', onSuccess }: LoginFormProps) {\n  const router = useRouter()\n  const { login, isLoading, error, clearError } = useAuthStore()\n  const [showPassword, setShowPassword] = useState(false)\n\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    watch,\n    formState: { errors },\n  } = useForm<LoginFormData>({\n    resolver: zodResolver(loginSchema),\n    defaultValues: {\n      username: '',\n      password: '',\n      rememberMe: false,\n    },\n  })\n\n  const onSubmit = async (data: LoginFormData) => {\n    clearError()\n    \n    const loginData: LoginRequest = {\n      username: data.username,\n      password: data.password,\n      rememberMe: data.rememberMe,\n    }\n\n    const success = await login(loginData)\n    \n    if (success) {\n      if (onSuccess) {\n        onSuccess()\n      } else {\n        router.push(redirectTo)\n      }\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <div className=\"flex justify-center mb-4\">\n            <div className=\"p-3 bg-primary/10 rounded-full\">\n              <Shield className=\"h-8 w-8 text-primary\" />\n            </div>\n          </div>\n          <CardTitle className=\"text-2xl font-orbitron\">UAV Control System</CardTitle>\n          <CardDescription>\n            Sign in to access the UAV management dashboard\n          </CardDescription>\n        </CardHeader>\n        \n        <CardContent>\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n            {error && (\n              <Alert variant=\"destructive\">\n                <AlertCircle className=\"h-4 w-4\" />\n                <AlertDescription>{error}</AlertDescription>\n              </Alert>\n            )}\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"username\">Username</Label>\n              <Input\n                id=\"username\"\n                type=\"text\"\n                placeholder=\"Enter your username\"\n                {...register('username')}\n                className={errors.username ? 'border-red-500' : ''}\n                disabled={isLoading}\n              />\n              {errors.username && (\n                <p className=\"text-sm text-red-500\">{errors.username.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">Password</Label>\n              <div className=\"relative\">\n                <Input\n                  id=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  placeholder=\"Enter your password\"\n                  {...register('password')}\n                  className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}\n                  disabled={isLoading}\n                />\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  disabled={isLoading}\n                >\n                  {showPassword ? (\n                    <EyeOff className=\"h-4 w-4 text-muted-foreground\" />\n                  ) : (\n                    <Eye className=\"h-4 w-4 text-muted-foreground\" />\n                  )}\n                </Button>\n              </div>\n              {errors.password && (\n                <p className=\"text-sm text-red-500\">{errors.password.message}</p>\n              )}\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id=\"rememberMe\"\n                  checked={watch('rememberMe')}\n                  onCheckedChange={(checked) => setValue('rememberMe', !!checked)}\n                  disabled={isLoading}\n                />\n                <Label htmlFor=\"rememberMe\" className=\"text-sm\">\n                  Remember me\n                </Label>\n              </div>\n              \n              <Link\n                href=\"/auth/forgot-password\"\n                className=\"text-sm text-primary hover:underline\"\n              >\n                Forgot password?\n              </Link>\n            </div>\n\n            <Button\n              type=\"submit\"\n              className=\"w-full\"\n              disabled={isLoading}\n            >\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                  Signing in...\n                </>\n              ) : (\n                'Sign In'\n              )}\n            </Button>\n          </form>\n\n          <div className=\"mt-6 text-center\">\n            <p className=\"text-sm text-muted-foreground\">\n              Don't have an account?{' '}\n              <Link\n                href=\"/auth/register\"\n                className=\"text-primary hover:underline font-medium\"\n              >\n                Sign up\n              </Link>\n            </p>\n          </div>\n\n          <div className=\"mt-4 pt-4 border-t\">\n            <div className=\"text-center\">\n              <p className=\"text-xs text-muted-foreground\">\n                Secure access to UAV management system\n              </p>\n              <div className=\"flex justify-center items-center mt-2 space-x-4 text-xs text-muted-foreground\">\n                <span>🔒 SSL Encrypted</span>\n                <span>🛡️ Multi-factor Auth</span>\n                <span>📊 Audit Logged</span>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": ["LoginForm", "loginSchema", "z", "object", "username", "string", "min", "password", "rememberMe", "boolean", "optional", "redirectTo", "onSuccess", "router", "useRouter", "login", "isLoading", "error", "clearError", "useAuthStore", "showPassword", "setShowPassword", "useState", "register", "handleSubmit", "setValue", "watch", "formState", "errors", "useForm", "resolver", "zodResolver", "defaultValues", "onSubmit", "data", "loginData", "success", "push", "div", "className", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Shield", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "form", "<PERSON><PERSON>", "variant", "AlertCircle", "AlertDescription", "Label", "htmlFor", "Input", "id", "type", "placeholder", "disabled", "p", "message", "<PERSON><PERSON>", "size", "onClick", "Eye<PERSON>ff", "Eye", "Checkbox", "checked", "onCheckedChange", "Link", "href", "Loader2", "span"], "mappings": "AAAA;;;;;+BA+BgBA;;;eAAAA;;;;+DA7BgB;4BACN;+BACF;qBACI;sBACV;6DACD;wBACM;uBACD;uBACA;0BACG;sBACiD;uBAClC;6BACkB;2BAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG7B,MAAMC,cAAcC,OAAC,CAACC,MAAM,CAAC;IAC3BC,UAAUF,OAAC,CAACG,MAAM,GAAGC,GAAG,CAAC,GAAG;IAC5BC,UAAUL,OAAC,CAACG,MAAM,GAAGC,GAAG,CAAC,GAAG;IAC5BE,YAAYN,OAAC,CAACO,OAAO,GAAGC,QAAQ;AAClC;AASO,SAASV,UAAU,EAAEW,aAAa,YAAY,EAAEC,SAAS,EAAkB;IAChF,MAAMC,SAASC,IAAAA,qBAAS;IACxB,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAE,GAAGC,IAAAA,uBAAY;IAC5D,MAAM,CAACC,cAAcC,gBAAgB,GAAGC,IAAAA,eAAQ,EAAC;IAEjD,MAAM,EACJC,QAAQ,EACRC,YAAY,EACZC,QAAQ,EACRC,KAAK,EACLC,WAAW,EAAEC,MAAM,EAAE,EACtB,GAAGC,IAAAA,sBAAO,EAAgB;QACzBC,UAAUC,IAAAA,gBAAW,EAAC9B;QACtB+B,eAAe;YACb5B,UAAU;YACVG,UAAU;YACVC,YAAY;QACd;IACF;IAEA,MAAMyB,WAAW,OAAOC;QACtBhB;QAEA,MAAMiB,YAA0B;YAC9B/B,UAAU8B,KAAK9B,QAAQ;YACvBG,UAAU2B,KAAK3B,QAAQ;YACvBC,YAAY0B,KAAK1B,UAAU;QAC7B;QAEA,MAAM4B,UAAU,MAAMrB,MAAMoB;QAE5B,IAAIC,SAAS;YACX,IAAIxB,WAAW;gBACbA;YACF,OAAO;gBACLC,OAAOwB,IAAI,CAAC1B;YACd;QACF;IACF;IAEA,qBACE,qBAAC2B;QAAIC,WAAU;kBACb,cAAA,sBAACC,UAAI;YAACD,WAAU;;8BACd,sBAACE,gBAAU;oBAACF,WAAU;;sCACpB,qBAACD;4BAAIC,WAAU;sCACb,cAAA,qBAACD;gCAAIC,WAAU;0CACb,cAAA,qBAACG,mBAAM;oCAACH,WAAU;;;;sCAGtB,qBAACI,eAAS;4BAACJ,WAAU;sCAAyB;;sCAC9C,qBAACK,qBAAe;sCAAC;;;;8BAKnB,sBAACC,iBAAW;;sCACV,sBAACC;4BAAKb,UAAUT,aAAaS;4BAAWM,WAAU;;gCAC/CtB,uBACC,sBAAC8B,YAAK;oCAACC,SAAQ;;sDACb,qBAACC,wBAAW;4CAACV,WAAU;;sDACvB,qBAACW,uBAAgB;sDAAEjC;;;;8CAIvB,sBAACqB;oCAAIC,WAAU;;sDACb,qBAACY,YAAK;4CAACC,SAAQ;sDAAW;;sDAC1B,qBAACC,YAAK;4CACJC,IAAG;4CACHC,MAAK;4CACLC,aAAY;4CACX,GAAGjC,SAAS,WAAW;4CACxBgB,WAAWX,OAAOxB,QAAQ,GAAG,mBAAmB;4CAChDqD,UAAUzC;;wCAEXY,OAAOxB,QAAQ,kBACd,qBAACsD;4CAAEnB,WAAU;sDAAwBX,OAAOxB,QAAQ,CAACuD,OAAO;;;;8CAIhE,sBAACrB;oCAAIC,WAAU;;sDACb,qBAACY,YAAK;4CAACC,SAAQ;sDAAW;;sDAC1B,sBAACd;4CAAIC,WAAU;;8DACb,qBAACc,YAAK;oDACJC,IAAG;oDACHC,MAAMnC,eAAe,SAAS;oDAC9BoC,aAAY;oDACX,GAAGjC,SAAS,WAAW;oDACxBgB,WAAWX,OAAOrB,QAAQ,GAAG,yBAAyB;oDACtDkD,UAAUzC;;8DAEZ,qBAAC4C,cAAM;oDACLL,MAAK;oDACLP,SAAQ;oDACRa,MAAK;oDACLtB,WAAU;oDACVuB,SAAS,IAAMzC,gBAAgB,CAACD;oDAChCqC,UAAUzC;8DAETI,6BACC,qBAAC2C,mBAAM;wDAACxB,WAAU;uEAElB,qBAACyB,gBAAG;wDAACzB,WAAU;;;;;wCAIpBX,OAAOrB,QAAQ,kBACd,qBAACmD;4CAAEnB,WAAU;sDAAwBX,OAAOrB,QAAQ,CAACoD,OAAO;;;;8CAIhE,sBAACrB;oCAAIC,WAAU;;sDACb,sBAACD;4CAAIC,WAAU;;8DACb,qBAAC0B,kBAAQ;oDACPX,IAAG;oDACHY,SAASxC,MAAM;oDACfyC,iBAAiB,CAACD,UAAYzC,SAAS,cAAc,CAAC,CAACyC;oDACvDT,UAAUzC;;8DAEZ,qBAACmC,YAAK;oDAACC,SAAQ;oDAAab,WAAU;8DAAU;;;;sDAKlD,qBAAC6B,aAAI;4CACHC,MAAK;4CACL9B,WAAU;sDACX;;;;8CAKH,qBAACqB,cAAM;oCACLL,MAAK;oCACLhB,WAAU;oCACVkB,UAAUzC;8CAETA,0BACC;;0DACE,qBAACsD,oBAAO;gDAAC/B,WAAU;;4CAA8B;;yCAInD;;;;sCAKN,qBAACD;4BAAIC,WAAU;sCACb,cAAA,sBAACmB;gCAAEnB,WAAU;;oCAAgC;oCACpB;kDACvB,qBAAC6B,aAAI;wCACHC,MAAK;wCACL9B,WAAU;kDACX;;;;;sCAML,qBAACD;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACmB;wCAAEnB,WAAU;kDAAgC;;kDAG7C,sBAACD;wCAAIC,WAAU;;0DACb,qBAACgC;0DAAK;;0DACN,qBAACA;0DAAK;;0DACN,qBAACA;0DAAK;;;;;;;;;;;;AAQtB"}