{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\features\\dashboard\\__tests__\\realtime-alerts.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { RealtimeAlerts } from '../realtime-alerts'\nimport { useDashboardStore } from '@/stores/dashboard-store'\nimport { createMockAlert } from '@/lib/test-utils'\nimport { SystemAlert } from '@/types/uav'\n\n// Mock the dashboard store\njest.mock('@/stores/dashboard-store')\nconst mockUseDashboardStore = useDashboardStore as jest.MockedFunction<typeof useDashboardStore>\n\n// Mock framer-motion\njest.mock('framer-motion', () => ({\n  motion: {\n    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,\n    li: ({ children, ...props }: any) => <li {...props}>{children}</li>,\n  },\n  AnimatePresence: ({ children }: any) => children,\n}))\n\n// Mock animated components\njest.mock('@/components/ui/animated-alert', () => ({\n  AnimatedAlert: ({ children, ...props }: any) => (\n    <div data-testid=\"animated-alert\" {...props}>\n      {children}\n    </div>\n  ),\n  RealtimeAlerts: ({ children }: any) => (\n    <div data-testid=\"realtime-alerts-container\">\n      {children}\n    </div>\n  ),\n}))\n\njest.mock('@/components/ui/animated-components', () => ({\n  StaggerContainer: ({ children }: any) => (\n    <div data-testid=\"stagger-container\">{children}</div>\n  ),\n  StaggerItem: ({ children }: any) => (\n    <div data-testid=\"stagger-item\">{children}</div>\n  ),\n}))\n\ndescribe('RealtimeAlerts Component', () => {\n  const mockAlerts: SystemAlert[] = [\n    createMockAlert({\n      id: 1,\n      type: 'ERROR',\n      title: 'Critical Battery',\n      message: 'UAV-001 battery level is critically low (5%)',\n      severity: 'HIGH',\n      acknowledged: false,\n      timestamp: '2024-01-01T10:00:00Z',\n    }),\n    createMockAlert({\n      id: 2,\n      type: 'WARNING',\n      title: 'Maintenance Required',\n      message: 'UAV-002 requires scheduled maintenance',\n      severity: 'MEDIUM',\n      acknowledged: false,\n      timestamp: '2024-01-01T09:30:00Z',\n    }),\n    createMockAlert({\n      id: 3,\n      type: 'INFO',\n      title: 'Flight Completed',\n      message: 'UAV-003 has successfully completed mission Alpha',\n      severity: 'LOW',\n      acknowledged: true,\n      timestamp: '2024-01-01T09:00:00Z',\n    }),\n  ]\n\n  const mockAcknowledgeAlert = jest.fn()\n  const mockRemoveAlert = jest.fn()\n  const mockClearAlerts = jest.fn()\n  const mockToggleAlerts = jest.fn()\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n\n    mockUseDashboardStore.mockReturnValue({\n      alerts: mockAlerts,\n      showAlerts: true,\n      acknowledgeAlert: mockAcknowledgeAlert,\n      removeAlert: mockRemoveAlert,\n      clearAlerts: mockClearAlerts,\n      toggleAlerts: mockToggleAlerts,\n      getAlertCounts: jest.fn(() => ({\n        total: 3,\n        error: 1,\n        warning: 1,\n        info: 1,\n        high: 1,\n        medium: 1,\n        low: 1,\n      })),\n      getUnacknowledgedAlerts: jest.fn(() => mockAlerts.filter(a => !a.acknowledged)),\n      // Other store properties\n      metrics: null,\n      flightActivity: null,\n      batteryStats: null,\n      hibernatePodMetrics: null,\n      chartData: null,\n      recentLocationUpdates: [],\n      isConnected: true,\n      lastUpdate: null,\n      connectionError: null,\n      selectedTimeRange: '24h',\n      autoRefresh: true,\n      refreshInterval: 30,\n      updateMetrics: jest.fn(),\n      updateFlightActivity: jest.fn(),\n      updateBatteryStats: jest.fn(),\n      updateHibernatePodMetrics: jest.fn(),\n      updateChartData: jest.fn(),\n      addAlert: jest.fn(),\n      addLocationUpdate: jest.fn(),\n      setConnectionStatus: jest.fn(),\n      clearConnectionError: jest.fn(),\n      setTimeRange: jest.fn(),\n      toggleAutoRefresh: jest.fn(),\n      setRefreshInterval: jest.fn(),\n      resetData: jest.fn(),\n    })\n  })\n\n  it('renders correctly', () => {\n    render(<RealtimeAlerts />)\n    \n    expect(screen.getByText('Real-time Alerts')).toBeInTheDocument()\n    expect(screen.getByText('System notifications and warnings')).toBeInTheDocument()\n    expect(screen.getByTestId('stagger-container')).toBeInTheDocument()\n  })\n\n  it('displays all alerts', () => {\n    render(<RealtimeAlerts />)\n    \n    expect(screen.getByText('Critical Battery')).toBeInTheDocument()\n    expect(screen.getByText('Maintenance Required')).toBeInTheDocument()\n    expect(screen.getByText('Flight Completed')).toBeInTheDocument()\n  })\n\n  it('shows alert count in header', () => {\n    render(<RealtimeAlerts />)\n    \n    expect(screen.getByText('3')).toBeInTheDocument() // Total alert count\n  })\n\n  it('displays different alert types with correct styling', () => {\n    render(<RealtimeAlerts />)\n    \n    const errorAlert = screen.getByText('Critical Battery').closest('[data-testid=\"stagger-item\"]')\n    const warningAlert = screen.getByText('Maintenance Required').closest('[data-testid=\"stagger-item\"]')\n    const infoAlert = screen.getByText('Flight Completed').closest('[data-testid=\"stagger-item\"]')\n    \n    expect(errorAlert).toBeInTheDocument()\n    expect(warningAlert).toBeInTheDocument()\n    expect(infoAlert).toBeInTheDocument()\n  })\n\n  it('handles alert acknowledgment', () => {\n    render(<RealtimeAlerts />)\n    \n    const acknowledgeButton = screen.getAllByRole('button', { name: /acknowledge/i })[0]\n    fireEvent.click(acknowledgeButton)\n    \n    expect(mockAcknowledgeAlert).toHaveBeenCalledWith(1)\n  })\n\n  it('handles alert removal', () => {\n    render(<RealtimeAlerts />)\n    \n    const removeButton = screen.getAllByRole('button', { name: /remove|dismiss/i })[0]\n    fireEvent.click(removeButton)\n    \n    expect(mockRemoveAlert).toHaveBeenCalledWith(1)\n  })\n\n  it('handles clear all alerts', () => {\n    render(<RealtimeAlerts />)\n    \n    const clearAllButton = screen.getByRole('button', { name: /clear all/i })\n    fireEvent.click(clearAllButton)\n    \n    expect(mockClearAlerts).toHaveBeenCalled()\n  })\n\n  it('toggles alerts visibility', () => {\n    render(<RealtimeAlerts />)\n    \n    const toggleButton = screen.getByRole('button', { name: /toggle alerts/i })\n    fireEvent.click(toggleButton)\n    \n    expect(mockToggleAlerts).toHaveBeenCalled()\n  })\n\n  it('hides alerts when showAlerts is false', () => {\n    mockUseDashboardStore.mockReturnValue({\n      ...mockUseDashboardStore(),\n      showAlerts: false,\n    })\n    \n    render(<RealtimeAlerts />)\n    \n    expect(screen.queryByText('Critical Battery')).not.toBeInTheDocument()\n    expect(screen.queryByText('Maintenance Required')).not.toBeInTheDocument()\n  })\n\n  it('displays empty state when no alerts', () => {\n    mockUseDashboardStore.mockReturnValue({\n      ...mockUseDashboardStore(),\n      alerts: [],\n    })\n    \n    render(<RealtimeAlerts />)\n    \n    expect(screen.getByText(/no alerts/i)).toBeInTheDocument()\n    expect(screen.getByText(/all systems are running normally/i)).toBeInTheDocument()\n  })\n\n  it('filters alerts by type', () => {\n    render(<RealtimeAlerts />)\n    \n    const errorFilter = screen.getByRole('button', { name: /error/i })\n    fireEvent.click(errorFilter)\n    \n    expect(screen.getByText('Critical Battery')).toBeInTheDocument()\n    expect(screen.queryByText('Maintenance Required')).not.toBeInTheDocument()\n    expect(screen.queryByText('Flight Completed')).not.toBeInTheDocument()\n  })\n\n  it('filters alerts by severity', () => {\n    render(<RealtimeAlerts />)\n    \n    const highSeverityFilter = screen.getByRole('button', { name: /high/i })\n    fireEvent.click(highSeverityFilter)\n    \n    expect(screen.getByText('Critical Battery')).toBeInTheDocument()\n    expect(screen.queryByText('Maintenance Required')).not.toBeInTheDocument()\n    expect(screen.queryByText('Flight Completed')).not.toBeInTheDocument()\n  })\n\n  it('shows only unacknowledged alerts when filter is applied', () => {\n    render(<RealtimeAlerts />)\n    \n    const unacknowledgedFilter = screen.getByRole('button', { name: /unacknowledged/i })\n    fireEvent.click(unacknowledgedFilter)\n    \n    expect(screen.getByText('Critical Battery')).toBeInTheDocument()\n    expect(screen.getByText('Maintenance Required')).toBeInTheDocument()\n    expect(screen.queryByText('Flight Completed')).not.toBeInTheDocument()\n  })\n\n  it('displays alert timestamps', () => {\n    render(<RealtimeAlerts />)\n    \n    // Timestamps would be formatted and displayed\n    expect(screen.getByText(/10:00/)).toBeInTheDocument()\n    expect(screen.getByText(/09:30/)).toBeInTheDocument()\n    expect(screen.getByText(/09:00/)).toBeInTheDocument()\n  })\n\n  it('shows alert severity badges', () => {\n    render(<RealtimeAlerts />)\n    \n    expect(screen.getByText('HIGH')).toBeInTheDocument()\n    expect(screen.getByText('MEDIUM')).toBeInTheDocument()\n    expect(screen.getByText('LOW')).toBeInTheDocument()\n  })\n\n  it('handles real-time alert updates', async () => {\n    const { rerender } = render(<RealtimeAlerts />)\n    \n    const newAlert = createMockAlert({\n      id: 4,\n      type: 'ERROR',\n      title: 'Connection Lost',\n      message: 'Lost connection to UAV-004',\n      severity: 'HIGH',\n      acknowledged: false,\n      timestamp: new Date().toISOString(),\n    })\n    \n    mockUseDashboardStore.mockReturnValue({\n      ...mockUseDashboardStore(),\n      alerts: [...mockAlerts, newAlert],\n    })\n    \n    rerender(<RealtimeAlerts />)\n    \n    await waitFor(() => {\n      expect(screen.getByText('Connection Lost')).toBeInTheDocument()\n    })\n  })\n\n  it('sorts alerts by timestamp (newest first)', () => {\n    render(<RealtimeAlerts />)\n    \n    const alertTitles = screen.getAllByRole('heading', { level: 4 })\n    expect(alertTitles[0]).toHaveTextContent('Critical Battery') // 10:00\n    expect(alertTitles[1]).toHaveTextContent('Maintenance Required') // 09:30\n    expect(alertTitles[2]).toHaveTextContent('Flight Completed') // 09:00\n  })\n\n  it('limits displayed alerts to maximum count', () => {\n    const manyAlerts = Array.from({ length: 25 }, (_, i) => \n      createMockAlert({\n        id: i + 1,\n        title: `Alert ${i + 1}`,\n        timestamp: new Date(Date.now() - i * 60000).toISOString(),\n      })\n    )\n    \n    mockUseDashboardStore.mockReturnValue({\n      ...mockUseDashboardStore(),\n      alerts: manyAlerts,\n    })\n    \n    render(<RealtimeAlerts maxAlerts={20} />)\n    \n    const alertItems = screen.getAllByTestId('stagger-item')\n    expect(alertItems).toHaveLength(20)\n  })\n\n  it('shows load more button when there are more alerts', () => {\n    const manyAlerts = Array.from({ length: 25 }, (_, i) => \n      createMockAlert({\n        id: i + 1,\n        title: `Alert ${i + 1}`,\n      })\n    )\n    \n    mockUseDashboardStore.mockReturnValue({\n      ...mockUseDashboardStore(),\n      alerts: manyAlerts,\n    })\n    \n    render(<RealtimeAlerts maxAlerts={20} />)\n    \n    expect(screen.getByRole('button', { name: /load more/i })).toBeInTheDocument()\n  })\n\n  it('handles alert search', () => {\n    render(<RealtimeAlerts />)\n    \n    const searchInput = screen.getByPlaceholderText(/search alerts/i)\n    fireEvent.change(searchInput, { target: { value: 'battery' } })\n    \n    expect(screen.getByText('Critical Battery')).toBeInTheDocument()\n    expect(screen.queryByText('Maintenance Required')).not.toBeInTheDocument()\n    expect(screen.queryByText('Flight Completed')).not.toBeInTheDocument()\n  })\n\n  it('supports keyboard navigation', () => {\n    render(<RealtimeAlerts />)\n    \n    const firstAlert = screen.getByText('Critical Battery').closest('[data-testid=\"stagger-item\"]')\n    const acknowledgeButton = screen.getAllByRole('button', { name: /acknowledge/i })[0]\n    \n    acknowledgeButton.focus()\n    expect(acknowledgeButton).toHaveFocus()\n    \n    fireEvent.keyDown(acknowledgeButton, { key: 'Enter' })\n    expect(mockAcknowledgeAlert).toHaveBeenCalledWith(1)\n  })\n\n  it('shows connection status indicator', () => {\n    render(<RealtimeAlerts />)\n    \n    expect(screen.getByTestId('connection-indicator')).toBeInTheDocument()\n    expect(screen.getByText(/connected/i)).toBeInTheDocument()\n  })\n\n  it('handles disconnected state', () => {\n    mockUseDashboardStore.mockReturnValue({\n      ...mockUseDashboardStore(),\n      isConnected: false,\n      connectionError: 'Connection lost',\n    })\n    \n    render(<RealtimeAlerts />)\n    \n    expect(screen.getByText(/disconnected/i)).toBeInTheDocument()\n    expect(screen.getByText('Connection lost')).toBeInTheDocument()\n  })\n\n  it('auto-refreshes alerts when connected', () => {\n    jest.useFakeTimers()\n    \n    render(<RealtimeAlerts autoRefresh={true} refreshInterval={5000} />)\n    \n    // Fast-forward time\n    jest.advanceTimersByTime(5000)\n    \n    // Auto-refresh would trigger store updates\n    expect(screen.getByTestId('stagger-container')).toBeInTheDocument()\n    \n    jest.useRealTimers()\n  })\n})\n"], "names": ["jest", "mock", "motion", "div", "children", "props", "li", "AnimatePresence", "<PERSON><PERSON><PERSON><PERSON>", "data-testid", "RealtimeAlerts", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "StaggerItem", "mockUseDashboardStore", "useDashboardStore", "describe", "mock<PERSON>ler<PERSON>", "createMockAlert", "id", "type", "title", "message", "severity", "acknowledged", "timestamp", "mockAcknowledgeAlert", "fn", "mockRemoveAlert", "mockClearAlerts", "mockToggleAlerts", "beforeEach", "clearAllMocks", "mockReturnValue", "alerts", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON><PERSON><PERSON>", "getAlertCounts", "total", "error", "warning", "info", "high", "medium", "low", "getUnacknowledgedAlerts", "filter", "a", "metrics", "flightActivity", "batteryStats", "hibernatePodMetrics", "chartData", "recentLocationUpdates", "isConnected", "lastUpdate", "connectionError", "selectedTimeRange", "autoRefresh", "refreshInterval", "updateMetrics", "updateFlightActivity", "updateBatteryStats", "updateHibernatePodMetrics", "updateChartData", "add<PERSON><PERSON><PERSON>", "addLocationUpdate", "setConnectionStatus", "clearConnectionError", "setTimeRange", "toggleAutoRefresh", "setRefreshInterval", "resetData", "it", "render", "expect", "screen", "getByText", "toBeInTheDocument", "getByTestId", "<PERSON><PERSON><PERSON><PERSON>", "closest", "warningAlert", "infoAlert", "acknowledge<PERSON><PERSON>on", "getAllByRole", "name", "fireEvent", "click", "toHaveBeenCalledWith", "removeButton", "clearAllButton", "getByRole", "toHaveBeenCalled", "to<PERSON><PERSON><PERSON><PERSON>", "queryByText", "not", "errorFilter", "highSeverityFilter", "unacknowledgedFilter", "rerender", "<PERSON><PERSON><PERSON><PERSON>", "Date", "toISOString", "waitFor", "alertTitles", "level", "toHaveTextContent", "manyAlerts", "Array", "from", "length", "_", "i", "now", "max<PERSON><PERSON><PERSON>", "alertItems", "getAllByTestId", "toHave<PERSON>ength", "searchInput", "getByPlaceholderText", "change", "target", "value", "first<PERSON><PERSON><PERSON>", "focus", "toHaveFocus", "keyDown", "key", "useFakeTimers", "advanceTimersByTime", "useRealTimers"], "mappings": ";AAOA,2BAA2B;AAC3BA,KAAKC,IAAI,CAAC;AAGV,qBAAqB;AACrBD,KAAKC,IAAI,CAAC,iBAAiB,IAAO,CAAA;QAChCC,QAAQ;YACNC,KAAK,CAAC,EAAEC,QAAQ,EAAE,GAAGC,OAAY,iBAAK,qBAACF;oBAAK,GAAGE,KAAK;8BAAGD;;YACvDE,IAAI,CAAC,EAAEF,QAAQ,EAAE,GAAGC,OAAY,iBAAK,qBAACC;oBAAI,GAAGD,KAAK;8BAAGD;;QACvD;QACAG,iBAAiB,CAAC,EAAEH,QAAQ,EAAO,GAAKA;IAC1C,CAAA;AAEA,2BAA2B;AAC3BJ,KAAKC,IAAI,CAAC,kCAAkC,IAAO,CAAA;QACjDO,eAAe,CAAC,EAAEJ,QAAQ,EAAE,GAAGC,OAAY,iBACzC,qBAACF;gBAAIM,eAAY;gBAAkB,GAAGJ,KAAK;0BACxCD;;QAGLM,gBAAgB,CAAC,EAAEN,QAAQ,EAAO,iBAChC,qBAACD;gBAAIM,eAAY;0BACdL;;IAGP,CAAA;AAEAJ,KAAKC,IAAI,CAAC,uCAAuC,IAAO,CAAA;QACtDU,kBAAkB,CAAC,EAAEP,QAAQ,EAAO,iBAClC,qBAACD;gBAAIM,eAAY;0BAAqBL;;QAExCQ,aAAa,CAAC,EAAER,QAAQ,EAAO,iBAC7B,qBAACD;gBAAIM,eAAY;0BAAgBL;;IAErC,CAAA;;;;;8DAzCkB;2BACiC;gCACpB;gCACG;;;;;;AAMlC,MAAMS,wBAAwBC,iCAAiB;AAkC/CC,SAAS,4BAA4B;IACnC,MAAMC,aAA4B;QAChCC,IAAAA,0BAAe,EAAC;YACdC,IAAI;YACJC,MAAM;YACNC,OAAO;YACPC,SAAS;YACTC,UAAU;YACVC,cAAc;YACdC,WAAW;QACb;QACAP,IAAAA,0BAAe,EAAC;YACdC,IAAI;YACJC,MAAM;YACNC,OAAO;YACPC,SAAS;YACTC,UAAU;YACVC,cAAc;YACdC,WAAW;QACb;QACAP,IAAAA,0BAAe,EAAC;YACdC,IAAI;YACJC,MAAM;YACNC,OAAO;YACPC,SAAS;YACTC,UAAU;YACVC,cAAc;YACdC,WAAW;QACb;KACD;IAED,MAAMC,uBAAuBzB,KAAK0B,EAAE;IACpC,MAAMC,kBAAkB3B,KAAK0B,EAAE;IAC/B,MAAME,kBAAkB5B,KAAK0B,EAAE;IAC/B,MAAMG,mBAAmB7B,KAAK0B,EAAE;IAEhCI,WAAW;QACT9B,KAAK+B,aAAa;QAElBlB,sBAAsBmB,eAAe,CAAC;YACpCC,QAAQjB;YACRkB,YAAY;YACZC,kBAAkBV;YAClBW,aAAaT;YACbU,aAAaT;YACbU,cAAcT;YACdU,gBAAgBvC,KAAK0B,EAAE,CAAC,IAAO,CAAA;oBAC7Bc,OAAO;oBACPC,OAAO;oBACPC,SAAS;oBACTC,MAAM;oBACNC,MAAM;oBACNC,QAAQ;oBACRC,KAAK;gBACP,CAAA;YACAC,yBAAyB/C,KAAK0B,EAAE,CAAC,IAAMV,WAAWgC,MAAM,CAACC,CAAAA,IAAK,CAACA,EAAE1B,YAAY;YAC7E,yBAAyB;YACzB2B,SAAS;YACTC,gBAAgB;YAChBC,cAAc;YACdC,qBAAqB;YACrBC,WAAW;YACXC,uBAAuB,EAAE;YACzBC,aAAa;YACbC,YAAY;YACZC,iBAAiB;YACjBC,mBAAmB;YACnBC,aAAa;YACbC,iBAAiB;YACjBC,eAAe9D,KAAK0B,EAAE;YACtBqC,sBAAsB/D,KAAK0B,EAAE;YAC7BsC,oBAAoBhE,KAAK0B,EAAE;YAC3BuC,2BAA2BjE,KAAK0B,EAAE;YAClCwC,iBAAiBlE,KAAK0B,EAAE;YACxByC,UAAUnE,KAAK0B,EAAE;YACjB0C,mBAAmBpE,KAAK0B,EAAE;YAC1B2C,qBAAqBrE,KAAK0B,EAAE;YAC5B4C,sBAAsBtE,KAAK0B,EAAE;YAC7B6C,cAAcvE,KAAK0B,EAAE;YACrB8C,mBAAmBxE,KAAK0B,EAAE;YAC1B+C,oBAAoBzE,KAAK0B,EAAE;YAC3BgD,WAAW1E,KAAK0B,EAAE;QACpB;IACF;IAEAiD,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtBmE,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;QAC9DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,sCAAsCC,iBAAiB;QAC/EH,OAAOC,iBAAM,CAACG,WAAW,CAAC,sBAAsBD,iBAAiB;IACnE;IAEAL,GAAG,uBAAuB;QACxBC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtBmE,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;QAC9DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,yBAAyBC,iBAAiB;QAClEH,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;IAChE;IAEAL,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtBmE,OAAOC,iBAAM,CAACC,SAAS,CAAC,MAAMC,iBAAiB,GAAG,oBAAoB;;IACxE;IAEAL,GAAG,uDAAuD;QACxDC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,MAAMwE,aAAaJ,iBAAM,CAACC,SAAS,CAAC,oBAAoBI,OAAO,CAAC;QAChE,MAAMC,eAAeN,iBAAM,CAACC,SAAS,CAAC,wBAAwBI,OAAO,CAAC;QACtE,MAAME,YAAYP,iBAAM,CAACC,SAAS,CAAC,oBAAoBI,OAAO,CAAC;QAE/DN,OAAOK,YAAYF,iBAAiB;QACpCH,OAAOO,cAAcJ,iBAAiB;QACtCH,OAAOQ,WAAWL,iBAAiB;IACrC;IAEAL,GAAG,gCAAgC;QACjCC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,MAAM4E,oBAAoBR,iBAAM,CAACS,YAAY,CAAC,UAAU;YAAEC,MAAM;QAAe,EAAE,CAAC,EAAE;QACpFC,oBAAS,CAACC,KAAK,CAACJ;QAEhBT,OAAOpD,sBAAsBkE,oBAAoB,CAAC;IACpD;IAEAhB,GAAG,yBAAyB;QAC1BC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,MAAMkF,eAAed,iBAAM,CAACS,YAAY,CAAC,UAAU;YAAEC,MAAM;QAAkB,EAAE,CAAC,EAAE;QAClFC,oBAAS,CAACC,KAAK,CAACE;QAEhBf,OAAOlD,iBAAiBgE,oBAAoB,CAAC;IAC/C;IAEAhB,GAAG,4BAA4B;QAC7BC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,MAAMmF,iBAAiBf,iBAAM,CAACgB,SAAS,CAAC,UAAU;YAAEN,MAAM;QAAa;QACvEC,oBAAS,CAACC,KAAK,CAACG;QAEhBhB,OAAOjD,iBAAiBmE,gBAAgB;IAC1C;IAEApB,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,MAAMsF,eAAelB,iBAAM,CAACgB,SAAS,CAAC,UAAU;YAAEN,MAAM;QAAiB;QACzEC,oBAAS,CAACC,KAAK,CAACM;QAEhBnB,OAAOhD,kBAAkBkE,gBAAgB;IAC3C;IAEApB,GAAG,yCAAyC;QAC1C9D,sBAAsBmB,eAAe,CAAC;YACpC,GAAGnB,uBAAuB;YAC1BqB,YAAY;QACd;QAEA0C,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtBmE,OAAOC,iBAAM,CAACmB,WAAW,CAAC,qBAAqBC,GAAG,CAAClB,iBAAiB;QACpEH,OAAOC,iBAAM,CAACmB,WAAW,CAAC,yBAAyBC,GAAG,CAAClB,iBAAiB;IAC1E;IAEAL,GAAG,uCAAuC;QACxC9D,sBAAsBmB,eAAe,CAAC;YACpC,GAAGnB,uBAAuB;YAC1BoB,QAAQ,EAAE;QACZ;QAEA2C,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtBmE,OAAOC,iBAAM,CAACC,SAAS,CAAC,eAAeC,iBAAiB;QACxDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,sCAAsCC,iBAAiB;IACjF;IAEAL,GAAG,0BAA0B;QAC3BC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,MAAMyF,cAAcrB,iBAAM,CAACgB,SAAS,CAAC,UAAU;YAAEN,MAAM;QAAS;QAChEC,oBAAS,CAACC,KAAK,CAACS;QAEhBtB,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;QAC9DH,OAAOC,iBAAM,CAACmB,WAAW,CAAC,yBAAyBC,GAAG,CAAClB,iBAAiB;QACxEH,OAAOC,iBAAM,CAACmB,WAAW,CAAC,qBAAqBC,GAAG,CAAClB,iBAAiB;IACtE;IAEAL,GAAG,8BAA8B;QAC/BC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,MAAM0F,qBAAqBtB,iBAAM,CAACgB,SAAS,CAAC,UAAU;YAAEN,MAAM;QAAQ;QACtEC,oBAAS,CAACC,KAAK,CAACU;QAEhBvB,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;QAC9DH,OAAOC,iBAAM,CAACmB,WAAW,CAAC,yBAAyBC,GAAG,CAAClB,iBAAiB;QACxEH,OAAOC,iBAAM,CAACmB,WAAW,CAAC,qBAAqBC,GAAG,CAAClB,iBAAiB;IACtE;IAEAL,GAAG,2DAA2D;QAC5DC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,MAAM2F,uBAAuBvB,iBAAM,CAACgB,SAAS,CAAC,UAAU;YAAEN,MAAM;QAAkB;QAClFC,oBAAS,CAACC,KAAK,CAACW;QAEhBxB,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;QAC9DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,yBAAyBC,iBAAiB;QAClEH,OAAOC,iBAAM,CAACmB,WAAW,CAAC,qBAAqBC,GAAG,CAAClB,iBAAiB;IACtE;IAEAL,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,8CAA8C;QAC9CmE,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;QACnDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;QACnDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;IACrD;IAEAL,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtBmE,OAAOC,iBAAM,CAACC,SAAS,CAAC,SAASC,iBAAiB;QAClDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,WAAWC,iBAAiB;QACpDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,QAAQC,iBAAiB;IACnD;IAEAL,GAAG,mCAAmC;QACpC,MAAM,EAAE2B,QAAQ,EAAE,GAAG1B,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAE3C,MAAM6F,WAAWtF,IAAAA,0BAAe,EAAC;YAC/BC,IAAI;YACJC,MAAM;YACNC,OAAO;YACPC,SAAS;YACTC,UAAU;YACVC,cAAc;YACdC,WAAW,IAAIgF,OAAOC,WAAW;QACnC;QAEA5F,sBAAsBmB,eAAe,CAAC;YACpC,GAAGnB,uBAAuB;YAC1BoB,QAAQ;mBAAIjB;gBAAYuF;aAAS;QACnC;QAEAD,uBAAS,qBAAC5F,8BAAc;QAExB,MAAMgG,IAAAA,kBAAO,EAAC;YACZ7B,OAAOC,iBAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;QAC/D;IACF;IAEAL,GAAG,4CAA4C;QAC7CC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,MAAMiG,cAAc7B,iBAAM,CAACS,YAAY,CAAC,WAAW;YAAEqB,OAAO;QAAE;QAC9D/B,OAAO8B,WAAW,CAAC,EAAE,EAAEE,iBAAiB,CAAC,oBAAoB,QAAQ;;QACrEhC,OAAO8B,WAAW,CAAC,EAAE,EAAEE,iBAAiB,CAAC,wBAAwB,QAAQ;;QACzEhC,OAAO8B,WAAW,CAAC,EAAE,EAAEE,iBAAiB,CAAC,oBAAoB,QAAQ;;IACvE;IAEAlC,GAAG,4CAA4C;QAC7C,MAAMmC,aAAaC,MAAMC,IAAI,CAAC;YAAEC,QAAQ;QAAG,GAAG,CAACC,GAAGC,IAChDlG,IAAAA,0BAAe,EAAC;gBACdC,IAAIiG,IAAI;gBACR/F,OAAO,CAAC,MAAM,EAAE+F,IAAI,EAAE,CAAC;gBACvB3F,WAAW,IAAIgF,KAAKA,KAAKY,GAAG,KAAKD,IAAI,OAAOV,WAAW;YACzD;QAGF5F,sBAAsBmB,eAAe,CAAC;YACpC,GAAGnB,uBAAuB;YAC1BoB,QAAQ6E;QACV;QAEAlC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;YAAC2G,WAAW;;QAElC,MAAMC,aAAaxC,iBAAM,CAACyC,cAAc,CAAC;QACzC1C,OAAOyC,YAAYE,YAAY,CAAC;IAClC;IAEA7C,GAAG,qDAAqD;QACtD,MAAMmC,aAAaC,MAAMC,IAAI,CAAC;YAAEC,QAAQ;QAAG,GAAG,CAACC,GAAGC,IAChDlG,IAAAA,0BAAe,EAAC;gBACdC,IAAIiG,IAAI;gBACR/F,OAAO,CAAC,MAAM,EAAE+F,IAAI,EAAE,CAAC;YACzB;QAGFtG,sBAAsBmB,eAAe,CAAC;YACpC,GAAGnB,uBAAuB;YAC1BoB,QAAQ6E;QACV;QAEAlC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;YAAC2G,WAAW;;QAElCxC,OAAOC,iBAAM,CAACgB,SAAS,CAAC,UAAU;YAAEN,MAAM;QAAa,IAAIR,iBAAiB;IAC9E;IAEAL,GAAG,wBAAwB;QACzBC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,MAAM+G,cAAc3C,iBAAM,CAAC4C,oBAAoB,CAAC;QAChDjC,oBAAS,CAACkC,MAAM,CAACF,aAAa;YAAEG,QAAQ;gBAAEC,OAAO;YAAU;QAAE;QAE7DhD,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;QAC9DH,OAAOC,iBAAM,CAACmB,WAAW,CAAC,yBAAyBC,GAAG,CAAClB,iBAAiB;QACxEH,OAAOC,iBAAM,CAACmB,WAAW,CAAC,qBAAqBC,GAAG,CAAClB,iBAAiB;IACtE;IAEAL,GAAG,gCAAgC;QACjCC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtB,MAAMoH,aAAahD,iBAAM,CAACC,SAAS,CAAC,oBAAoBI,OAAO,CAAC;QAChE,MAAMG,oBAAoBR,iBAAM,CAACS,YAAY,CAAC,UAAU;YAAEC,MAAM;QAAe,EAAE,CAAC,EAAE;QAEpFF,kBAAkByC,KAAK;QACvBlD,OAAOS,mBAAmB0C,WAAW;QAErCvC,oBAAS,CAACwC,OAAO,CAAC3C,mBAAmB;YAAE4C,KAAK;QAAQ;QACpDrD,OAAOpD,sBAAsBkE,oBAAoB,CAAC;IACpD;IAEAhB,GAAG,qCAAqC;QACtCC,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtBmE,OAAOC,iBAAM,CAACG,WAAW,CAAC,yBAAyBD,iBAAiB;QACpEH,OAAOC,iBAAM,CAACC,SAAS,CAAC,eAAeC,iBAAiB;IAC1D;IAEAL,GAAG,8BAA8B;QAC/B9D,sBAAsBmB,eAAe,CAAC;YACpC,GAAGnB,uBAAuB;YAC1B2C,aAAa;YACbE,iBAAiB;QACnB;QAEAkB,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;QAEtBmE,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC3DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;IAC/D;IAEAL,GAAG,wCAAwC;QACzC3E,KAAKmI,aAAa;QAElBvD,IAAAA,iBAAM,gBAAC,qBAAClE,8BAAc;YAACkD,aAAa;YAAMC,iBAAiB;;QAE3D,oBAAoB;QACpB7D,KAAKoI,mBAAmB,CAAC;QAEzB,2CAA2C;QAC3CvD,OAAOC,iBAAM,CAACG,WAAW,CAAC,sBAAsBD,iBAAiB;QAEjEhF,KAAKqI,aAAa;IACpB;AACF"}