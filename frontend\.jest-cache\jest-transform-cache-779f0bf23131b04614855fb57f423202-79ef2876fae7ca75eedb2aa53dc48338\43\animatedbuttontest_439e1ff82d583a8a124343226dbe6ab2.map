{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\__tests__\\animated-button.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { AnimatedButton, FloatingActionButton, AnimatedIconButton, ProgressButton } from '../animated-button'\nimport { mockPrefersReducedMotion, mockFramerMotion } from '@/lib/test-utils'\nimport { Loader2, Plus, Download } from 'lucide-react'\n\n// Mock framer-motion for testing\nmockFramerMotion()\n\ndescribe('AnimatedButton Component', () => {\n  beforeEach(() => {\n    jest.clearAllMocks()\n  })\n\n  it('renders correctly', () => {\n    render(<AnimatedButton>Animated Button</AnimatedButton>)\n    expect(screen.getByRole('button', { name: /animated button/i })).toBeInTheDocument()\n  })\n\n  it('handles click events', () => {\n    const handleClick = jest.fn()\n    render(<AnimatedButton onClick={handleClick}>Click me</AnimatedButton>)\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).toHaveBeenCalledTimes(1)\n  })\n\n  it('shows loading state', () => {\n    render(<AnimatedButton loading>Loading Button</AnimatedButton>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toBeDisabled()\n    expect(screen.getByTestId('loader')).toBeInTheDocument()\n  })\n\n  it('is disabled when disabled prop is true', () => {\n    render(<AnimatedButton disabled>Disabled Button</AnimatedButton>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toBeDisabled()\n  })\n\n  it('does not trigger click when disabled', () => {\n    const handleClick = jest.fn()\n    render(<AnimatedButton disabled onClick={handleClick}>Disabled</AnimatedButton>)\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).not.toHaveBeenCalled()\n  })\n\n  it('does not trigger click when loading', () => {\n    const handleClick = jest.fn()\n    render(<AnimatedButton loading onClick={handleClick}>Loading</AnimatedButton>)\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).not.toHaveBeenCalled()\n  })\n\n  it('applies custom className', () => {\n    render(<AnimatedButton className=\"custom-class\">Custom</AnimatedButton>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toHaveClass('custom-class')\n  })\n\n  it('handles ripple effect when enabled', () => {\n    render(<AnimatedButton ripple>Ripple Button</AnimatedButton>)\n    const button = screen.getByRole('button')\n    \n    fireEvent.click(button)\n    // Ripple effect should be triggered (tested through animation state)\n    expect(button).toBeInTheDocument()\n  })\n\n  it('respects prefers-reduced-motion', () => {\n    mockPrefersReducedMotion(true)\n    render(<AnimatedButton>Reduced Motion</AnimatedButton>)\n    \n    const button = screen.getByRole('button')\n    expect(button).toBeInTheDocument()\n    // Animation should be disabled when prefers-reduced-motion is set\n  })\n\n  it('supports glow effect', () => {\n    render(<AnimatedButton glow>Glow Button</AnimatedButton>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toBeInTheDocument()\n    // Glow effect should be applied through CSS classes\n  })\n\n  it('supports magnetic effect', () => {\n    render(<AnimatedButton magnetic>Magnetic Button</AnimatedButton>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toBeInTheDocument()\n    // Magnetic effect should be applied through motion properties\n  })\n})\n\ndescribe('FloatingActionButton Component', () => {\n  it('renders correctly', () => {\n    render(\n      <FloatingActionButton>\n        <Plus className=\"h-6 w-6\" />\n      </FloatingActionButton>\n    )\n    \n    const button = screen.getByRole('button')\n    expect(button).toBeInTheDocument()\n    expect(button).toHaveClass('fixed')\n  })\n\n  it('applies correct position classes', () => {\n    const { rerender } = render(\n      <FloatingActionButton position=\"bottom-right\">\n        <Plus className=\"h-6 w-6\" />\n      </FloatingActionButton>\n    )\n    \n    let button = screen.getByRole('button')\n    expect(button).toHaveClass('bottom-6', 'right-6')\n    \n    rerender(\n      <FloatingActionButton position=\"bottom-left\">\n        <Plus className=\"h-6 w-6\" />\n      </FloatingActionButton>\n    )\n    \n    button = screen.getByRole('button')\n    expect(button).toHaveClass('bottom-6', 'left-6')\n  })\n\n  it('handles click events', () => {\n    const handleClick = jest.fn()\n    render(\n      <FloatingActionButton onClick={handleClick}>\n        <Plus className=\"h-6 w-6\" />\n      </FloatingActionButton>\n    )\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).toHaveBeenCalledTimes(1)\n  })\n\n  it('shows tooltip when provided', async () => {\n    render(\n      <FloatingActionButton tooltip=\"Add new item\">\n        <Plus className=\"h-6 w-6\" />\n      </FloatingActionButton>\n    )\n    \n    const button = screen.getByRole('button')\n    fireEvent.mouseEnter(button)\n    \n    await waitFor(() => {\n      expect(screen.getByText('Add new item')).toBeInTheDocument()\n    })\n  })\n})\n\ndescribe('AnimatedIconButton Component', () => {\n  it('renders correctly', () => {\n    render(\n      <AnimatedIconButton icon={Plus} aria-label=\"Add item\" />\n    )\n    \n    const button = screen.getByRole('button', { name: /add item/i })\n    expect(button).toBeInTheDocument()\n  })\n\n  it('applies size classes correctly', () => {\n    const { rerender } = render(\n      <AnimatedIconButton icon={Plus} size=\"sm\" aria-label=\"Small button\" />\n    )\n    \n    let button = screen.getByRole('button')\n    expect(button).toHaveClass('h-8', 'w-8')\n    \n    rerender(\n      <AnimatedIconButton icon={Plus} size=\"lg\" aria-label=\"Large button\" />\n    )\n    \n    button = screen.getByRole('button')\n    expect(button).toHaveClass('h-12', 'w-12')\n  })\n\n  it('applies variant styles correctly', () => {\n    const { rerender } = render(\n      <AnimatedIconButton icon={Plus} variant=\"ghost\" aria-label=\"Ghost button\" />\n    )\n    \n    let button = screen.getByRole('button')\n    expect(button).toHaveClass('hover:bg-accent')\n    \n    rerender(\n      <AnimatedIconButton icon={Plus} variant=\"outline\" aria-label=\"Outline button\" />\n    )\n    \n    button = screen.getByRole('button')\n    expect(button).toHaveClass('border')\n  })\n\n  it('handles click events', () => {\n    const handleClick = jest.fn()\n    render(\n      <AnimatedIconButton \n        icon={Plus} \n        onClick={handleClick} \n        aria-label=\"Clickable button\" \n      />\n    )\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).toHaveBeenCalledTimes(1)\n  })\n\n  it('is disabled when disabled prop is true', () => {\n    render(\n      <AnimatedIconButton \n        icon={Plus} \n        disabled \n        aria-label=\"Disabled button\" \n      />\n    )\n    \n    const button = screen.getByRole('button')\n    expect(button).toBeDisabled()\n  })\n})\n\ndescribe('ProgressButton Component', () => {\n  it('renders correctly', () => {\n    render(<ProgressButton progress={0}>Download</ProgressButton>)\n    expect(screen.getByRole('button', { name: /download/i })).toBeInTheDocument()\n  })\n\n  it('shows progress bar', () => {\n    render(<ProgressButton progress={50}>Downloading...</ProgressButton>)\n    \n    const progressBar = screen.getByRole('progressbar')\n    expect(progressBar).toBeInTheDocument()\n    expect(progressBar).toHaveAttribute('aria-valuenow', '50')\n  })\n\n  it('shows completion state', () => {\n    render(<ProgressButton progress={100} completed>Completed</ProgressButton>)\n    \n    const button = screen.getByRole('button')\n    expect(button).toBeInTheDocument()\n    expect(screen.getByTestId('check-icon')).toBeInTheDocument()\n  })\n\n  it('handles click events when not in progress', () => {\n    const handleClick = jest.fn()\n    render(<ProgressButton progress={0} onClick={handleClick}>Start</ProgressButton>)\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).toHaveBeenCalledTimes(1)\n  })\n\n  it('does not handle click events when in progress', () => {\n    const handleClick = jest.fn()\n    render(<ProgressButton progress={50} onClick={handleClick}>In Progress</ProgressButton>)\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).not.toHaveBeenCalled()\n  })\n\n  it('shows custom icon when provided', () => {\n    render(\n      <ProgressButton progress={0} icon={Download}>\n        Download File\n      </ProgressButton>\n    )\n    \n    expect(screen.getByTestId('download-icon')).toBeInTheDocument()\n  })\n\n  it('applies correct progress width', () => {\n    render(<ProgressButton progress={75}>Progress</ProgressButton>)\n    \n    const progressBar = screen.getByRole('progressbar')\n    const progressFill = progressBar.querySelector('[style*=\"width\"]')\n    expect(progressFill).toHaveStyle('width: 75%')\n  })\n\n  it('handles progress animation', async () => {\n    const { rerender } = render(<ProgressButton progress={0}>Start</ProgressButton>)\n    \n    rerender(<ProgressButton progress={50}>Progress</ProgressButton>)\n    \n    await waitFor(() => {\n      const progressBar = screen.getByRole('progressbar')\n      expect(progressBar).toHaveAttribute('aria-valuenow', '50')\n    })\n  })\n\n  it('resets to initial state when progress is 0', () => {\n    const { rerender } = render(<ProgressButton progress={100} completed>Done</ProgressButton>)\n    \n    rerender(<ProgressButton progress={0}>Start Again</ProgressButton>)\n    \n    const button = screen.getByRole('button')\n    expect(button).not.toBeDisabled()\n    expect(screen.queryByTestId('check-icon')).not.toBeInTheDocument()\n  })\n})\n"], "names": ["mockFramerMotion", "describe", "beforeEach", "jest", "clearAllMocks", "it", "render", "AnimatedButton", "expect", "screen", "getByRole", "name", "toBeInTheDocument", "handleClick", "fn", "onClick", "fireEvent", "click", "toHaveBeenCalledTimes", "loading", "button", "toBeDisabled", "getByTestId", "disabled", "not", "toHaveBeenCalled", "className", "toHaveClass", "ripple", "mockPrefersReducedMotion", "glow", "magnetic", "FloatingActionButton", "Plus", "rerender", "position", "tooltip", "mouseEnter", "waitFor", "getByText", "AnimatedIconButton", "icon", "aria-label", "size", "variant", "ProgressButton", "progress", "progressBar", "toHaveAttribute", "completed", "Download", "progressFill", "querySelector", "toHaveStyle", "queryByTestId"], "mappings": ";;;;;8DAAkB;2BACiC;gCACsC;6BAEjD;;;;;;AAExC,iCAAiC;AACjCA,IAAAA,2BAAgB;AAEhBC,SAAS,4BAA4B;IACnCC,WAAW;QACTC,KAAKC,aAAa;IACpB;IAEAC,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;sBAAC;;QACvBC,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAmB,IAAIC,iBAAiB;IACpF;IAEAP,GAAG,wBAAwB;QACzB,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACQ,SAASF;sBAAa;;QAE7CG,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaK,qBAAqB,CAAC;IAC5C;IAEAb,GAAG,uBAAuB;QACxBC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACY,OAAO;sBAAC;;QAC/B,MAAMC,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOY,QAAQC,YAAY;QAC3Bb,OAAOC,iBAAM,CAACa,WAAW,CAAC,WAAWV,iBAAiB;IACxD;IAEAP,GAAG,0CAA0C;QAC3CC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACgB,QAAQ;sBAAC;;QAChC,MAAMH,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOY,QAAQC,YAAY;IAC7B;IAEAhB,GAAG,wCAAwC;QACzC,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACgB,QAAQ;YAACR,SAASF;sBAAa;;QAEtDG,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaW,GAAG,CAACC,gBAAgB;IAC1C;IAEApB,GAAG,uCAAuC;QACxC,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACY,OAAO;YAACJ,SAASF;sBAAa;;QAErDG,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaW,GAAG,CAACC,gBAAgB;IAC1C;IAEApB,GAAG,4BAA4B;QAC7BC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACmB,WAAU;sBAAe;;QAChD,MAAMN,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOY,QAAQO,WAAW,CAAC;IAC7B;IAEAtB,GAAG,sCAAsC;QACvCC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACqB,MAAM;sBAAC;;QAC9B,MAAMR,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCM,oBAAS,CAACC,KAAK,CAACG;QAChB,qEAAqE;QACrEZ,OAAOY,QAAQR,iBAAiB;IAClC;IAEAP,GAAG,mCAAmC;QACpCwB,IAAAA,mCAAwB,EAAC;QACzBvB,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;sBAAC;;QAEvB,MAAMa,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQR,iBAAiB;IAChC,kEAAkE;IACpE;IAEAP,GAAG,wBAAwB;QACzBC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACuB,IAAI;sBAAC;;QAC5B,MAAMV,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOY,QAAQR,iBAAiB;IAChC,oDAAoD;IACtD;IAEAP,GAAG,4BAA4B;QAC7BC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAACwB,QAAQ;sBAAC;;QAChC,MAAMX,SAASX,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOY,QAAQR,iBAAiB;IAChC,8DAA8D;IAChE;AACF;AAEAX,SAAS,kCAAkC;IACzCI,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBACJ,qBAAC0B,oCAAoB;sBACnB,cAAA,qBAACC,iBAAI;gBAACP,WAAU;;;QAIpB,MAAMN,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQR,iBAAiB;QAChCJ,OAAOY,QAAQO,WAAW,CAAC;IAC7B;IAEAtB,GAAG,oCAAoC;QACrC,MAAM,EAAE6B,QAAQ,EAAE,GAAG5B,IAAAA,iBAAM,gBACzB,qBAAC0B,oCAAoB;YAACG,UAAS;sBAC7B,cAAA,qBAACF,iBAAI;gBAACP,WAAU;;;QAIpB,IAAIN,SAASX,iBAAM,CAACC,SAAS,CAAC;QAC9BF,OAAOY,QAAQO,WAAW,CAAC,YAAY;QAEvCO,uBACE,qBAACF,oCAAoB;YAACG,UAAS;sBAC7B,cAAA,qBAACF,iBAAI;gBAACP,WAAU;;;QAIpBN,SAASX,iBAAM,CAACC,SAAS,CAAC;QAC1BF,OAAOY,QAAQO,WAAW,CAAC,YAAY;IACzC;IAEAtB,GAAG,wBAAwB;QACzB,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBACJ,qBAAC0B,oCAAoB;YAACjB,SAASF;sBAC7B,cAAA,qBAACoB,iBAAI;gBAACP,WAAU;;;QAIpBV,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaK,qBAAqB,CAAC;IAC5C;IAEAb,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBACJ,qBAAC0B,oCAAoB;YAACI,SAAQ;sBAC5B,cAAA,qBAACH,iBAAI;gBAACP,WAAU;;;QAIpB,MAAMN,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCM,oBAAS,CAACqB,UAAU,CAACjB;QAErB,MAAMkB,IAAAA,kBAAO,EAAC;YACZ9B,OAAOC,iBAAM,CAAC8B,SAAS,CAAC,iBAAiB3B,iBAAiB;QAC5D;IACF;AACF;AAEAX,SAAS,gCAAgC;IACvCI,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBACJ,qBAACkC,kCAAkB;YAACC,MAAMR,iBAAI;YAAES,cAAW;;QAG7C,MAAMtB,SAASX,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAY;QAC9DH,OAAOY,QAAQR,iBAAiB;IAClC;IAEAP,GAAG,kCAAkC;QACnC,MAAM,EAAE6B,QAAQ,EAAE,GAAG5B,IAAAA,iBAAM,gBACzB,qBAACkC,kCAAkB;YAACC,MAAMR,iBAAI;YAAEU,MAAK;YAAKD,cAAW;;QAGvD,IAAItB,SAASX,iBAAM,CAACC,SAAS,CAAC;QAC9BF,OAAOY,QAAQO,WAAW,CAAC,OAAO;QAElCO,uBACE,qBAACM,kCAAkB;YAACC,MAAMR,iBAAI;YAAEU,MAAK;YAAKD,cAAW;;QAGvDtB,SAASX,iBAAM,CAACC,SAAS,CAAC;QAC1BF,OAAOY,QAAQO,WAAW,CAAC,QAAQ;IACrC;IAEAtB,GAAG,oCAAoC;QACrC,MAAM,EAAE6B,QAAQ,EAAE,GAAG5B,IAAAA,iBAAM,gBACzB,qBAACkC,kCAAkB;YAACC,MAAMR,iBAAI;YAAEW,SAAQ;YAAQF,cAAW;;QAG7D,IAAItB,SAASX,iBAAM,CAACC,SAAS,CAAC;QAC9BF,OAAOY,QAAQO,WAAW,CAAC;QAE3BO,uBACE,qBAACM,kCAAkB;YAACC,MAAMR,iBAAI;YAAEW,SAAQ;YAAUF,cAAW;;QAG/DtB,SAASX,iBAAM,CAACC,SAAS,CAAC;QAC1BF,OAAOY,QAAQO,WAAW,CAAC;IAC7B;IAEAtB,GAAG,wBAAwB;QACzB,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBACJ,qBAACkC,kCAAkB;YACjBC,MAAMR,iBAAI;YACVlB,SAASF;YACT6B,cAAW;;QAIf1B,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaK,qBAAqB,CAAC;IAC5C;IAEAb,GAAG,0CAA0C;QAC3CC,IAAAA,iBAAM,gBACJ,qBAACkC,kCAAkB;YACjBC,MAAMR,iBAAI;YACVV,QAAQ;YACRmB,cAAW;;QAIf,MAAMtB,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQC,YAAY;IAC7B;AACF;AAEApB,SAAS,4BAA4B;IACnCI,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAACuC,8BAAc;YAACC,UAAU;sBAAG;;QACpCtC,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAY,IAAIC,iBAAiB;IAC7E;IAEAP,GAAG,sBAAsB;QACvBC,IAAAA,iBAAM,gBAAC,qBAACuC,8BAAc;YAACC,UAAU;sBAAI;;QAErC,MAAMC,cAActC,iBAAM,CAACC,SAAS,CAAC;QACrCF,OAAOuC,aAAanC,iBAAiB;QACrCJ,OAAOuC,aAAaC,eAAe,CAAC,iBAAiB;IACvD;IAEA3C,GAAG,0BAA0B;QAC3BC,IAAAA,iBAAM,gBAAC,qBAACuC,8BAAc;YAACC,UAAU;YAAKG,SAAS;sBAAC;;QAEhD,MAAM7B,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQR,iBAAiB;QAChCJ,OAAOC,iBAAM,CAACa,WAAW,CAAC,eAAeV,iBAAiB;IAC5D;IAEAP,GAAG,6CAA6C;QAC9C,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBAAC,qBAACuC,8BAAc;YAACC,UAAU;YAAG/B,SAASF;sBAAa;;QAE1DG,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaK,qBAAqB,CAAC;IAC5C;IAEAb,GAAG,iDAAiD;QAClD,MAAMQ,cAAcV,KAAKW,EAAE;QAC3BR,IAAAA,iBAAM,gBAAC,qBAACuC,8BAAc;YAACC,UAAU;YAAI/B,SAASF;sBAAa;;QAE3DG,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaW,GAAG,CAACC,gBAAgB;IAC1C;IAEApB,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBACJ,qBAACuC,8BAAc;YAACC,UAAU;YAAGL,MAAMS,qBAAQ;sBAAE;;QAK/C1C,OAAOC,iBAAM,CAACa,WAAW,CAAC,kBAAkBV,iBAAiB;IAC/D;IAEAP,GAAG,kCAAkC;QACnCC,IAAAA,iBAAM,gBAAC,qBAACuC,8BAAc;YAACC,UAAU;sBAAI;;QAErC,MAAMC,cAActC,iBAAM,CAACC,SAAS,CAAC;QACrC,MAAMyC,eAAeJ,YAAYK,aAAa,CAAC;QAC/C5C,OAAO2C,cAAcE,WAAW,CAAC;IACnC;IAEAhD,GAAG,8BAA8B;QAC/B,MAAM,EAAE6B,QAAQ,EAAE,GAAG5B,IAAAA,iBAAM,gBAAC,qBAACuC,8BAAc;YAACC,UAAU;sBAAG;;QAEzDZ,uBAAS,qBAACW,8BAAc;YAACC,UAAU;sBAAI;;QAEvC,MAAMR,IAAAA,kBAAO,EAAC;YACZ,MAAMS,cAActC,iBAAM,CAACC,SAAS,CAAC;YACrCF,OAAOuC,aAAaC,eAAe,CAAC,iBAAiB;QACvD;IACF;IAEA3C,GAAG,8CAA8C;QAC/C,MAAM,EAAE6B,QAAQ,EAAE,GAAG5B,IAAAA,iBAAM,gBAAC,qBAACuC,8BAAc;YAACC,UAAU;YAAKG,SAAS;sBAAC;;QAErEf,uBAAS,qBAACW,8BAAc;YAACC,UAAU;sBAAG;;QAEtC,MAAM1B,SAASX,iBAAM,CAACC,SAAS,CAAC;QAChCF,OAAOY,QAAQI,GAAG,CAACH,YAAY;QAC/Bb,OAAOC,iBAAM,CAAC6C,aAAa,CAAC,eAAe9B,GAAG,CAACZ,iBAAiB;IAClE;AACF"}