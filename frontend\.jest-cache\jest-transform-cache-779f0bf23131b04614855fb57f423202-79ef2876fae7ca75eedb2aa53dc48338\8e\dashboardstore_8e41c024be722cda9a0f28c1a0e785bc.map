{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\stores\\dashboard-store.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools, subscribeWithSelector } from 'zustand/middleware';\nimport { immer } from 'zustand/middleware/immer';\nimport { SystemAlert, UAVLocationUpdate, BatteryAlert } from '@/types/uav';\n\n// Dashboard-specific types\nexport interface DashboardMetrics {\n  totalUAVs: number;\n  authorizedUAVs: number;\n  unauthorizedUAVs: number;\n  activeFlights: number;\n  hibernatingUAVs: number;\n  lowBatteryCount: number;\n  chargingCount: number;\n  maintenanceCount: number;\n  emergencyCount: number;\n}\n\nexport interface FlightActivity {\n  activeFlights: number;\n  todayFlights: number;\n  completedFlights: number;\n  flights: Array<{\n    id: number;\n    uavRfid: string;\n    missionName: string;\n    startTime: string;\n    status: string;\n  }>;\n}\n\nexport interface BatteryStatistics {\n  lowBattery: number;\n  criticalBattery: number;\n  overheating: number;\n  charging: number;\n  healthy: number;\n}\n\nexport interface HibernatePodMetrics {\n  currentCapacity: number;\n  maxCapacity: number;\n  utilizationPercentage: number;\n  isFull: boolean;\n}\n\nexport interface ChartData {\n  flightTrends: Array<{\n    time: string;\n    flights: number;\n    completed: number;\n  }>;\n  batteryLevels: Array<{\n    uavId: number;\n    rfidTag: string;\n    batteryLevel: number;\n    status: string;\n  }>;\n  systemLoad: Array<{\n    timestamp: string;\n    cpuUsage: number;\n    memoryUsage: number;\n    networkUsage: number;\n  }>;\n}\n\ninterface DashboardState {\n  // Real-time data\n  metrics: DashboardMetrics | null;\n  flightActivity: FlightActivity | null;\n  batteryStats: BatteryStatistics | null;\n  hibernatePodMetrics: HibernatePodMetrics | null;\n  chartData: ChartData | null;\n  alerts: SystemAlert[];\n  recentLocationUpdates: UAVLocationUpdate[];\n\n  // Connection status\n  isConnected: boolean;\n  lastUpdate: string | null;\n  connectionError: string | null;\n\n  // UI state\n  selectedTimeRange: '1h' | '6h' | '24h' | '7d';\n  autoRefresh: boolean;\n  refreshInterval: number; // in seconds\n  showAlerts: boolean;\n\n  // Actions\n  updateMetrics: (metrics: DashboardMetrics) => void;\n  updateFlightActivity: (activity: FlightActivity) => void;\n  updateBatteryStats: (stats: BatteryStatistics) => void;\n  updateHibernatePodMetrics: (metrics: HibernatePodMetrics) => void;\n  updateChartData: (data: Partial<ChartData>) => void;\n  addAlert: (alert: SystemAlert) => void;\n  removeAlert: (alertId: string) => void;\n  acknowledgeAlert: (alertId: string) => void;\n  clearAlerts: () => void;\n  addLocationUpdate: (update: UAVLocationUpdate) => void;\n  \n  // Connection management\n  setConnectionStatus: (connected: boolean, error?: string) => void;\n  updateLastUpdate: () => void;\n  \n  // UI actions\n  setTimeRange: (range: '1h' | '6h' | '24h' | '7d') => void;\n  setAutoRefresh: (enabled: boolean) => void;\n  setRefreshInterval: (interval: number) => void;\n  setShowAlerts: (show: boolean) => void;\n  \n  // Data fetching\n  fetchDashboardData: () => Promise<void>;\n  refreshData: () => Promise<void>;\n}\n\nexport const useDashboardStore = create<DashboardState>()(\n  devtools(\n    subscribeWithSelector(\n      immer((set, get) => ({\n        // Initial state\n        metrics: null,\n        flightActivity: null,\n        batteryStats: null,\n        hibernatePodMetrics: null,\n        chartData: null,\n        alerts: [],\n        recentLocationUpdates: [],\n        isConnected: false,\n        lastUpdate: null,\n        connectionError: null,\n        selectedTimeRange: '24h',\n        autoRefresh: true,\n        refreshInterval: 30,\n        showAlerts: true,\n\n        // Update metrics\n        updateMetrics: (metrics: DashboardMetrics) => {\n          set((state) => {\n            state.metrics = metrics;\n            state.lastUpdate = new Date().toISOString();\n          });\n        },\n\n        // Update flight activity\n        updateFlightActivity: (activity: FlightActivity) => {\n          set((state) => {\n            state.flightActivity = activity;\n          });\n        },\n\n        // Update battery statistics\n        updateBatteryStats: (stats: BatteryStatistics) => {\n          set((state) => {\n            state.batteryStats = stats;\n          });\n        },\n\n        // Update hibernate pod metrics\n        updateHibernatePodMetrics: (metrics: HibernatePodMetrics) => {\n          set((state) => {\n            state.hibernatePodMetrics = metrics;\n          });\n        },\n\n        // Update chart data\n        updateChartData: (data: Partial<ChartData>) => {\n          set((state) => {\n            if (state.chartData) {\n              state.chartData = { ...state.chartData, ...data };\n            } else {\n              state.chartData = data as ChartData;\n            }\n          });\n        },\n\n        // Add alert\n        addAlert: (alert: SystemAlert) => {\n          set((state) => {\n            // Avoid duplicates\n            const exists = state.alerts.some(a => a.id === alert.id);\n            if (!exists) {\n              state.alerts.unshift(alert);\n              // Keep only last 50 alerts\n              if (state.alerts.length > 50) {\n                state.alerts = state.alerts.slice(0, 50);\n              }\n            }\n          });\n        },\n\n        // Remove alert\n        removeAlert: (alertId: string) => {\n          set((state) => {\n            state.alerts = state.alerts.filter(a => a.id !== alertId);\n          });\n        },\n\n        // Acknowledge alert\n        acknowledgeAlert: (alertId: string) => {\n          set((state) => {\n            const alert = state.alerts.find(a => a.id === alertId);\n            if (alert) {\n              alert.acknowledged = true;\n            }\n          });\n        },\n\n        // Clear all alerts\n        clearAlerts: () => {\n          set((state) => {\n            state.alerts = [];\n          });\n        },\n\n        // Add location update\n        addLocationUpdate: (update: UAVLocationUpdate) => {\n          set((state) => {\n            state.recentLocationUpdates.unshift(update);\n            // Keep only last 100 updates\n            if (state.recentLocationUpdates.length > 100) {\n              state.recentLocationUpdates = state.recentLocationUpdates.slice(0, 100);\n            }\n          });\n        },\n\n        // Set connection status\n        setConnectionStatus: (connected: boolean, error?: string) => {\n          set((state) => {\n            state.isConnected = connected;\n            state.connectionError = error || null;\n            if (connected) {\n              state.lastUpdate = new Date().toISOString();\n            }\n          });\n        },\n\n        // Update last update timestamp\n        updateLastUpdate: () => {\n          set((state) => {\n            state.lastUpdate = new Date().toISOString();\n          });\n        },\n\n        // Set time range\n        setTimeRange: (range: '1h' | '6h' | '24h' | '7d') => {\n          set((state) => {\n            state.selectedTimeRange = range;\n          });\n          // Trigger data refresh with new time range\n          get().refreshData();\n        },\n\n        // Set auto refresh\n        setAutoRefresh: (enabled: boolean) => {\n          set((state) => {\n            state.autoRefresh = enabled;\n          });\n        },\n\n        // Set refresh interval\n        setRefreshInterval: (interval: number) => {\n          set((state) => {\n            state.refreshInterval = interval;\n          });\n        },\n\n        // Set show alerts\n        setShowAlerts: (show: boolean) => {\n          set((state) => {\n            state.showAlerts = show;\n          });\n        },\n\n        // Fetch dashboard data\n        fetchDashboardData: async () => {\n          try {\n            // This would typically call multiple API endpoints\n            // For now, we'll simulate the data structure\n            const response = await fetch('/api/analytics/dashboard');\n            if (response.ok) {\n              const data = await response.json();\n              \n              set((state) => {\n                if (data.metrics) state.metrics = data.metrics;\n                if (data.flightActivity) state.flightActivity = data.flightActivity;\n                if (data.batteryStats) state.batteryStats = data.batteryStats;\n                if (data.hibernatePodMetrics) state.hibernatePodMetrics = data.hibernatePodMetrics;\n                if (data.chartData) state.chartData = data.chartData;\n                state.lastUpdate = new Date().toISOString();\n                state.connectionError = null;\n              });\n            }\n          } catch (error) {\n            set((state) => {\n              state.connectionError = error instanceof Error ? error.message : 'Failed to fetch dashboard data';\n            });\n          }\n        },\n\n        // Refresh data\n        refreshData: async () => {\n          await get().fetchDashboardData();\n        },\n      }))\n    ),\n    {\n      name: 'dashboard-store',\n    }\n  )\n);\n\n// Selectors for computed values\nexport const useDashboardMetrics = () => useDashboardStore((state) => state.metrics);\nexport const useFlightActivity = () => useDashboardStore((state) => state.flightActivity);\nexport const useBatteryStats = () => useDashboardStore((state) => state.batteryStats);\nexport const useHibernatePodMetrics = () => useDashboardStore((state) => state.hibernatePodMetrics);\nexport const useChartData = () => useDashboardStore((state) => state.chartData);\nexport const useAlerts = () => useDashboardStore((state) => state.alerts);\nexport const useConnectionStatus = () => useDashboardStore((state) => ({\n  isConnected: state.isConnected,\n  lastUpdate: state.lastUpdate,\n  error: state.connectionError,\n}));\n\n// Computed selectors\nexport const useUnacknowledgedAlerts = () => \n  useDashboardStore((state) => state.alerts.filter(alert => !alert.acknowledged));\n\nexport const useCriticalAlerts = () => \n  useDashboardStore((state) => state.alerts.filter(alert => alert.type === 'CRITICAL' || alert.type === 'ERROR'));\n\nexport const useRecentLocationUpdates = () => \n  useDashboardStore((state) => state.recentLocationUpdates.slice(0, 10));\n"], "names": ["useAlerts", "useBatteryStats", "useChartData", "useConnectionStatus", "useCriticalAlerts", "useDashboardMetrics", "useDashboardStore", "useFlightActivity", "useHibernatePodMetrics", "useRecentLocationUpdates", "useUnacknowledgedAlerts", "create", "devtools", "subscribeWithSelector", "immer", "set", "get", "metrics", "flightActivity", "batteryStats", "hibernatePodMetrics", "chartData", "alerts", "recentLocationUpdates", "isConnected", "lastUpdate", "connectionError", "selectedTimeRange", "autoRefresh", "refreshInterval", "show<PERSON><PERSON><PERSON>", "updateMetrics", "state", "Date", "toISOString", "updateFlightActivity", "activity", "updateBatteryStats", "stats", "updateHibernatePodMetrics", "updateChartData", "data", "add<PERSON><PERSON><PERSON>", "alert", "exists", "some", "a", "id", "unshift", "length", "slice", "<PERSON><PERSON><PERSON><PERSON>", "alertId", "filter", "<PERSON><PERSON><PERSON><PERSON>", "find", "acknowledged", "<PERSON><PERSON><PERSON><PERSON>", "addLocationUpdate", "update", "setConnectionStatus", "connected", "error", "updateLastUpdate", "setTimeRange", "range", "refreshData", "setAutoRefresh", "enabled", "setRefreshInterval", "interval", "setShow<PERSON>ler<PERSON>", "show", "fetchDashboardData", "response", "fetch", "ok", "json", "Error", "message", "name", "type"], "mappings": ";;;;;;;;;;;IA4TaA,SAAS;eAATA;;IAHAC,eAAe;eAAfA;;IAEAC,YAAY;eAAZA;;IAEAC,mBAAmB;eAAnBA;;IAUAC,iBAAiB;eAAjBA;;IAhBAC,mBAAmB;eAAnBA;;IArMAC,iBAAiB;eAAjBA;;IAsMAC,iBAAiB;eAAjBA;;IAEAC,sBAAsB;eAAtBA;;IAgBAC,wBAAwB;eAAxBA;;IANAC,uBAAuB;eAAvBA;;;yBApUU;4BACyB;uBAC1B;AAgHf,MAAMJ,oBAAoBK,IAAAA,eAAM,IACrCC,IAAAA,oBAAQ,EACNC,IAAAA,iCAAqB,EACnBC,IAAAA,YAAK,EAAC,CAACC,KAAKC,MAAS,CAAA;QACnB,gBAAgB;QAChBC,SAAS;QACTC,gBAAgB;QAChBC,cAAc;QACdC,qBAAqB;QACrBC,WAAW;QACXC,QAAQ,EAAE;QACVC,uBAAuB,EAAE;QACzBC,aAAa;QACbC,YAAY;QACZC,iBAAiB;QACjBC,mBAAmB;QACnBC,aAAa;QACbC,iBAAiB;QACjBC,YAAY;QAEZ,iBAAiB;QACjBC,eAAe,CAACd;YACdF,IAAI,CAACiB;gBACHA,MAAMf,OAAO,GAAGA;gBAChBe,MAAMP,UAAU,GAAG,IAAIQ,OAAOC,WAAW;YAC3C;QACF;QAEA,yBAAyB;QACzBC,sBAAsB,CAACC;YACrBrB,IAAI,CAACiB;gBACHA,MAAMd,cAAc,GAAGkB;YACzB;QACF;QAEA,4BAA4B;QAC5BC,oBAAoB,CAACC;YACnBvB,IAAI,CAACiB;gBACHA,MAAMb,YAAY,GAAGmB;YACvB;QACF;QAEA,+BAA+B;QAC/BC,2BAA2B,CAACtB;YAC1BF,IAAI,CAACiB;gBACHA,MAAMZ,mBAAmB,GAAGH;YAC9B;QACF;QAEA,oBAAoB;QACpBuB,iBAAiB,CAACC;YAChB1B,IAAI,CAACiB;gBACH,IAAIA,MAAMX,SAAS,EAAE;oBACnBW,MAAMX,SAAS,GAAG;wBAAE,GAAGW,MAAMX,SAAS;wBAAE,GAAGoB,IAAI;oBAAC;gBAClD,OAAO;oBACLT,MAAMX,SAAS,GAAGoB;gBACpB;YACF;QACF;QAEA,YAAY;QACZC,UAAU,CAACC;YACT5B,IAAI,CAACiB;gBACH,mBAAmB;gBACnB,MAAMY,SAASZ,MAAMV,MAAM,CAACuB,IAAI,CAACC,CAAAA,IAAKA,EAAEC,EAAE,KAAKJ,MAAMI,EAAE;gBACvD,IAAI,CAACH,QAAQ;oBACXZ,MAAMV,MAAM,CAAC0B,OAAO,CAACL;oBACrB,2BAA2B;oBAC3B,IAAIX,MAAMV,MAAM,CAAC2B,MAAM,GAAG,IAAI;wBAC5BjB,MAAMV,MAAM,GAAGU,MAAMV,MAAM,CAAC4B,KAAK,CAAC,GAAG;oBACvC;gBACF;YACF;QACF;QAEA,eAAe;QACfC,aAAa,CAACC;YACZrC,IAAI,CAACiB;gBACHA,MAAMV,MAAM,GAAGU,MAAMV,MAAM,CAAC+B,MAAM,CAACP,CAAAA,IAAKA,EAAEC,EAAE,KAAKK;YACnD;QACF;QAEA,oBAAoB;QACpBE,kBAAkB,CAACF;YACjBrC,IAAI,CAACiB;gBACH,MAAMW,QAAQX,MAAMV,MAAM,CAACiC,IAAI,CAACT,CAAAA,IAAKA,EAAEC,EAAE,KAAKK;gBAC9C,IAAIT,OAAO;oBACTA,MAAMa,YAAY,GAAG;gBACvB;YACF;QACF;QAEA,mBAAmB;QACnBC,aAAa;YACX1C,IAAI,CAACiB;gBACHA,MAAMV,MAAM,GAAG,EAAE;YACnB;QACF;QAEA,sBAAsB;QACtBoC,mBAAmB,CAACC;YAClB5C,IAAI,CAACiB;gBACHA,MAAMT,qBAAqB,CAACyB,OAAO,CAACW;gBACpC,6BAA6B;gBAC7B,IAAI3B,MAAMT,qBAAqB,CAAC0B,MAAM,GAAG,KAAK;oBAC5CjB,MAAMT,qBAAqB,GAAGS,MAAMT,qBAAqB,CAAC2B,KAAK,CAAC,GAAG;gBACrE;YACF;QACF;QAEA,wBAAwB;QACxBU,qBAAqB,CAACC,WAAoBC;YACxC/C,IAAI,CAACiB;gBACHA,MAAMR,WAAW,GAAGqC;gBACpB7B,MAAMN,eAAe,GAAGoC,SAAS;gBACjC,IAAID,WAAW;oBACb7B,MAAMP,UAAU,GAAG,IAAIQ,OAAOC,WAAW;gBAC3C;YACF;QACF;QAEA,+BAA+B;QAC/B6B,kBAAkB;YAChBhD,IAAI,CAACiB;gBACHA,MAAMP,UAAU,GAAG,IAAIQ,OAAOC,WAAW;YAC3C;QACF;QAEA,iBAAiB;QACjB8B,cAAc,CAACC;YACblD,IAAI,CAACiB;gBACHA,MAAML,iBAAiB,GAAGsC;YAC5B;YACA,2CAA2C;YAC3CjD,MAAMkD,WAAW;QACnB;QAEA,mBAAmB;QACnBC,gBAAgB,CAACC;YACfrD,IAAI,CAACiB;gBACHA,MAAMJ,WAAW,GAAGwC;YACtB;QACF;QAEA,uBAAuB;QACvBC,oBAAoB,CAACC;YACnBvD,IAAI,CAACiB;gBACHA,MAAMH,eAAe,GAAGyC;YAC1B;QACF;QAEA,kBAAkB;QAClBC,eAAe,CAACC;YACdzD,IAAI,CAACiB;gBACHA,MAAMF,UAAU,GAAG0C;YACrB;QACF;QAEA,uBAAuB;QACvBC,oBAAoB;YAClB,IAAI;gBACF,mDAAmD;gBACnD,6CAA6C;gBAC7C,MAAMC,WAAW,MAAMC,MAAM;gBAC7B,IAAID,SAASE,EAAE,EAAE;oBACf,MAAMnC,OAAO,MAAMiC,SAASG,IAAI;oBAEhC9D,IAAI,CAACiB;wBACH,IAAIS,KAAKxB,OAAO,EAAEe,MAAMf,OAAO,GAAGwB,KAAKxB,OAAO;wBAC9C,IAAIwB,KAAKvB,cAAc,EAAEc,MAAMd,cAAc,GAAGuB,KAAKvB,cAAc;wBACnE,IAAIuB,KAAKtB,YAAY,EAAEa,MAAMb,YAAY,GAAGsB,KAAKtB,YAAY;wBAC7D,IAAIsB,KAAKrB,mBAAmB,EAAEY,MAAMZ,mBAAmB,GAAGqB,KAAKrB,mBAAmB;wBAClF,IAAIqB,KAAKpB,SAAS,EAAEW,MAAMX,SAAS,GAAGoB,KAAKpB,SAAS;wBACpDW,MAAMP,UAAU,GAAG,IAAIQ,OAAOC,WAAW;wBACzCF,MAAMN,eAAe,GAAG;oBAC1B;gBACF;YACF,EAAE,OAAOoC,OAAO;gBACd/C,IAAI,CAACiB;oBACHA,MAAMN,eAAe,GAAGoC,iBAAiBgB,QAAQhB,MAAMiB,OAAO,GAAG;gBACnE;YACF;QACF;QAEA,eAAe;QACfb,aAAa;YACX,MAAMlD,MAAMyD,kBAAkB;QAChC;IACF,CAAA,KAEF;IACEO,MAAM;AACR;AAKG,MAAM3E,sBAAsB,IAAMC,kBAAkB,CAAC0B,QAAUA,MAAMf,OAAO;AAC5E,MAAMV,oBAAoB,IAAMD,kBAAkB,CAAC0B,QAAUA,MAAMd,cAAc;AACjF,MAAMjB,kBAAkB,IAAMK,kBAAkB,CAAC0B,QAAUA,MAAMb,YAAY;AAC7E,MAAMX,yBAAyB,IAAMF,kBAAkB,CAAC0B,QAAUA,MAAMZ,mBAAmB;AAC3F,MAAMlB,eAAe,IAAMI,kBAAkB,CAAC0B,QAAUA,MAAMX,SAAS;AACvE,MAAMrB,YAAY,IAAMM,kBAAkB,CAAC0B,QAAUA,MAAMV,MAAM;AACjE,MAAMnB,sBAAsB,IAAMG,kBAAkB,CAAC0B,QAAW,CAAA;YACrER,aAAaQ,MAAMR,WAAW;YAC9BC,YAAYO,MAAMP,UAAU;YAC5BqC,OAAO9B,MAAMN,eAAe;QAC9B,CAAA;AAGO,MAAMhB,0BAA0B,IACrCJ,kBAAkB,CAAC0B,QAAUA,MAAMV,MAAM,CAAC+B,MAAM,CAACV,CAAAA,QAAS,CAACA,MAAMa,YAAY;AAExE,MAAMpD,oBAAoB,IAC/BE,kBAAkB,CAAC0B,QAAUA,MAAMV,MAAM,CAAC+B,MAAM,CAACV,CAAAA,QAASA,MAAMsC,IAAI,KAAK,cAActC,MAAMsC,IAAI,KAAK;AAEjG,MAAMxE,2BAA2B,IACtCH,kBAAkB,CAAC0B,QAAUA,MAAMT,qBAAqB,CAAC2B,KAAK,CAAC,GAAG"}