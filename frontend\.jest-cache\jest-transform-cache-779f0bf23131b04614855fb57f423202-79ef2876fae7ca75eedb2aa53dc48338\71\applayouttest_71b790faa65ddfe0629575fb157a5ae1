07e551498baa12311740f4ceb01413ce
"use strict";
// Mock the layout components
jest.mock("../header", ()=>({
        Header: ({ onMenuClick })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "header",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                    onClick: onMenuClick,
                    "data-testid": "menu-button",
                    children: "Menu"
                })
            })
    }));
jest.mock("../sidebar", ()=>({
        Sidebar: ({ collapsed, onToggle })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "sidebar",
                "data-collapsed": collapsed,
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                    onClick: onToggle,
                    "data-testid": "sidebar-toggle",
                    children: "Toggle"
                })
            })
    }));
jest.mock("../page-transition", ()=>({
        PageTransition: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "page-transition",
                children: children
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _applayout = require("../app-layout");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock framer-motion
(0, _testutils.mockFramerMotion)();
describe("AppLayout Component", ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Test content"
            })
        }));
        expect(_testutils.screen.getByTestId("header")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("sidebar")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("page-transition")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Test content")).toBeInTheDocument();
    });
    it("applies custom className", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            className: "custom-class",
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Content"
            })
        }));
        const main = _testutils.screen.getByRole("main");
        expect(main).toHaveClass("custom-class");
    });
    it("manages sidebar collapsed state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Content"
            })
        }));
        const sidebar = _testutils.screen.getByTestId("sidebar");
        const sidebarToggle = _testutils.screen.getByTestId("sidebar-toggle");
        // Initially not collapsed
        expect(sidebar).toHaveAttribute("data-collapsed", "false");
        // Toggle sidebar
        _testutils.fireEvent.click(sidebarToggle);
        expect(sidebar).toHaveAttribute("data-collapsed", "true");
        // Toggle again
        _testutils.fireEvent.click(sidebarToggle);
        expect(sidebar).toHaveAttribute("data-collapsed", "false");
    });
    it("handles header menu click", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Content"
            })
        }));
        const sidebar = _testutils.screen.getByTestId("sidebar");
        const menuButton = _testutils.screen.getByTestId("menu-button");
        // Initially not collapsed
        expect(sidebar).toHaveAttribute("data-collapsed", "false");
        // Click menu button
        _testutils.fireEvent.click(menuButton);
        expect(sidebar).toHaveAttribute("data-collapsed", "true");
    });
    it("has proper layout structure", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Content"
            })
        }));
        // Check main layout structure
        const container = _testutils.screen.getByTestId("header").parentElement?.parentElement;
        expect(container).toHaveClass("flex", "h-screen", "bg-background");
        // Check main content area
        const main = _testutils.screen.getByRole("main");
        expect(main).toHaveClass("flex-1", "overflow-y-auto");
        // Check content container
        const contentContainer = main.firstElementChild;
        expect(contentContainer).toHaveClass("container", "mx-auto", "px-4", "py-6");
    });
    it("hides sidebar on mobile", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Content"
            })
        }));
        const sidebarContainer = _testutils.screen.getByTestId("sidebar").parentElement;
        expect(sidebarContainer).toHaveClass("hidden", "md:flex");
    });
    it("renders children inside page transition", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "child-content",
                children: "Child content"
            })
        }));
        const pageTransition = _testutils.screen.getByTestId("page-transition");
        const childContent = _testutils.screen.getByTestId("child-content");
        expect(pageTransition).toContainElement(childContent);
    });
    it("maintains accessibility standards", async ()=>{
        const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)(_applayout.AppLayout, {
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                    children: "Page Title"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                    children: "Page content"
                })
            ]
        }));
        // Check for proper semantic structure
        expect(_testutils.screen.getByRole("main")).toBeInTheDocument();
        // Run accessibility tests
        await (0, _testutils.runAxeTest)(container);
    });
    it("handles responsive design", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Content"
            })
        }));
        // Check responsive classes
        const mainContentArea = _testutils.screen.getByTestId("header").parentElement;
        expect(mainContentArea).toHaveClass("flex-1", "flex", "flex-col", "overflow-hidden");
    });
    it("passes props correctly to child components", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Content"
            })
        }));
        const sidebar = _testutils.screen.getByTestId("sidebar");
        const header = _testutils.screen.getByTestId("header");
        // Sidebar should receive collapsed prop
        expect(sidebar).toHaveAttribute("data-collapsed", "false");
        // Header should have menu button (indicating onMenuClick prop was passed)
        expect(_testutils.screen.getByTestId("menu-button")).toBeInTheDocument();
    });
    it("handles keyboard navigation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Content"
            })
        }));
        const menuButton = _testutils.screen.getByTestId("menu-button");
        const sidebarToggle = _testutils.screen.getByTestId("sidebar-toggle");
        // Focus should work on interactive elements
        menuButton.focus();
        expect(menuButton).toHaveFocus();
        sidebarToggle.focus();
        expect(sidebarToggle).toHaveFocus();
    });
    it("supports nested content structure", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("header", {
                        children: "Page Header"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("section", {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("article", {
                            children: "Article content"
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("footer", {
                        children: "Page Footer"
                    })
                ]
            })
        }));
        expect(_testutils.screen.getByText("Page Header")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Article content")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Page Footer")).toBeInTheDocument();
    });
    it("maintains state consistency", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_applayout.AppLayout, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Content"
            })
        }));
        const sidebar = _testutils.screen.getByTestId("sidebar");
        const menuButton = _testutils.screen.getByTestId("menu-button");
        const sidebarToggle = _testutils.screen.getByTestId("sidebar-toggle");
        // Both buttons should affect the same state
        _testutils.fireEvent.click(menuButton);
        expect(sidebar).toHaveAttribute("data-collapsed", "true");
        _testutils.fireEvent.click(sidebarToggle);
        expect(sidebar).toHaveAttribute("data-collapsed", "false");
        _testutils.fireEvent.click(menuButton);
        expect(sidebar).toHaveAttribute("data-collapsed", "true");
    });
});

//# sourceMappingURL=data:application/json;base64,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