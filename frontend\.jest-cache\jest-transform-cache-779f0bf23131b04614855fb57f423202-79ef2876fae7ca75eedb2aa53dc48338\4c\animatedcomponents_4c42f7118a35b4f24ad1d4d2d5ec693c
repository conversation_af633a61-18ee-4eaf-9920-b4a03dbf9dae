e8dcc9c67281181ac6eb6361203c083f
"use client";
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AnimatedButton: function() {
        return AnimatedButton;
    },
    AnimatedCard: function() {
        return AnimatedCard;
    },
    AnimatedModal: function() {
        return AnimatedModal;
    },
    AnimatedPage: function() {
        return AnimatedPage;
    },
    AnimatedSpinner: function() {
        return AnimatedSpinner;
    },
    Bounce: function() {
        return Bounce;
    },
    Fade: function() {
        return Fade;
    },
    Float: function() {
        return Float;
    },
    Glow: function() {
        return Glow;
    },
    Magnetic: function() {
        return Magnetic;
    },
    Pulse: function() {
        return Pulse;
    },
    ScaleOnHover: function() {
        return ScaleOnHover;
    },
    Shake: function() {
        return Shake;
    },
    Slide: function() {
        return Slide;
    },
    StaggerContainer: function() {
        return StaggerContainer;
    },
    StaggerItem: function() {
        return StaggerItem;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _framermotion = require("framer-motion");
const _utils = require("../../lib/utils");
const _animations = require("../../lib/animations");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function AnimatedPage({ children, className, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        variants: (0, _animations.getAnimationVariants)(_animations.pageVariants),
        initial: "initial",
        animate: "animate",
        exit: "exit",
        className: (0, _utils.cn)("w-full", className),
        ...props,
        children: children
    });
}
function AnimatedCard({ children, className, hover = true, tap = true, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        variants: (0, _animations.getAnimationVariants)(_animations.cardVariants),
        initial: "hidden",
        animate: "visible",
        whileHover: hover ? "hover" : undefined,
        whileTap: tap ? "tap" : undefined,
        className: (0, _utils.cn)("cursor-pointer", className),
        ...props,
        children: children
    });
}
function StaggerContainer({ children, className, staggerDelay = 0.1, ...props }) {
    const variants = {
        ..._animations.staggerContainer,
        visible: {
            ..._animations.staggerContainer.visible,
            transition: {
                staggerChildren: staggerDelay,
                delayChildren: 0.1
            }
        }
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        variants: (0, _animations.getAnimationVariants)(variants),
        initial: "hidden",
        animate: "visible",
        className: className,
        ...props,
        children: children
    });
}
function StaggerItem({ children, className, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        variants: (0, _animations.getAnimationVariants)(_animations.staggerItem),
        className: className,
        ...props,
        children: children
    });
}
function AnimatedModal({ children, className, isOpen, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.AnimatePresence, {
        children: isOpen && /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
            variants: (0, _animations.getAnimationVariants)(_animations.modalVariants),
            initial: "hidden",
            animate: "visible",
            exit: "exit",
            className: className,
            ...props,
            children: children
        })
    });
}
function AnimatedButton({ children, className, disabled = false, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.button, {
        variants: (0, _animations.getAnimationVariants)(_animations.buttonVariants),
        initial: "rest",
        whileHover: !disabled ? "hover" : undefined,
        whileTap: !disabled ? "tap" : undefined,
        className: className,
        disabled: disabled,
        ...props,
        children: children
    });
}
function Fade({ children, className, show, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.AnimatePresence, {
        children: show && /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
            variants: (0, _animations.getAnimationVariants)(_animations.fadeVariants),
            initial: "hidden",
            animate: "visible",
            exit: "exit",
            className: className,
            ...props,
            children: children
        })
    });
}
function Slide({ children, className, direction, show, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.AnimatePresence, {
        children: show && /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
            variants: (0, _animations.getAnimationVariants)(_animations.slideVariants[direction]),
            initial: "hidden",
            animate: "visible",
            exit: "exit",
            className: className,
            ...props,
            children: children
        })
    });
}
function AnimatedSpinner({ className, size = 24, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        animate: {
            rotate: 360
        },
        transition: {
            duration: 1,
            repeat: Infinity,
            ease: "linear"
        },
        className: (0, _utils.cn)("inline-block", className),
        style: {
            width: size,
            height: size
        },
        ...props,
        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("svg", {
            className: "w-full h-full",
            viewBox: "0 0 24 24",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("circle", {
                    cx: "12",
                    cy: "12",
                    r: "10",
                    stroke: "currentColor",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeDasharray: "32",
                    strokeDashoffset: "32",
                    className: "opacity-25"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("circle", {
                    cx: "12",
                    cy: "12",
                    r: "10",
                    stroke: "currentColor",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeDasharray: "32",
                    strokeDashoffset: "24"
                })
            ]
        })
    });
}
function ScaleOnHover({ children, className, scale = 1.05, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        whileHover: {
            scale
        },
        whileTap: {
            scale: 0.95
        },
        transition: (0, _animations.getTransition)({
            duration: 0.2
        }),
        className: className,
        ...props,
        children: children
    });
}
function Pulse({ children, className, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        animate: {
            scale: [
                1,
                1.05,
                1
            ],
            opacity: [
                1,
                0.8,
                1
            ]
        },
        transition: {
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
        },
        className: className,
        ...props,
        children: children
    });
}
function Bounce({ children, className, trigger = false, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        animate: trigger ? {
            y: [
                0,
                -10,
                0
            ]
        } : {},
        transition: {
            duration: 0.5,
            ease: "easeOut"
        },
        className: className,
        ...props,
        children: children
    });
}
function Shake({ children, className, trigger = false, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        animate: trigger ? {
            x: [
                0,
                -10,
                10,
                -10,
                10,
                0
            ]
        } : {},
        transition: {
            duration: 0.5,
            ease: "easeInOut"
        },
        className: className,
        ...props,
        children: children
    });
}
function Float({ children, className, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        animate: {
            y: [
                0,
                -5,
                0
            ]
        },
        transition: {
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
        },
        className: className,
        ...props,
        children: children
    });
}
function Glow({ children, className, color = "blue", ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        whileHover: {
            boxShadow: `0 0 20px rgba(59, 130, 246, 0.5)`
        },
        transition: {
            duration: 0.3
        },
        className: className,
        ...props,
        children: children
    });
}
function Magnetic({ children, className, strength = 0.3, ...props }) {
    const [mousePosition, setMousePosition] = _react.default.useState({
        x: 0,
        y: 0
    });
    const [isHovered, setIsHovered] = _react.default.useState(false);
    const handleMouseMove = (e)=>{
        const rect = e.currentTarget.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        setMousePosition({
            x: (e.clientX - centerX) * strength,
            y: (e.clientY - centerY) * strength
        });
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        animate: isHovered ? {
            x: mousePosition.x,
            y: mousePosition.y
        } : {
            x: 0,
            y: 0
        },
        transition: {
            type: "spring",
            stiffness: 300,
            damping: 30
        },
        onMouseMove: handleMouseMove,
        onMouseEnter: ()=>setIsHovered(true),
        onMouseLeave: ()=>setIsHovered(false),
        className: className,
        ...props,
        children: children
    });
}

//# sourceMappingURL=data:application/json;base64,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