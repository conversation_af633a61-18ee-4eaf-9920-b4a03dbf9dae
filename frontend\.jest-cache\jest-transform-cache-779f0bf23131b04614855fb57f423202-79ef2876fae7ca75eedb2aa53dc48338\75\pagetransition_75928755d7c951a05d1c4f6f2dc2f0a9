c2f1e9361a500e149c389c522f1471b7
"use client";
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "PageTransition", {
    enumerable: true,
    get: function() {
        return PageTransition;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _framermotion = require("framer-motion");
const _navigation = require("next/navigation");
const _animations = require("../../lib/animations");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function PageTransition({ children }) {
    const pathname = (0, _navigation.usePathname)();
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.AnimatePresence, {
        mode: "wait",
        initial: false,
        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
            variants: (0, _animations.getAnimationVariants)(_animations.pageVariants),
            initial: "initial",
            animate: "animate",
            exit: "exit",
            className: "w-full h-full",
            children: children
        }, pathname)
    });
}

//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFxEYUNodWFuZ0JhY2tlbmRcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxccGFnZS10cmFuc2l0aW9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IHsgdXNlUGF0aG5hbWUgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgeyBwYWdlVmFyaWFudHMsIGdldEFuaW1hdGlvblZhcmlhbnRzIH0gZnJvbSAnQC9saWIvYW5pbWF0aW9ucydcblxuaW50ZXJmYWNlIFBhZ2VUcmFuc2l0aW9uUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBQYWdlVHJhbnNpdGlvbih7IGNoaWxkcmVuIH06IFBhZ2VUcmFuc2l0aW9uUHJvcHMpIHtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpXG5cbiAgcmV0dXJuIChcbiAgICA8QW5pbWF0ZVByZXNlbmNlIG1vZGU9XCJ3YWl0XCIgaW5pdGlhbD17ZmFsc2V9PlxuICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAga2V5PXtwYXRobmFtZX1cbiAgICAgICAgdmFyaWFudHM9e2dldEFuaW1hdGlvblZhcmlhbnRzKHBhZ2VWYXJpYW50cyl9XG4gICAgICAgIGluaXRpYWw9XCJpbml0aWFsXCJcbiAgICAgICAgYW5pbWF0ZT1cImFuaW1hdGVcIlxuICAgICAgICBleGl0PVwiZXhpdFwiXG4gICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGxcIlxuICAgICAgPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L21vdGlvbi5kaXY+XG4gICAgPC9BbmltYXRlUHJlc2VuY2U+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJQYWdlVHJhbnNpdGlvbiIsImNoaWxkcmVuIiwicGF0aG5hbWUiLCJ1c2VQYXRobmFtZSIsIkFuaW1hdGVQcmVzZW5jZSIsIm1vZGUiLCJpbml0aWFsIiwibW90aW9uIiwiZGl2IiwidmFyaWFudHMiLCJnZXRBbmltYXRpb25WYXJpYW50cyIsInBhZ2VWYXJpYW50cyIsImFuaW1hdGUiLCJleGl0IiwiY2xhc3NOYW1lIl0sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7K0JBV2dCQTs7O2VBQUFBOzs7OzhEQVRFOzhCQUNzQjs0QkFDWjs0QkFDdUI7Ozs7OztBQU01QyxTQUFTQSxlQUFlLEVBQUVDLFFBQVEsRUFBdUI7SUFDOUQsTUFBTUMsV0FBV0MsSUFBQUEsdUJBQVc7SUFFNUIscUJBQ0UscUJBQUNDLDZCQUFlO1FBQUNDLE1BQUs7UUFBT0MsU0FBUztrQkFDcEMsY0FBQSxxQkFBQ0Msb0JBQU0sQ0FBQ0MsR0FBRztZQUVUQyxVQUFVQyxJQUFBQSxnQ0FBb0IsRUFBQ0Msd0JBQVk7WUFDM0NMLFNBQVE7WUFDUk0sU0FBUTtZQUNSQyxNQUFLO1lBQ0xDLFdBQVU7c0JBRVRiO1dBUElDOztBQVdiIn0=