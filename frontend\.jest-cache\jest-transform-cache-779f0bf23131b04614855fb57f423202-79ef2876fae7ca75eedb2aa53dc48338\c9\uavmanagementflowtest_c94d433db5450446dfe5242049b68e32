2cb83e31a3e101d4fef1a56c2be78057
"use strict";
// Mock the API
jest.mock("@/api/uav-api");
// Mock react-hot-toast
jest.mock("react-hot-toast", ()=>({
        toast: {
            success: jest.fn(),
            error: jest.fn()
        }
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../lib/test-utils");
const _uavstore = require("../../stores/uav-store");
const _uavapi = require("../../api/uav-api");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockedUAVApi = _uavapi.uavApi;
// Integration test component for UAV management
const UAVManagementTestComponent = ()=>{
    const { uavs, selectedUAV, isLoading, error, fetchUAVs, selectUAV, updateUAVStatus, createUAV, updateUAV, deleteUAV } = (0, _uavstore.useUAVStore)();
    const [showCreateForm, setShowCreateForm] = _react.default.useState(false);
    const [formData, setFormData] = _react.default.useState({
        rfidTag: "",
        ownerName: "",
        model: ""
    });
    _react.default.useEffect(()=>{
        fetchUAVs();
    }, [
        fetchUAVs
    ]);
    const handleCreateUAV = async (e)=>{
        e.preventDefault();
        const success = await createUAV(formData);
        if (success) {
            setShowCreateForm(false);
            setFormData({
                rfidTag: "",
                ownerName: "",
                model: ""
            });
        }
    };
    const handleUpdateStatus = async (uavId)=>{
        await updateUAVStatus(uavId);
    };
    const handleDeleteUAV = async (uavId)=>{
        if (window.confirm("Are you sure you want to delete this UAV?")) {
            await deleteUAV(uavId);
        }
    };
    if (isLoading && uavs.length === 0) {
        return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
            children: "Loading UAVs..."
        });
    }
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                children: "UAV Management"
            }),
            error && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                role: "alert",
                children: error
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        onClick: ()=>setShowCreateForm(true),
                        children: "Add New UAV"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        onClick: ()=>fetchUAVs(),
                        disabled: isLoading,
                        children: isLoading ? "Refreshing..." : "Refresh"
                    })
                ]
            }),
            showCreateForm && /*#__PURE__*/ (0, _jsxruntime.jsxs)("form", {
                onSubmit: handleCreateUAV,
                "data-testid": "create-form",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                        children: "Create New UAV"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                                htmlFor: "rfidTag",
                                children: "RFID Tag"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                                id: "rfidTag",
                                type: "text",
                                value: formData.rfidTag,
                                onChange: (e)=>setFormData((prev)=>({
                                            ...prev,
                                            rfidTag: e.target.value
                                        })),
                                required: true
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                                htmlFor: "ownerName",
                                children: "Owner Name"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                                id: "ownerName",
                                type: "text",
                                value: formData.ownerName,
                                onChange: (e)=>setFormData((prev)=>({
                                            ...prev,
                                            ownerName: e.target.value
                                        })),
                                required: true
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                                htmlFor: "model",
                                children: "Model"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                                id: "model",
                                type: "text",
                                value: formData.model,
                                onChange: (e)=>setFormData((prev)=>({
                                            ...prev,
                                            model: e.target.value
                                        })),
                                required: true
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        type: "submit",
                        disabled: isLoading,
                        children: isLoading ? "Creating..." : "Create UAV"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        type: "button",
                        onClick: ()=>setShowCreateForm(false),
                        children: "Cancel"
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "uav-list",
                children: uavs.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                    children: "No UAVs found"
                }) : uavs.map((uav)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        "data-testid": `uav-item-${uav.id}`,
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                children: uav.rfidTag
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                children: [
                                    "Owner: ",
                                    uav.ownerName
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                children: [
                                    "Model: ",
                                    uav.model
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                children: [
                                    "Status: ",
                                    uav.status
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                children: [
                                    "Operational Status: ",
                                    uav.operationalStatus
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                        onClick: ()=>selectUAV(uav),
                                        children: selectedUAV?.id === uav.id ? "Selected" : "Select"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                        onClick: ()=>handleUpdateStatus(uav.id),
                                        children: "Update Status"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                        onClick: ()=>handleDeleteUAV(uav.id),
                                        children: "Delete"
                                    })
                                ]
                            })
                        ]
                    }, uav.id))
            }),
            selectedUAV && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": "selected-uav-details",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                        children: "Selected UAV Details"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "ID: ",
                            selectedUAV.id
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "RFID: ",
                            selectedUAV.rfidTag
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "Owner: ",
                            selectedUAV.ownerName
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "Model: ",
                            selectedUAV.model
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "Flight Hours: ",
                            selectedUAV.totalFlightHours
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "Flight Cycles: ",
                            selectedUAV.totalFlightCycles
                        ]
                    })
                ]
            })
        ]
    });
};
describe("UAV Management Flow Integration Tests", ()=>{
    const mockUAVs = [
        (0, _testutils.createMockUAV)({
            id: 1,
            rfidTag: "UAV-001",
            ownerName: "John Doe",
            model: "Quadcopter X1",
            status: "AUTHORIZED",
            operationalStatus: "READY"
        }),
        (0, _testutils.createMockUAV)({
            id: 2,
            rfidTag: "UAV-002",
            ownerName: "Jane Smith",
            model: "Fixed Wing Y2",
            status: "PENDING",
            operationalStatus: "MAINTENANCE"
        })
    ];
    beforeEach(()=>{
        jest.clearAllMocks();
        // Reset UAV store
        _uavstore.useUAVStore.setState({
            uavs: [],
            selectedUAV: null,
            isLoading: false,
            error: null,
            filters: {},
            pagination: {
                page: 1,
                limit: 10,
                total: 0
            }
        });
        // Mock window.confirm
        window.confirm = jest.fn();
    });
    it("loads and displays UAVs on mount", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Should show loading initially
        expect(_testutils.screen.getByText("Loading UAVs...")).toBeInTheDocument();
        // Wait for UAVs to load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
            expect(_testutils.screen.getByText("UAV-002")).toBeInTheDocument();
        });
        // Should display UAV details
        expect(_testutils.screen.getByText("Owner: John Doe")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Model: Quadcopter X1")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Status: AUTHORIZED")).toBeInTheDocument();
        // Verify API was called
        expect(mockedUAVApi.getUAVs).toHaveBeenCalled();
    });
    it("handles UAV selection", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for UAVs to load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
        });
        // Select first UAV
        const selectButton = _testutils.screen.getAllByText("Select")[0];
        _testutils.fireEvent.click(selectButton);
        // Should show selected UAV details
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByTestId("selected-uav-details")).toBeInTheDocument();
            expect(_testutils.screen.getByText("ID: 1")).toBeInTheDocument();
            expect(_testutils.screen.getByText("RFID: UAV-001")).toBeInTheDocument();
        });
        // Button should show as selected
        expect(_testutils.screen.getByText("Selected")).toBeInTheDocument();
    });
    it("creates new UAV successfully", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue([]);
        const newUAV = (0, _testutils.createMockUAV)({
            id: 3,
            rfidTag: "UAV-003",
            ownerName: "New Owner",
            model: "New Model"
        });
        mockedUAVApi.createUAV.mockResolvedValue({
            success: true,
            data: newUAV,
            message: "UAV created successfully"
        });
        // Mock updated list after creation
        mockedUAVApi.getUAVs.mockResolvedValueOnce([]).mockResolvedValueOnce([
            newUAV
        ]);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for initial load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("No UAVs found")).toBeInTheDocument();
        });
        // Open create form
        _testutils.fireEvent.click(_testutils.screen.getByText("Add New UAV"));
        expect(_testutils.screen.getByTestId("create-form")).toBeInTheDocument();
        // Fill in form
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("RFID Tag"), {
            target: {
                value: "UAV-003"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Owner Name"), {
            target: {
                value: "New Owner"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Model"), {
            target: {
                value: "New Model"
            }
        });
        // Submit form
        _testutils.fireEvent.click(_testutils.screen.getByRole("button", {
            name: "Create UAV"
        }));
        // Wait for creation to complete
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-003")).toBeInTheDocument();
        });
        // Form should be hidden
        expect(_testutils.screen.queryByTestId("create-form")).not.toBeInTheDocument();
        // Verify API was called
        expect(mockedUAVApi.createUAV).toHaveBeenCalledWith({
            rfidTag: "UAV-003",
            ownerName: "New Owner",
            model: "New Model"
        });
    });
    it("handles UAV creation failure", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue([]);
        mockedUAVApi.createUAV.mockRejectedValue(new Error("RFID tag already exists"));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for initial load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("No UAVs found")).toBeInTheDocument();
        });
        // Open create form and fill it
        _testutils.fireEvent.click(_testutils.screen.getByText("Add New UAV"));
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("RFID Tag"), {
            target: {
                value: "DUPLICATE-TAG"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Owner Name"), {
            target: {
                value: "Owner"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Model"), {
            target: {
                value: "Model"
            }
        });
        // Submit form
        _testutils.fireEvent.click(_testutils.screen.getByRole("button", {
            name: "Create UAV"
        }));
        // Wait for error
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
            expect(_testutils.screen.getByText("RFID tag already exists")).toBeInTheDocument();
        });
        // Form should still be visible
        expect(_testutils.screen.getByTestId("create-form")).toBeInTheDocument();
    });
    it("updates UAV status", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        mockedUAVApi.updateUAVStatus.mockResolvedValue({
            success: true,
            message: "Status updated"
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for UAVs to load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
        });
        // Click update status for first UAV
        const updateButtons = _testutils.screen.getAllByText("Update Status");
        _testutils.fireEvent.click(updateButtons[0]);
        // Wait for update to complete
        await (0, _testutils.waitFor)(()=>{
            expect(mockedUAVApi.updateUAVStatus).toHaveBeenCalledWith(1);
        });
    });
    it("deletes UAV with confirmation", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        mockedUAVApi.deleteUAV.mockResolvedValue({
            success: true,
            message: "UAV deleted"
        });
        // Mock updated list after deletion
        mockedUAVApi.getUAVs.mockResolvedValueOnce(mockUAVs).mockResolvedValueOnce([
            mockUAVs[1]
        ]);
        window.confirm.mockReturnValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for UAVs to load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
        });
        // Click delete for first UAV
        const deleteButtons = _testutils.screen.getAllByText("Delete");
        _testutils.fireEvent.click(deleteButtons[0]);
        // Should show confirmation
        expect(window.confirm).toHaveBeenCalledWith("Are you sure you want to delete this UAV?");
        // Wait for deletion to complete
        await (0, _testutils.waitFor)(()=>{
            expect(mockedUAVApi.deleteUAV).toHaveBeenCalledWith(1);
        });
    });
    it("cancels deletion when user declines confirmation", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        window.confirm.mockReturnValue(false);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for UAVs to load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
        });
        // Click delete for first UAV
        const deleteButtons = _testutils.screen.getAllByText("Delete");
        _testutils.fireEvent.click(deleteButtons[0]);
        // Should show confirmation but not call delete API
        expect(window.confirm).toHaveBeenCalled();
        expect(mockedUAVApi.deleteUAV).not.toHaveBeenCalled();
    });
    it("handles refresh functionality", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for initial load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
        });
        // Clear previous calls
        mockedUAVApi.getUAVs.mockClear();
        // Click refresh
        _testutils.fireEvent.click(_testutils.screen.getByText("Refresh"));
        // Should show loading state
        expect(_testutils.screen.getByText("Refreshing...")).toBeInTheDocument();
        // Wait for refresh to complete
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Refresh")).toBeInTheDocument();
        });
        // Verify API was called again
        expect(mockedUAVApi.getUAVs).toHaveBeenCalled();
    });
    it("handles API errors gracefully", async ()=>{
        mockedUAVApi.getUAVs.mockRejectedValue(new Error("Network error"));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for error to appear
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Network error")).toBeInTheDocument();
        });
        // Should not show loading state
        expect(_testutils.screen.queryByText("Loading UAVs...")).not.toBeInTheDocument();
    });
});

//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFxEYUNodWFuZ0JhY2tlbmRcXGZyb250ZW5kXFxzcmNcXF9fdGVzdHNfX1xcaW50ZWdyYXRpb25cXHVhdi1tYW5hZ2VtZW50LWZsb3cudGVzdC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgcmVuZGVyLCBzY3JlZW4sIGZpcmVFdmVudCwgd2FpdEZvciB9IGZyb20gJ0AvbGliL3Rlc3QtdXRpbHMnXG5pbXBvcnQgeyB1c2VVQVZTdG9yZSB9IGZyb20gJ0Avc3RvcmVzL3Vhdi1zdG9yZSdcbmltcG9ydCB7IHVhdkFwaSB9IGZyb20gJ0AvYXBpL3Vhdi1hcGknXG5pbXBvcnQgeyBjcmVhdGVNb2NrVUFWIH0gZnJvbSAnQC9saWIvdGVzdC11dGlscydcbmltcG9ydCB7IFVBViB9IGZyb20gJ0AvdHlwZXMvdWF2J1xuXG4vLyBNb2NrIHRoZSBBUElcbmplc3QubW9jaygnQC9hcGkvdWF2LWFwaScpXG5jb25zdCBtb2NrZWRVQVZBcGkgPSB1YXZBcGkgYXMgamVzdC5Nb2NrZWQ8dHlwZW9mIHVhdkFwaT5cblxuLy8gTW9jayByZWFjdC1ob3QtdG9hc3Rcbmplc3QubW9jaygncmVhY3QtaG90LXRvYXN0JywgKCkgPT4gKHtcbiAgdG9hc3Q6IHtcbiAgICBzdWNjZXNzOiBqZXN0LmZuKCksXG4gICAgZXJyb3I6IGplc3QuZm4oKSxcbiAgfSxcbn0pKVxuXG4vLyBJbnRlZ3JhdGlvbiB0ZXN0IGNvbXBvbmVudCBmb3IgVUFWIG1hbmFnZW1lbnRcbmNvbnN0IFVBVk1hbmFnZW1lbnRUZXN0Q29tcG9uZW50ID0gKCkgPT4ge1xuICBjb25zdCB7IFxuICAgIHVhdnMsIFxuICAgIHNlbGVjdGVkVUFWLCBcbiAgICBpc0xvYWRpbmcsIFxuICAgIGVycm9yLFxuICAgIGZldGNoVUFWcyxcbiAgICBzZWxlY3RVQVYsXG4gICAgdXBkYXRlVUFWU3RhdHVzLFxuICAgIGNyZWF0ZVVBVixcbiAgICB1cGRhdGVVQVYsXG4gICAgZGVsZXRlVUFWXG4gIH0gPSB1c2VVQVZTdG9yZSgpXG5cbiAgY29uc3QgW3Nob3dDcmVhdGVGb3JtLCBzZXRTaG93Q3JlYXRlRm9ybV0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSBSZWFjdC51c2VTdGF0ZSh7XG4gICAgcmZpZFRhZzogJycsXG4gICAgb3duZXJOYW1lOiAnJyxcbiAgICBtb2RlbDogJycsXG4gIH0pXG5cbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaFVBVnMoKVxuICB9LCBbZmV0Y2hVQVZzXSlcblxuICBjb25zdCBoYW5kbGVDcmVhdGVVQVYgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgY29uc3Qgc3VjY2VzcyA9IGF3YWl0IGNyZWF0ZVVBVihmb3JtRGF0YSlcbiAgICBpZiAoc3VjY2Vzcykge1xuICAgICAgc2V0U2hvd0NyZWF0ZUZvcm0oZmFsc2UpXG4gICAgICBzZXRGb3JtRGF0YSh7IHJmaWRUYWc6ICcnLCBvd25lck5hbWU6ICcnLCBtb2RlbDogJycgfSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVVcGRhdGVTdGF0dXMgPSBhc3luYyAodWF2SWQ6IG51bWJlcikgPT4ge1xuICAgIGF3YWl0IHVwZGF0ZVVBVlN0YXR1cyh1YXZJZClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZVVBViA9IGFzeW5jICh1YXZJZDogbnVtYmVyKSA9PiB7XG4gICAgaWYgKHdpbmRvdy5jb25maXJtKCdBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIHRoaXMgVUFWPycpKSB7XG4gICAgICBhd2FpdCBkZWxldGVVQVYodWF2SWQpXG4gICAgfVxuICB9XG5cbiAgaWYgKGlzTG9hZGluZyAmJiB1YXZzLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiA8ZGl2PkxvYWRpbmcgVUFWcy4uLjwvZGl2PlxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2PlxuICAgICAgPGgxPlVBViBNYW5hZ2VtZW50PC9oMT5cbiAgICAgIFxuICAgICAge2Vycm9yICYmIDxkaXYgcm9sZT1cImFsZXJ0XCI+e2Vycm9yfTwvZGl2Pn1cbiAgICAgIFxuICAgICAgPGRpdj5cbiAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q3JlYXRlRm9ybSh0cnVlKX0+XG4gICAgICAgICAgQWRkIE5ldyBVQVZcbiAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDxidXR0b24gb25DbGljaz17KCkgPT4gZmV0Y2hVQVZzKCl9IGRpc2FibGVkPXtpc0xvYWRpbmd9PlxuICAgICAgICAgIHtpc0xvYWRpbmcgPyAnUmVmcmVzaGluZy4uLicgOiAnUmVmcmVzaCd9XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHtzaG93Q3JlYXRlRm9ybSAmJiAoXG4gICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVDcmVhdGVVQVZ9IGRhdGEtdGVzdGlkPVwiY3JlYXRlLWZvcm1cIj5cbiAgICAgICAgICA8aDI+Q3JlYXRlIE5ldyBVQVY8L2gyPlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cInJmaWRUYWdcIj5SRklEIFRhZzwvbGFiZWw+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgaWQ9XCJyZmlkVGFnXCJcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucmZpZFRhZ31cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHJmaWRUYWc6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwib3duZXJOYW1lXCI+T3duZXIgTmFtZTwvbGFiZWw+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgaWQ9XCJvd25lck5hbWVcIlxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5vd25lck5hbWV9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBvd25lck5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwibW9kZWxcIj5Nb2RlbDwvbGFiZWw+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgaWQ9XCJtb2RlbFwiXG4gICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLm1vZGVsfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgbW9kZWw6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGJ1dHRvbiB0eXBlPVwic3VibWl0XCIgZGlzYWJsZWQ9e2lzTG9hZGluZ30+XG4gICAgICAgICAgICB7aXNMb2FkaW5nID8gJ0NyZWF0aW5nLi4uJyA6ICdDcmVhdGUgVUFWJ31cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8YnV0dG9uIHR5cGU9XCJidXR0b25cIiBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q3JlYXRlRm9ybShmYWxzZSl9PlxuICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZm9ybT5cbiAgICAgICl9XG5cbiAgICAgIDxkaXYgZGF0YS10ZXN0aWQ9XCJ1YXYtbGlzdFwiPlxuICAgICAgICB7dWF2cy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgPHA+Tm8gVUFWcyBmb3VuZDwvcD5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICB1YXZzLm1hcCh1YXYgPT4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e3Vhdi5pZH0gZGF0YS10ZXN0aWQ9e2B1YXYtaXRlbS0ke3Vhdi5pZH1gfT5cbiAgICAgICAgICAgICAgPGgzPnt1YXYucmZpZFRhZ308L2gzPlxuICAgICAgICAgICAgICA8cD5Pd25lcjoge3Vhdi5vd25lck5hbWV9PC9wPlxuICAgICAgICAgICAgICA8cD5Nb2RlbDoge3Vhdi5tb2RlbH08L3A+XG4gICAgICAgICAgICAgIDxwPlN0YXR1czoge3Vhdi5zdGF0dXN9PC9wPlxuICAgICAgICAgICAgICA8cD5PcGVyYXRpb25hbCBTdGF0dXM6IHt1YXYub3BlcmF0aW9uYWxTdGF0dXN9PC9wPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9eygpID0+IHNlbGVjdFVBVih1YXYpfT5cbiAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFVBVj8uaWQgPT09IHVhdi5pZCA/ICdTZWxlY3RlZCcgOiAnU2VsZWN0J31cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9eygpID0+IGhhbmRsZVVwZGF0ZVN0YXR1cyh1YXYuaWQpfT5cbiAgICAgICAgICAgICAgICAgIFVwZGF0ZSBTdGF0dXNcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZVVBVih1YXYuaWQpfT5cbiAgICAgICAgICAgICAgICAgIERlbGV0ZVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpXG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAge3NlbGVjdGVkVUFWICYmIChcbiAgICAgICAgPGRpdiBkYXRhLXRlc3RpZD1cInNlbGVjdGVkLXVhdi1kZXRhaWxzXCI+XG4gICAgICAgICAgPGgyPlNlbGVjdGVkIFVBViBEZXRhaWxzPC9oMj5cbiAgICAgICAgICA8cD5JRDoge3NlbGVjdGVkVUFWLmlkfTwvcD5cbiAgICAgICAgICA8cD5SRklEOiB7c2VsZWN0ZWRVQVYucmZpZFRhZ308L3A+XG4gICAgICAgICAgPHA+T3duZXI6IHtzZWxlY3RlZFVBVi5vd25lck5hbWV9PC9wPlxuICAgICAgICAgIDxwPk1vZGVsOiB7c2VsZWN0ZWRVQVYubW9kZWx9PC9wPlxuICAgICAgICAgIDxwPkZsaWdodCBIb3Vyczoge3NlbGVjdGVkVUFWLnRvdGFsRmxpZ2h0SG91cnN9PC9wPlxuICAgICAgICAgIDxwPkZsaWdodCBDeWNsZXM6IHtzZWxlY3RlZFVBVi50b3RhbEZsaWdodEN5Y2xlc308L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5kZXNjcmliZSgnVUFWIE1hbmFnZW1lbnQgRmxvdyBJbnRlZ3JhdGlvbiBUZXN0cycsICgpID0+IHtcbiAgY29uc3QgbW9ja1VBVnM6IFVBVltdID0gW1xuICAgIGNyZWF0ZU1vY2tVQVYoe1xuICAgICAgaWQ6IDEsXG4gICAgICByZmlkVGFnOiAnVUFWLTAwMScsXG4gICAgICBvd25lck5hbWU6ICdKb2huIERvZScsXG4gICAgICBtb2RlbDogJ1F1YWRjb3B0ZXIgWDEnLFxuICAgICAgc3RhdHVzOiAnQVVUSE9SSVpFRCcsXG4gICAgICBvcGVyYXRpb25hbFN0YXR1czogJ1JFQURZJyxcbiAgICB9KSxcbiAgICBjcmVhdGVNb2NrVUFWKHtcbiAgICAgIGlkOiAyLFxuICAgICAgcmZpZFRhZzogJ1VBVi0wMDInLFxuICAgICAgb3duZXJOYW1lOiAnSmFuZSBTbWl0aCcsXG4gICAgICBtb2RlbDogJ0ZpeGVkIFdpbmcgWTInLFxuICAgICAgc3RhdHVzOiAnUEVORElORycsXG4gICAgICBvcGVyYXRpb25hbFN0YXR1czogJ01BSU5URU5BTkNFJyxcbiAgICB9KSxcbiAgXVxuXG4gIGJlZm9yZUVhY2goKCkgPT4ge1xuICAgIGplc3QuY2xlYXJBbGxNb2NrcygpXG4gICAgXG4gICAgLy8gUmVzZXQgVUFWIHN0b3JlXG4gICAgdXNlVUFWU3RvcmUuc2V0U3RhdGUoe1xuICAgICAgdWF2czogW10sXG4gICAgICBzZWxlY3RlZFVBVjogbnVsbCxcbiAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICBlcnJvcjogbnVsbCxcbiAgICAgIGZpbHRlcnM6IHt9LFxuICAgICAgcGFnaW5hdGlvbjogeyBwYWdlOiAxLCBsaW1pdDogMTAsIHRvdGFsOiAwIH0sXG4gICAgfSlcblxuICAgIC8vIE1vY2sgd2luZG93LmNvbmZpcm1cbiAgICB3aW5kb3cuY29uZmlybSA9IGplc3QuZm4oKVxuICB9KVxuXG4gIGl0KCdsb2FkcyBhbmQgZGlzcGxheXMgVUFWcyBvbiBtb3VudCcsIGFzeW5jICgpID0+IHtcbiAgICBtb2NrZWRVQVZBcGkuZ2V0VUFWcy5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrVUFWcylcblxuICAgIHJlbmRlcig8VUFWTWFuYWdlbWVudFRlc3RDb21wb25lbnQgLz4pXG5cbiAgICAvLyBTaG91bGQgc2hvdyBsb2FkaW5nIGluaXRpYWxseVxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdMb2FkaW5nIFVBVnMuLi4nKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuXG4gICAgLy8gV2FpdCBmb3IgVUFWcyB0byBsb2FkXG4gICAgYXdhaXQgd2FpdEZvcigoKSA9PiB7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnVUFWLTAwMScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnVUFWLTAwMicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgfSlcblxuICAgIC8vIFNob3VsZCBkaXNwbGF5IFVBViBkZXRhaWxzXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ093bmVyOiBKb2huIERvZScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ01vZGVsOiBRdWFkY29wdGVyIFgxJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnU3RhdHVzOiBBVVRIT1JJWkVEJykpLnRvQmVJblRoZURvY3VtZW50KClcblxuICAgIC8vIFZlcmlmeSBBUEkgd2FzIGNhbGxlZFxuICAgIGV4cGVjdChtb2NrZWRVQVZBcGkuZ2V0VUFWcykudG9IYXZlQmVlbkNhbGxlZCgpXG4gIH0pXG5cbiAgaXQoJ2hhbmRsZXMgVUFWIHNlbGVjdGlvbicsIGFzeW5jICgpID0+IHtcbiAgICBtb2NrZWRVQVZBcGkuZ2V0VUFWcy5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrVUFWcylcblxuICAgIHJlbmRlcig8VUFWTWFuYWdlbWVudFRlc3RDb21wb25lbnQgLz4pXG5cbiAgICAvLyBXYWl0IGZvciBVQVZzIHRvIGxvYWRcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdVQVYtMDAxJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICB9KVxuXG4gICAgLy8gU2VsZWN0IGZpcnN0IFVBVlxuICAgIGNvbnN0IHNlbGVjdEJ1dHRvbiA9IHNjcmVlbi5nZXRBbGxCeVRleHQoJ1NlbGVjdCcpWzBdXG4gICAgZmlyZUV2ZW50LmNsaWNrKHNlbGVjdEJ1dHRvbilcblxuICAgIC8vIFNob3VsZCBzaG93IHNlbGVjdGVkIFVBViBkZXRhaWxzXG4gICAgYXdhaXQgd2FpdEZvcigoKSA9PiB7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdzZWxlY3RlZC11YXYtZGV0YWlscycpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnSUQ6IDEnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ1JGSUQ6IFVBVi0wMDEnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG5cbiAgICAvLyBCdXR0b24gc2hvdWxkIHNob3cgYXMgc2VsZWN0ZWRcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnU2VsZWN0ZWQnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdjcmVhdGVzIG5ldyBVQVYgc3VjY2Vzc2Z1bGx5JywgYXN5bmMgKCkgPT4ge1xuICAgIG1vY2tlZFVBVkFwaS5nZXRVQVZzLm1vY2tSZXNvbHZlZFZhbHVlKFtdKVxuICAgIFxuICAgIGNvbnN0IG5ld1VBViA9IGNyZWF0ZU1vY2tVQVYoe1xuICAgICAgaWQ6IDMsXG4gICAgICByZmlkVGFnOiAnVUFWLTAwMycsXG4gICAgICBvd25lck5hbWU6ICdOZXcgT3duZXInLFxuICAgICAgbW9kZWw6ICdOZXcgTW9kZWwnLFxuICAgIH0pXG4gICAgXG4gICAgbW9ja2VkVUFWQXBpLmNyZWF0ZVVBVi5tb2NrUmVzb2x2ZWRWYWx1ZSh7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YTogbmV3VUFWLFxuICAgICAgbWVzc2FnZTogJ1VBViBjcmVhdGVkIHN1Y2Nlc3NmdWxseScsXG4gICAgfSlcblxuICAgIC8vIE1vY2sgdXBkYXRlZCBsaXN0IGFmdGVyIGNyZWF0aW9uXG4gICAgbW9ja2VkVUFWQXBpLmdldFVBVnMubW9ja1Jlc29sdmVkVmFsdWVPbmNlKFtdKS5tb2NrUmVzb2x2ZWRWYWx1ZU9uY2UoW25ld1VBVl0pXG5cbiAgICByZW5kZXIoPFVBVk1hbmFnZW1lbnRUZXN0Q29tcG9uZW50IC8+KVxuXG4gICAgLy8gV2FpdCBmb3IgaW5pdGlhbCBsb2FkXG4gICAgYXdhaXQgd2FpdEZvcigoKSA9PiB7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnTm8gVUFWcyBmb3VuZCcpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgfSlcblxuICAgIC8vIE9wZW4gY3JlYXRlIGZvcm1cbiAgICBmaXJlRXZlbnQuY2xpY2soc2NyZWVuLmdldEJ5VGV4dCgnQWRkIE5ldyBVQVYnKSlcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdjcmVhdGUtZm9ybScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG5cbiAgICAvLyBGaWxsIGluIGZvcm1cbiAgICBmaXJlRXZlbnQuY2hhbmdlKHNjcmVlbi5nZXRCeUxhYmVsVGV4dCgnUkZJRCBUYWcnKSwge1xuICAgICAgdGFyZ2V0OiB7IHZhbHVlOiAnVUFWLTAwMycgfVxuICAgIH0pXG4gICAgZmlyZUV2ZW50LmNoYW5nZShzY3JlZW4uZ2V0QnlMYWJlbFRleHQoJ093bmVyIE5hbWUnKSwge1xuICAgICAgdGFyZ2V0OiB7IHZhbHVlOiAnTmV3IE93bmVyJyB9XG4gICAgfSlcbiAgICBmaXJlRXZlbnQuY2hhbmdlKHNjcmVlbi5nZXRCeUxhYmVsVGV4dCgnTW9kZWwnKSwge1xuICAgICAgdGFyZ2V0OiB7IHZhbHVlOiAnTmV3IE1vZGVsJyB9XG4gICAgfSlcblxuICAgIC8vIFN1Ym1pdCBmb3JtXG4gICAgZmlyZUV2ZW50LmNsaWNrKHNjcmVlbi5nZXRCeVJvbGUoJ2J1dHRvbicsIHsgbmFtZTogJ0NyZWF0ZSBVQVYnIH0pKVxuXG4gICAgLy8gV2FpdCBmb3IgY3JlYXRpb24gdG8gY29tcGxldGVcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdVQVYtMDAzJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICB9KVxuXG4gICAgLy8gRm9ybSBzaG91bGQgYmUgaGlkZGVuXG4gICAgZXhwZWN0KHNjcmVlbi5xdWVyeUJ5VGVzdElkKCdjcmVhdGUtZm9ybScpKS5ub3QudG9CZUluVGhlRG9jdW1lbnQoKVxuXG4gICAgLy8gVmVyaWZ5IEFQSSB3YXMgY2FsbGVkXG4gICAgZXhwZWN0KG1vY2tlZFVBVkFwaS5jcmVhdGVVQVYpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKHtcbiAgICAgIHJmaWRUYWc6ICdVQVYtMDAzJyxcbiAgICAgIG93bmVyTmFtZTogJ05ldyBPd25lcicsXG4gICAgICBtb2RlbDogJ05ldyBNb2RlbCcsXG4gICAgfSlcbiAgfSlcblxuICBpdCgnaGFuZGxlcyBVQVYgY3JlYXRpb24gZmFpbHVyZScsIGFzeW5jICgpID0+IHtcbiAgICBtb2NrZWRVQVZBcGkuZ2V0VUFWcy5tb2NrUmVzb2x2ZWRWYWx1ZShbXSlcbiAgICBtb2NrZWRVQVZBcGkuY3JlYXRlVUFWLm1vY2tSZWplY3RlZFZhbHVlKG5ldyBFcnJvcignUkZJRCB0YWcgYWxyZWFkeSBleGlzdHMnKSlcblxuICAgIHJlbmRlcig8VUFWTWFuYWdlbWVudFRlc3RDb21wb25lbnQgLz4pXG5cbiAgICAvLyBXYWl0IGZvciBpbml0aWFsIGxvYWRcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdObyBVQVZzIGZvdW5kJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICB9KVxuXG4gICAgLy8gT3BlbiBjcmVhdGUgZm9ybSBhbmQgZmlsbCBpdFxuICAgIGZpcmVFdmVudC5jbGljayhzY3JlZW4uZ2V0QnlUZXh0KCdBZGQgTmV3IFVBVicpKVxuICAgIFxuICAgIGZpcmVFdmVudC5jaGFuZ2Uoc2NyZWVuLmdldEJ5TGFiZWxUZXh0KCdSRklEIFRhZycpLCB7XG4gICAgICB0YXJnZXQ6IHsgdmFsdWU6ICdEVVBMSUNBVEUtVEFHJyB9XG4gICAgfSlcbiAgICBmaXJlRXZlbnQuY2hhbmdlKHNjcmVlbi5nZXRCeUxhYmVsVGV4dCgnT3duZXIgTmFtZScpLCB7XG4gICAgICB0YXJnZXQ6IHsgdmFsdWU6ICdPd25lcicgfVxuICAgIH0pXG4gICAgZmlyZUV2ZW50LmNoYW5nZShzY3JlZW4uZ2V0QnlMYWJlbFRleHQoJ01vZGVsJyksIHtcbiAgICAgIHRhcmdldDogeyB2YWx1ZTogJ01vZGVsJyB9XG4gICAgfSlcblxuICAgIC8vIFN1Ym1pdCBmb3JtXG4gICAgZmlyZUV2ZW50LmNsaWNrKHNjcmVlbi5nZXRCeVJvbGUoJ2J1dHRvbicsIHsgbmFtZTogJ0NyZWF0ZSBVQVYnIH0pKVxuXG4gICAgLy8gV2FpdCBmb3IgZXJyb3JcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlSb2xlKCdhbGVydCcpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnUkZJRCB0YWcgYWxyZWFkeSBleGlzdHMnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG5cbiAgICAvLyBGb3JtIHNob3VsZCBzdGlsbCBiZSB2aXNpYmxlXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnY3JlYXRlLWZvcm0nKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCd1cGRhdGVzIFVBViBzdGF0dXMnLCBhc3luYyAoKSA9PiB7XG4gICAgbW9ja2VkVUFWQXBpLmdldFVBVnMubW9ja1Jlc29sdmVkVmFsdWUobW9ja1VBVnMpXG4gICAgbW9ja2VkVUFWQXBpLnVwZGF0ZVVBVlN0YXR1cy5tb2NrUmVzb2x2ZWRWYWx1ZSh7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgbWVzc2FnZTogJ1N0YXR1cyB1cGRhdGVkJyxcbiAgICB9KVxuXG4gICAgcmVuZGVyKDxVQVZNYW5hZ2VtZW50VGVzdENvbXBvbmVudCAvPilcblxuICAgIC8vIFdhaXQgZm9yIFVBVnMgdG8gbG9hZFxuICAgIGF3YWl0IHdhaXRGb3IoKCkgPT4ge1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ1VBVi0wMDEnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG5cbiAgICAvLyBDbGljayB1cGRhdGUgc3RhdHVzIGZvciBmaXJzdCBVQVZcbiAgICBjb25zdCB1cGRhdGVCdXR0b25zID0gc2NyZWVuLmdldEFsbEJ5VGV4dCgnVXBkYXRlIFN0YXR1cycpXG4gICAgZmlyZUV2ZW50LmNsaWNrKHVwZGF0ZUJ1dHRvbnNbMF0pXG5cbiAgICAvLyBXYWl0IGZvciB1cGRhdGUgdG8gY29tcGxldGVcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChtb2NrZWRVQVZBcGkudXBkYXRlVUFWU3RhdHVzKS50b0hhdmVCZWVuQ2FsbGVkV2l0aCgxKVxuICAgIH0pXG4gIH0pXG5cbiAgaXQoJ2RlbGV0ZXMgVUFWIHdpdGggY29uZmlybWF0aW9uJywgYXN5bmMgKCkgPT4ge1xuICAgIG1vY2tlZFVBVkFwaS5nZXRVQVZzLm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tVQVZzKVxuICAgIG1vY2tlZFVBVkFwaS5kZWxldGVVQVYubW9ja1Jlc29sdmVkVmFsdWUoe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6ICdVQVYgZGVsZXRlZCcsXG4gICAgfSlcbiAgICBcbiAgICAvLyBNb2NrIHVwZGF0ZWQgbGlzdCBhZnRlciBkZWxldGlvblxuICAgIG1vY2tlZFVBVkFwaS5nZXRVQVZzLm1vY2tSZXNvbHZlZFZhbHVlT25jZShtb2NrVUFWcykubW9ja1Jlc29sdmVkVmFsdWVPbmNlKFttb2NrVUFWc1sxXV0pXG4gICAgXG4gICAgOyh3aW5kb3cuY29uZmlybSBhcyBqZXN0Lk1vY2spLm1vY2tSZXR1cm5WYWx1ZSh0cnVlKVxuXG4gICAgcmVuZGVyKDxVQVZNYW5hZ2VtZW50VGVzdENvbXBvbmVudCAvPilcblxuICAgIC8vIFdhaXQgZm9yIFVBVnMgdG8gbG9hZFxuICAgIGF3YWl0IHdhaXRGb3IoKCkgPT4ge1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ1VBVi0wMDEnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG5cbiAgICAvLyBDbGljayBkZWxldGUgZm9yIGZpcnN0IFVBVlxuICAgIGNvbnN0IGRlbGV0ZUJ1dHRvbnMgPSBzY3JlZW4uZ2V0QWxsQnlUZXh0KCdEZWxldGUnKVxuICAgIGZpcmVFdmVudC5jbGljayhkZWxldGVCdXR0b25zWzBdKVxuXG4gICAgLy8gU2hvdWxkIHNob3cgY29uZmlybWF0aW9uXG4gICAgZXhwZWN0KHdpbmRvdy5jb25maXJtKS50b0hhdmVCZWVuQ2FsbGVkV2l0aCgnQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIFVBVj8nKVxuXG4gICAgLy8gV2FpdCBmb3IgZGVsZXRpb24gdG8gY29tcGxldGVcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChtb2NrZWRVQVZBcGkuZGVsZXRlVUFWKS50b0hhdmVCZWVuQ2FsbGVkV2l0aCgxKVxuICAgIH0pXG4gIH0pXG5cbiAgaXQoJ2NhbmNlbHMgZGVsZXRpb24gd2hlbiB1c2VyIGRlY2xpbmVzIGNvbmZpcm1hdGlvbicsIGFzeW5jICgpID0+IHtcbiAgICBtb2NrZWRVQVZBcGkuZ2V0VUFWcy5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrVUFWcylcbiAgICA7KHdpbmRvdy5jb25maXJtIGFzIGplc3QuTW9jaykubW9ja1JldHVyblZhbHVlKGZhbHNlKVxuXG4gICAgcmVuZGVyKDxVQVZNYW5hZ2VtZW50VGVzdENvbXBvbmVudCAvPilcblxuICAgIC8vIFdhaXQgZm9yIFVBVnMgdG8gbG9hZFxuICAgIGF3YWl0IHdhaXRGb3IoKCkgPT4ge1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ1VBVi0wMDEnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG5cbiAgICAvLyBDbGljayBkZWxldGUgZm9yIGZpcnN0IFVBVlxuICAgIGNvbnN0IGRlbGV0ZUJ1dHRvbnMgPSBzY3JlZW4uZ2V0QWxsQnlUZXh0KCdEZWxldGUnKVxuICAgIGZpcmVFdmVudC5jbGljayhkZWxldGVCdXR0b25zWzBdKVxuXG4gICAgLy8gU2hvdWxkIHNob3cgY29uZmlybWF0aW9uIGJ1dCBub3QgY2FsbCBkZWxldGUgQVBJXG4gICAgZXhwZWN0KHdpbmRvdy5jb25maXJtKS50b0hhdmVCZWVuQ2FsbGVkKClcbiAgICBleHBlY3QobW9ja2VkVUFWQXBpLmRlbGV0ZVVBVikubm90LnRvSGF2ZUJlZW5DYWxsZWQoKVxuICB9KVxuXG4gIGl0KCdoYW5kbGVzIHJlZnJlc2ggZnVuY3Rpb25hbGl0eScsIGFzeW5jICgpID0+IHtcbiAgICBtb2NrZWRVQVZBcGkuZ2V0VUFWcy5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrVUFWcylcblxuICAgIHJlbmRlcig8VUFWTWFuYWdlbWVudFRlc3RDb21wb25lbnQgLz4pXG5cbiAgICAvLyBXYWl0IGZvciBpbml0aWFsIGxvYWRcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdVQVYtMDAxJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICB9KVxuXG4gICAgLy8gQ2xlYXIgcHJldmlvdXMgY2FsbHNcbiAgICBtb2NrZWRVQVZBcGkuZ2V0VUFWcy5tb2NrQ2xlYXIoKVxuXG4gICAgLy8gQ2xpY2sgcmVmcmVzaFxuICAgIGZpcmVFdmVudC5jbGljayhzY3JlZW4uZ2V0QnlUZXh0KCdSZWZyZXNoJykpXG5cbiAgICAvLyBTaG91bGQgc2hvdyBsb2FkaW5nIHN0YXRlXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ1JlZnJlc2hpbmcuLi4nKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuXG4gICAgLy8gV2FpdCBmb3IgcmVmcmVzaCB0byBjb21wbGV0ZVxuICAgIGF3YWl0IHdhaXRGb3IoKCkgPT4ge1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ1JlZnJlc2gnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG5cbiAgICAvLyBWZXJpZnkgQVBJIHdhcyBjYWxsZWQgYWdhaW5cbiAgICBleHBlY3QobW9ja2VkVUFWQXBpLmdldFVBVnMpLnRvSGF2ZUJlZW5DYWxsZWQoKVxuICB9KVxuXG4gIGl0KCdoYW5kbGVzIEFQSSBlcnJvcnMgZ3JhY2VmdWxseScsIGFzeW5jICgpID0+IHtcbiAgICBtb2NrZWRVQVZBcGkuZ2V0VUFWcy5tb2NrUmVqZWN0ZWRWYWx1ZShuZXcgRXJyb3IoJ05ldHdvcmsgZXJyb3InKSlcblxuICAgIHJlbmRlcig8VUFWTWFuYWdlbWVudFRlc3RDb21wb25lbnQgLz4pXG5cbiAgICAvLyBXYWl0IGZvciBlcnJvciB0byBhcHBlYXJcbiAgICBhd2FpdCB3YWl0Rm9yKCgpID0+IHtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlSb2xlKCdhbGVydCcpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnTmV0d29yayBlcnJvcicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgfSlcblxuICAgIC8vIFNob3VsZCBub3Qgc2hvdyBsb2FkaW5nIHN0YXRlXG4gICAgZXhwZWN0KHNjcmVlbi5xdWVyeUJ5VGV4dCgnTG9hZGluZyBVQVZzLi4uJykpLm5vdC50b0JlSW5UaGVEb2N1bWVudCgpXG4gIH0pXG59KVxuIl0sIm5hbWVzIjpbImplc3QiLCJtb2NrIiwidG9hc3QiLCJzdWNjZXNzIiwiZm4iLCJlcnJvciIsIm1vY2tlZFVBVkFwaSIsInVhdkFwaSIsIlVBVk1hbmFnZW1lbnRUZXN0Q29tcG9uZW50IiwidWF2cyIsInNlbGVjdGVkVUFWIiwiaXNMb2FkaW5nIiwiZmV0Y2hVQVZzIiwic2VsZWN0VUFWIiwidXBkYXRlVUFWU3RhdHVzIiwiY3JlYXRlVUFWIiwidXBkYXRlVUFWIiwiZGVsZXRlVUFWIiwidXNlVUFWU3RvcmUiLCJzaG93Q3JlYXRlRm9ybSIsInNldFNob3dDcmVhdGVGb3JtIiwiUmVhY3QiLCJ1c2VTdGF0ZSIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJyZmlkVGFnIiwib3duZXJOYW1lIiwibW9kZWwiLCJ1c2VFZmZlY3QiLCJoYW5kbGVDcmVhdGVVQVYiLCJlIiwicHJldmVudERlZmF1bHQiLCJoYW5kbGVVcGRhdGVTdGF0dXMiLCJ1YXZJZCIsImhhbmRsZURlbGV0ZVVBViIsIndpbmRvdyIsImNvbmZpcm0iLCJsZW5ndGgiLCJkaXYiLCJoMSIsInJvbGUiLCJidXR0b24iLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJmb3JtIiwib25TdWJtaXQiLCJkYXRhLXRlc3RpZCIsImgyIiwibGFiZWwiLCJodG1sRm9yIiwiaW5wdXQiLCJpZCIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwicHJldiIsInRhcmdldCIsInJlcXVpcmVkIiwicCIsIm1hcCIsInVhdiIsImgzIiwic3RhdHVzIiwib3BlcmF0aW9uYWxTdGF0dXMiLCJ0b3RhbEZsaWdodEhvdXJzIiwidG90YWxGbGlnaHRDeWNsZXMiLCJkZXNjcmliZSIsIm1vY2tVQVZzIiwiY3JlYXRlTW9ja1VBViIsImJlZm9yZUVhY2giLCJjbGVhckFsbE1vY2tzIiwic2V0U3RhdGUiLCJmaWx0ZXJzIiwicGFnaW5hdGlvbiIsInBhZ2UiLCJsaW1pdCIsInRvdGFsIiwiaXQiLCJnZXRVQVZzIiwibW9ja1Jlc29sdmVkVmFsdWUiLCJyZW5kZXIiLCJleHBlY3QiLCJzY3JlZW4iLCJnZXRCeVRleHQiLCJ0b0JlSW5UaGVEb2N1bWVudCIsIndhaXRGb3IiLCJ0b0hhdmVCZWVuQ2FsbGVkIiwic2VsZWN0QnV0dG9uIiwiZ2V0QWxsQnlUZXh0IiwiZmlyZUV2ZW50IiwiY2xpY2siLCJnZXRCeVRlc3RJZCIsIm5ld1VBViIsImRhdGEiLCJtZXNzYWdlIiwibW9ja1Jlc29sdmVkVmFsdWVPbmNlIiwiY2hhbmdlIiwiZ2V0QnlMYWJlbFRleHQiLCJnZXRCeVJvbGUiLCJuYW1lIiwicXVlcnlCeVRlc3RJZCIsIm5vdCIsInRvSGF2ZUJlZW5DYWxsZWRXaXRoIiwibW9ja1JlamVjdGVkVmFsdWUiLCJFcnJvciIsInVwZGF0ZUJ1dHRvbnMiLCJtb2NrUmV0dXJuVmFsdWUiLCJkZWxldGVCdXR0b25zIiwibW9ja0NsZWFyIiwicXVlcnlCeVRleHQiXSwibWFwcGluZ3MiOiI7QUFPQSxlQUFlO0FBQ2ZBLEtBQUtDLElBQUksQ0FBQztBQUdWLHVCQUF1QjtBQUN2QkQsS0FBS0MsSUFBSSxDQUFDLG1CQUFtQixJQUFPLENBQUE7UUFDbENDLE9BQU87WUFDTEMsU0FBU0gsS0FBS0ksRUFBRTtZQUNoQkMsT0FBT0wsS0FBS0ksRUFBRTtRQUNoQjtJQUNGLENBQUE7Ozs7OzhEQWpCa0I7MkJBQ2lDOzBCQUN2Qjt3QkFDTDs7Ozs7O0FBTXZCLE1BQU1FLGVBQWVDLGNBQU07QUFVM0IsZ0RBQWdEO0FBQ2hELE1BQU1DLDZCQUE2QjtJQUNqQyxNQUFNLEVBQ0pDLElBQUksRUFDSkMsV0FBVyxFQUNYQyxTQUFTLEVBQ1ROLEtBQUssRUFDTE8sU0FBUyxFQUNUQyxTQUFTLEVBQ1RDLGVBQWUsRUFDZkMsU0FBUyxFQUNUQyxTQUFTLEVBQ1RDLFNBQVMsRUFDVixHQUFHQyxJQUFBQSxxQkFBVztJQUVmLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR0MsY0FBSyxDQUFDQyxRQUFRLENBQUM7SUFDM0QsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdILGNBQUssQ0FBQ0MsUUFBUSxDQUFDO1FBQzdDRyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsT0FBTztJQUNUO0lBRUFOLGNBQUssQ0FBQ08sU0FBUyxDQUFDO1FBQ2RoQjtJQUNGLEdBQUc7UUFBQ0E7S0FBVTtJQUVkLE1BQU1pQixrQkFBa0IsT0FBT0M7UUFDN0JBLEVBQUVDLGNBQWM7UUFDaEIsTUFBTTVCLFVBQVUsTUFBTVksVUFBVVE7UUFDaEMsSUFBSXBCLFNBQVM7WUFDWGlCLGtCQUFrQjtZQUNsQkksWUFBWTtnQkFBRUMsU0FBUztnQkFBSUMsV0FBVztnQkFBSUMsT0FBTztZQUFHO1FBQ3REO0lBQ0Y7SUFFQSxNQUFNSyxxQkFBcUIsT0FBT0M7UUFDaEMsTUFBTW5CLGdCQUFnQm1CO0lBQ3hCO0lBRUEsTUFBTUMsa0JBQWtCLE9BQU9EO1FBQzdCLElBQUlFLE9BQU9DLE9BQU8sQ0FBQyw4Q0FBOEM7WUFDL0QsTUFBTW5CLFVBQVVnQjtRQUNsQjtJQUNGO0lBRUEsSUFBSXRCLGFBQWFGLEtBQUs0QixNQUFNLEtBQUssR0FBRztRQUNsQyxxQkFBTyxxQkFBQ0M7c0JBQUk7O0lBQ2Q7SUFFQSxxQkFDRSxzQkFBQ0E7OzBCQUNDLHFCQUFDQzswQkFBRzs7WUFFSGxDLHVCQUFTLHFCQUFDaUM7Z0JBQUlFLE1BQUs7MEJBQVNuQzs7MEJBRTdCLHNCQUFDaUM7O2tDQUNDLHFCQUFDRzt3QkFBT0MsU0FBUyxJQUFNdEIsa0JBQWtCO2tDQUFPOztrQ0FHaEQscUJBQUNxQjt3QkFBT0MsU0FBUyxJQUFNOUI7d0JBQWErQixVQUFVaEM7a0NBQzNDQSxZQUFZLGtCQUFrQjs7OztZQUlsQ1EsZ0NBQ0Msc0JBQUN5QjtnQkFBS0MsVUFBVWhCO2dCQUFpQmlCLGVBQVk7O2tDQUMzQyxxQkFBQ0M7a0NBQUc7O2tDQUVKLHNCQUFDVDs7MENBQ0MscUJBQUNVO2dDQUFNQyxTQUFROzBDQUFVOzswQ0FDekIscUJBQUNDO2dDQUNDQyxJQUFHO2dDQUNIQyxNQUFLO2dDQUNMQyxPQUFPOUIsU0FBU0UsT0FBTztnQ0FDdkI2QixVQUFVLENBQUN4QixJQUFNTixZQUFZK0IsQ0FBQUEsT0FBUyxDQUFBOzRDQUFFLEdBQUdBLElBQUk7NENBQUU5QixTQUFTSyxFQUFFMEIsTUFBTSxDQUFDSCxLQUFLO3dDQUFDLENBQUE7Z0NBQ3pFSSxRQUFROzs7O2tDQUlaLHNCQUFDbkI7OzBDQUNDLHFCQUFDVTtnQ0FBTUMsU0FBUTswQ0FBWTs7MENBQzNCLHFCQUFDQztnQ0FDQ0MsSUFBRztnQ0FDSEMsTUFBSztnQ0FDTEMsT0FBTzlCLFNBQVNHLFNBQVM7Z0NBQ3pCNEIsVUFBVSxDQUFDeEIsSUFBTU4sWUFBWStCLENBQUFBLE9BQVMsQ0FBQTs0Q0FBRSxHQUFHQSxJQUFJOzRDQUFFN0IsV0FBV0ksRUFBRTBCLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FBQyxDQUFBO2dDQUMzRUksUUFBUTs7OztrQ0FJWixzQkFBQ25COzswQ0FDQyxxQkFBQ1U7Z0NBQU1DLFNBQVE7MENBQVE7OzBDQUN2QixxQkFBQ0M7Z0NBQ0NDLElBQUc7Z0NBQ0hDLE1BQUs7Z0NBQ0xDLE9BQU85QixTQUFTSSxLQUFLO2dDQUNyQjJCLFVBQVUsQ0FBQ3hCLElBQU1OLFlBQVkrQixDQUFBQSxPQUFTLENBQUE7NENBQUUsR0FBR0EsSUFBSTs0Q0FBRTVCLE9BQU9HLEVBQUUwQixNQUFNLENBQUNILEtBQUs7d0NBQUMsQ0FBQTtnQ0FDdkVJLFFBQVE7Ozs7a0NBSVoscUJBQUNoQjt3QkFBT1csTUFBSzt3QkFBU1QsVUFBVWhDO2tDQUM3QkEsWUFBWSxnQkFBZ0I7O2tDQUUvQixxQkFBQzhCO3dCQUFPVyxNQUFLO3dCQUFTVixTQUFTLElBQU10QixrQkFBa0I7a0NBQVE7Ozs7MEJBTW5FLHFCQUFDa0I7Z0JBQUlRLGVBQVk7MEJBQ2RyQyxLQUFLNEIsTUFBTSxLQUFLLGtCQUNmLHFCQUFDcUI7OEJBQUU7cUJBRUhqRCxLQUFLa0QsR0FBRyxDQUFDQyxDQUFBQSxvQkFDUCxzQkFBQ3RCO3dCQUFpQlEsZUFBYSxDQUFDLFNBQVMsRUFBRWMsSUFBSVQsRUFBRSxDQUFDLENBQUM7OzBDQUNqRCxxQkFBQ1U7MENBQUlELElBQUluQyxPQUFPOzswQ0FDaEIsc0JBQUNpQzs7b0NBQUU7b0NBQVFFLElBQUlsQyxTQUFTOzs7MENBQ3hCLHNCQUFDZ0M7O29DQUFFO29DQUFRRSxJQUFJakMsS0FBSzs7OzBDQUNwQixzQkFBQytCOztvQ0FBRTtvQ0FBU0UsSUFBSUUsTUFBTTs7OzBDQUN0QixzQkFBQ0o7O29DQUFFO29DQUFxQkUsSUFBSUcsaUJBQWlCOzs7MENBRTdDLHNCQUFDekI7O2tEQUNDLHFCQUFDRzt3Q0FBT0MsU0FBUyxJQUFNN0IsVUFBVStDO2tEQUM5QmxELGFBQWF5QyxPQUFPUyxJQUFJVCxFQUFFLEdBQUcsYUFBYTs7a0RBRTdDLHFCQUFDVjt3Q0FBT0MsU0FBUyxJQUFNVixtQkFBbUI0QixJQUFJVCxFQUFFO2tEQUFHOztrREFHbkQscUJBQUNWO3dDQUFPQyxTQUFTLElBQU1SLGdCQUFnQjBCLElBQUlULEVBQUU7a0RBQUc7Ozs7O3VCQWQxQ1MsSUFBSVQsRUFBRTs7WUF1QnJCekMsNkJBQ0Msc0JBQUM0QjtnQkFBSVEsZUFBWTs7a0NBQ2YscUJBQUNDO2tDQUFHOztrQ0FDSixzQkFBQ1c7OzRCQUFFOzRCQUFLaEQsWUFBWXlDLEVBQUU7OztrQ0FDdEIsc0JBQUNPOzs0QkFBRTs0QkFBT2hELFlBQVllLE9BQU87OztrQ0FDN0Isc0JBQUNpQzs7NEJBQUU7NEJBQVFoRCxZQUFZZ0IsU0FBUzs7O2tDQUNoQyxzQkFBQ2dDOzs0QkFBRTs0QkFBUWhELFlBQVlpQixLQUFLOzs7a0NBQzVCLHNCQUFDK0I7OzRCQUFFOzRCQUFlaEQsWUFBWXNELGdCQUFnQjs7O2tDQUM5QyxzQkFBQ047OzRCQUFFOzRCQUFnQmhELFlBQVl1RCxpQkFBaUI7Ozs7Ozs7QUFLMUQ7QUFFQUMsU0FBUyx5Q0FBeUM7SUFDaEQsTUFBTUMsV0FBa0I7UUFDdEJDLElBQUFBLHdCQUFhLEVBQUM7WUFDWmpCLElBQUk7WUFDSjFCLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxPQUFPO1lBQ1BtQyxRQUFRO1lBQ1JDLG1CQUFtQjtRQUNyQjtRQUNBSyxJQUFBQSx3QkFBYSxFQUFDO1lBQ1pqQixJQUFJO1lBQ0oxQixTQUFTO1lBQ1RDLFdBQVc7WUFDWEMsT0FBTztZQUNQbUMsUUFBUTtZQUNSQyxtQkFBbUI7UUFDckI7S0FDRDtJQUVETSxXQUFXO1FBQ1RyRSxLQUFLc0UsYUFBYTtRQUVsQixrQkFBa0I7UUFDbEJwRCxxQkFBVyxDQUFDcUQsUUFBUSxDQUFDO1lBQ25COUQsTUFBTSxFQUFFO1lBQ1JDLGFBQWE7WUFDYkMsV0FBVztZQUNYTixPQUFPO1lBQ1BtRSxTQUFTLENBQUM7WUFDVkMsWUFBWTtnQkFBRUMsTUFBTTtnQkFBR0MsT0FBTztnQkFBSUMsT0FBTztZQUFFO1FBQzdDO1FBRUEsc0JBQXNCO1FBQ3RCekMsT0FBT0MsT0FBTyxHQUFHcEMsS0FBS0ksRUFBRTtJQUMxQjtJQUVBeUUsR0FBRyxvQ0FBb0M7UUFDckN2RSxhQUFhd0UsT0FBTyxDQUFDQyxpQkFBaUIsQ0FBQ1o7UUFFdkNhLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDeEU7UUFFUixnQ0FBZ0M7UUFDaEN5RSxPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsb0JBQW9CQyxpQkFBaUI7UUFFN0Qsd0JBQXdCO1FBQ3hCLE1BQU1DLElBQUFBLGtCQUFPLEVBQUM7WUFDWkosT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLFlBQVlDLGlCQUFpQjtZQUNyREgsT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLFlBQVlDLGlCQUFpQjtRQUN2RDtRQUVBLDZCQUE2QjtRQUM3QkgsT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLG9CQUFvQkMsaUJBQWlCO1FBQzdESCxPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMseUJBQXlCQyxpQkFBaUI7UUFDbEVILE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyx1QkFBdUJDLGlCQUFpQjtRQUVoRSx3QkFBd0I7UUFDeEJILE9BQU8zRSxhQUFhd0UsT0FBTyxFQUFFUSxnQkFBZ0I7SUFDL0M7SUFFQVQsR0FBRyx5QkFBeUI7UUFDMUJ2RSxhQUFhd0UsT0FBTyxDQUFDQyxpQkFBaUIsQ0FBQ1o7UUFFdkNhLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDeEU7UUFFUix3QkFBd0I7UUFDeEIsTUFBTTZFLElBQUFBLGtCQUFPLEVBQUM7WUFDWkosT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLFlBQVlDLGlCQUFpQjtRQUN2RDtRQUVBLG1CQUFtQjtRQUNuQixNQUFNRyxlQUFlTCxpQkFBTSxDQUFDTSxZQUFZLENBQUMsU0FBUyxDQUFDLEVBQUU7UUFDckRDLG9CQUFTLENBQUNDLEtBQUssQ0FBQ0g7UUFFaEIsbUNBQW1DO1FBQ25DLE1BQU1GLElBQUFBLGtCQUFPLEVBQUM7WUFDWkosT0FBT0MsaUJBQU0sQ0FBQ1MsV0FBVyxDQUFDLHlCQUF5QlAsaUJBQWlCO1lBQ3BFSCxPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsVUFBVUMsaUJBQWlCO1lBQ25ESCxPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsa0JBQWtCQyxpQkFBaUI7UUFDN0Q7UUFFQSxpQ0FBaUM7UUFDakNILE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyxhQUFhQyxpQkFBaUI7SUFDeEQ7SUFFQVAsR0FBRyxnQ0FBZ0M7UUFDakN2RSxhQUFhd0UsT0FBTyxDQUFDQyxpQkFBaUIsQ0FBQyxFQUFFO1FBRXpDLE1BQU1hLFNBQVN4QixJQUFBQSx3QkFBYSxFQUFDO1lBQzNCakIsSUFBSTtZQUNKMUIsU0FBUztZQUNUQyxXQUFXO1lBQ1hDLE9BQU87UUFDVDtRQUVBckIsYUFBYVMsU0FBUyxDQUFDZ0UsaUJBQWlCLENBQUM7WUFDdkM1RSxTQUFTO1lBQ1QwRixNQUFNRDtZQUNORSxTQUFTO1FBQ1g7UUFFQSxtQ0FBbUM7UUFDbkN4RixhQUFhd0UsT0FBTyxDQUFDaUIscUJBQXFCLENBQUMsRUFBRSxFQUFFQSxxQkFBcUIsQ0FBQztZQUFDSDtTQUFPO1FBRTdFWixJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ3hFO1FBRVIsd0JBQXdCO1FBQ3hCLE1BQU02RSxJQUFBQSxrQkFBTyxFQUFDO1lBQ1pKLE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyxrQkFBa0JDLGlCQUFpQjtRQUM3RDtRQUVBLG1CQUFtQjtRQUNuQkssb0JBQVMsQ0FBQ0MsS0FBSyxDQUFDUixpQkFBTSxDQUFDQyxTQUFTLENBQUM7UUFDakNGLE9BQU9DLGlCQUFNLENBQUNTLFdBQVcsQ0FBQyxnQkFBZ0JQLGlCQUFpQjtRQUUzRCxlQUFlO1FBQ2ZLLG9CQUFTLENBQUNPLE1BQU0sQ0FBQ2QsaUJBQU0sQ0FBQ2UsY0FBYyxDQUFDLGFBQWE7WUFDbER6QyxRQUFRO2dCQUFFSCxPQUFPO1lBQVU7UUFDN0I7UUFDQW9DLG9CQUFTLENBQUNPLE1BQU0sQ0FBQ2QsaUJBQU0sQ0FBQ2UsY0FBYyxDQUFDLGVBQWU7WUFDcER6QyxRQUFRO2dCQUFFSCxPQUFPO1lBQVk7UUFDL0I7UUFDQW9DLG9CQUFTLENBQUNPLE1BQU0sQ0FBQ2QsaUJBQU0sQ0FBQ2UsY0FBYyxDQUFDLFVBQVU7WUFDL0N6QyxRQUFRO2dCQUFFSCxPQUFPO1lBQVk7UUFDL0I7UUFFQSxjQUFjO1FBQ2RvQyxvQkFBUyxDQUFDQyxLQUFLLENBQUNSLGlCQUFNLENBQUNnQixTQUFTLENBQUMsVUFBVTtZQUFFQyxNQUFNO1FBQWE7UUFFaEUsZ0NBQWdDO1FBQ2hDLE1BQU1kLElBQUFBLGtCQUFPLEVBQUM7WUFDWkosT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLFlBQVlDLGlCQUFpQjtRQUN2RDtRQUVBLHdCQUF3QjtRQUN4QkgsT0FBT0MsaUJBQU0sQ0FBQ2tCLGFBQWEsQ0FBQyxnQkFBZ0JDLEdBQUcsQ0FBQ2pCLGlCQUFpQjtRQUVqRSx3QkFBd0I7UUFDeEJILE9BQU8zRSxhQUFhUyxTQUFTLEVBQUV1RixvQkFBb0IsQ0FBQztZQUNsRDdFLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBa0QsR0FBRyxnQ0FBZ0M7UUFDakN2RSxhQUFhd0UsT0FBTyxDQUFDQyxpQkFBaUIsQ0FBQyxFQUFFO1FBQ3pDekUsYUFBYVMsU0FBUyxDQUFDd0YsaUJBQWlCLENBQUMsSUFBSUMsTUFBTTtRQUVuRHhCLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDeEU7UUFFUix3QkFBd0I7UUFDeEIsTUFBTTZFLElBQUFBLGtCQUFPLEVBQUM7WUFDWkosT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLGtCQUFrQkMsaUJBQWlCO1FBQzdEO1FBRUEsK0JBQStCO1FBQy9CSyxvQkFBUyxDQUFDQyxLQUFLLENBQUNSLGlCQUFNLENBQUNDLFNBQVMsQ0FBQztRQUVqQ00sb0JBQVMsQ0FBQ08sTUFBTSxDQUFDZCxpQkFBTSxDQUFDZSxjQUFjLENBQUMsYUFBYTtZQUNsRHpDLFFBQVE7Z0JBQUVILE9BQU87WUFBZ0I7UUFDbkM7UUFDQW9DLG9CQUFTLENBQUNPLE1BQU0sQ0FBQ2QsaUJBQU0sQ0FBQ2UsY0FBYyxDQUFDLGVBQWU7WUFDcER6QyxRQUFRO2dCQUFFSCxPQUFPO1lBQVE7UUFDM0I7UUFDQW9DLG9CQUFTLENBQUNPLE1BQU0sQ0FBQ2QsaUJBQU0sQ0FBQ2UsY0FBYyxDQUFDLFVBQVU7WUFDL0N6QyxRQUFRO2dCQUFFSCxPQUFPO1lBQVE7UUFDM0I7UUFFQSxjQUFjO1FBQ2RvQyxvQkFBUyxDQUFDQyxLQUFLLENBQUNSLGlCQUFNLENBQUNnQixTQUFTLENBQUMsVUFBVTtZQUFFQyxNQUFNO1FBQWE7UUFFaEUsaUJBQWlCO1FBQ2pCLE1BQU1kLElBQUFBLGtCQUFPLEVBQUM7WUFDWkosT0FBT0MsaUJBQU0sQ0FBQ2dCLFNBQVMsQ0FBQyxVQUFVZCxpQkFBaUI7WUFDbkRILE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyw0QkFBNEJDLGlCQUFpQjtRQUN2RTtRQUVBLCtCQUErQjtRQUMvQkgsT0FBT0MsaUJBQU0sQ0FBQ1MsV0FBVyxDQUFDLGdCQUFnQlAsaUJBQWlCO0lBQzdEO0lBRUFQLEdBQUcsc0JBQXNCO1FBQ3ZCdkUsYUFBYXdFLE9BQU8sQ0FBQ0MsaUJBQWlCLENBQUNaO1FBQ3ZDN0QsYUFBYVEsZUFBZSxDQUFDaUUsaUJBQWlCLENBQUM7WUFDN0M1RSxTQUFTO1lBQ1QyRixTQUFTO1FBQ1g7UUFFQWQsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUN4RTtRQUVSLHdCQUF3QjtRQUN4QixNQUFNNkUsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsWUFBWUMsaUJBQWlCO1FBQ3ZEO1FBRUEsb0NBQW9DO1FBQ3BDLE1BQU1xQixnQkFBZ0J2QixpQkFBTSxDQUFDTSxZQUFZLENBQUM7UUFDMUNDLG9CQUFTLENBQUNDLEtBQUssQ0FBQ2UsYUFBYSxDQUFDLEVBQUU7UUFFaEMsOEJBQThCO1FBQzlCLE1BQU1wQixJQUFBQSxrQkFBTyxFQUFDO1lBQ1pKLE9BQU8zRSxhQUFhUSxlQUFlLEVBQUV3RixvQkFBb0IsQ0FBQztRQUM1RDtJQUNGO0lBRUF6QixHQUFHLGlDQUFpQztRQUNsQ3ZFLGFBQWF3RSxPQUFPLENBQUNDLGlCQUFpQixDQUFDWjtRQUN2QzdELGFBQWFXLFNBQVMsQ0FBQzhELGlCQUFpQixDQUFDO1lBQ3ZDNUUsU0FBUztZQUNUMkYsU0FBUztRQUNYO1FBRUEsbUNBQW1DO1FBQ25DeEYsYUFBYXdFLE9BQU8sQ0FBQ2lCLHFCQUFxQixDQUFDNUIsVUFBVTRCLHFCQUFxQixDQUFDO1lBQUM1QixRQUFRLENBQUMsRUFBRTtTQUFDO1FBRXRGaEMsT0FBT0MsT0FBTyxDQUFlc0UsZUFBZSxDQUFDO1FBRS9DMUIsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUN4RTtRQUVSLHdCQUF3QjtRQUN4QixNQUFNNkUsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsWUFBWUMsaUJBQWlCO1FBQ3ZEO1FBRUEsNkJBQTZCO1FBQzdCLE1BQU11QixnQkFBZ0J6QixpQkFBTSxDQUFDTSxZQUFZLENBQUM7UUFDMUNDLG9CQUFTLENBQUNDLEtBQUssQ0FBQ2lCLGFBQWEsQ0FBQyxFQUFFO1FBRWhDLDJCQUEyQjtRQUMzQjFCLE9BQU85QyxPQUFPQyxPQUFPLEVBQUVrRSxvQkFBb0IsQ0FBQztRQUU1QyxnQ0FBZ0M7UUFDaEMsTUFBTWpCLElBQUFBLGtCQUFPLEVBQUM7WUFDWkosT0FBTzNFLGFBQWFXLFNBQVMsRUFBRXFGLG9CQUFvQixDQUFDO1FBQ3REO0lBQ0Y7SUFFQXpCLEdBQUcsb0RBQW9EO1FBQ3JEdkUsYUFBYXdFLE9BQU8sQ0FBQ0MsaUJBQWlCLENBQUNaO1FBQ3JDaEMsT0FBT0MsT0FBTyxDQUFlc0UsZUFBZSxDQUFDO1FBRS9DMUIsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUN4RTtRQUVSLHdCQUF3QjtRQUN4QixNQUFNNkUsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsWUFBWUMsaUJBQWlCO1FBQ3ZEO1FBRUEsNkJBQTZCO1FBQzdCLE1BQU11QixnQkFBZ0J6QixpQkFBTSxDQUFDTSxZQUFZLENBQUM7UUFDMUNDLG9CQUFTLENBQUNDLEtBQUssQ0FBQ2lCLGFBQWEsQ0FBQyxFQUFFO1FBRWhDLG1EQUFtRDtRQUNuRDFCLE9BQU85QyxPQUFPQyxPQUFPLEVBQUVrRCxnQkFBZ0I7UUFDdkNMLE9BQU8zRSxhQUFhVyxTQUFTLEVBQUVvRixHQUFHLENBQUNmLGdCQUFnQjtJQUNyRDtJQUVBVCxHQUFHLGlDQUFpQztRQUNsQ3ZFLGFBQWF3RSxPQUFPLENBQUNDLGlCQUFpQixDQUFDWjtRQUV2Q2EsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUN4RTtRQUVSLHdCQUF3QjtRQUN4QixNQUFNNkUsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsWUFBWUMsaUJBQWlCO1FBQ3ZEO1FBRUEsdUJBQXVCO1FBQ3ZCOUUsYUFBYXdFLE9BQU8sQ0FBQzhCLFNBQVM7UUFFOUIsZ0JBQWdCO1FBQ2hCbkIsb0JBQVMsQ0FBQ0MsS0FBSyxDQUFDUixpQkFBTSxDQUFDQyxTQUFTLENBQUM7UUFFakMsNEJBQTRCO1FBQzVCRixPQUFPQyxpQkFBTSxDQUFDQyxTQUFTLENBQUMsa0JBQWtCQyxpQkFBaUI7UUFFM0QsK0JBQStCO1FBQy9CLE1BQU1DLElBQUFBLGtCQUFPLEVBQUM7WUFDWkosT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLFlBQVlDLGlCQUFpQjtRQUN2RDtRQUVBLDhCQUE4QjtRQUM5QkgsT0FBTzNFLGFBQWF3RSxPQUFPLEVBQUVRLGdCQUFnQjtJQUMvQztJQUVBVCxHQUFHLGlDQUFpQztRQUNsQ3ZFLGFBQWF3RSxPQUFPLENBQUN5QixpQkFBaUIsQ0FBQyxJQUFJQyxNQUFNO1FBRWpEeEIsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUN4RTtRQUVSLDJCQUEyQjtRQUMzQixNQUFNNkUsSUFBQUEsa0JBQU8sRUFBQztZQUNaSixPQUFPQyxpQkFBTSxDQUFDZ0IsU0FBUyxDQUFDLFVBQVVkLGlCQUFpQjtZQUNuREgsT0FBT0MsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDLGtCQUFrQkMsaUJBQWlCO1FBQzdEO1FBRUEsZ0NBQWdDO1FBQ2hDSCxPQUFPQyxpQkFBTSxDQUFDMkIsV0FBVyxDQUFDLG9CQUFvQlIsR0FBRyxDQUFDakIsaUJBQWlCO0lBQ3JFO0FBQ0YifQ==