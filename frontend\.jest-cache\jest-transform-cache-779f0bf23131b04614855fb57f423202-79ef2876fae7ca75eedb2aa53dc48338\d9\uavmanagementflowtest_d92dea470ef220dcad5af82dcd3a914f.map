{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\__tests__\\integration\\uav-management-flow.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { useUAVStore } from '@/stores/uav-store'\nimport { uavApi } from '@/api/uav-api'\nimport { createMockUAV } from '@/lib/test-utils'\nimport { UAV } from '@/types/uav'\n\n// Mock the API\njest.mock('@/api/uav-api')\nconst mockedUAVApi = uavApi as jest.Mocked<typeof uavApi>\n\n// Mock react-hot-toast\njest.mock('react-hot-toast', () => ({\n  toast: {\n    success: jest.fn(),\n    error: jest.fn(),\n  },\n}))\n\n// Integration test component for UAV management\nconst UAVManagementTestComponent = () => {\n  const {\n    uavs,\n    selectedUAV,\n    loading: isLoading,\n    error,\n    fetchUAVs,\n    setSelectedUAV,\n    updateUAVStatus,\n    createUAV,\n    updateUAV,\n    deleteUAV\n  } = useUAVStore()\n\n  const [showCreateForm, setShowCreateForm] = React.useState(false)\n  const [formData, setFormData] = React.useState({\n    rfidTag: '',\n    ownerName: '',\n    model: '',\n  })\n\n  React.useEffect(() => {\n    fetchUAVs()\n  }, [fetchUAVs])\n\n  const handleCreateUAV = async (e: React.FormEvent) => {\n    e.preventDefault()\n    const success = await createUAV(formData)\n    if (success) {\n      setShowCreateForm(false)\n      setFormData({ rfidTag: '', ownerName: '', model: '' })\n    }\n  }\n\n  const handleUpdateStatus = async (uavId: number) => {\n    await updateUAVStatus(uavId)\n  }\n\n  const handleDeleteUAV = async (uavId: number) => {\n    if (window.confirm('Are you sure you want to delete this UAV?')) {\n      await deleteUAV(uavId)\n    }\n  }\n\n  if (isLoading && uavs.length === 0) {\n    return <div>Loading UAVs...</div>\n  }\n\n  return (\n    <div>\n      <h1>UAV Management</h1>\n      \n      {error && <div role=\"alert\">{error}</div>}\n      \n      <div>\n        <button onClick={() => setShowCreateForm(true)}>\n          Add New UAV\n        </button>\n        <button onClick={() => fetchUAVs()} disabled={isLoading}>\n          {isLoading ? 'Refreshing...' : 'Refresh'}\n        </button>\n      </div>\n\n      {showCreateForm && (\n        <form onSubmit={handleCreateUAV} data-testid=\"create-form\">\n          <h2>Create New UAV</h2>\n          \n          <div>\n            <label htmlFor=\"rfidTag\">RFID Tag</label>\n            <input\n              id=\"rfidTag\"\n              type=\"text\"\n              value={formData.rfidTag}\n              onChange={(e) => setFormData(prev => ({ ...prev, rfidTag: e.target.value }))}\n              required\n            />\n          </div>\n          \n          <div>\n            <label htmlFor=\"ownerName\">Owner Name</label>\n            <input\n              id=\"ownerName\"\n              type=\"text\"\n              value={formData.ownerName}\n              onChange={(e) => setFormData(prev => ({ ...prev, ownerName: e.target.value }))}\n              required\n            />\n          </div>\n          \n          <div>\n            <label htmlFor=\"model\">Model</label>\n            <input\n              id=\"model\"\n              type=\"text\"\n              value={formData.model}\n              onChange={(e) => setFormData(prev => ({ ...prev, model: e.target.value }))}\n              required\n            />\n          </div>\n          \n          <button type=\"submit\" disabled={isLoading}>\n            {isLoading ? 'Creating...' : 'Create UAV'}\n          </button>\n          <button type=\"button\" onClick={() => setShowCreateForm(false)}>\n            Cancel\n          </button>\n        </form>\n      )}\n\n      <div data-testid=\"uav-list\">\n        {uavs.length === 0 ? (\n          <p>No UAVs found</p>\n        ) : (\n          uavs.map(uav => (\n            <div key={uav.id} data-testid={`uav-item-${uav.id}`}>\n              <h3>{uav.rfidTag}</h3>\n              <p>Owner: {uav.ownerName}</p>\n              <p>Model: {uav.model}</p>\n              <p>Status: {uav.status}</p>\n              <p>Operational Status: {uav.operationalStatus}</p>\n              \n              <div>\n                <button onClick={() => setSelectedUAV(uav)}>\n                  {selectedUAV?.id === uav.id ? 'Selected' : 'Select'}\n                </button>\n                <button onClick={() => handleUpdateStatus(uav.id)}>\n                  Update Status\n                </button>\n                <button onClick={() => handleDeleteUAV(uav.id)}>\n                  Delete\n                </button>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      {selectedUAV && (\n        <div data-testid=\"selected-uav-details\">\n          <h2>Selected UAV Details</h2>\n          <p>ID: {selectedUAV.id}</p>\n          <p>RFID: {selectedUAV.rfidTag}</p>\n          <p>Owner: {selectedUAV.ownerName}</p>\n          <p>Model: {selectedUAV.model}</p>\n          <p>Flight Hours: {selectedUAV.totalFlightHours}</p>\n          <p>Flight Cycles: {selectedUAV.totalFlightCycles}</p>\n        </div>\n      )}\n    </div>\n  )\n}\n\ndescribe('UAV Management Flow Integration Tests', () => {\n  const mockUAVs: UAV[] = [\n    createMockUAV({\n      id: 1,\n      rfidTag: 'UAV-001',\n      ownerName: 'John Doe',\n      model: 'Quadcopter X1',\n      status: 'AUTHORIZED',\n      operationalStatus: 'READY',\n    }),\n    createMockUAV({\n      id: 2,\n      rfidTag: 'UAV-002',\n      ownerName: 'Jane Smith',\n      model: 'Fixed Wing Y2',\n      status: 'PENDING',\n      operationalStatus: 'MAINTENANCE',\n    }),\n  ]\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n    \n    // Reset UAV store\n    useUAVStore.setState({\n      uavs: [],\n      selectedUAV: null,\n      loading: false,\n      error: null,\n      filter: {},\n      pagination: { page: 1, limit: 10, sortBy: 'id', sortOrder: 'desc' },\n      searchQuery: '',\n      regions: [],\n      systemStats: null,\n      hibernatePodStatus: null,\n    })\n\n    // Mock window.confirm\n    window.confirm = jest.fn()\n  })\n\n  it('loads and displays UAVs on mount', async () => {\n    mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs)\n\n    render(<UAVManagementTestComponent />)\n\n    // Should show loading initially\n    expect(screen.getByText('Loading UAVs...')).toBeInTheDocument()\n\n    // Wait for UAVs to load\n    await waitFor(() => {\n      expect(screen.getByText('UAV-001')).toBeInTheDocument()\n      expect(screen.getByText('UAV-002')).toBeInTheDocument()\n    })\n\n    // Should display UAV details\n    expect(screen.getByText('Owner: John Doe')).toBeInTheDocument()\n    expect(screen.getByText('Model: Quadcopter X1')).toBeInTheDocument()\n    expect(screen.getByText('Status: AUTHORIZED')).toBeInTheDocument()\n\n    // Verify API was called\n    expect(mockedUAVApi.getUAVs).toHaveBeenCalled()\n  })\n\n  it('handles UAV selection', async () => {\n    mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs)\n\n    render(<UAVManagementTestComponent />)\n\n    // Wait for UAVs to load\n    await waitFor(() => {\n      expect(screen.getByText('UAV-001')).toBeInTheDocument()\n    })\n\n    // Select first UAV\n    const selectButton = screen.getAllByText('Select')[0]\n    fireEvent.click(selectButton)\n\n    // Should show selected UAV details\n    await waitFor(() => {\n      expect(screen.getByTestId('selected-uav-details')).toBeInTheDocument()\n      expect(screen.getByText('ID: 1')).toBeInTheDocument()\n      expect(screen.getByText('RFID: UAV-001')).toBeInTheDocument()\n    })\n\n    // Button should show as selected\n    expect(screen.getByText('Selected')).toBeInTheDocument()\n  })\n\n  it('creates new UAV successfully', async () => {\n    mockedUAVApi.getUAVs.mockResolvedValue([])\n    \n    const newUAV = createMockUAV({\n      id: 3,\n      rfidTag: 'UAV-003',\n      ownerName: 'New Owner',\n      model: 'New Model',\n    })\n    \n    mockedUAVApi.createUAV.mockResolvedValue({\n      success: true,\n      data: newUAV,\n      message: 'UAV created successfully',\n    })\n\n    // Mock updated list after creation\n    mockedUAVApi.getUAVs.mockResolvedValueOnce([]).mockResolvedValueOnce([newUAV])\n\n    render(<UAVManagementTestComponent />)\n\n    // Wait for initial load\n    await waitFor(() => {\n      expect(screen.getByText('No UAVs found')).toBeInTheDocument()\n    })\n\n    // Open create form\n    fireEvent.click(screen.getByText('Add New UAV'))\n    expect(screen.getByTestId('create-form')).toBeInTheDocument()\n\n    // Fill in form\n    fireEvent.change(screen.getByLabelText('RFID Tag'), {\n      target: { value: 'UAV-003' }\n    })\n    fireEvent.change(screen.getByLabelText('Owner Name'), {\n      target: { value: 'New Owner' }\n    })\n    fireEvent.change(screen.getByLabelText('Model'), {\n      target: { value: 'New Model' }\n    })\n\n    // Submit form\n    fireEvent.click(screen.getByRole('button', { name: 'Create UAV' }))\n\n    // Wait for creation to complete\n    await waitFor(() => {\n      expect(screen.getByText('UAV-003')).toBeInTheDocument()\n    })\n\n    // Form should be hidden\n    expect(screen.queryByTestId('create-form')).not.toBeInTheDocument()\n\n    // Verify API was called\n    expect(mockedUAVApi.createUAV).toHaveBeenCalledWith({\n      rfidTag: 'UAV-003',\n      ownerName: 'New Owner',\n      model: 'New Model',\n    })\n  })\n\n  it('handles UAV creation failure', async () => {\n    mockedUAVApi.getUAVs.mockResolvedValue([])\n    mockedUAVApi.createUAV.mockRejectedValue(new Error('RFID tag already exists'))\n\n    render(<UAVManagementTestComponent />)\n\n    // Wait for initial load\n    await waitFor(() => {\n      expect(screen.getByText('No UAVs found')).toBeInTheDocument()\n    })\n\n    // Open create form and fill it\n    fireEvent.click(screen.getByText('Add New UAV'))\n    \n    fireEvent.change(screen.getByLabelText('RFID Tag'), {\n      target: { value: 'DUPLICATE-TAG' }\n    })\n    fireEvent.change(screen.getByLabelText('Owner Name'), {\n      target: { value: 'Owner' }\n    })\n    fireEvent.change(screen.getByLabelText('Model'), {\n      target: { value: 'Model' }\n    })\n\n    // Submit form\n    fireEvent.click(screen.getByRole('button', { name: 'Create UAV' }))\n\n    // Wait for error\n    await waitFor(() => {\n      expect(screen.getByRole('alert')).toBeInTheDocument()\n      expect(screen.getByText('RFID tag already exists')).toBeInTheDocument()\n    })\n\n    // Form should still be visible\n    expect(screen.getByTestId('create-form')).toBeInTheDocument()\n  })\n\n  it('updates UAV status', async () => {\n    mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs)\n    mockedUAVApi.updateUAVStatus.mockResolvedValue({\n      success: true,\n      message: 'Status updated',\n    })\n\n    render(<UAVManagementTestComponent />)\n\n    // Wait for UAVs to load\n    await waitFor(() => {\n      expect(screen.getByText('UAV-001')).toBeInTheDocument()\n    })\n\n    // Click update status for first UAV\n    const updateButtons = screen.getAllByText('Update Status')\n    fireEvent.click(updateButtons[0])\n\n    // Wait for update to complete\n    await waitFor(() => {\n      expect(mockedUAVApi.updateUAVStatus).toHaveBeenCalledWith(1)\n    })\n  })\n\n  it('deletes UAV with confirmation', async () => {\n    mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs)\n    mockedUAVApi.deleteUAV.mockResolvedValue({\n      success: true,\n      message: 'UAV deleted',\n    })\n    \n    // Mock updated list after deletion\n    mockedUAVApi.getUAVs.mockResolvedValueOnce(mockUAVs).mockResolvedValueOnce([mockUAVs[1]])\n    \n    ;(window.confirm as jest.Mock).mockReturnValue(true)\n\n    render(<UAVManagementTestComponent />)\n\n    // Wait for UAVs to load\n    await waitFor(() => {\n      expect(screen.getByText('UAV-001')).toBeInTheDocument()\n    })\n\n    // Click delete for first UAV\n    const deleteButtons = screen.getAllByText('Delete')\n    fireEvent.click(deleteButtons[0])\n\n    // Should show confirmation\n    expect(window.confirm).toHaveBeenCalledWith('Are you sure you want to delete this UAV?')\n\n    // Wait for deletion to complete\n    await waitFor(() => {\n      expect(mockedUAVApi.deleteUAV).toHaveBeenCalledWith(1)\n    })\n  })\n\n  it('cancels deletion when user declines confirmation', async () => {\n    mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs)\n    ;(window.confirm as jest.Mock).mockReturnValue(false)\n\n    render(<UAVManagementTestComponent />)\n\n    // Wait for UAVs to load\n    await waitFor(() => {\n      expect(screen.getByText('UAV-001')).toBeInTheDocument()\n    })\n\n    // Click delete for first UAV\n    const deleteButtons = screen.getAllByText('Delete')\n    fireEvent.click(deleteButtons[0])\n\n    // Should show confirmation but not call delete API\n    expect(window.confirm).toHaveBeenCalled()\n    expect(mockedUAVApi.deleteUAV).not.toHaveBeenCalled()\n  })\n\n  it('handles refresh functionality', async () => {\n    mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs)\n\n    render(<UAVManagementTestComponent />)\n\n    // Wait for initial load\n    await waitFor(() => {\n      expect(screen.getByText('UAV-001')).toBeInTheDocument()\n    })\n\n    // Clear previous calls\n    mockedUAVApi.getUAVs.mockClear()\n\n    // Click refresh\n    fireEvent.click(screen.getByText('Refresh'))\n\n    // Should show loading state\n    expect(screen.getByText('Refreshing...')).toBeInTheDocument()\n\n    // Wait for refresh to complete\n    await waitFor(() => {\n      expect(screen.getByText('Refresh')).toBeInTheDocument()\n    })\n\n    // Verify API was called again\n    expect(mockedUAVApi.getUAVs).toHaveBeenCalled()\n  })\n\n  it('handles API errors gracefully', async () => {\n    mockedUAVApi.getUAVs.mockRejectedValue(new Error('Network error'))\n\n    render(<UAVManagementTestComponent />)\n\n    // Wait for error to appear\n    await waitFor(() => {\n      expect(screen.getByRole('alert')).toBeInTheDocument()\n      expect(screen.getByText('Network error')).toBeInTheDocument()\n    })\n\n    // Should not show loading state\n    expect(screen.queryByText('Loading UAVs...')).not.toBeInTheDocument()\n  })\n})\n"], "names": ["jest", "mock", "toast", "success", "fn", "error", "mockedUAVApi", "uavApi", "UAVManagementTestComponent", "uavs", "selectedUAV", "loading", "isLoading", "fetchUAVs", "setSelectedUAV", "updateUAVStatus", "createUAV", "updateUAV", "deleteUAV", "useUAVStore", "showCreateForm", "setShowCreateForm", "React", "useState", "formData", "setFormData", "rfidTag", "ownerName", "model", "useEffect", "handleCreateUAV", "e", "preventDefault", "handleUpdateStatus", "uavId", "handleDeleteUAV", "window", "confirm", "length", "div", "h1", "role", "button", "onClick", "disabled", "form", "onSubmit", "data-testid", "h2", "label", "htmlFor", "input", "id", "type", "value", "onChange", "prev", "target", "required", "p", "map", "uav", "h3", "status", "operationalStatus", "totalFlightHours", "totalFlightCycles", "describe", "mockUAVs", "createMockUAV", "beforeEach", "clearAllMocks", "setState", "filter", "pagination", "page", "limit", "sortBy", "sortOrder", "searchQuery", "regions", "systemStats", "hibernatePodStatus", "it", "getUAVs", "mockResolvedValue", "render", "expect", "screen", "getByText", "toBeInTheDocument", "waitFor", "toHaveBeenCalled", "selectButton", "getAllByText", "fireEvent", "click", "getByTestId", "newUAV", "data", "message", "mockResolvedValueOnce", "change", "getByLabelText", "getByRole", "name", "queryByTestId", "not", "toHaveBeenCalledWith", "mockRejectedValue", "Error", "updateButtons", "mockReturnValue", "deleteButtons", "mockClear", "queryByText"], "mappings": ";AAOA,eAAe;AACfA,KAAKC,IAAI,CAAC;AAGV,uBAAuB;AACvBD,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC,OAAO;YACLC,SAASH,KAAKI,EAAE;YAChBC,OAAOL,KAAKI,EAAE;QAChB;IACF,CAAA;;;;;8DAjBkB;2BACiC;0BACvB;wBACL;;;;;;AAMvB,MAAME,eAAeC,cAAM;AAU3B,gDAAgD;AAChD,MAAMC,6BAA6B;IACjC,MAAM,EACJC,IAAI,EACJC,WAAW,EACXC,SAASC,SAAS,EAClBP,KAAK,EACLQ,SAAS,EACTC,cAAc,EACdC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,SAAS,EACV,GAAGC,IAAAA,qBAAW;IAEf,MAAM,CAACC,gBAAgBC,kBAAkB,GAAGC,cAAK,CAACC,QAAQ,CAAC;IAC3D,MAAM,CAACC,UAAUC,YAAY,GAAGH,cAAK,CAACC,QAAQ,CAAC;QAC7CG,SAAS;QACTC,WAAW;QACXC,OAAO;IACT;IAEAN,cAAK,CAACO,SAAS,CAAC;QACdhB;IACF,GAAG;QAACA;KAAU;IAEd,MAAMiB,kBAAkB,OAAOC;QAC7BA,EAAEC,cAAc;QAChB,MAAM7B,UAAU,MAAMa,UAAUQ;QAChC,IAAIrB,SAAS;YACXkB,kBAAkB;YAClBI,YAAY;gBAAEC,SAAS;gBAAIC,WAAW;gBAAIC,OAAO;YAAG;QACtD;IACF;IAEA,MAAMK,qBAAqB,OAAOC;QAChC,MAAMnB,gBAAgBmB;IACxB;IAEA,MAAMC,kBAAkB,OAAOD;QAC7B,IAAIE,OAAOC,OAAO,CAAC,8CAA8C;YAC/D,MAAMnB,UAAUgB;QAClB;IACF;IAEA,IAAItB,aAAaH,KAAK6B,MAAM,KAAK,GAAG;QAClC,qBAAO,qBAACC;sBAAI;;IACd;IAEA,qBACE,sBAACA;;0BACC,qBAACC;0BAAG;;YAEHnC,uBAAS,qBAACkC;gBAAIE,MAAK;0BAASpC;;0BAE7B,sBAACkC;;kCACC,qBAACG;wBAAOC,SAAS,IAAMtB,kBAAkB;kCAAO;;kCAGhD,qBAACqB;wBAAOC,SAAS,IAAM9B;wBAAa+B,UAAUhC;kCAC3CA,YAAY,kBAAkB;;;;YAIlCQ,gCACC,sBAACyB;gBAAKC,UAAUhB;gBAAiBiB,eAAY;;kCAC3C,qBAACC;kCAAG;;kCAEJ,sBAACT;;0CACC,qBAACU;gCAAMC,SAAQ;0CAAU;;0CACzB,qBAACC;gCACCC,IAAG;gCACHC,MAAK;gCACLC,OAAO9B,SAASE,OAAO;gCACvB6B,UAAU,CAACxB,IAAMN,YAAY+B,CAAAA,OAAS,CAAA;4CAAE,GAAGA,IAAI;4CAAE9B,SAASK,EAAE0B,MAAM,CAACH,KAAK;wCAAC,CAAA;gCACzEI,QAAQ;;;;kCAIZ,sBAACnB;;0CACC,qBAACU;gCAAMC,SAAQ;0CAAY;;0CAC3B,qBAACC;gCACCC,IAAG;gCACHC,MAAK;gCACLC,OAAO9B,SAASG,SAAS;gCACzB4B,UAAU,CAACxB,IAAMN,YAAY+B,CAAAA,OAAS,CAAA;4CAAE,GAAGA,IAAI;4CAAE7B,WAAWI,EAAE0B,MAAM,CAACH,KAAK;wCAAC,CAAA;gCAC3EI,QAAQ;;;;kCAIZ,sBAACnB;;0CACC,qBAACU;gCAAMC,SAAQ;0CAAQ;;0CACvB,qBAACC;gCACCC,IAAG;gCACHC,MAAK;gCACLC,OAAO9B,SAASI,KAAK;gCACrB2B,UAAU,CAACxB,IAAMN,YAAY+B,CAAAA,OAAS,CAAA;4CAAE,GAAGA,IAAI;4CAAE5B,OAAOG,EAAE0B,MAAM,CAACH,KAAK;wCAAC,CAAA;gCACvEI,QAAQ;;;;kCAIZ,qBAAChB;wBAAOW,MAAK;wBAAST,UAAUhC;kCAC7BA,YAAY,gBAAgB;;kCAE/B,qBAAC8B;wBAAOW,MAAK;wBAASV,SAAS,IAAMtB,kBAAkB;kCAAQ;;;;0BAMnE,qBAACkB;gBAAIQ,eAAY;0BACdtC,KAAK6B,MAAM,KAAK,kBACf,qBAACqB;8BAAE;qBAEHlD,KAAKmD,GAAG,CAACC,CAAAA,oBACP,sBAACtB;wBAAiBQ,eAAa,CAAC,SAAS,EAAEc,IAAIT,EAAE,CAAC,CAAC;;0CACjD,qBAACU;0CAAID,IAAInC,OAAO;;0CAChB,sBAACiC;;oCAAE;oCAAQE,IAAIlC,SAAS;;;0CACxB,sBAACgC;;oCAAE;oCAAQE,IAAIjC,KAAK;;;0CACpB,sBAAC+B;;oCAAE;oCAASE,IAAIE,MAAM;;;0CACtB,sBAACJ;;oCAAE;oCAAqBE,IAAIG,iBAAiB;;;0CAE7C,sBAACzB;;kDACC,qBAACG;wCAAOC,SAAS,IAAM7B,eAAe+C;kDACnCnD,aAAa0C,OAAOS,IAAIT,EAAE,GAAG,aAAa;;kDAE7C,qBAACV;wCAAOC,SAAS,IAAMV,mBAAmB4B,IAAIT,EAAE;kDAAG;;kDAGnD,qBAACV;wCAAOC,SAAS,IAAMR,gBAAgB0B,IAAIT,EAAE;kDAAG;;;;;uBAd1CS,IAAIT,EAAE;;YAuBrB1C,6BACC,sBAAC6B;gBAAIQ,eAAY;;kCACf,qBAACC;kCAAG;;kCACJ,sBAACW;;4BAAE;4BAAKjD,YAAY0C,EAAE;;;kCACtB,sBAACO;;4BAAE;4BAAOjD,YAAYgB,OAAO;;;kCAC7B,sBAACiC;;4BAAE;4BAAQjD,YAAYiB,SAAS;;;kCAChC,sBAACgC;;4BAAE;4BAAQjD,YAAYkB,KAAK;;;kCAC5B,sBAAC+B;;4BAAE;4BAAejD,YAAYuD,gBAAgB;;;kCAC9C,sBAACN;;4BAAE;4BAAgBjD,YAAYwD,iBAAiB;;;;;;;AAK1D;AAEAC,SAAS,yCAAyC;IAChD,MAAMC,WAAkB;QACtBC,IAAAA,wBAAa,EAAC;YACZjB,IAAI;YACJ1B,SAAS;YACTC,WAAW;YACXC,OAAO;YACPmC,QAAQ;YACRC,mBAAmB;QACrB;QACAK,IAAAA,wBAAa,EAAC;YACZjB,IAAI;YACJ1B,SAAS;YACTC,WAAW;YACXC,OAAO;YACPmC,QAAQ;YACRC,mBAAmB;QACrB;KACD;IAEDM,WAAW;QACTtE,KAAKuE,aAAa;QAElB,kBAAkB;QAClBpD,qBAAW,CAACqD,QAAQ,CAAC;YACnB/D,MAAM,EAAE;YACRC,aAAa;YACbC,SAAS;YACTN,OAAO;YACPoE,QAAQ,CAAC;YACTC,YAAY;gBAAEC,MAAM;gBAAGC,OAAO;gBAAIC,QAAQ;gBAAMC,WAAW;YAAO;YAClEC,aAAa;YACbC,SAAS,EAAE;YACXC,aAAa;YACbC,oBAAoB;QACtB;QAEA,sBAAsB;QACtB9C,OAAOC,OAAO,GAAGrC,KAAKI,EAAE;IAC1B;IAEA+E,GAAG,oCAAoC;QACrC7E,aAAa8E,OAAO,CAACC,iBAAiB,CAACjB;QAEvCkB,IAAAA,iBAAM,gBAAC,qBAAC9E;QAER,gCAAgC;QAChC+E,OAAOC,iBAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;QAE7D,wBAAwB;QACxB,MAAMC,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;YACrDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;QACvD;QAEA,6BAA6B;QAC7BH,OAAOC,iBAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;QAC7DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,yBAAyBC,iBAAiB;QAClEH,OAAOC,iBAAM,CAACC,SAAS,CAAC,uBAAuBC,iBAAiB;QAEhE,wBAAwB;QACxBH,OAAOjF,aAAa8E,OAAO,EAAEQ,gBAAgB;IAC/C;IAEAT,GAAG,yBAAyB;QAC1B7E,aAAa8E,OAAO,CAACC,iBAAiB,CAACjB;QAEvCkB,IAAAA,iBAAM,gBAAC,qBAAC9E;QAER,wBAAwB;QACxB,MAAMmF,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;QACvD;QAEA,mBAAmB;QACnB,MAAMG,eAAeL,iBAAM,CAACM,YAAY,CAAC,SAAS,CAAC,EAAE;QACrDC,oBAAS,CAACC,KAAK,CAACH;QAEhB,mCAAmC;QACnC,MAAMF,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACS,WAAW,CAAC,yBAAyBP,iBAAiB;YACpEH,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;YACnDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC7D;QAEA,iCAAiC;QACjCH,OAAOC,iBAAM,CAACC,SAAS,CAAC,aAAaC,iBAAiB;IACxD;IAEAP,GAAG,gCAAgC;QACjC7E,aAAa8E,OAAO,CAACC,iBAAiB,CAAC,EAAE;QAEzC,MAAMa,SAAS7B,IAAAA,wBAAa,EAAC;YAC3BjB,IAAI;YACJ1B,SAAS;YACTC,WAAW;YACXC,OAAO;QACT;QAEAtB,aAAaU,SAAS,CAACqE,iBAAiB,CAAC;YACvClF,SAAS;YACTgG,MAAMD;YACNE,SAAS;QACX;QAEA,mCAAmC;QACnC9F,aAAa8E,OAAO,CAACiB,qBAAqB,CAAC,EAAE,EAAEA,qBAAqB,CAAC;YAACH;SAAO;QAE7EZ,IAAAA,iBAAM,gBAAC,qBAAC9E;QAER,wBAAwB;QACxB,MAAMmF,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC7D;QAEA,mBAAmB;QACnBK,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOC,iBAAM,CAACS,WAAW,CAAC,gBAAgBP,iBAAiB;QAE3D,eAAe;QACfK,oBAAS,CAACO,MAAM,CAACd,iBAAM,CAACe,cAAc,CAAC,aAAa;YAClD9C,QAAQ;gBAAEH,OAAO;YAAU;QAC7B;QACAyC,oBAAS,CAACO,MAAM,CAACd,iBAAM,CAACe,cAAc,CAAC,eAAe;YACpD9C,QAAQ;gBAAEH,OAAO;YAAY;QAC/B;QACAyC,oBAAS,CAACO,MAAM,CAACd,iBAAM,CAACe,cAAc,CAAC,UAAU;YAC/C9C,QAAQ;gBAAEH,OAAO;YAAY;QAC/B;QAEA,cAAc;QACdyC,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACgB,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAa;QAEhE,gCAAgC;QAChC,MAAMd,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;QACvD;QAEA,wBAAwB;QACxBH,OAAOC,iBAAM,CAACkB,aAAa,CAAC,gBAAgBC,GAAG,CAACjB,iBAAiB;QAEjE,wBAAwB;QACxBH,OAAOjF,aAAaU,SAAS,EAAE4F,oBAAoB,CAAC;YAClDlF,SAAS;YACTC,WAAW;YACXC,OAAO;QACT;IACF;IAEAuD,GAAG,gCAAgC;QACjC7E,aAAa8E,OAAO,CAACC,iBAAiB,CAAC,EAAE;QACzC/E,aAAaU,SAAS,CAAC6F,iBAAiB,CAAC,IAAIC,MAAM;QAEnDxB,IAAAA,iBAAM,gBAAC,qBAAC9E;QAER,wBAAwB;QACxB,MAAMmF,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC7D;QAEA,+BAA+B;QAC/BK,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QAEjCM,oBAAS,CAACO,MAAM,CAACd,iBAAM,CAACe,cAAc,CAAC,aAAa;YAClD9C,QAAQ;gBAAEH,OAAO;YAAgB;QACnC;QACAyC,oBAAS,CAACO,MAAM,CAACd,iBAAM,CAACe,cAAc,CAAC,eAAe;YACpD9C,QAAQ;gBAAEH,OAAO;YAAQ;QAC3B;QACAyC,oBAAS,CAACO,MAAM,CAACd,iBAAM,CAACe,cAAc,CAAC,UAAU;YAC/C9C,QAAQ;gBAAEH,OAAO;YAAQ;QAC3B;QAEA,cAAc;QACdyC,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACgB,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAa;QAEhE,iBAAiB;QACjB,MAAMd,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACgB,SAAS,CAAC,UAAUd,iBAAiB;YACnDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,4BAA4BC,iBAAiB;QACvE;QAEA,+BAA+B;QAC/BH,OAAOC,iBAAM,CAACS,WAAW,CAAC,gBAAgBP,iBAAiB;IAC7D;IAEAP,GAAG,sBAAsB;QACvB7E,aAAa8E,OAAO,CAACC,iBAAiB,CAACjB;QACvC9D,aAAaS,eAAe,CAACsE,iBAAiB,CAAC;YAC7ClF,SAAS;YACTiG,SAAS;QACX;QAEAd,IAAAA,iBAAM,gBAAC,qBAAC9E;QAER,wBAAwB;QACxB,MAAMmF,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;QACvD;QAEA,oCAAoC;QACpC,MAAMqB,gBAAgBvB,iBAAM,CAACM,YAAY,CAAC;QAC1CC,oBAAS,CAACC,KAAK,CAACe,aAAa,CAAC,EAAE;QAEhC,8BAA8B;QAC9B,MAAMpB,IAAAA,kBAAO,EAAC;YACZJ,OAAOjF,aAAaS,eAAe,EAAE6F,oBAAoB,CAAC;QAC5D;IACF;IAEAzB,GAAG,iCAAiC;QAClC7E,aAAa8E,OAAO,CAACC,iBAAiB,CAACjB;QACvC9D,aAAaY,SAAS,CAACmE,iBAAiB,CAAC;YACvClF,SAAS;YACTiG,SAAS;QACX;QAEA,mCAAmC;QACnC9F,aAAa8E,OAAO,CAACiB,qBAAqB,CAACjC,UAAUiC,qBAAqB,CAAC;YAACjC,QAAQ,CAAC,EAAE;SAAC;QAEtFhC,OAAOC,OAAO,CAAe2E,eAAe,CAAC;QAE/C1B,IAAAA,iBAAM,gBAAC,qBAAC9E;QAER,wBAAwB;QACxB,MAAMmF,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;QACvD;QAEA,6BAA6B;QAC7B,MAAMuB,gBAAgBzB,iBAAM,CAACM,YAAY,CAAC;QAC1CC,oBAAS,CAACC,KAAK,CAACiB,aAAa,CAAC,EAAE;QAEhC,2BAA2B;QAC3B1B,OAAOnD,OAAOC,OAAO,EAAEuE,oBAAoB,CAAC;QAE5C,gCAAgC;QAChC,MAAMjB,IAAAA,kBAAO,EAAC;YACZJ,OAAOjF,aAAaY,SAAS,EAAE0F,oBAAoB,CAAC;QACtD;IACF;IAEAzB,GAAG,oDAAoD;QACrD7E,aAAa8E,OAAO,CAACC,iBAAiB,CAACjB;QACrChC,OAAOC,OAAO,CAAe2E,eAAe,CAAC;QAE/C1B,IAAAA,iBAAM,gBAAC,qBAAC9E;QAER,wBAAwB;QACxB,MAAMmF,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;QACvD;QAEA,6BAA6B;QAC7B,MAAMuB,gBAAgBzB,iBAAM,CAACM,YAAY,CAAC;QAC1CC,oBAAS,CAACC,KAAK,CAACiB,aAAa,CAAC,EAAE;QAEhC,mDAAmD;QACnD1B,OAAOnD,OAAOC,OAAO,EAAEuD,gBAAgB;QACvCL,OAAOjF,aAAaY,SAAS,EAAEyF,GAAG,CAACf,gBAAgB;IACrD;IAEAT,GAAG,iCAAiC;QAClC7E,aAAa8E,OAAO,CAACC,iBAAiB,CAACjB;QAEvCkB,IAAAA,iBAAM,gBAAC,qBAAC9E;QAER,wBAAwB;QACxB,MAAMmF,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;QACvD;QAEA,uBAAuB;QACvBpF,aAAa8E,OAAO,CAAC8B,SAAS;QAE9B,gBAAgB;QAChBnB,oBAAS,CAACC,KAAK,CAACR,iBAAM,CAACC,SAAS,CAAC;QAEjC,4BAA4B;QAC5BF,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAE3D,+BAA+B;QAC/B,MAAMC,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;QACvD;QAEA,8BAA8B;QAC9BH,OAAOjF,aAAa8E,OAAO,EAAEQ,gBAAgB;IAC/C;IAEAT,GAAG,iCAAiC;QAClC7E,aAAa8E,OAAO,CAACyB,iBAAiB,CAAC,IAAIC,MAAM;QAEjDxB,IAAAA,iBAAM,gBAAC,qBAAC9E;QAER,2BAA2B;QAC3B,MAAMmF,IAAAA,kBAAO,EAAC;YACZJ,OAAOC,iBAAM,CAACgB,SAAS,CAAC,UAAUd,iBAAiB;YACnDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC7D;QAEA,gCAAgC;QAChCH,OAAOC,iBAAM,CAAC2B,WAAW,CAAC,oBAAoBR,GAAG,CAACjB,iBAAiB;IACrE;AACF"}