{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\auth\\__tests__\\login-form.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { LoginForm } from '../login-form'\nimport { useAuthStore } from '@/stores/auth-store'\nimport { createMockUser, runAxeTest } from '@/lib/test-utils'\nimport { useRouter } from 'next/navigation'\n\n// Mock Next.js router\njest.mock('next/navigation', () => ({\n  useRouter: jest.fn(),\n}))\n\n// Mock the auth store\njest.mock('@/stores/auth-store')\nconst mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>\n\n// Mock react-hot-toast\njest.mock('react-hot-toast', () => ({\n  toast: {\n    success: jest.fn(),\n    error: jest.fn(),\n  },\n}))\n\ndescribe('LoginForm Component', () => {\n  const mockPush = jest.fn()\n  const mockLogin = jest.fn()\n  const mockClearError = jest.fn()\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n    \n    // Mock router\n    ;(useRouter as jest.Mock).mockReturnValue({\n      push: mockPush,\n      replace: jest.fn(),\n      prefetch: jest.fn(),\n    })\n\n    // Mock auth store\n    mockUseAuthStore.mockReturnValue({\n      login: mockLogin,\n      isLoading: false,\n      error: null,\n      clearError: mockClearError,\n      user: null,\n      token: null,\n      refreshToken: null,\n      isAuthenticated: false,\n      logout: jest.fn(),\n      register: jest.fn(),\n      refreshToken: jest.fn(),\n      changePassword: jest.fn(),\n      updateProfile: jest.fn(),\n      hasPermission: jest.fn(),\n      hasRole: jest.fn(),\n      canAccess: jest.fn(),\n      checkSession: jest.fn(),\n      setLoading: jest.fn(),\n      fetchUserProfile: jest.fn(),\n      updateLastActivity: jest.fn(),\n    })\n  })\n\n  it('renders correctly', () => {\n    render(<LoginForm />)\n    \n    expect(screen.getByText('UAV Control System')).toBeInTheDocument()\n    expect(screen.getByText('Sign in to access the UAV management dashboard')).toBeInTheDocument()\n    expect(screen.getByLabelText(/username/i)).toBeInTheDocument()\n    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()\n    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()\n  })\n\n  it('handles form submission with valid data', async () => {\n    mockLogin.mockResolvedValue(true)\n    \n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    const passwordInput = screen.getByLabelText(/password/i)\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(mockClearError).toHaveBeenCalled()\n      expect(mockLogin).toHaveBeenCalledWith({\n        username: 'testuser',\n        password: 'password123',\n        rememberMe: false,\n      })\n    })\n  })\n\n  it('redirects to dashboard on successful login', async () => {\n    mockLogin.mockResolvedValue(true)\n    \n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    const passwordInput = screen.getByLabelText(/password/i)\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(mockPush).toHaveBeenCalledWith('/dashboard')\n    })\n  })\n\n  it('calls onSuccess callback when provided', async () => {\n    const mockOnSuccess = jest.fn()\n    mockLogin.mockResolvedValue(true)\n    \n    render(<LoginForm onSuccess={mockOnSuccess} />)\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    const passwordInput = screen.getByLabelText(/password/i)\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(mockOnSuccess).toHaveBeenCalled()\n      expect(mockPush).not.toHaveBeenCalled()\n    })\n  })\n\n  it('redirects to custom path when provided', async () => {\n    mockLogin.mockResolvedValue(true)\n    \n    render(<LoginForm redirectTo=\"/custom-path\" />)\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    const passwordInput = screen.getByLabelText(/password/i)\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(mockPush).toHaveBeenCalledWith('/custom-path')\n    })\n  })\n\n  it('handles remember me checkbox', async () => {\n    mockLogin.mockResolvedValue(true)\n    \n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    const passwordInput = screen.getByLabelText(/password/i)\n    const rememberMeCheckbox = screen.getByLabelText(/remember me/i)\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(rememberMeCheckbox)\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(mockLogin).toHaveBeenCalledWith({\n        username: 'testuser',\n        password: 'password123',\n        rememberMe: true,\n      })\n    })\n  })\n\n  it('toggles password visibility', () => {\n    render(<LoginForm />)\n    \n    const passwordInput = screen.getByLabelText(/password/i)\n    const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i })\n    \n    expect(passwordInput).toHaveAttribute('type', 'password')\n    \n    fireEvent.click(toggleButton)\n    expect(passwordInput).toHaveAttribute('type', 'text')\n    \n    fireEvent.click(toggleButton)\n    expect(passwordInput).toHaveAttribute('type', 'password')\n  })\n\n  it('displays loading state during login', () => {\n    mockUseAuthStore.mockReturnValue({\n      ...mockUseAuthStore(),\n      isLoading: true,\n    })\n    \n    render(<LoginForm />)\n    \n    const submitButton = screen.getByRole('button', { name: /signing in/i })\n    expect(submitButton).toBeDisabled()\n    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()\n  })\n\n  it('displays error message when login fails', () => {\n    const errorMessage = 'Invalid credentials'\n    mockUseAuthStore.mockReturnValue({\n      ...mockUseAuthStore(),\n      error: errorMessage,\n    })\n    \n    render(<LoginForm />)\n    \n    expect(screen.getByText(errorMessage)).toBeInTheDocument()\n    expect(screen.getByRole('alert')).toBeInTheDocument()\n  })\n\n  it('validates required fields', async () => {\n    render(<LoginForm />)\n    \n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(screen.getByText(/username is required/i)).toBeInTheDocument()\n      expect(screen.getByText(/password is required/i)).toBeInTheDocument()\n    })\n    \n    expect(mockLogin).not.toHaveBeenCalled()\n  })\n\n  it('validates username format', async () => {\n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'ab' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(screen.getByText(/username must be at least 3 characters/i)).toBeInTheDocument()\n    })\n  })\n\n  it('validates password format', async () => {\n    render(<LoginForm />)\n    \n    const passwordInput = screen.getByLabelText(/password/i)\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(passwordInput, { target: { value: '123' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(screen.getByText(/password must be at least 6 characters/i)).toBeInTheDocument()\n    })\n  })\n\n  it('clears error when user starts typing', () => {\n    mockUseAuthStore.mockReturnValue({\n      ...mockUseAuthStore(),\n      error: 'Some error',\n    })\n    \n    render(<LoginForm />)\n    \n    expect(screen.getByText('Some error')).toBeInTheDocument()\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    fireEvent.change(usernameInput, { target: { value: 'test' } })\n    \n    expect(mockClearError).toHaveBeenCalled()\n  })\n\n  it('handles keyboard navigation', () => {\n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    const passwordInput = screen.getByLabelText(/password/i)\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    usernameInput.focus()\n    expect(usernameInput).toHaveFocus()\n    \n    fireEvent.keyDown(usernameInput, { key: 'Tab' })\n    expect(passwordInput).toHaveFocus()\n    \n    fireEvent.keyDown(passwordInput, { key: 'Tab' })\n    // Should focus on remember me checkbox or submit button\n  })\n\n  it('handles Enter key submission', async () => {\n    mockLogin.mockResolvedValue(true)\n    \n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    const passwordInput = screen.getByLabelText(/password/i)\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.keyDown(passwordInput, { key: 'Enter', code: 'Enter' })\n    \n    await waitFor(() => {\n      expect(mockLogin).toHaveBeenCalled()\n    })\n  })\n\n  it('maintains accessibility standards', async () => {\n    const { container } = render(<LoginForm />)\n    \n    // Check for proper labeling\n    expect(screen.getByLabelText(/username/i)).toBeInTheDocument()\n    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()\n    \n    // Check for form structure\n    expect(screen.getByRole('form') || container.querySelector('form')).toBeInTheDocument()\n    \n    // Run accessibility tests\n    await runAxeTest(container)\n  })\n\n  it('supports autofill and autocomplete', () => {\n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    const passwordInput = screen.getByLabelText(/password/i)\n    \n    expect(usernameInput).toHaveAttribute('autoComplete', 'username')\n    expect(passwordInput).toHaveAttribute('autoComplete', 'current-password')\n  })\n\n  it('prevents multiple submissions', async () => {\n    mockLogin.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve(true), 100)))\n    \n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    const passwordInput = screen.getByLabelText(/password/i)\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    \n    fireEvent.click(submitButton)\n    fireEvent.click(submitButton)\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(mockLogin).toHaveBeenCalledTimes(1)\n    })\n  })\n\n  it('handles network errors gracefully', async () => {\n    mockLogin.mockResolvedValue(false)\n    mockUseAuthStore.mockReturnValue({\n      ...mockUseAuthStore(),\n      error: 'Network error',\n    })\n    \n    render(<LoginForm />)\n    \n    const usernameInput = screen.getByLabelText(/username/i)\n    const passwordInput = screen.getByLabelText(/password/i)\n    const submitButton = screen.getByRole('button', { name: /sign in/i })\n    \n    fireEvent.change(usernameInput, { target: { value: 'testuser' } })\n    fireEvent.change(passwordInput, { target: { value: 'password123' } })\n    fireEvent.click(submitButton)\n    \n    await waitFor(() => {\n      expect(screen.getByText('Network error')).toBeInTheDocument()\n      expect(mockPush).not.toHaveBeenCalled()\n    })\n  })\n})\n"], "names": ["jest", "mock", "useRouter", "fn", "toast", "success", "error", "mockUseAuthStore", "useAuthStore", "describe", "mockPush", "mockLogin", "mockClearError", "beforeEach", "clearAllMocks", "mockReturnValue", "push", "replace", "prefetch", "login", "isLoading", "clearError", "user", "token", "refreshToken", "isAuthenticated", "logout", "register", "changePassword", "updateProfile", "hasPermission", "hasRole", "canAccess", "checkSession", "setLoading", "fetchUserProfile", "updateLastActivity", "it", "render", "LoginForm", "expect", "screen", "getByText", "toBeInTheDocument", "getByLabelText", "getByRole", "name", "mockResolvedValue", "usernameInput", "passwordInput", "submitButton", "fireEvent", "change", "target", "value", "click", "waitFor", "toHaveBeenCalled", "toHaveBeenCalledWith", "username", "password", "rememberMe", "mockOnSuccess", "onSuccess", "not", "redirectTo", "rememberMeCheckbox", "to<PERSON><PERSON><PERSON><PERSON>", "toHaveAttribute", "toBeDisabled", "getByTestId", "errorMessage", "focus", "toHaveFocus", "keyDown", "key", "code", "container", "querySelector", "runAxeTest", "mockImplementation", "Promise", "resolve", "setTimeout", "toHaveBeenCalledTimes"], "mappings": ";AAOA,sBAAsB;AACtBA,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC,WAAWF,KAAKG,EAAE;IACpB,CAAA;AAEA,sBAAsB;AACtBH,KAAKC,IAAI,CAAC;AAGV,uBAAuB;AACvBD,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCG,OAAO;YACLC,SAASL,KAAKG,EAAE;YAChBG,OAAON,KAAKG,EAAE;QAChB;IACF,CAAA;;;;;8DAtBkB;2BACiC;2BACzB;2BACG;4BAEH;;;;;;AAS1B,MAAMI,mBAAmBC,uBAAY;AAUrCC,SAAS,uBAAuB;IAC9B,MAAMC,WAAWV,KAAKG,EAAE;IACxB,MAAMQ,YAAYX,KAAKG,EAAE;IACzB,MAAMS,iBAAiBZ,KAAKG,EAAE;IAE9BU,WAAW;QACTb,KAAKc,aAAa;QAGhBZ,qBAAS,CAAea,eAAe,CAAC;YACxCC,MAAMN;YACNO,SAASjB,KAAKG,EAAE;YAChBe,UAAUlB,KAAKG,EAAE;QACnB;QAEA,kBAAkB;QAClBI,iBAAiBQ,eAAe,CAAC;YAC/BI,OAAOR;YACPS,WAAW;YACXd,OAAO;YACPe,YAAYT;YACZU,MAAM;YACNC,OAAO;YACPC,cAAc;YACdC,iBAAiB;YACjBC,QAAQ1B,KAAKG,EAAE;YACfwB,UAAU3B,KAAKG,EAAE;YACjBqB,cAAcxB,KAAKG,EAAE;YACrByB,gBAAgB5B,KAAKG,EAAE;YACvB0B,eAAe7B,KAAKG,EAAE;YACtB2B,eAAe9B,KAAKG,EAAE;YACtB4B,SAAS/B,KAAKG,EAAE;YAChB6B,WAAWhC,KAAKG,EAAE;YAClB8B,cAAcjC,KAAKG,EAAE;YACrB+B,YAAYlC,KAAKG,EAAE;YACnBgC,kBAAkBnC,KAAKG,EAAE;YACzBiC,oBAAoBpC,KAAKG,EAAE;QAC7B;IACF;IAEAkC,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjBC,OAAOC,iBAAM,CAACC,SAAS,CAAC,uBAAuBC,iBAAiB;QAChEH,OAAOC,iBAAM,CAACC,SAAS,CAAC,mDAAmDC,iBAAiB;QAC5FH,OAAOC,iBAAM,CAACG,cAAc,CAAC,cAAcD,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACG,cAAc,CAAC,cAAcD,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW,IAAIH,iBAAiB;IAC5E;IAEAN,GAAG,2CAA2C;QAC5C1B,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAO5B,gBAAgB6C,gBAAgB;YACvCjB,OAAO7B,WAAW+C,oBAAoB,CAAC;gBACrCC,UAAU;gBACVC,UAAU;gBACVC,YAAY;YACd;QACF;IACF;IAEAxB,GAAG,8CAA8C;QAC/C1B,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAO9B,UAAUgD,oBAAoB,CAAC;QACxC;IACF;IAEArB,GAAG,0CAA0C;QAC3C,MAAMyB,gBAAgB9D,KAAKG,EAAE;QAC7BQ,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;YAACwB,WAAWD;;QAE7B,MAAMd,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAOsB,eAAeL,gBAAgB;YACtCjB,OAAO9B,UAAUsD,GAAG,CAACP,gBAAgB;QACvC;IACF;IAEApB,GAAG,0CAA0C;QAC3C1B,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;YAAC0B,YAAW;;QAE7B,MAAMjB,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAO9B,UAAUgD,oBAAoB,CAAC;QACxC;IACF;IAEArB,GAAG,gCAAgC;QACjC1B,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMsB,qBAAqBzB,iBAAM,CAACG,cAAc,CAAC;QACjD,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACW;QAChBf,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAO7B,WAAW+C,oBAAoB,CAAC;gBACrCC,UAAU;gBACVC,UAAU;gBACVC,YAAY;YACd;QACF;IACF;IAEAxB,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMU,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMuB,eAAe1B,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAA8B;QAEtFN,OAAOS,eAAemB,eAAe,CAAC,QAAQ;QAE9CjB,oBAAS,CAACI,KAAK,CAACY;QAChB3B,OAAOS,eAAemB,eAAe,CAAC,QAAQ;QAE9CjB,oBAAS,CAACI,KAAK,CAACY;QAChB3B,OAAOS,eAAemB,eAAe,CAAC,QAAQ;IAChD;IAEA/B,GAAG,uCAAuC;QACxC9B,iBAAiBQ,eAAe,CAAC;YAC/B,GAAGR,kBAAkB;YACrBa,WAAW;QACb;QAEAkB,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMW,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAc;QACtEN,OAAOU,cAAcmB,YAAY;QACjC7B,OAAOC,iBAAM,CAAC6B,WAAW,CAAC,oBAAoB3B,iBAAiB;IACjE;IAEAN,GAAG,2CAA2C;QAC5C,MAAMkC,eAAe;QACrBhE,iBAAiBQ,eAAe,CAAC;YAC/B,GAAGR,kBAAkB;YACrBD,OAAOiE;QACT;QAEAjC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjBC,OAAOC,iBAAM,CAACC,SAAS,CAAC6B,eAAe5B,iBAAiB;QACxDH,OAAOC,iBAAM,CAACI,SAAS,CAAC,UAAUF,iBAAiB;IACrD;IAEAN,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMW,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QACnEK,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAOC,iBAAM,CAACC,SAAS,CAAC,0BAA0BC,iBAAiB;YACnEH,OAAOC,iBAAM,CAACC,SAAS,CAAC,0BAA0BC,iBAAiB;QACrE;QAEAH,OAAO7B,WAAWqD,GAAG,CAACP,gBAAgB;IACxC;IAEApB,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAK;QAAE;QAC1DH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAOC,iBAAM,CAACC,SAAS,CAAC,4CAA4CC,iBAAiB;QACvF;IACF;IAEAN,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMU,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAM;QAAE;QAC3DH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAOC,iBAAM,CAACC,SAAS,CAAC,4CAA4CC,iBAAiB;QACvF;IACF;IAEAN,GAAG,wCAAwC;QACzC9B,iBAAiBQ,eAAe,CAAC;YAC/B,GAAGR,kBAAkB;YACrBD,OAAO;QACT;QAEAgC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjBC,OAAOC,iBAAM,CAACC,SAAS,CAAC,eAAeC,iBAAiB;QAExD,MAAMK,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5CO,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAO;QAAE;QAE5Dd,OAAO5B,gBAAgB6C,gBAAgB;IACzC;IAEApB,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEE,cAAcwB,KAAK;QACnBhC,OAAOQ,eAAeyB,WAAW;QAEjCtB,oBAAS,CAACuB,OAAO,CAAC1B,eAAe;YAAE2B,KAAK;QAAM;QAC9CnC,OAAOS,eAAewB,WAAW;QAEjCtB,oBAAS,CAACuB,OAAO,CAACzB,eAAe;YAAE0B,KAAK;QAAM;IAC9C,wDAAwD;IAC1D;IAEAtC,GAAG,gCAAgC;QACjC1B,UAAUoC,iBAAiB,CAAC;QAE5BT,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAE5CO,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACuB,OAAO,CAACzB,eAAe;YAAE0B,KAAK;YAASC,MAAM;QAAQ;QAE/D,MAAMpB,IAAAA,kBAAO,EAAC;YACZhB,OAAO7B,WAAW8C,gBAAgB;QACpC;IACF;IAEApB,GAAG,qCAAqC;QACtC,MAAM,EAAEwC,SAAS,EAAE,GAAGvC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEvC,4BAA4B;QAC5BC,OAAOC,iBAAM,CAACG,cAAc,CAAC,cAAcD,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACG,cAAc,CAAC,cAAcD,iBAAiB;QAE5D,2BAA2B;QAC3BH,OAAOC,iBAAM,CAACI,SAAS,CAAC,WAAWgC,UAAUC,aAAa,CAAC,SAASnC,iBAAiB;QAErF,0BAA0B;QAC1B,MAAMoC,IAAAA,qBAAU,EAACF;IACnB;IAEAxC,GAAG,sCAAsC;QACvCC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAE5CJ,OAAOQ,eAAeoB,eAAe,CAAC,gBAAgB;QACtD5B,OAAOS,eAAemB,eAAe,CAAC,gBAAgB;IACxD;IAEA/B,GAAG,iCAAiC;QAClC1B,UAAUqE,kBAAkB,CAAC,IAAM,IAAIC,QAAQC,CAAAA,UAAWC,WAAW,IAAMD,QAAQ,OAAO;QAE1F5C,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QAEnEH,oBAAS,CAACI,KAAK,CAACL;QAChBC,oBAAS,CAACI,KAAK,CAACL;QAChBC,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAO7B,WAAWyE,qBAAqB,CAAC;QAC1C;IACF;IAEA/C,GAAG,qCAAqC;QACtC1B,UAAUoC,iBAAiB,CAAC;QAC5BxC,iBAAiBQ,eAAe,CAAC;YAC/B,GAAGR,kBAAkB;YACrBD,OAAO;QACT;QAEAgC,IAAAA,iBAAM,gBAAC,qBAACC,oBAAS;QAEjB,MAAMS,gBAAgBP,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMK,gBAAgBR,iBAAM,CAACG,cAAc,CAAC;QAC5C,MAAMM,eAAeT,iBAAM,CAACI,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAW;QAEnEK,oBAAS,CAACC,MAAM,CAACJ,eAAe;YAAEK,QAAQ;gBAAEC,OAAO;YAAW;QAAE;QAChEH,oBAAS,CAACC,MAAM,CAACH,eAAe;YAAEI,QAAQ;gBAAEC,OAAO;YAAc;QAAE;QACnEH,oBAAS,CAACI,KAAK,CAACL;QAEhB,MAAMM,IAAAA,kBAAO,EAAC;YACZhB,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC3DH,OAAO9B,UAAUsD,GAAG,CAACP,gBAAgB;QACvC;IACF;AACF"}