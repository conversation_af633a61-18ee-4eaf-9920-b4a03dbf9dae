f15249732f68fade880436eb863766dc
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useUAVStore", {
    enumerable: true,
    get: function() {
        return useUAVStore;
    }
});
const _zustand = require("zustand");
const _middleware = require("zustand/middleware");
const _immer = require("zustand/middleware/immer");
const _uavapi = require("../api/uav-api");
const _reacthottoast = require("react-hot-toast");
const useUAVStore = (0, _zustand.create)()((0, _middleware.devtools)((0, _middleware.subscribeWithSelector)((0, _immer.immer)((set, get)=>({
        // Initial state
        uavs: [],
        selectedUAV: null,
        regions: [],
        systemStats: null,
        hibernatePodStatus: null,
        loading: false,
        error: null,
        filter: {},
        pagination: {
            page: 1,
            limit: 10,
            sortBy: "id",
            sortOrder: "desc"
        },
        searchQuery: "",
        // Fetch UAVs
        fetchUAVs: async ()=>{
            set((state)=>{
                state.loading = true;
                state.error = null;
            });
            try {
                const { filter, pagination, searchQuery } = get();
                const finalFilter = searchQuery ? {
                    ...filter,
                    search: searchQuery
                } : filter;
                const uavs = await _uavapi.uavApi.getUAVs(finalFilter, pagination);
                set((state)=>{
                    state.uavs = uavs;
                    state.loading = false;
                });
            } catch (error) {
                set((state)=>{
                    state.error = error instanceof Error ? error.message : "Failed to fetch UAVs";
                    state.loading = false;
                });
            }
        },
        // Fetch UAV by ID
        fetchUAVById: async (id)=>{
            set((state)=>{
                state.loading = true;
                state.error = null;
            });
            try {
                const uav = await _uavapi.uavApi.getUAVById(id);
                set((state)=>{
                    state.selectedUAV = uav;
                    state.loading = false;
                });
            } catch (error) {
                set((state)=>{
                    state.error = error instanceof Error ? error.message : "Failed to fetch UAV";
                    state.loading = false;
                });
            }
        },
        // Create UAV
        createUAV: async (uav)=>{
            set((state)=>{
                state.loading = true;
                state.error = null;
            });
            try {
                const response = await _uavapi.uavApi.createUAV(uav);
                if (response.success && response.data) {
                    set((state)=>{
                        state.uavs.unshift(response.data);
                        state.loading = false;
                    });
                    _reacthottoast.toast.success("UAV created successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Failed to create UAV");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Failed to create UAV";
                set((state)=>{
                    state.error = message;
                    state.loading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Update UAV
        updateUAV: async (id, uav)=>{
            set((state)=>{
                state.loading = true;
                state.error = null;
            });
            try {
                const response = await _uavapi.uavApi.updateUAV(id, uav);
                if (response.success && response.data) {
                    set((state)=>{
                        const index = state.uavs.findIndex((u)=>u.id === id);
                        if (index !== -1) {
                            state.uavs[index] = response.data;
                        }
                        if (state.selectedUAV?.id === id) {
                            state.selectedUAV = response.data;
                        }
                        state.loading = false;
                    });
                    _reacthottoast.toast.success("UAV updated successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Failed to update UAV");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Failed to update UAV";
                set((state)=>{
                    state.error = message;
                    state.loading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Delete UAV
        deleteUAV: async (id)=>{
            set((state)=>{
                state.loading = true;
                state.error = null;
            });
            try {
                const response = await _uavapi.uavApi.deleteUAV(id);
                if (response.success) {
                    set((state)=>{
                        state.uavs = state.uavs.filter((u)=>u.id !== id);
                        if (state.selectedUAV?.id === id) {
                            state.selectedUAV = null;
                        }
                        state.loading = false;
                    });
                    _reacthottoast.toast.success("UAV deleted successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Failed to delete UAV");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Failed to delete UAV";
                set((state)=>{
                    state.error = message;
                    state.loading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Update UAV status
        updateUAVStatus: async (id)=>{
            try {
                const response = await _uavapi.uavApi.updateUAVStatus(id);
                if (response.success) {
                    set((state)=>{
                        const index = state.uavs.findIndex((u)=>u.id === id);
                        if (index !== -1) {
                            // Toggle status
                            const currentStatus = state.uavs[index].status;
                            state.uavs[index].status = currentStatus === "AUTHORIZED" ? "UNAUTHORIZED" : "AUTHORIZED";
                        }
                    });
                    _reacthottoast.toast.success("UAV status updated successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Failed to update UAV status");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Failed to update UAV status";
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Fetch regions
        fetchRegions: async ()=>{
            try {
                const regions = await _uavapi.regionApi.getRegions();
                set((state)=>{
                    state.regions = regions;
                });
            } catch (error) {
                console.error("Failed to fetch regions:", error);
            }
        },
        // Add region to UAV
        addRegionToUAV: async (uavId, regionId)=>{
            try {
                const response = await _uavapi.uavApi.addRegionToUAV(uavId, regionId);
                if (response.success && response.data) {
                    set((state)=>{
                        const index = state.uavs.findIndex((u)=>u.id === uavId);
                        if (index !== -1) {
                            state.uavs[index] = response.data;
                        }
                    });
                    _reacthottoast.toast.success("Region added to UAV successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Failed to add region to UAV");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Failed to add region to UAV";
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Remove region from UAV
        removeRegionFromUAV: async (uavId, regionId)=>{
            try {
                const response = await _uavapi.uavApi.removeRegionFromUAV(uavId, regionId);
                if (response.success) {
                    set((state)=>{
                        const index = state.uavs.findIndex((u)=>u.id === uavId);
                        if (index !== -1) {
                            state.uavs[index].regions = state.uavs[index].regions.filter((r)=>r.id !== regionId);
                        }
                    });
                    _reacthottoast.toast.success("Region removed from UAV successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Failed to remove region from UAV");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Failed to remove region from UAV";
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Fetch hibernate pod status
        fetchHibernatePodStatus: async ()=>{
            try {
                const status = await _uavapi.hibernatePodApi.getStatus();
                set((state)=>{
                    state.hibernatePodStatus = status;
                });
            } catch (error) {
                console.error("Failed to fetch hibernate pod status:", error);
            }
        },
        // Add to hibernate pod
        addToHibernatePod: async (uavId)=>{
            try {
                const response = await _uavapi.hibernatePodApi.addUAV(uavId);
                if (response.success) {
                    set((state)=>{
                        const index = state.uavs.findIndex((u)=>u.id === uavId);
                        if (index !== -1) {
                            state.uavs[index].inHibernatePod = true;
                            state.uavs[index].operationalStatus = "HIBERNATING";
                        }
                    });
                    // Refresh hibernate pod status
                    get().fetchHibernatePodStatus();
                    _reacthottoast.toast.success("UAV added to hibernate pod successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Failed to add UAV to hibernate pod");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Failed to add UAV to hibernate pod";
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Remove from hibernate pod
        removeFromHibernatePod: async (uavId)=>{
            try {
                const response = await _uavapi.hibernatePodApi.removeUAV(uavId);
                if (response.success) {
                    set((state)=>{
                        const index = state.uavs.findIndex((u)=>u.id === uavId);
                        if (index !== -1) {
                            state.uavs[index].inHibernatePod = false;
                            state.uavs[index].operationalStatus = "READY";
                        }
                    });
                    // Refresh hibernate pod status
                    get().fetchHibernatePodStatus();
                    _reacthottoast.toast.success("UAV removed from hibernate pod successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Failed to remove UAV from hibernate pod");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Failed to remove UAV from hibernate pod";
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Fetch system statistics
        fetchSystemStats: async ()=>{
            try {
                const stats = await _uavapi.uavApi.getSystemStatistics();
                set((state)=>{
                    state.systemStats = stats;
                });
            } catch (error) {
                console.error("Failed to fetch system statistics:", error);
            }
        },
        // UI actions
        setFilter: (filter)=>{
            set((state)=>{
                state.filter = {
                    ...state.filter,
                    ...filter
                };
            });
        },
        setPagination: (pagination)=>{
            set((state)=>{
                state.pagination = {
                    ...state.pagination,
                    ...pagination
                };
            });
        },
        setSearchQuery: (query)=>{
            set((state)=>{
                state.searchQuery = query;
            });
        },
        setSelectedUAV: (uav)=>{
            set((state)=>{
                state.selectedUAV = uav;
            });
        },
        clearError: ()=>{
            set((state)=>{
                state.error = null;
            });
        },
        // Bulk actions
        bulkUpdateStatus: async (uavIds, status)=>{
            try {
                const response = await _uavapi.uavApi.bulkUpdateStatus(uavIds, status);
                if (response.success) {
                    set((state)=>{
                        uavIds.forEach((id)=>{
                            const index = state.uavs.findIndex((u)=>u.id === id);
                            if (index !== -1) {
                                state.uavs[index].status = status;
                            }
                        });
                    });
                    _reacthottoast.toast.success(`${uavIds.length} UAVs updated successfully`);
                    return true;
                } else {
                    throw new Error(response.message || "Failed to update UAVs");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Failed to update UAVs";
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        bulkDelete: async (uavIds)=>{
            try {
                const response = await _uavapi.uavApi.bulkDelete(uavIds);
                if (response.success) {
                    set((state)=>{
                        state.uavs = state.uavs.filter((u)=>!uavIds.includes(u.id));
                    });
                    _reacthottoast.toast.success(`${uavIds.length} UAVs deleted successfully`);
                    return true;
                } else {
                    throw new Error(response.message || "Failed to delete UAVs");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Failed to delete UAVs";
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Real-time updates
        updateUAVInStore: (uav)=>{
            set((state)=>{
                const index = state.uavs.findIndex((u)=>u.id === uav.id);
                if (index !== -1) {
                    state.uavs[index] = uav;
                } else {
                    state.uavs.unshift(uav);
                }
                if (state.selectedUAV?.id === uav.id) {
                    state.selectedUAV = uav;
                }
            });
        },
        removeUAVFromStore: (uavId)=>{
            set((state)=>{
                state.uavs = state.uavs.filter((u)=>u.id !== uavId);
                if (state.selectedUAV?.id === uavId) {
                    state.selectedUAV = null;
                }
            });
        },
        updateHibernatePodInStore: (status)=>{
            set((state)=>{
                state.hibernatePodStatus = status;
            });
        }
    }))), {
    name: "uav-store"
}));

//# sourceMappingURL=data:application/json;base64,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