60a9df37a169eb1219984177e88d375c
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _input = require("../input");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
describe("Input Component", ()=>{
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            placeholder: "Enter text"
        }));
        const input = _testutils.screen.getByPlaceholderText("Enter text");
        expect(input).toBeInTheDocument();
        expect(input).toHaveClass("flex", "h-10", "w-full", "rounded-md", "border");
    });
    it("handles value changes", ()=>{
        const handleChange = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            onChange: handleChange
        }));
        const input = _testutils.screen.getByRole("textbox");
        _testutils.fireEvent.change(input, {
            target: {
                value: "test value"
            }
        });
        expect(handleChange).toHaveBeenCalledTimes(1);
        expect(input).toHaveValue("test value");
    });
    it("supports controlled input", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            value: "initial",
            onChange: ()=>{}
        }));
        let input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveValue("initial");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            value: "updated",
            onChange: ()=>{}
        }));
        input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveValue("updated");
    });
    it("supports uncontrolled input", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            defaultValue: "default"
        }));
        const input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveValue("default");
        _testutils.fireEvent.change(input, {
            target: {
                value: "changed"
            }
        });
        expect(input).toHaveValue("changed");
    });
    it("applies different input types", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "email"
        }));
        let input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveAttribute("type", "email");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "password"
        }));
        input = _testutils.screen.getByLabelText(/password/i) || _testutils.screen.getByDisplayValue("");
        expect(input).toHaveAttribute("type", "password");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "number"
        }));
        input = _testutils.screen.getByRole("spinbutton");
        expect(input).toHaveAttribute("type", "number");
    });
    it("handles disabled state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            disabled: true,
            placeholder: "Disabled input"
        }));
        const input = _testutils.screen.getByPlaceholderText("Disabled input");
        expect(input).toBeDisabled();
        expect(input).toHaveClass("disabled:cursor-not-allowed", "disabled:opacity-50");
    });
    it("handles readonly state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            readOnly: true,
            value: "readonly value"
        }));
        const input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveAttribute("readonly");
        expect(input).toHaveValue("readonly value");
    });
    it("applies custom className", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            className: "custom-input"
        }));
        const input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveClass("custom-input");
    });
    it("forwards ref correctly", ()=>{
        const ref = /*#__PURE__*/ _react.default.createRef();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            ref: ref
        }));
        expect(ref.current).toBeInstanceOf(HTMLInputElement);
    });
    it("supports all HTML input attributes", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            id: "test-input",
            name: "testName",
            placeholder: "Test placeholder",
            maxLength: 50,
            minLength: 5,
            required: true,
            "aria-label": "Test input",
            "data-testid": "test-input"
        }));
        const input = _testutils.screen.getByTestId("test-input");
        expect(input).toHaveAttribute("id", "test-input");
        expect(input).toHaveAttribute("name", "testName");
        expect(input).toHaveAttribute("placeholder", "Test placeholder");
        expect(input).toHaveAttribute("maxLength", "50");
        expect(input).toHaveAttribute("minLength", "5");
        expect(input).toHaveAttribute("required");
        expect(input).toHaveAttribute("aria-label", "Test input");
    });
    it("handles focus and blur events", ()=>{
        const handleFocus = jest.fn();
        const handleBlur = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            onFocus: handleFocus,
            onBlur: handleBlur
        }));
        const input = _testutils.screen.getByRole("textbox");
        // Use direct focus() method to actually set focus
        input.focus();
        _testutils.fireEvent.focus(input);
        expect(handleFocus).toHaveBeenCalledTimes(1);
        expect(input).toHaveFocus();
        input.blur();
        _testutils.fireEvent.blur(input);
        expect(handleBlur).toHaveBeenCalledTimes(1);
        expect(input).not.toHaveFocus();
    });
    it("handles keyboard events", ()=>{
        const handleKeyDown = jest.fn();
        const handleKeyUp = jest.fn();
        const handleKeyPress = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            onKeyDown: handleKeyDown,
            onKeyUp: handleKeyUp,
            onKeyPress: handleKeyPress
        }));
        const input = _testutils.screen.getByRole("textbox");
        _testutils.fireEvent.keyDown(input, {
            key: "Enter",
            code: "Enter"
        });
        expect(handleKeyDown).toHaveBeenCalledTimes(1);
        _testutils.fireEvent.keyUp(input, {
            key: "Enter",
            code: "Enter"
        });
        expect(handleKeyUp).toHaveBeenCalledTimes(1);
        _testutils.fireEvent.keyPress(input, {
            key: "a",
            code: "KeyA"
        });
        expect(handleKeyPress).toHaveBeenCalledTimes(1);
    });
    it("supports form validation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("form", {
            "data-testid": "test-form",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
                    type: "email",
                    required: true,
                    pattern: "[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}$",
                    title: "Please enter a valid email address"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                    type: "submit",
                    children: "Submit"
                })
            ]
        }));
        const input = _testutils.screen.getByRole("textbox");
        const form = _testutils.screen.getByTestId("test-form");
        expect(input).toHaveAttribute("required");
        expect(input).toHaveAttribute("pattern");
        expect(input).toHaveAttribute("title");
        // Test invalid input
        _testutils.fireEvent.change(input, {
            target: {
                value: "invalid-email"
            }
        });
        _testutils.fireEvent.submit(form);
        expect(input).toBeInvalid();
    });
    it("handles number input with min/max", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "number",
            min: 0,
            max: 100,
            step: 5,
            defaultValue: 50
        }));
        const input = _testutils.screen.getByRole("spinbutton");
        expect(input).toHaveAttribute("min", "0");
        expect(input).toHaveAttribute("max", "100");
        expect(input).toHaveAttribute("step", "5");
        expect(input).toHaveValue(50);
    });
    it("handles file input", ()=>{
        const handleChange = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "file",
            accept: ".jpg,.png,.pdf",
            multiple: true,
            onChange: handleChange
        }));
        const input = _testutils.screen.getByRole("button", {
            name: /choose files/i
        }) || _testutils.screen.getByLabelText(/file/i) || document.querySelector('input[type="file"]');
        expect(input).toHaveAttribute("type", "file");
        expect(input).toHaveAttribute("accept", ".jpg,.png,.pdf");
        expect(input).toHaveAttribute("multiple");
    });
    it("supports search input with clear functionality", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "search",
            defaultValue: "search term"
        }));
        const input = _testutils.screen.getByRole("searchbox");
        expect(input).toHaveAttribute("type", "search");
        expect(input).toHaveValue("search term");
    });
    it("handles date and time inputs", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "date"
        }));
        let input = _testutils.screen.getByDisplayValue("") || document.querySelector('input[type="date"]');
        expect(input).toHaveAttribute("type", "date");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "time"
        }));
        input = _testutils.screen.getByDisplayValue("") || document.querySelector('input[type="time"]');
        expect(input).toHaveAttribute("type", "time");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "datetime-local"
        }));
        input = _testutils.screen.getByDisplayValue("") || document.querySelector('input[type="datetime-local"]');
        expect(input).toHaveAttribute("type", "datetime-local");
    });
    it("maintains accessibility standards", async ()=>{
        const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                    htmlFor: "accessible-input",
                    children: "Email Address"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
                    id: "accessible-input",
                    type: "email",
                    required: true,
                    "aria-describedby": "email-help"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    id: "email-help",
                    children: "Enter your email address"
                })
            ]
        }));
        const input = _testutils.screen.getByLabelText("Email Address");
        expect(input).toHaveAttribute("aria-describedby", "email-help");
        // Run accessibility tests
        await (0, _testutils.runAxeTest)(container);
    });
    it("handles input with error state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            "aria-invalid": "true",
            "aria-describedby": "error-message",
            className: "border-red-500"
        }));
        const input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveAttribute("aria-invalid", "true");
        expect(input).toHaveAttribute("aria-describedby", "error-message");
        expect(input).toHaveClass("border-red-500");
    });
    it("supports input groups and addons", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            className: "flex",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                    className: "input-addon",
                    children: "$"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
                    type: "number",
                    placeholder: "0.00",
                    className: "rounded-l-none"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                    className: "input-addon",
                    children: ".00"
                })
            ]
        }));
        const input = _testutils.screen.getByRole("spinbutton");
        expect(input).toHaveClass("rounded-l-none");
        expect(_testutils.screen.getByText("$")).toBeInTheDocument();
        expect(_testutils.screen.getByText(".00")).toBeInTheDocument();
    });
});

//# sourceMappingURL=data:application/json;base64,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