{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\lib\\test-utils.tsx"], "sourcesContent": ["import React, { ReactElement } from 'react'\nimport { render, RenderOptions, screen, waitFor } from '@testing-library/react'\nimport userEvent from '@testing-library/user-event'\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\nimport { useUAVStore } from '@/stores/uav-store'\nimport { useDashboardStore } from '@/stores/dashboard-store'\nimport { useAuthStore } from '@/stores/auth-store'\nimport { User, Role, Permission } from '@/types/auth'\nimport { UAV, SystemAlert, DockingStation } from '@/types/uav'\nimport { axe, toHaveNoViolations } from 'jest-axe'\n\n// Extend Jest matchers\nexpect.extend(toHaveNoViolations)\n\n// Create a test query client\nconst createTestQueryClient = () =>\n  new QueryClient({\n    defaultOptions: {\n      queries: {\n        retry: false,\n        cacheTime: 0,\n      },\n    },\n  })\n\n// Test wrapper component\ninterface TestProvidersProps {\n  children: React.ReactNode\n  queryClient?: QueryClient\n}\n\nfunction TestProviders({ children, queryClient }: TestProvidersProps) {\n  const client = queryClient || createTestQueryClient()\n\n  return (\n    <QueryClientProvider client={client}>\n      {children}\n    </QueryClientProvider>\n  )\n}\n\n// Custom render function\nconst customRender = (\n  ui: ReactElement,\n  options?: Omit<RenderOptions, 'wrapper'> & {\n    queryClient?: QueryClient\n  }\n) => {\n  const { queryClient, ...renderOptions } = options || {}\n\n  return render(ui, {\n    wrapper: ({ children }) => (\n      <TestProviders queryClient={queryClient}>\n        {children}\n      </TestProviders>\n    ),\n    ...renderOptions,\n  })\n}\n\n// Mock data factories\nexport const createMockUser = (overrides?: Partial<User>): User => ({\n  id: 1,\n  username: 'testuser',\n  email: '<EMAIL>',\n  firstName: 'Test',\n  lastName: 'User',\n  roles: [createMockRole()],\n  permissions: [createMockPermission()],\n  isActive: true,\n  createdAt: '2024-01-01T00:00:00Z',\n  updatedAt: '2024-01-01T00:00:00Z',\n  ...overrides,\n})\n\nexport const createMockRole = (overrides?: Partial<Role>): Role => ({\n  id: 1,\n  name: 'ADMIN',\n  description: 'Administrator role',\n  permissions: [createMockPermission()],\n  ...overrides,\n})\n\nexport const createMockPermission = (overrides?: Partial<Permission>): Permission => ({\n  id: 1,\n  name: 'UAV_READ',\n  resource: 'UAV',\n  action: 'READ',\n  description: 'Read UAV data',\n  ...overrides,\n})\n\nexport const createMockUAV = (overrides?: Partial<UAV>): UAV => ({\n  id: 1,\n  rfidTag: 'UAV-001',\n  ownerName: 'Test Owner',\n  model: 'Test Model',\n  status: 'AUTHORIZED',\n  operationalStatus: 'READY',\n  inHibernatePod: false,\n  totalFlightHours: 10.5,\n  totalFlightCycles: 25,\n  createdAt: '2024-01-01T00:00:00Z',\n  updatedAt: '2024-01-01T00:00:00Z',\n  regions: [],\n  ...overrides,\n})\n\nexport const createMockAlert = (overrides?: Partial<SystemAlert>): SystemAlert => ({\n  id: 1,\n  type: 'WARNING',\n  title: 'Test Alert',\n  message: 'This is a test alert',\n  timestamp: new Date().toISOString(),\n  acknowledged: false,\n  source: 'SYSTEM',\n  severity: 'MEDIUM',\n  ...overrides,\n})\n\nexport const createMockDockingStation = (overrides?: Partial<DockingStation>): DockingStation => ({\n  id: 1,\n  name: 'Test Station',\n  location: { latitude: 40.7128, longitude: -74.0060 },\n  status: 'AVAILABLE',\n  capacity: 4,\n  currentOccupancy: 1,\n  isActive: true,\n  ...overrides,\n})\n\n// Store helpers\nexport const setupAuthStore = (user?: User) => {\n  const store = useAuthStore.getState()\n  \n  if (user) {\n    store.user = user\n    store.isAuthenticated = true\n    store.token = 'mock-token'\n  } else {\n    store.user = null\n    store.isAuthenticated = false\n    store.token = null\n  }\n  \n  store.isLoading = false\n  store.error = null\n}\n\nexport const setupUAVStore = (uavs: UAV[] = []) => {\n  const store = useUAVStore.getState()\n  store.uavs = uavs\n  store.loading = false\n  store.error = null\n  store.selectedUAV = null\n}\n\nexport const setupDashboardStore = () => {\n  const store = useDashboardStore.getState()\n  store.metrics = {\n    totalUAVs: 10,\n    authorizedUAVs: 8,\n    unauthorizedUAVs: 2,\n    activeFlights: 3,\n    hibernatingUAVs: 2,\n    lowBatteryCount: 1,\n    chargingCount: 2,\n    maintenanceCount: 1,\n    emergencyCount: 0,\n  }\n  store.isConnected = true\n  store.lastUpdate = new Date().toISOString()\n  store.alerts = []\n}\n\n// Test utilities\nexport const waitForLoadingToFinish = () => {\n  return new Promise(resolve => setTimeout(resolve, 0))\n}\n\nexport const mockApiResponse = <T,>(data: T, success = true) => ({\n  success,\n  message: success ? 'Success' : 'Error',\n  data: success ? data : undefined,\n})\n\nexport const mockApiError = (message = 'API Error') => {\n  throw new Error(message)\n}\n\n// Mock fetch\nexport const mockFetch = (response: any, ok = true) => {\n  global.fetch = jest.fn(() =>\n    Promise.resolve({\n      ok,\n      json: () => Promise.resolve(response),\n      text: () => Promise.resolve(JSON.stringify(response)),\n      status: ok ? 200 : 400,\n      statusText: ok ? 'OK' : 'Bad Request',\n    })\n  ) as jest.Mock\n}\n\n// Cleanup function\nexport const cleanup = () => {\n  // Reset all stores\n  useAuthStore.getState().logout()\n  useUAVStore.setState({\n    uavs: [],\n    selectedUAV: null,\n    loading: false,\n    error: null,\n    filter: {},\n    searchQuery: '',\n  })\n  useDashboardStore.setState({\n    metrics: null,\n    flightActivity: null,\n    batteryStats: null,\n    hibernatePodMetrics: null,\n    chartData: null,\n    alerts: [],\n    recentLocationUpdates: [],\n    isConnected: false,\n    lastUpdate: null,\n    connectionError: null,\n  })\n  \n  // Clear mocks\n  jest.clearAllMocks()\n  \n  // Clear localStorage\n  localStorage.clear()\n  sessionStorage.clear()\n}\n\n// Animation testing utilities\nexport const mockFramerMotion = () => {\n  jest.mock('framer-motion', () => ({\n    motion: {\n      div: ({ children, ...props }: any) => <div {...props}>{children}</div>,\n      button: ({ children, ...props }: any) => <button {...props}>{children}</button>,\n      span: ({ children, ...props }: any) => <span {...props}>{children}</span>,\n      section: ({ children, ...props }: any) => <section {...props}>{children}</section>,\n    },\n    AnimatePresence: ({ children }: any) => children,\n    useAnimation: () => ({\n      start: jest.fn(),\n      stop: jest.fn(),\n      set: jest.fn(),\n    }),\n    useMotionValue: () => ({\n      get: jest.fn(),\n      set: jest.fn(),\n    }),\n  }))\n}\n\nexport const mockPrefersReducedMotion = (value: boolean) => {\n  Object.defineProperty(window, 'matchMedia', {\n    writable: true,\n    value: jest.fn().mockImplementation(query => ({\n      matches: query === '(prefers-reduced-motion: reduce)' ? value : false,\n      media: query,\n      onchange: null,\n      addListener: jest.fn(),\n      removeListener: jest.fn(),\n      addEventListener: jest.fn(),\n      removeEventListener: jest.fn(),\n      dispatchEvent: jest.fn(),\n    })),\n  })\n}\n\n// Accessibility testing utilities\nexport const runAxeTest = async (container: HTMLElement) => {\n  const results = await axe(container)\n  expect(results).toHaveNoViolations()\n}\n\nexport const checkAriaAttributes = (element: HTMLElement, expectedAttributes: Record<string, string>) => {\n  Object.entries(expectedAttributes).forEach(([attr, value]) => {\n    expect(element).toHaveAttribute(attr, value)\n  })\n}\n\n// User interaction helpers\nexport const createUser = () => userEvent.setup()\n\nexport const typeIntoInput = async (input: HTMLElement, text: string) => {\n  const user = createUser()\n  await user.clear(input)\n  await user.type(input, text)\n}\n\nexport const clickElement = async (element: HTMLElement) => {\n  const user = createUser()\n  await user.click(element)\n}\n\n// Wait utilities\nexport const waitForElementToBeRemoved = async (element: HTMLElement) => {\n  await waitFor(() => {\n    expect(element).not.toBeInTheDocument()\n  })\n}\n\nexport const waitForLoadingToComplete = async () => {\n  await waitFor(() => {\n    expect(screen.queryByTestId('loading')).not.toBeInTheDocument()\n  }, { timeout: 5000 })\n}\n\n// Custom matchers\nexpect.extend({\n  toBeInTheDocument(received) {\n    const pass = received !== null && received !== undefined\n    return {\n      message: () => `expected element ${pass ? 'not ' : ''}to be in the document`,\n      pass,\n    }\n  },\n})\n\n// Re-export everything\nexport * from '@testing-library/react'\nexport { customRender as render }\nexport { createTestQueryClient }\nexport { userEvent }\n"], "names": ["checkAriaAttributes", "cleanup", "clickElement", "createMockAlert", "createMockDockingStation", "createMockPermission", "createMockRole", "createMockUAV", "createMockUser", "createTestQueryClient", "createUser", "mockApiError", "mockApiResponse", "mockFetch", "mockFramerMotion", "mockPrefersReducedMotion", "render", "customRender", "runAxeTest", "setupAuthStore", "setupDashboardStore", "setupUAVStore", "typeIntoInput", "userEvent", "waitForElementToBeRemoved", "waitForLoadingToComplete", "waitForLoadingToFinish", "expect", "extend", "toHaveNoViolations", "QueryClient", "defaultOptions", "queries", "retry", "cacheTime", "TestProviders", "children", "queryClient", "client", "QueryClientProvider", "ui", "options", "renderOptions", "wrapper", "overrides", "id", "username", "email", "firstName", "lastName", "roles", "permissions", "isActive", "createdAt", "updatedAt", "name", "description", "resource", "action", "rfidTag", "ownerName", "model", "status", "operationalStatus", "inHibernatePod", "totalFlightHours", "totalFlightCycles", "regions", "type", "title", "message", "timestamp", "Date", "toISOString", "acknowledged", "source", "severity", "location", "latitude", "longitude", "capacity", "currentOccupancy", "user", "store", "useAuthStore", "getState", "isAuthenticated", "token", "isLoading", "error", "uavs", "useUAVStore", "loading", "selectedUAV", "useDashboardStore", "metrics", "totalUAVs", "authorizedUAVs", "unauthorizedUAVs", "activeFlights", "hibernatingUAVs", "lowBatteryCount", "chargingCount", "maintenanceCount", "emergencyCount", "isConnected", "lastUpdate", "alerts", "Promise", "resolve", "setTimeout", "data", "success", "undefined", "Error", "response", "ok", "global", "fetch", "jest", "fn", "json", "text", "JSON", "stringify", "statusText", "logout", "setState", "filter", "searchQuery", "flightActivity", "batteryStats", "hibernatePodMetrics", "chartData", "recentLocationUpdates", "connectionError", "clearAllMocks", "localStorage", "clear", "sessionStorage", "mock", "motion", "div", "props", "button", "span", "section", "AnimatePresence", "useAnimation", "start", "stop", "set", "useMotionValue", "get", "value", "Object", "defineProperty", "window", "writable", "mockImplementation", "query", "matches", "media", "onchange", "addListener", "removeListener", "addEventListener", "removeEventListener", "dispatchEvent", "container", "results", "axe", "element", "expectedAttributes", "entries", "for<PERSON>ach", "attr", "toHaveAttribute", "setup", "input", "click", "waitFor", "not", "toBeInTheDocument", "screen", "queryByTestId", "timeout", "received", "pass"], "mappings": ";;;;;;;;;;;IAwRaA,mBAAmB;eAAnBA;;IA5EAC,OAAO;eAAPA;;IA2FAC,YAAY;eAAZA;;IA3LAC,eAAe;eAAfA;;IAYAC,wBAAwB;eAAxBA;;IArCAC,oBAAoB;eAApBA;;IARAC,cAAc;eAAdA;;IAiBAC,aAAa;eAAbA;;IA/BAC,cAAc;eAAdA;;IA0QJC,qBAAqB;eAArBA;;IAxCIC,UAAU;eAAVA;;IArGAC,YAAY;eAAZA;;IANAC,eAAe;eAAfA;;IAWAC,SAAS;eAATA;;IA8CAC,gBAAgB;eAAhBA;;IAqBAC,wBAAwB;eAAxBA;;IAoEYC,MAAM;eAAtBC;;IAnDIC,UAAU;eAAVA;;IA/IAC,cAAc;eAAdA;;IAyBAC,mBAAmB;eAAnBA;;IARAC,aAAa;eAAbA;;IA4IAC,aAAa;eAAbA;;IAuCJC,SAAS;eAATA,kBAAS;;IA3BLC,yBAAyB;eAAzBA;;IAMAC,wBAAwB;eAAxBA;;IAnIAC,sBAAsB;eAAtBA;;;;8DAhLuB;qCACmB;kEACjC;4BAC2B;0BACrB;gCACM;2BACL;yBAGW;;;;;;;;;;;;;;;;;;;AAExC,uBAAuB;AACvBC,OAAOC,MAAM,CAACC,2BAAkB;AAEhC,6BAA6B;AAC7B,MAAMpB,wBAAwB,IAC5B,IAAIqB,uBAAW,CAAC;QACdC,gBAAgB;YACdC,SAAS;gBACPC,OAAO;gBACPC,WAAW;YACb;QACF;IACF;AAQF,SAASC,cAAc,EAAEC,QAAQ,EAAEC,WAAW,EAAsB;IAClE,MAAMC,SAASD,eAAe5B;IAE9B,qBACE,qBAAC8B,+BAAmB;QAACD,QAAQA;kBAC1BF;;AAGP;AAEA,yBAAyB;AACzB,MAAMnB,eAAe,CACnBuB,IACAC;IAIA,MAAM,EAAEJ,WAAW,EAAE,GAAGK,eAAe,GAAGD,WAAW,CAAC;IAEtD,OAAOzB,IAAAA,cAAM,EAACwB,IAAI;QAChBG,SAAS,CAAC,EAAEP,QAAQ,EAAE,iBACpB,qBAACD;gBAAcE,aAAaA;0BACzBD;;QAGL,GAAGM,aAAa;IAClB;AACF;AAGO,MAAMlC,iBAAiB,CAACoC,YAAqC,CAAA;QAClEC,IAAI;QACJC,UAAU;QACVC,OAAO;QACPC,WAAW;QACXC,UAAU;QACVC,OAAO;YAAC5C;SAAiB;QACzB6C,aAAa;YAAC9C;SAAuB;QACrC+C,UAAU;QACVC,WAAW;QACXC,WAAW;QACX,GAAGV,SAAS;IACd,CAAA;AAEO,MAAMtC,iBAAiB,CAACsC,YAAqC,CAAA;QAClEC,IAAI;QACJU,MAAM;QACNC,aAAa;QACbL,aAAa;YAAC9C;SAAuB;QACrC,GAAGuC,SAAS;IACd,CAAA;AAEO,MAAMvC,uBAAuB,CAACuC,YAAiD,CAAA;QACpFC,IAAI;QACJU,MAAM;QACNE,UAAU;QACVC,QAAQ;QACRF,aAAa;QACb,GAAGZ,SAAS;IACd,CAAA;AAEO,MAAMrC,gBAAgB,CAACqC,YAAmC,CAAA;QAC/DC,IAAI;QACJc,SAAS;QACTC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,mBAAmB;QACnBC,gBAAgB;QAChBC,kBAAkB;QAClBC,mBAAmB;QACnBb,WAAW;QACXC,WAAW;QACXa,SAAS,EAAE;QACX,GAAGvB,SAAS;IACd,CAAA;AAEO,MAAMzC,kBAAkB,CAACyC,YAAmD,CAAA;QACjFC,IAAI;QACJuB,MAAM;QACNC,OAAO;QACPC,SAAS;QACTC,WAAW,IAAIC,OAAOC,WAAW;QACjCC,cAAc;QACdC,QAAQ;QACRC,UAAU;QACV,GAAGhC,SAAS;IACd,CAAA;AAEO,MAAMxC,2BAA2B,CAACwC,YAAyD,CAAA;QAChGC,IAAI;QACJU,MAAM;QACNsB,UAAU;YAAEC,UAAU;YAASC,WAAW,CAAC;QAAQ;QACnDjB,QAAQ;QACRkB,UAAU;QACVC,kBAAkB;QAClB7B,UAAU;QACV,GAAGR,SAAS;IACd,CAAA;AAGO,MAAMzB,iBAAiB,CAAC+D;IAC7B,MAAMC,QAAQC,uBAAY,CAACC,QAAQ;IAEnC,IAAIH,MAAM;QACRC,MAAMD,IAAI,GAAGA;QACbC,MAAMG,eAAe,GAAG;QACxBH,MAAMI,KAAK,GAAG;IAChB,OAAO;QACLJ,MAAMD,IAAI,GAAG;QACbC,MAAMG,eAAe,GAAG;QACxBH,MAAMI,KAAK,GAAG;IAChB;IAEAJ,MAAMK,SAAS,GAAG;IAClBL,MAAMM,KAAK,GAAG;AAChB;AAEO,MAAMpE,gBAAgB,CAACqE,OAAc,EAAE;IAC5C,MAAMP,QAAQQ,qBAAW,CAACN,QAAQ;IAClCF,MAAMO,IAAI,GAAGA;IACbP,MAAMS,OAAO,GAAG;IAChBT,MAAMM,KAAK,GAAG;IACdN,MAAMU,WAAW,GAAG;AACtB;AAEO,MAAMzE,sBAAsB;IACjC,MAAM+D,QAAQW,iCAAiB,CAACT,QAAQ;IACxCF,MAAMY,OAAO,GAAG;QACdC,WAAW;QACXC,gBAAgB;QAChBC,kBAAkB;QAClBC,eAAe;QACfC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,kBAAkB;QAClBC,gBAAgB;IAClB;IACArB,MAAMsB,WAAW,GAAG;IACpBtB,MAAMuB,UAAU,GAAG,IAAIlC,OAAOC,WAAW;IACzCU,MAAMwB,MAAM,GAAG,EAAE;AACnB;AAGO,MAAMjF,yBAAyB;IACpC,OAAO,IAAIkF,QAAQC,CAAAA,UAAWC,WAAWD,SAAS;AACpD;AAEO,MAAMjG,kBAAkB,CAAKmG,MAASC,UAAU,IAAI,GAAM,CAAA;QAC/DA;QACA1C,SAAS0C,UAAU,YAAY;QAC/BD,MAAMC,UAAUD,OAAOE;IACzB,CAAA;AAEO,MAAMtG,eAAe,CAAC2D,UAAU,WAAW;IAChD,MAAM,IAAI4C,MAAM5C;AAClB;AAGO,MAAMzD,YAAY,CAACsG,UAAeC,KAAK,IAAI;IAChDC,OAAOC,KAAK,GAAGC,KAAKC,EAAE,CAAC,IACrBZ,QAAQC,OAAO,CAAC;YACdO;YACAK,MAAM,IAAMb,QAAQC,OAAO,CAACM;YAC5BO,MAAM,IAAMd,QAAQC,OAAO,CAACc,KAAKC,SAAS,CAACT;YAC3CrD,QAAQsD,KAAK,MAAM;YACnBS,YAAYT,KAAK,OAAO;QAC1B;AAEJ;AAGO,MAAMnH,UAAU;IACrB,mBAAmB;IACnBmF,uBAAY,CAACC,QAAQ,GAAGyC,MAAM;IAC9BnC,qBAAW,CAACoC,QAAQ,CAAC;QACnBrC,MAAM,EAAE;QACRG,aAAa;QACbD,SAAS;QACTH,OAAO;QACPuC,QAAQ,CAAC;QACTC,aAAa;IACf;IACAnC,iCAAiB,CAACiC,QAAQ,CAAC;QACzBhC,SAAS;QACTmC,gBAAgB;QAChBC,cAAc;QACdC,qBAAqB;QACrBC,WAAW;QACX1B,QAAQ,EAAE;QACV2B,uBAAuB,EAAE;QACzB7B,aAAa;QACbC,YAAY;QACZ6B,iBAAiB;IACnB;IAEA,cAAc;IACdhB,KAAKiB,aAAa;IAElB,qBAAqB;IACrBC,aAAaC,KAAK;IAClBC,eAAeD,KAAK;AACtB;AAGO,MAAM5H,mBAAmB;IAC9ByG,KAAKqB,IAAI,CAAC,iBAAiB,IAAO,CAAA;YAChCC,QAAQ;gBACNC,KAAK,CAAC,EAAE1G,QAAQ,EAAE,GAAG2G,OAAY,iBAAK,qBAACD;wBAAK,GAAGC,KAAK;kCAAG3G;;gBACvD4G,QAAQ,CAAC,EAAE5G,QAAQ,EAAE,GAAG2G,OAAY,iBAAK,qBAACC;wBAAQ,GAAGD,KAAK;kCAAG3G;;gBAC7D6G,MAAM,CAAC,EAAE7G,QAAQ,EAAE,GAAG2G,OAAY,iBAAK,qBAACE;wBAAM,GAAGF,KAAK;kCAAG3G;;gBACzD8G,SAAS,CAAC,EAAE9G,QAAQ,EAAE,GAAG2G,OAAY,iBAAK,qBAACG;wBAAS,GAAGH,KAAK;kCAAG3G;;YACjE;YACA+G,iBAAiB,CAAC,EAAE/G,QAAQ,EAAO,GAAKA;YACxCgH,cAAc,IAAO,CAAA;oBACnBC,OAAO9B,KAAKC,EAAE;oBACd8B,MAAM/B,KAAKC,EAAE;oBACb+B,KAAKhC,KAAKC,EAAE;gBACd,CAAA;YACAgC,gBAAgB,IAAO,CAAA;oBACrBC,KAAKlC,KAAKC,EAAE;oBACZ+B,KAAKhC,KAAKC,EAAE;gBACd,CAAA;QACF,CAAA;AACF;AAEO,MAAMzG,2BAA2B,CAAC2I;IACvCC,OAAOC,cAAc,CAACC,QAAQ,cAAc;QAC1CC,UAAU;QACVJ,OAAOnC,KAAKC,EAAE,GAAGuC,kBAAkB,CAACC,CAAAA,QAAU,CAAA;gBAC5CC,SAASD,UAAU,qCAAqCN,QAAQ;gBAChEQ,OAAOF;gBACPG,UAAU;gBACVC,aAAa7C,KAAKC,EAAE;gBACpB6C,gBAAgB9C,KAAKC,EAAE;gBACvB8C,kBAAkB/C,KAAKC,EAAE;gBACzB+C,qBAAqBhD,KAAKC,EAAE;gBAC5BgD,eAAejD,KAAKC,EAAE;YACxB,CAAA;IACF;AACF;AAGO,MAAMtG,aAAa,OAAOuJ;IAC/B,MAAMC,UAAU,MAAMC,IAAAA,YAAG,EAACF;IAC1B9I,OAAO+I,SAAS7I,kBAAkB;AACpC;AAEO,MAAM7B,sBAAsB,CAAC4K,SAAsBC;IACxDlB,OAAOmB,OAAO,CAACD,oBAAoBE,OAAO,CAAC,CAAC,CAACC,MAAMtB,MAAM;QACvD/H,OAAOiJ,SAASK,eAAe,CAACD,MAAMtB;IACxC;AACF;AAGO,MAAMhJ,aAAa,IAAMa,kBAAS,CAAC2J,KAAK;AAExC,MAAM5J,gBAAgB,OAAO6J,OAAoBzD;IACtD,MAAMxC,OAAOxE;IACb,MAAMwE,KAAKwD,KAAK,CAACyC;IACjB,MAAMjG,KAAKd,IAAI,CAAC+G,OAAOzD;AACzB;AAEO,MAAMxH,eAAe,OAAO0K;IACjC,MAAM1F,OAAOxE;IACb,MAAMwE,KAAKkG,KAAK,CAACR;AACnB;AAGO,MAAMpJ,4BAA4B,OAAOoJ;IAC9C,MAAMS,IAAAA,eAAO,EAAC;QACZ1J,OAAOiJ,SAASU,GAAG,CAACC,iBAAiB;IACvC;AACF;AAEO,MAAM9J,2BAA2B;IACtC,MAAM4J,IAAAA,eAAO,EAAC;QACZ1J,OAAO6J,cAAM,CAACC,aAAa,CAAC,YAAYH,GAAG,CAACC,iBAAiB;IAC/D,GAAG;QAAEG,SAAS;IAAK;AACrB;AAEA,kBAAkB;AAClB/J,OAAOC,MAAM,CAAC;IACZ2J,mBAAkBI,QAAQ;QACxB,MAAMC,OAAOD,aAAa,QAAQA,aAAa1E;QAC/C,OAAO;YACL3C,SAAS,IAAM,CAAC,iBAAiB,EAAEsH,OAAO,SAAS,GAAG,qBAAqB,CAAC;YAC5EA;QACF;IACF;AACF"}