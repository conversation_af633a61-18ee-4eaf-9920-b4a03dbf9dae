{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\animated-button.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion, HTMLMotionProps } from 'framer-motion'\nimport { Button, ButtonProps } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\nimport { buttonVariants, getAnimationVariants } from '@/lib/animations'\n\ninterface AnimatedButtonProps extends Omit<ButtonProps, 'asChild'> {\n  children: React.ReactNode\n  className?: string\n  disabled?: boolean\n  loading?: boolean\n  ripple?: boolean\n  glow?: boolean\n  magnetic?: boolean\n}\n\nexport function AnimatedButton({ \n  children, \n  className, \n  disabled = false, \n  loading = false,\n  ripple = false,\n  glow = false,\n  magnetic = false,\n  onClick,\n  ...props \n}: AnimatedButtonProps) {\n  const [isClicked, setIsClicked] = React.useState(false)\n  const [ripplePosition, setRipplePosition] = React.useState({ x: 0, y: 0 })\n\n  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {\n    if (disabled || loading) return\n\n    if (ripple) {\n      const rect = e.currentTarget.getBoundingClientRect()\n      setRipplePosition({\n        x: e.clientX - rect.left,\n        y: e.clientY - rect.top,\n      })\n      setIsClicked(true)\n      setTimeout(() => setIsClicked(false), 600)\n    }\n\n    onClick?.(e)\n  }\n\n  const buttonVariant = {\n    rest: { scale: 1 },\n    hover: { \n      scale: disabled ? 1 : 1.05,\n      boxShadow: glow ? '0 0 20px rgba(59, 130, 246, 0.5)' : undefined,\n    },\n    tap: { scale: disabled ? 1 : 0.95 },\n  }\n\n  return (\n    <motion.div\n      variants={getAnimationVariants(buttonVariant)}\n      initial=\"rest\"\n      whileHover={!disabled ? \"hover\" : undefined}\n      whileTap={!disabled ? \"tap\" : undefined}\n      className=\"relative inline-block\"\n    >\n      <Button\n        className={cn(\n          'relative overflow-hidden',\n          loading && 'cursor-wait',\n          className\n        )}\n        disabled={disabled || loading}\n        onClick={handleClick}\n        {...props}\n      >\n        {/* Loading spinner */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"absolute inset-0 flex items-center justify-center bg-current/10\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}\n              className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full\"\n            />\n          </motion.div>\n        )}\n\n        {/* Ripple effect */}\n        {ripple && isClicked && (\n          <motion.div\n            initial={{ scale: 0, opacity: 0.5 }}\n            animate={{ scale: 4, opacity: 0 }}\n            transition={{ duration: 0.6, ease: 'easeOut' }}\n            className=\"absolute bg-white rounded-full pointer-events-none\"\n            style={{\n              left: ripplePosition.x - 10,\n              top: ripplePosition.y - 10,\n              width: 20,\n              height: 20,\n            }}\n          />\n        )}\n\n        {/* Button content */}\n        <motion.div\n          animate={loading ? { opacity: 0.5 } : { opacity: 1 }}\n          className=\"flex items-center space-x-2\"\n        >\n          {children}\n        </motion.div>\n      </Button>\n    </motion.div>\n  )\n}\n\n// Floating Action Button\ninterface FloatingActionButtonProps extends AnimatedButtonProps {\n  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'\n}\n\nexport function FloatingActionButton({ \n  children, \n  className, \n  position = 'bottom-right',\n  ...props \n}: FloatingActionButtonProps) {\n  const positionClasses = {\n    'bottom-right': 'fixed bottom-6 right-6',\n    'bottom-left': 'fixed bottom-6 left-6',\n    'top-right': 'fixed top-6 right-6',\n    'top-left': 'fixed top-6 left-6',\n  }\n\n  return (\n    <motion.div\n      initial={{ scale: 0, opacity: 0 }}\n      animate={{ scale: 1, opacity: 1 }}\n      exit={{ scale: 0, opacity: 0 }}\n      whileHover={{ scale: 1.1 }}\n      whileTap={{ scale: 0.9 }}\n      className={cn(positionClasses[position], 'z-50')}\n    >\n      <AnimatedButton\n        className={cn(\n          'rounded-full w-14 h-14 shadow-lg',\n          className\n        )}\n        glow\n        ripple\n        {...props}\n      >\n        {children}\n      </AnimatedButton>\n    </motion.div>\n  )\n}\n\n// Icon Button with animations\ninterface AnimatedIconButtonProps extends AnimatedButtonProps {\n  icon: React.ReactNode\n  label?: string\n  showLabel?: boolean\n}\n\nexport function AnimatedIconButton({ \n  icon, \n  label, \n  showLabel = false, \n  className, \n  ...props \n}: AnimatedIconButtonProps) {\n  return (\n    <AnimatedButton\n      className={cn(\n        'relative group',\n        !showLabel && 'w-10 h-10 p-0',\n        className\n      )}\n      {...props}\n    >\n      <motion.div\n        whileHover={{ rotate: 15 }}\n        transition={{ duration: 0.2 }}\n      >\n        {icon}\n      </motion.div>\n      \n      {label && showLabel && (\n        <span className=\"ml-2\">{label}</span>\n      )}\n      \n      {label && !showLabel && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8, y: 10 }}\n          whileHover={{ opacity: 1, scale: 1, y: 0 }}\n          className=\"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap pointer-events-none\"\n        >\n          {label}\n          <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900\" />\n        </motion.div>\n      )}\n    </AnimatedButton>\n  )\n}\n\n// Button with progress indicator\ninterface ProgressButtonProps extends AnimatedButtonProps {\n  progress?: number\n  showProgress?: boolean\n}\n\nexport function ProgressButton({ \n  progress = 0, \n  showProgress = false, \n  children, \n  className, \n  ...props \n}: ProgressButtonProps) {\n  return (\n    <AnimatedButton\n      className={cn('relative overflow-hidden', className)}\n      {...props}\n    >\n      {showProgress && (\n        <motion.div\n          initial={{ width: 0 }}\n          animate={{ width: `${progress}%` }}\n          transition={{ duration: 0.3 }}\n          className=\"absolute left-0 top-0 h-full bg-white/20 pointer-events-none\"\n        />\n      )}\n      {children}\n    </AnimatedButton>\n  )\n}\n"], "names": ["AnimatedButton", "AnimatedIconButton", "FloatingActionButton", "ProgressButton", "children", "className", "disabled", "loading", "ripple", "glow", "magnetic", "onClick", "props", "isClicked", "setIsClicked", "React", "useState", "ripplePosition", "setRipplePosition", "x", "y", "handleClick", "e", "rect", "currentTarget", "getBoundingClientRect", "clientX", "left", "clientY", "top", "setTimeout", "buttonVariant", "rest", "scale", "hover", "boxShadow", "undefined", "tap", "motion", "div", "variants", "getAnimationVariants", "initial", "whileHover", "whileTap", "<PERSON><PERSON>", "cn", "opacity", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "style", "width", "height", "position", "positionClasses", "exit", "icon", "label", "showLabel", "span", "progress", "showProgress"], "mappings": "AAAA;;;;;;;;;;;;IAkBgBA,cAAc;eAAdA;;IAqJAC,kBAAkB;eAAlBA;;IA5CAC,oBAAoB;eAApBA;;IA2FAC,cAAc;eAAdA;;;;8DApNE;8BACsB;wBACJ;uBACjB;4BACkC;;;;;;AAY9C,SAASH,eAAe,EAC7BI,QAAQ,EACRC,SAAS,EACTC,WAAW,KAAK,EAChBC,UAAU,KAAK,EACfC,SAAS,KAAK,EACdC,OAAO,KAAK,EACZC,WAAW,KAAK,EAChBC,OAAO,EACP,GAAGC,OACiB;IACpB,MAAM,CAACC,WAAWC,aAAa,GAAGC,cAAK,CAACC,QAAQ,CAAC;IACjD,MAAM,CAACC,gBAAgBC,kBAAkB,GAAGH,cAAK,CAACC,QAAQ,CAAC;QAAEG,GAAG;QAAGC,GAAG;IAAE;IAExE,MAAMC,cAAc,CAACC;QACnB,IAAIhB,YAAYC,SAAS;QAEzB,IAAIC,QAAQ;YACV,MAAMe,OAAOD,EAAEE,aAAa,CAACC,qBAAqB;YAClDP,kBAAkB;gBAChBC,GAAGG,EAAEI,OAAO,GAAGH,KAAKI,IAAI;gBACxBP,GAAGE,EAAEM,OAAO,GAAGL,KAAKM,GAAG;YACzB;YACAf,aAAa;YACbgB,WAAW,IAAMhB,aAAa,QAAQ;QACxC;QAEAH,UAAUW;IACZ;IAEA,MAAMS,gBAAgB;QACpBC,MAAM;YAAEC,OAAO;QAAE;QACjBC,OAAO;YACLD,OAAO3B,WAAW,IAAI;YACtB6B,WAAW1B,OAAO,qCAAqC2B;QACzD;QACAC,KAAK;YAAEJ,OAAO3B,WAAW,IAAI;QAAK;IACpC;IAEA,qBACE,qBAACgC,oBAAM,CAACC,GAAG;QACTC,UAAUC,IAAAA,gCAAoB,EAACV;QAC/BW,SAAQ;QACRC,YAAY,CAACrC,WAAW,UAAU8B;QAClCQ,UAAU,CAACtC,WAAW,QAAQ8B;QAC9B/B,WAAU;kBAEV,cAAA,sBAACwC,cAAM;YACLxC,WAAWyC,IAAAA,SAAE,EACX,4BACAvC,WAAW,eACXF;YAEFC,UAAUA,YAAYC;YACtBI,SAASU;YACR,GAAGT,KAAK;;gBAGRL,yBACC,qBAAC+B,oBAAM,CAACC,GAAG;oBACTG,SAAS;wBAAEK,SAAS;oBAAE;oBACtBC,SAAS;wBAAED,SAAS;oBAAE;oBACtB1C,WAAU;8BAEV,cAAA,qBAACiC,oBAAM,CAACC,GAAG;wBACTS,SAAS;4BAAEC,QAAQ;wBAAI;wBACvBC,YAAY;4BAAEC,UAAU;4BAAGC,QAAQC;4BAAUC,MAAM;wBAAS;wBAC5DjD,WAAU;;;gBAMfG,UAAUK,2BACT,qBAACyB,oBAAM,CAACC,GAAG;oBACTG,SAAS;wBAAET,OAAO;wBAAGc,SAAS;oBAAI;oBAClCC,SAAS;wBAAEf,OAAO;wBAAGc,SAAS;oBAAE;oBAChCG,YAAY;wBAAEC,UAAU;wBAAKG,MAAM;oBAAU;oBAC7CjD,WAAU;oBACVkD,OAAO;wBACL5B,MAAMV,eAAeE,CAAC,GAAG;wBACzBU,KAAKZ,eAAeG,CAAC,GAAG;wBACxBoC,OAAO;wBACPC,QAAQ;oBACV;;8BAKJ,qBAACnB,oBAAM,CAACC,GAAG;oBACTS,SAASzC,UAAU;wBAAEwC,SAAS;oBAAI,IAAI;wBAAEA,SAAS;oBAAE;oBACnD1C,WAAU;8BAETD;;;;;AAKX;AAOO,SAASF,qBAAqB,EACnCE,QAAQ,EACRC,SAAS,EACTqD,WAAW,cAAc,EACzB,GAAG9C,OACuB;IAC1B,MAAM+C,kBAAkB;QACtB,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,YAAY;IACd;IAEA,qBACE,qBAACrB,oBAAM,CAACC,GAAG;QACTG,SAAS;YAAET,OAAO;YAAGc,SAAS;QAAE;QAChCC,SAAS;YAAEf,OAAO;YAAGc,SAAS;QAAE;QAChCa,MAAM;YAAE3B,OAAO;YAAGc,SAAS;QAAE;QAC7BJ,YAAY;YAAEV,OAAO;QAAI;QACzBW,UAAU;YAAEX,OAAO;QAAI;QACvB5B,WAAWyC,IAAAA,SAAE,EAACa,eAAe,CAACD,SAAS,EAAE;kBAEzC,cAAA,qBAAC1D;YACCK,WAAWyC,IAAAA,SAAE,EACX,oCACAzC;YAEFI,IAAI;YACJD,MAAM;YACL,GAAGI,KAAK;sBAERR;;;AAIT;AASO,SAASH,mBAAmB,EACjC4D,IAAI,EACJC,KAAK,EACLC,YAAY,KAAK,EACjB1D,SAAS,EACT,GAAGO,OACqB;IACxB,qBACE,sBAACZ;QACCK,WAAWyC,IAAAA,SAAE,EACX,kBACA,CAACiB,aAAa,iBACd1D;QAED,GAAGO,KAAK;;0BAET,qBAAC0B,oBAAM,CAACC,GAAG;gBACTI,YAAY;oBAAEM,QAAQ;gBAAG;gBACzBC,YAAY;oBAAEC,UAAU;gBAAI;0BAE3BU;;YAGFC,SAASC,2BACR,qBAACC;gBAAK3D,WAAU;0BAAQyD;;YAGzBA,SAAS,CAACC,2BACT,sBAACzB,oBAAM,CAACC,GAAG;gBACTG,SAAS;oBAAEK,SAAS;oBAAGd,OAAO;oBAAKb,GAAG;gBAAG;gBACzCuB,YAAY;oBAAEI,SAAS;oBAAGd,OAAO;oBAAGb,GAAG;gBAAE;gBACzCf,WAAU;;oBAETyD;kCACD,qBAACvB;wBAAIlC,WAAU;;;;;;AAKzB;AAQO,SAASF,eAAe,EAC7B8D,WAAW,CAAC,EACZC,eAAe,KAAK,EACpB9D,QAAQ,EACRC,SAAS,EACT,GAAGO,OACiB;IACpB,qBACE,sBAACZ;QACCK,WAAWyC,IAAAA,SAAE,EAAC,4BAA4BzC;QACzC,GAAGO,KAAK;;YAERsD,8BACC,qBAAC5B,oBAAM,CAACC,GAAG;gBACTG,SAAS;oBAAEc,OAAO;gBAAE;gBACpBR,SAAS;oBAAEQ,OAAO,CAAC,EAAES,SAAS,CAAC,CAAC;gBAAC;gBACjCf,YAAY;oBAAEC,UAAU;gBAAI;gBAC5B9C,WAAU;;YAGbD;;;AAGP"}