{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\layout\\__tests__\\page-transition.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen } from '@/lib/test-utils'\nimport { PageTransition } from '../page-transition'\nimport { usePathname } from 'next/navigation'\nimport { mockFramerMotion, mockPrefersReducedMotion } from '@/lib/test-utils'\n\n// Mock Next.js navigation\njest.mock('next/navigation', () => ({\n  usePathname: jest.fn(),\n}))\n\n// Mock animations\njest.mock('@/lib/animations', () => ({\n  pageVariants: {\n    initial: { opacity: 0, y: 20, scale: 0.98 },\n    animate: { opacity: 1, y: 0, scale: 1 },\n    exit: { opacity: 0, y: -20, scale: 0.98 },\n  },\n  getAnimationVariants: jest.fn((variants) => variants),\n}))\n\n// Mock framer-motion\nmockFramerMotion()\n\ndescribe('PageTransition Component', () => {\n  beforeEach(() => {\n    jest.clearAllMocks()\n    ;(usePathname as jest.Mock).mockReturnValue('/dashboard')\n  })\n\n  it('renders children correctly', () => {\n    render(\n      <PageTransition>\n        <div data-testid=\"page-content\">Page Content</div>\n      </PageTransition>\n    )\n\n    expect(screen.getByTestId('page-content')).toBeInTheDocument()\n    expect(screen.getByText('Page Content')).toBeInTheDocument()\n  })\n\n  it('wraps content in motion div', () => {\n    render(\n      <PageTransition>\n        <div>Content</div>\n      </PageTransition>\n    )\n\n    // The motion.div should be present (mocked as regular div)\n    const wrapper = screen.getByText('Content').parentElement\n    expect(wrapper).toHaveClass('w-full', 'h-full')\n  })\n\n  it('uses pathname as key for animations', () => {\n    ;(usePathname as jest.Mock).mockReturnValue('/dashboard')\n    \n    const { rerender } = render(\n      <PageTransition>\n        <div>Dashboard Content</div>\n      </PageTransition>\n    )\n\n    expect(screen.getByText('Dashboard Content')).toBeInTheDocument()\n\n    // Change pathname\n    ;(usePathname as jest.Mock).mockReturnValue('/uavs')\n    \n    rerender(\n      <PageTransition>\n        <div>UAV Content</div>\n      </PageTransition>\n    )\n\n    expect(screen.getByText('UAV Content')).toBeInTheDocument()\n  })\n\n  it('applies correct animation variants', () => {\n    render(\n      <PageTransition>\n        <div>Animated Content</div>\n      </PageTransition>\n    )\n\n    // The motion div should have the correct props (tested through mocked framer-motion)\n    const content = screen.getByText('Animated Content')\n    expect(content).toBeInTheDocument()\n  })\n\n  it('handles multiple children', () => {\n    render(\n      <PageTransition>\n        <div>First Child</div>\n        <div>Second Child</div>\n        <span>Third Child</span>\n      </PageTransition>\n    )\n\n    expect(screen.getByText('First Child')).toBeInTheDocument()\n    expect(screen.getByText('Second Child')).toBeInTheDocument()\n    expect(screen.getByText('Third Child')).toBeInTheDocument()\n  })\n\n  it('handles complex nested content', () => {\n    render(\n      <PageTransition>\n        <div>\n          <header>Page Header</header>\n          <main>\n            <section>\n              <h1>Page Title</h1>\n              <p>Page description</p>\n            </section>\n          </main>\n          <footer>Page Footer</footer>\n        </div>\n      </PageTransition>\n    )\n\n    expect(screen.getByText('Page Header')).toBeInTheDocument()\n    expect(screen.getByText('Page Title')).toBeInTheDocument()\n    expect(screen.getByText('Page description')).toBeInTheDocument()\n    expect(screen.getByText('Page Footer')).toBeInTheDocument()\n  })\n\n  it('respects prefers-reduced-motion', () => {\n    mockPrefersReducedMotion(true)\n    \n    render(\n      <PageTransition>\n        <div>Reduced Motion Content</div>\n      </PageTransition>\n    )\n\n    expect(screen.getByText('Reduced Motion Content')).toBeInTheDocument()\n    // Animation should be disabled when prefers-reduced-motion is set\n  })\n\n  it('handles pathname changes correctly', () => {\n    const pathnames = ['/dashboard', '/uavs', '/map', '/hibernate-pod']\n    \n    pathnames.forEach((pathname, index) => {\n      ;(usePathname as jest.Mock).mockReturnValue(pathname)\n      \n      const { unmount } = render(\n        <PageTransition>\n          <div>Content for {pathname}</div>\n        </PageTransition>\n      )\n\n      expect(screen.getByText(`Content for ${pathname}`)).toBeInTheDocument()\n      unmount()\n    })\n  })\n\n  it('maintains full width and height', () => {\n    render(\n      <PageTransition>\n        <div data-testid=\"content\">Content</div>\n      </PageTransition>\n    )\n\n    const wrapper = screen.getByTestId('content').parentElement\n    expect(wrapper).toHaveClass('w-full', 'h-full')\n  })\n\n  it('handles empty children gracefully', () => {\n    render(<PageTransition>{null}</PageTransition>)\n    \n    // Should not throw error and should render the wrapper\n    const wrapper = document.querySelector('.w-full.h-full')\n    expect(wrapper).toBeInTheDocument()\n  })\n\n  it('handles string children', () => {\n    render(\n      <PageTransition>\n        Simple text content\n      </PageTransition>\n    )\n\n    expect(screen.getByText('Simple text content')).toBeInTheDocument()\n  })\n\n  it('handles React fragments', () => {\n    render(\n      <PageTransition>\n        <>\n          <div>Fragment Child 1</div>\n          <div>Fragment Child 2</div>\n        </>\n      </PageTransition>\n    )\n\n    expect(screen.getByText('Fragment Child 1')).toBeInTheDocument()\n    expect(screen.getByText('Fragment Child 2')).toBeInTheDocument()\n  })\n\n  it('preserves component state during transitions', () => {\n    const TestComponent = () => {\n      const [count, setCount] = React.useState(0)\n      return (\n        <div>\n          <span>Count: {count}</span>\n          <button onClick={() => setCount(c => c + 1)}>Increment</button>\n        </div>\n      )\n    }\n\n    render(\n      <PageTransition>\n        <TestComponent />\n      </PageTransition>\n    )\n\n    const button = screen.getByText('Increment')\n    const countDisplay = screen.getByText('Count: 0')\n\n    expect(countDisplay).toBeInTheDocument()\n    \n    // Click button to change state\n    button.click()\n    expect(screen.getByText('Count: 1')).toBeInTheDocument()\n  })\n\n  it('handles conditional rendering', () => {\n    const ConditionalComponent = ({ show }: { show: boolean }) => (\n      <PageTransition>\n        {show ? <div>Visible Content</div> : <div>Hidden Content</div>}\n      </PageTransition>\n    )\n\n    const { rerender } = render(<ConditionalComponent show={true} />)\n    expect(screen.getByText('Visible Content')).toBeInTheDocument()\n\n    rerender(<ConditionalComponent show={false} />)\n    expect(screen.getByText('Hidden Content')).toBeInTheDocument()\n  })\n\n  it('works with different pathname formats', () => {\n    const pathnames = [\n      '/',\n      '/dashboard',\n      '/uavs/123',\n      '/map?filter=active',\n      '/hibernate-pod#section1',\n    ]\n\n    pathnames.forEach(pathname => {\n      ;(usePathname as jest.Mock).mockReturnValue(pathname)\n      \n      const { unmount } = render(\n        <PageTransition>\n          <div>Content for {pathname}</div>\n        </PageTransition>\n      )\n\n      expect(screen.getByText(`Content for ${pathname}`)).toBeInTheDocument()\n      unmount()\n    })\n  })\n\n  it('handles rapid pathname changes', () => {\n    ;(usePathname as jest.Mock).mockReturnValue('/dashboard')\n    \n    const { rerender } = render(\n      <PageTransition>\n        <div>Dashboard</div>\n      </PageTransition>\n    )\n\n    // Rapidly change pathnames\n    ;(usePathname as jest.Mock).mockReturnValue('/uavs')\n    rerender(\n      <PageTransition>\n        <div>UAVs</div>\n      </PageTransition>\n    )\n\n    ;(usePathname as jest.Mock).mockReturnValue('/map')\n    rerender(\n      <PageTransition>\n        <div>Map</div>\n      </PageTransition>\n    )\n\n    expect(screen.getByText('Map')).toBeInTheDocument()\n  })\n\n  it('maintains accessibility during transitions', () => {\n    render(\n      <PageTransition>\n        <div role=\"main\" aria-label=\"Page content\">\n          <h1>Accessible Page</h1>\n          <button>Accessible Button</button>\n        </div>\n      </PageTransition>\n    )\n\n    expect(screen.getByRole('main')).toBeInTheDocument()\n    expect(screen.getByRole('button')).toBeInTheDocument()\n    expect(screen.getByLabelText('Page content')).toBeInTheDocument()\n  })\n})\n"], "names": ["jest", "mock", "usePathname", "fn", "pageVariants", "initial", "opacity", "y", "scale", "animate", "exit", "getAnimationVariants", "variants", "mockFramerMotion", "describe", "beforeEach", "clearAllMocks", "mockReturnValue", "it", "render", "PageTransition", "div", "data-testid", "expect", "screen", "getByTestId", "toBeInTheDocument", "getByText", "wrapper", "parentElement", "toHaveClass", "rerender", "content", "span", "header", "main", "section", "h1", "p", "footer", "mockPrefersReducedMotion", "pathnames", "for<PERSON>ach", "pathname", "index", "unmount", "document", "querySelector", "TestComponent", "count", "setCount", "React", "useState", "button", "onClick", "c", "countDisplay", "click", "ConditionalComponent", "show", "role", "aria-label", "getByRole", "getByLabelText"], "mappings": ";AAMA,0BAA0B;AAC1BA,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC,aAAaF,KAAKG,EAAE;IACtB,CAAA;AAEA,kBAAkB;AAClBH,KAAKC,IAAI,CAAC,oBAAoB,IAAO,CAAA;QACnCG,cAAc;YACZC,SAAS;gBAAEC,SAAS;gBAAGC,GAAG;gBAAIC,OAAO;YAAK;YAC1CC,SAAS;gBAAEH,SAAS;gBAAGC,GAAG;gBAAGC,OAAO;YAAE;YACtCE,MAAM;gBAAEJ,SAAS;gBAAGC,GAAG,CAAC;gBAAIC,OAAO;YAAK;QAC1C;QACAG,sBAAsBX,KAAKG,EAAE,CAAC,CAACS,WAAaA;IAC9C,CAAA;;;;;8DAnBkB;2BACa;gCACA;4BACH;;;;;;AAkB5B,qBAAqB;AACrBC,IAAAA,2BAAgB;AAEhBC,SAAS,4BAA4B;IACnCC,WAAW;QACTf,KAAKgB,aAAa;QAChBd,uBAAW,CAAee,eAAe,CAAC;IAC9C;IAEAC,GAAG,8BAA8B;QAC/BC,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;sBACb,cAAA,qBAACC;gBAAIC,eAAY;0BAAe;;;QAIpCC,OAAOC,iBAAM,CAACC,WAAW,CAAC,iBAAiBC,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACG,SAAS,CAAC,iBAAiBD,iBAAiB;IAC5D;IAEAR,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;sBACb,cAAA,qBAACC;0BAAI;;;QAIT,2DAA2D;QAC3D,MAAMO,UAAUJ,iBAAM,CAACG,SAAS,CAAC,WAAWE,aAAa;QACzDN,OAAOK,SAASE,WAAW,CAAC,UAAU;IACxC;IAEAZ,GAAG,uCAAuC;QACtChB,uBAAW,CAAee,eAAe,CAAC;QAE5C,MAAM,EAAEc,QAAQ,EAAE,GAAGZ,IAAAA,iBAAM,gBACzB,qBAACC,8BAAc;sBACb,cAAA,qBAACC;0BAAI;;;QAITE,OAAOC,iBAAM,CAACG,SAAS,CAAC,sBAAsBD,iBAAiB;QAG7DxB,uBAAW,CAAee,eAAe,CAAC;QAE5Cc,uBACE,qBAACX,8BAAc;sBACb,cAAA,qBAACC;0BAAI;;;QAITE,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;IAC3D;IAEAR,GAAG,sCAAsC;QACvCC,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;sBACb,cAAA,qBAACC;0BAAI;;;QAIT,qFAAqF;QACrF,MAAMW,UAAUR,iBAAM,CAACG,SAAS,CAAC;QACjCJ,OAAOS,SAASN,iBAAiB;IACnC;IAEAR,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBACJ,sBAACC,8BAAc;;8BACb,qBAACC;8BAAI;;8BACL,qBAACA;8BAAI;;8BACL,qBAACY;8BAAK;;;;QAIVV,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;QACzDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,iBAAiBD,iBAAiB;QAC1DH,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;IAC3D;IAEAR,GAAG,kCAAkC;QACnCC,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;sBACb,cAAA,sBAACC;;kCACC,qBAACa;kCAAO;;kCACR,qBAACC;kCACC,cAAA,sBAACC;;8CACC,qBAACC;8CAAG;;8CACJ,qBAACC;8CAAE;;;;;kCAGP,qBAACC;kCAAO;;;;;QAKdhB,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;QACzDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,eAAeD,iBAAiB;QACxDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,qBAAqBD,iBAAiB;QAC9DH,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;IAC3D;IAEAR,GAAG,mCAAmC;QACpCsB,IAAAA,mCAAwB,EAAC;QAEzBrB,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;sBACb,cAAA,qBAACC;0BAAI;;;QAITE,OAAOC,iBAAM,CAACG,SAAS,CAAC,2BAA2BD,iBAAiB;IACpE,kEAAkE;IACpE;IAEAR,GAAG,sCAAsC;QACvC,MAAMuB,YAAY;YAAC;YAAc;YAAS;YAAQ;SAAiB;QAEnEA,UAAUC,OAAO,CAAC,CAACC,UAAUC;YACzB1C,uBAAW,CAAee,eAAe,CAAC0B;YAE5C,MAAM,EAAEE,OAAO,EAAE,GAAG1B,IAAAA,iBAAM,gBACxB,qBAACC,8BAAc;0BACb,cAAA,sBAACC;;wBAAI;wBAAasB;;;;YAItBpB,OAAOC,iBAAM,CAACG,SAAS,CAAC,CAAC,YAAY,EAAEgB,SAAS,CAAC,GAAGjB,iBAAiB;YACrEmB;QACF;IACF;IAEA3B,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;sBACb,cAAA,qBAACC;gBAAIC,eAAY;0BAAU;;;QAI/B,MAAMM,UAAUJ,iBAAM,CAACC,WAAW,CAAC,WAAWI,aAAa;QAC3DN,OAAOK,SAASE,WAAW,CAAC,UAAU;IACxC;IAEAZ,GAAG,qCAAqC;QACtCC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;sBAAE;;QAExB,uDAAuD;QACvD,MAAMQ,UAAUkB,SAASC,aAAa,CAAC;QACvCxB,OAAOK,SAASF,iBAAiB;IACnC;IAEAR,GAAG,2BAA2B;QAC5BC,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;sBAAC;;QAKlBG,OAAOC,iBAAM,CAACG,SAAS,CAAC,wBAAwBD,iBAAiB;IACnE;IAEAR,GAAG,2BAA2B;QAC5BC,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;sBACb,cAAA;;kCACE,qBAACC;kCAAI;;kCACL,qBAACA;kCAAI;;;;;QAKXE,OAAOC,iBAAM,CAACG,SAAS,CAAC,qBAAqBD,iBAAiB;QAC9DH,OAAOC,iBAAM,CAACG,SAAS,CAAC,qBAAqBD,iBAAiB;IAChE;IAEAR,GAAG,gDAAgD;QACjD,MAAM8B,gBAAgB;YACpB,MAAM,CAACC,OAAOC,SAAS,GAAGC,cAAK,CAACC,QAAQ,CAAC;YACzC,qBACE,sBAAC/B;;kCACC,sBAACY;;4BAAK;4BAAQgB;;;kCACd,qBAACI;wBAAOC,SAAS,IAAMJ,SAASK,CAAAA,IAAKA,IAAI;kCAAI;;;;QAGnD;QAEApC,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;sBACb,cAAA,qBAAC4B;;QAIL,MAAMK,SAAS7B,iBAAM,CAACG,SAAS,CAAC;QAChC,MAAM6B,eAAehC,iBAAM,CAACG,SAAS,CAAC;QAEtCJ,OAAOiC,cAAc9B,iBAAiB;QAEtC,+BAA+B;QAC/B2B,OAAOI,KAAK;QACZlC,OAAOC,iBAAM,CAACG,SAAS,CAAC,aAAaD,iBAAiB;IACxD;IAEAR,GAAG,iCAAiC;QAClC,MAAMwC,uBAAuB,CAAC,EAAEC,IAAI,EAAqB,iBACvD,qBAACvC,8BAAc;0BACZuC,qBAAO,qBAACtC;8BAAI;mCAAwB,qBAACA;8BAAI;;;QAI9C,MAAM,EAAEU,QAAQ,EAAE,GAAGZ,IAAAA,iBAAM,gBAAC,qBAACuC;YAAqBC,MAAM;;QACxDpC,OAAOC,iBAAM,CAACG,SAAS,CAAC,oBAAoBD,iBAAiB;QAE7DK,uBAAS,qBAAC2B;YAAqBC,MAAM;;QACrCpC,OAAOC,iBAAM,CAACG,SAAS,CAAC,mBAAmBD,iBAAiB;IAC9D;IAEAR,GAAG,yCAAyC;QAC1C,MAAMuB,YAAY;YAChB;YACA;YACA;YACA;YACA;SACD;QAEDA,UAAUC,OAAO,CAACC,CAAAA;YACdzC,uBAAW,CAAee,eAAe,CAAC0B;YAE5C,MAAM,EAAEE,OAAO,EAAE,GAAG1B,IAAAA,iBAAM,gBACxB,qBAACC,8BAAc;0BACb,cAAA,sBAACC;;wBAAI;wBAAasB;;;;YAItBpB,OAAOC,iBAAM,CAACG,SAAS,CAAC,CAAC,YAAY,EAAEgB,SAAS,CAAC,GAAGjB,iBAAiB;YACrEmB;QACF;IACF;IAEA3B,GAAG,kCAAkC;QACjChB,uBAAW,CAAee,eAAe,CAAC;QAE5C,MAAM,EAAEc,QAAQ,EAAE,GAAGZ,IAAAA,iBAAM,gBACzB,qBAACC,8BAAc;sBACb,cAAA,qBAACC;0BAAI;;;QAKPnB,uBAAW,CAAee,eAAe,CAAC;QAC5Cc,uBACE,qBAACX,8BAAc;sBACb,cAAA,qBAACC;0BAAI;;;QAIPnB,uBAAW,CAAee,eAAe,CAAC;QAC5Cc,uBACE,qBAACX,8BAAc;sBACb,cAAA,qBAACC;0BAAI;;;QAITE,OAAOC,iBAAM,CAACG,SAAS,CAAC,QAAQD,iBAAiB;IACnD;IAEAR,GAAG,8CAA8C;QAC/CC,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;sBACb,cAAA,sBAACC;gBAAIuC,MAAK;gBAAOC,cAAW;;kCAC1B,qBAACxB;kCAAG;;kCACJ,qBAACgB;kCAAO;;;;;QAKd9B,OAAOC,iBAAM,CAACsC,SAAS,CAAC,SAASpC,iBAAiB;QAClDH,OAAOC,iBAAM,CAACsC,SAAS,CAAC,WAAWpC,iBAAiB;QACpDH,OAAOC,iBAAM,CAACuC,cAAc,CAAC,iBAAiBrC,iBAAiB;IACjE;AACF"}