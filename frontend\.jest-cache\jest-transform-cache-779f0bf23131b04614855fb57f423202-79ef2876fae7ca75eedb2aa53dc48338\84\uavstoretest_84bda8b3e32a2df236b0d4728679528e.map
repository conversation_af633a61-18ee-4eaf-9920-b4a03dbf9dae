{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\stores\\__tests__\\uav-store.test.ts"], "sourcesContent": ["import { renderHook, act } from '@testing-library/react'\nimport { useUAVStore } from '../uav-store'\nimport { createMockUAV, mockApiResponse, mockApiError } from '@/lib/test-utils'\nimport { uavApi } from '@/api/uav-api'\n\n// Mock the API\njest.mock('@/api/uav-api')\nconst mockedUavApi = uavApi as jest.Mocked<typeof uavApi>\n\ndescribe('UAV Store', () => {\n  beforeEach(() => {\n    // Reset store state\n    useUAVStore.setState({\n      uavs: [],\n      selectedUAV: null,\n      loading: false,\n      error: null,\n      filter: {},\n      searchQuery: '',\n      regions: [],\n      systemStats: null,\n      hibernatePodStatus: null,\n    })\n    \n    // Clear all mocks\n    jest.clearAllMocks()\n  })\n\n  describe('fetchUAVs', () => {\n    it('should fetch UAVs successfully', async () => {\n      const mockUAVs = [createMockUAV(), createMockUAV({ id: 2, rfidTag: 'UAV-002' })]\n      mockedUavApi.getUAVs.mockResolvedValue(mockUAVs)\n\n      const { result } = renderHook(() => useUAVStore())\n\n      await act(async () => {\n        await result.current.fetchUAVs()\n      })\n\n      expect(result.current.uavs).toEqual(mockUAVs)\n      expect(result.current.loading).toBe(false)\n      expect(result.current.error).toBeNull()\n      expect(mockedUavApi.getUAVs).toHaveBeenCalledTimes(1)\n    })\n\n    it('should handle fetch UAVs error', async () => {\n      const errorMessage = 'Failed to fetch UAVs'\n      mockedUavApi.getUAVs.mockRejectedValue(new Error(errorMessage))\n\n      const { result } = renderHook(() => useUAVStore())\n\n      await act(async () => {\n        await result.current.fetchUAVs()\n      })\n\n      expect(result.current.uavs).toEqual([])\n      expect(result.current.loading).toBe(false)\n      expect(result.current.error).toBe(errorMessage)\n    })\n\n    it('should set loading state during fetch', async () => {\n      let resolvePromise: (value: any) => void\n      const promise = new Promise(resolve => {\n        resolvePromise = resolve\n      })\n      mockedUavApi.getUAVs.mockReturnValue(promise as any)\n\n      const { result } = renderHook(() => useUAVStore())\n\n      act(() => {\n        result.current.fetchUAVs()\n      })\n\n      expect(result.current.loading).toBe(true)\n\n      await act(async () => {\n        resolvePromise([])\n      })\n\n      expect(result.current.loading).toBe(false)\n    })\n  })\n\n  describe('createUAV', () => {\n    it('should create UAV successfully', async () => {\n      const newUAV = createMockUAV()\n      const mockResponse = mockApiResponse(newUAV)\n      mockedUavApi.createUAV.mockResolvedValue(mockResponse)\n\n      const { result } = renderHook(() => useUAVStore())\n\n      let success: boolean\n      await act(async () => {\n        success = await result.current.createUAV({\n          rfidTag: newUAV.rfidTag,\n          ownerName: newUAV.ownerName,\n          model: newUAV.model,\n          status: newUAV.status,\n        })\n      })\n\n      expect(success!).toBe(true)\n      expect(result.current.uavs).toContain(newUAV)\n      expect(result.current.loading).toBe(false)\n      expect(result.current.error).toBeNull()\n    })\n\n    it('should handle create UAV error', async () => {\n      const errorMessage = 'Failed to create UAV'\n      const mockResponse = { success: false, message: errorMessage }\n      mockedUavApi.createUAV.mockResolvedValue(mockResponse)\n\n      const { result } = renderHook(() => useUAVStore())\n\n      let success: boolean\n      await act(async () => {\n        success = await result.current.createUAV({\n          rfidTag: 'UAV-001',\n          ownerName: 'Test Owner',\n          model: 'Test Model',\n          status: 'AUTHORIZED',\n        })\n      })\n\n      expect(success!).toBe(false)\n      expect(result.current.error).toBe(errorMessage)\n    })\n  })\n\n  describe('updateUAV', () => {\n    it('should update UAV successfully', async () => {\n      const existingUAV = createMockUAV()\n      const updatedUAV = { ...existingUAV, ownerName: 'Updated Owner' }\n      const mockResponse = mockApiResponse(updatedUAV)\n      \n      mockedUavApi.updateUAV.mockResolvedValue(mockResponse)\n\n      const { result } = renderHook(() => useUAVStore())\n\n      // Set initial state\n      act(() => {\n        useUAVStore.setState({ uavs: [existingUAV] })\n      })\n\n      let success: boolean\n      await act(async () => {\n        success = await result.current.updateUAV(existingUAV.id, { ownerName: 'Updated Owner' })\n      })\n\n      expect(success!).toBe(true)\n      expect(result.current.uavs[0].ownerName).toBe('Updated Owner')\n    })\n  })\n\n  describe('deleteUAV', () => {\n    it('should delete UAV successfully', async () => {\n      const uavToDelete = createMockUAV()\n      const mockResponse = mockApiResponse(undefined)\n      \n      mockedUavApi.deleteUAV.mockResolvedValue(mockResponse)\n\n      const { result } = renderHook(() => useUAVStore())\n\n      // Set initial state\n      act(() => {\n        useUAVStore.setState({ uavs: [uavToDelete] })\n      })\n\n      let success: boolean\n      await act(async () => {\n        success = await result.current.deleteUAV(uavToDelete.id)\n      })\n\n      expect(success!).toBe(true)\n      expect(result.current.uavs).toHaveLength(0)\n    })\n  })\n\n  describe('filters and search', () => {\n    it('should set filter correctly', () => {\n      const { result } = renderHook(() => useUAVStore())\n\n      act(() => {\n        result.current.setFilter({ status: 'AUTHORIZED' })\n      })\n\n      expect(result.current.filter.status).toBe('AUTHORIZED')\n    })\n\n    it('should set search query correctly', () => {\n      const { result } = renderHook(() => useUAVStore())\n\n      act(() => {\n        result.current.setSearchQuery('UAV-001')\n      })\n\n      expect(result.current.searchQuery).toBe('UAV-001')\n    })\n\n    it('should clear error', () => {\n      const { result } = renderHook(() => useUAVStore())\n\n      // Set error first\n      act(() => {\n        useUAVStore.setState({ error: 'Test error' })\n      })\n\n      expect(result.current.error).toBe('Test error')\n\n      act(() => {\n        result.current.clearError()\n      })\n\n      expect(result.current.error).toBeNull()\n    })\n  })\n\n  describe('selectedUAV', () => {\n    it('should set selected UAV', () => {\n      const uav = createMockUAV()\n      const { result } = renderHook(() => useUAVStore())\n\n      act(() => {\n        result.current.setSelectedUAV(uav)\n      })\n\n      expect(result.current.selectedUAV).toEqual(uav)\n    })\n\n    it('should clear selected UAV', () => {\n      const uav = createMockUAV()\n      const { result } = renderHook(() => useUAVStore())\n\n      // Set UAV first\n      act(() => {\n        result.current.setSelectedUAV(uav)\n      })\n\n      expect(result.current.selectedUAV).toEqual(uav)\n\n      // Clear UAV\n      act(() => {\n        result.current.setSelectedUAV(null)\n      })\n\n      expect(result.current.selectedUAV).toBeNull()\n    })\n  })\n\n  describe('real-time updates', () => {\n    it('should update UAV in store', () => {\n      const existingUAV = createMockUAV()\n      const updatedUAV = { ...existingUAV, ownerName: 'Updated Owner' }\n      \n      const { result } = renderHook(() => useUAVStore())\n\n      // Set initial state\n      act(() => {\n        useUAVStore.setState({ uavs: [existingUAV] })\n      })\n\n      // Update UAV\n      act(() => {\n        result.current.updateUAVInStore(updatedUAV)\n      })\n\n      expect(result.current.uavs[0].ownerName).toBe('Updated Owner')\n    })\n\n    it('should add new UAV if not exists', () => {\n      const newUAV = createMockUAV()\n      \n      const { result } = renderHook(() => useUAVStore())\n\n      act(() => {\n        result.current.updateUAVInStore(newUAV)\n      })\n\n      expect(result.current.uavs).toContain(newUAV)\n    })\n\n    it('should remove UAV from store', () => {\n      const uav = createMockUAV()\n      \n      const { result } = renderHook(() => useUAVStore())\n\n      // Set initial state\n      act(() => {\n        useUAVStore.setState({ uavs: [uav] })\n      })\n\n      // Remove UAV\n      act(() => {\n        result.current.removeUAVFromStore(uav.id)\n      })\n\n      expect(result.current.uavs).toHaveLength(0)\n    })\n  })\n})\n"], "names": ["jest", "mock", "mockedUavApi", "uavApi", "describe", "beforeEach", "useUAVStore", "setState", "uavs", "selectedUAV", "loading", "error", "filter", "searchQuery", "regions", "systemStats", "hibernatePodStatus", "clearAllMocks", "it", "mockUAVs", "createMockUAV", "id", "rfidTag", "getUAVs", "mockResolvedValue", "result", "renderHook", "act", "current", "fetchUAVs", "expect", "toEqual", "toBe", "toBeNull", "toHaveBeenCalledTimes", "errorMessage", "mockRejectedValue", "Error", "resolvePromise", "promise", "Promise", "resolve", "mockReturnValue", "newUAV", "mockResponse", "mockApiResponse", "createUAV", "success", "ownerName", "model", "status", "toContain", "message", "existingUAV", "updatedUAV", "updateUAV", "uavToDelete", "undefined", "deleteUAV", "toHave<PERSON>ength", "setFilter", "setSearch<PERSON>uery", "clearError", "uav", "setSelectedUAV", "updateUAVInStore", "removeUAVFromStore"], "mappings": ";AAKA,eAAe;AACfA,KAAKC,IAAI,CAAC;;;;uBANsB;0BACJ;2BACiC;wBACtC;AAIvB,MAAMC,eAAeC,cAAM;AAE3BC,SAAS,aAAa;IACpBC,WAAW;QACT,oBAAoB;QACpBC,qBAAW,CAACC,QAAQ,CAAC;YACnBC,MAAM,EAAE;YACRC,aAAa;YACbC,SAAS;YACTC,OAAO;YACPC,QAAQ,CAAC;YACTC,aAAa;YACbC,SAAS,EAAE;YACXC,aAAa;YACbC,oBAAoB;QACtB;QAEA,kBAAkB;QAClBhB,KAAKiB,aAAa;IACpB;IAEAb,SAAS,aAAa;QACpBc,GAAG,kCAAkC;YACnC,MAAMC,WAAW;gBAACC,IAAAA,wBAAa;gBAAIA,IAAAA,wBAAa,EAAC;oBAAEC,IAAI;oBAAGC,SAAS;gBAAU;aAAG;YAChFpB,aAAaqB,OAAO,CAACC,iBAAiB,CAACL;YAEvC,MAAM,EAAEM,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/C,MAAMqB,IAAAA,UAAG,EAAC;gBACR,MAAMF,OAAOG,OAAO,CAACC,SAAS;YAChC;YAEAC,OAAOL,OAAOG,OAAO,CAACpB,IAAI,EAAEuB,OAAO,CAACZ;YACpCW,OAAOL,OAAOG,OAAO,CAAClB,OAAO,EAAEsB,IAAI,CAAC;YACpCF,OAAOL,OAAOG,OAAO,CAACjB,KAAK,EAAEsB,QAAQ;YACrCH,OAAO5B,aAAaqB,OAAO,EAAEW,qBAAqB,CAAC;QACrD;QAEAhB,GAAG,kCAAkC;YACnC,MAAMiB,eAAe;YACrBjC,aAAaqB,OAAO,CAACa,iBAAiB,CAAC,IAAIC,MAAMF;YAEjD,MAAM,EAAEV,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/C,MAAMqB,IAAAA,UAAG,EAAC;gBACR,MAAMF,OAAOG,OAAO,CAACC,SAAS;YAChC;YAEAC,OAAOL,OAAOG,OAAO,CAACpB,IAAI,EAAEuB,OAAO,CAAC,EAAE;YACtCD,OAAOL,OAAOG,OAAO,CAAClB,OAAO,EAAEsB,IAAI,CAAC;YACpCF,OAAOL,OAAOG,OAAO,CAACjB,KAAK,EAAEqB,IAAI,CAACG;QACpC;QAEAjB,GAAG,yCAAyC;YAC1C,IAAIoB;YACJ,MAAMC,UAAU,IAAIC,QAAQC,CAAAA;gBAC1BH,iBAAiBG;YACnB;YACAvC,aAAaqB,OAAO,CAACmB,eAAe,CAACH;YAErC,MAAM,EAAEd,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/CqB,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACC,SAAS;YAC1B;YAEAC,OAAOL,OAAOG,OAAO,CAAClB,OAAO,EAAEsB,IAAI,CAAC;YAEpC,MAAML,IAAAA,UAAG,EAAC;gBACRW,eAAe,EAAE;YACnB;YAEAR,OAAOL,OAAOG,OAAO,CAAClB,OAAO,EAAEsB,IAAI,CAAC;QACtC;IACF;IAEA5B,SAAS,aAAa;QACpBc,GAAG,kCAAkC;YACnC,MAAMyB,SAASvB,IAAAA,wBAAa;YAC5B,MAAMwB,eAAeC,IAAAA,0BAAe,EAACF;YACrCzC,aAAa4C,SAAS,CAACtB,iBAAiB,CAACoB;YAEzC,MAAM,EAAEnB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/C,IAAIyC;YACJ,MAAMpB,IAAAA,UAAG,EAAC;gBACRoB,UAAU,MAAMtB,OAAOG,OAAO,CAACkB,SAAS,CAAC;oBACvCxB,SAASqB,OAAOrB,OAAO;oBACvB0B,WAAWL,OAAOK,SAAS;oBAC3BC,OAAON,OAAOM,KAAK;oBACnBC,QAAQP,OAAOO,MAAM;gBACvB;YACF;YAEApB,OAAOiB,SAAUf,IAAI,CAAC;YACtBF,OAAOL,OAAOG,OAAO,CAACpB,IAAI,EAAE2C,SAAS,CAACR;YACtCb,OAAOL,OAAOG,OAAO,CAAClB,OAAO,EAAEsB,IAAI,CAAC;YACpCF,OAAOL,OAAOG,OAAO,CAACjB,KAAK,EAAEsB,QAAQ;QACvC;QAEAf,GAAG,kCAAkC;YACnC,MAAMiB,eAAe;YACrB,MAAMS,eAAe;gBAAEG,SAAS;gBAAOK,SAASjB;YAAa;YAC7DjC,aAAa4C,SAAS,CAACtB,iBAAiB,CAACoB;YAEzC,MAAM,EAAEnB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/C,IAAIyC;YACJ,MAAMpB,IAAAA,UAAG,EAAC;gBACRoB,UAAU,MAAMtB,OAAOG,OAAO,CAACkB,SAAS,CAAC;oBACvCxB,SAAS;oBACT0B,WAAW;oBACXC,OAAO;oBACPC,QAAQ;gBACV;YACF;YAEApB,OAAOiB,SAAUf,IAAI,CAAC;YACtBF,OAAOL,OAAOG,OAAO,CAACjB,KAAK,EAAEqB,IAAI,CAACG;QACpC;IACF;IAEA/B,SAAS,aAAa;QACpBc,GAAG,kCAAkC;YACnC,MAAMmC,cAAcjC,IAAAA,wBAAa;YACjC,MAAMkC,aAAa;gBAAE,GAAGD,WAAW;gBAAEL,WAAW;YAAgB;YAChE,MAAMJ,eAAeC,IAAAA,0BAAe,EAACS;YAErCpD,aAAaqD,SAAS,CAAC/B,iBAAiB,CAACoB;YAEzC,MAAM,EAAEnB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/C,oBAAoB;YACpBqB,IAAAA,UAAG,EAAC;gBACFrB,qBAAW,CAACC,QAAQ,CAAC;oBAAEC,MAAM;wBAAC6C;qBAAY;gBAAC;YAC7C;YAEA,IAAIN;YACJ,MAAMpB,IAAAA,UAAG,EAAC;gBACRoB,UAAU,MAAMtB,OAAOG,OAAO,CAAC2B,SAAS,CAACF,YAAYhC,EAAE,EAAE;oBAAE2B,WAAW;gBAAgB;YACxF;YAEAlB,OAAOiB,SAAUf,IAAI,CAAC;YACtBF,OAAOL,OAAOG,OAAO,CAACpB,IAAI,CAAC,EAAE,CAACwC,SAAS,EAAEhB,IAAI,CAAC;QAChD;IACF;IAEA5B,SAAS,aAAa;QACpBc,GAAG,kCAAkC;YACnC,MAAMsC,cAAcpC,IAAAA,wBAAa;YACjC,MAAMwB,eAAeC,IAAAA,0BAAe,EAACY;YAErCvD,aAAawD,SAAS,CAAClC,iBAAiB,CAACoB;YAEzC,MAAM,EAAEnB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/C,oBAAoB;YACpBqB,IAAAA,UAAG,EAAC;gBACFrB,qBAAW,CAACC,QAAQ,CAAC;oBAAEC,MAAM;wBAACgD;qBAAY;gBAAC;YAC7C;YAEA,IAAIT;YACJ,MAAMpB,IAAAA,UAAG,EAAC;gBACRoB,UAAU,MAAMtB,OAAOG,OAAO,CAAC8B,SAAS,CAACF,YAAYnC,EAAE;YACzD;YAEAS,OAAOiB,SAAUf,IAAI,CAAC;YACtBF,OAAOL,OAAOG,OAAO,CAACpB,IAAI,EAAEmD,YAAY,CAAC;QAC3C;IACF;IAEAvD,SAAS,sBAAsB;QAC7Bc,GAAG,+BAA+B;YAChC,MAAM,EAAEO,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/CqB,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACgC,SAAS,CAAC;oBAAEV,QAAQ;gBAAa;YAClD;YAEApB,OAAOL,OAAOG,OAAO,CAAChB,MAAM,CAACsC,MAAM,EAAElB,IAAI,CAAC;QAC5C;QAEAd,GAAG,qCAAqC;YACtC,MAAM,EAAEO,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/CqB,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACiC,cAAc,CAAC;YAChC;YAEA/B,OAAOL,OAAOG,OAAO,CAACf,WAAW,EAAEmB,IAAI,CAAC;QAC1C;QAEAd,GAAG,sBAAsB;YACvB,MAAM,EAAEO,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/C,kBAAkB;YAClBqB,IAAAA,UAAG,EAAC;gBACFrB,qBAAW,CAACC,QAAQ,CAAC;oBAAEI,OAAO;gBAAa;YAC7C;YAEAmB,OAAOL,OAAOG,OAAO,CAACjB,KAAK,EAAEqB,IAAI,CAAC;YAElCL,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACkC,UAAU;YAC3B;YAEAhC,OAAOL,OAAOG,OAAO,CAACjB,KAAK,EAAEsB,QAAQ;QACvC;IACF;IAEA7B,SAAS,eAAe;QACtBc,GAAG,2BAA2B;YAC5B,MAAM6C,MAAM3C,IAAAA,wBAAa;YACzB,MAAM,EAAEK,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/CqB,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACoC,cAAc,CAACD;YAChC;YAEAjC,OAAOL,OAAOG,OAAO,CAACnB,WAAW,EAAEsB,OAAO,CAACgC;QAC7C;QAEA7C,GAAG,6BAA6B;YAC9B,MAAM6C,MAAM3C,IAAAA,wBAAa;YACzB,MAAM,EAAEK,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/C,gBAAgB;YAChBqB,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACoC,cAAc,CAACD;YAChC;YAEAjC,OAAOL,OAAOG,OAAO,CAACnB,WAAW,EAAEsB,OAAO,CAACgC;YAE3C,YAAY;YACZpC,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACoC,cAAc,CAAC;YAChC;YAEAlC,OAAOL,OAAOG,OAAO,CAACnB,WAAW,EAAEwB,QAAQ;QAC7C;IACF;IAEA7B,SAAS,qBAAqB;QAC5Bc,GAAG,8BAA8B;YAC/B,MAAMmC,cAAcjC,IAAAA,wBAAa;YACjC,MAAMkC,aAAa;gBAAE,GAAGD,WAAW;gBAAEL,WAAW;YAAgB;YAEhE,MAAM,EAAEvB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/C,oBAAoB;YACpBqB,IAAAA,UAAG,EAAC;gBACFrB,qBAAW,CAACC,QAAQ,CAAC;oBAAEC,MAAM;wBAAC6C;qBAAY;gBAAC;YAC7C;YAEA,aAAa;YACb1B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACqC,gBAAgB,CAACX;YAClC;YAEAxB,OAAOL,OAAOG,OAAO,CAACpB,IAAI,CAAC,EAAE,CAACwC,SAAS,EAAEhB,IAAI,CAAC;QAChD;QAEAd,GAAG,oCAAoC;YACrC,MAAMyB,SAASvB,IAAAA,wBAAa;YAE5B,MAAM,EAAEK,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/CqB,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACqC,gBAAgB,CAACtB;YAClC;YAEAb,OAAOL,OAAOG,OAAO,CAACpB,IAAI,EAAE2C,SAAS,CAACR;QACxC;QAEAzB,GAAG,gCAAgC;YACjC,MAAM6C,MAAM3C,IAAAA,wBAAa;YAEzB,MAAM,EAAEK,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAMpB,IAAAA,qBAAW;YAE/C,oBAAoB;YACpBqB,IAAAA,UAAG,EAAC;gBACFrB,qBAAW,CAACC,QAAQ,CAAC;oBAAEC,MAAM;wBAACuD;qBAAI;gBAAC;YACrC;YAEA,aAAa;YACbpC,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACsC,kBAAkB,CAACH,IAAI1C,EAAE;YAC1C;YAEAS,OAAOL,OAAOG,OAAO,CAACpB,IAAI,EAAEmD,YAAY,CAAC;QAC3C;IACF;AACF"}