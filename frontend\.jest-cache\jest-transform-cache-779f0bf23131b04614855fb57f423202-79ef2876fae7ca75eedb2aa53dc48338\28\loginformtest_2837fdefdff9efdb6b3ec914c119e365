b3d43abb867878f7f6e3f2f8d465de0a
"use strict";
// Mock Next.js router
jest.mock("next/navigation", ()=>({
        useRouter: jest.fn()
    }));
// Mock the auth store
jest.mock("@/stores/auth-store");
// Mock react-hot-toast
jest.mock("react-hot-toast", ()=>({
        toast: {
            success: jest.fn(),
            error: jest.fn()
        }
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _loginform = require("../login-form");
const _authstore = require("../../../stores/auth-store");
const _navigation = require("next/navigation");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockUseAuthStore = _authstore.useAuthStore;
describe("LoginForm Component", ()=>{
    const mockPush = jest.fn();
    const mockLogin = jest.fn();
    const mockClearError = jest.fn();
    beforeEach(()=>{
        jest.clearAllMocks();
        _navigation.useRouter.mockReturnValue({
            push: mockPush,
            replace: jest.fn(),
            prefetch: jest.fn()
        });
        // Mock auth store
        mockUseAuthStore.mockReturnValue({
            login: mockLogin,
            isLoading: false,
            error: null,
            clearError: mockClearError,
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            logout: jest.fn(),
            register: jest.fn(),
            refreshToken: jest.fn(),
            changePassword: jest.fn(),
            updateProfile: jest.fn(),
            hasPermission: jest.fn(),
            hasRole: jest.fn(),
            canAccess: jest.fn(),
            checkSession: jest.fn(),
            setLoading: jest.fn(),
            fetchUserProfile: jest.fn(),
            updateLastActivity: jest.fn()
        });
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        expect(_testutils.screen.getByText("UAV Control System")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Sign in to access the UAV management dashboard")).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText(/username/i)).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText(/password/i)).toBeInTheDocument();
        expect(_testutils.screen.getByRole("button", {
            name: /sign in/i
        })).toBeInTheDocument();
    });
    it("handles form submission with valid data", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockClearError).toHaveBeenCalled();
            expect(mockLogin).toHaveBeenCalledWith({
                username: "testuser",
                password: "password123",
                rememberMe: false
            });
        });
    });
    it("redirects to dashboard on successful login", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockPush).toHaveBeenCalledWith("/dashboard");
        });
    });
    it("calls onSuccess callback when provided", async ()=>{
        const mockOnSuccess = jest.fn();
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {
            onSuccess: mockOnSuccess
        }));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockOnSuccess).toHaveBeenCalled();
            expect(mockPush).not.toHaveBeenCalled();
        });
    });
    it("redirects to custom path when provided", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {
            redirectTo: "/custom-path"
        }));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockPush).toHaveBeenCalledWith("/custom-path");
        });
    });
    it("handles remember me checkbox", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const rememberMeCheckbox = _testutils.screen.getByLabelText(/remember me/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(rememberMeCheckbox);
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockLogin).toHaveBeenCalledWith({
                username: "testuser",
                password: "password123",
                rememberMe: true
            });
        });
    });
    it("toggles password visibility", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        // Find the toggle button by its position (it's the only button that's not the submit button)
        const buttons = _testutils.screen.getAllByRole("button");
        const toggleButton = buttons.find((button)=>button.type === "button");
        expect(passwordInput).toHaveAttribute("type", "password");
        if (toggleButton) {
            _testutils.fireEvent.click(toggleButton);
            expect(passwordInput).toHaveAttribute("type", "text");
            _testutils.fireEvent.click(toggleButton);
            expect(passwordInput).toHaveAttribute("type", "password");
        }
    });
    it("displays loading state during login", ()=>{
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            isLoading: true
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const submitButton = _testutils.screen.getByRole("button", {
            name: /signing in/i
        });
        expect(submitButton).toBeDisabled();
        expect(_testutils.screen.getByTestId("loading-spinner")).toBeInTheDocument();
    });
    it("displays error message when login fails", ()=>{
        const errorMessage = "Invalid credentials";
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            error: errorMessage
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        expect(_testutils.screen.getByText(errorMessage)).toBeInTheDocument();
        expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
    });
    it("validates required fields", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText(/username is required/i)).toBeInTheDocument();
            expect(_testutils.screen.getByText(/password is required/i)).toBeInTheDocument();
        });
        expect(mockLogin).not.toHaveBeenCalled();
    });
    it("validates username format", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "ab"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText(/username must be at least 3 characters/i)).toBeInTheDocument();
        });
    });
    it("validates password format", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText(/password must be at least 6 characters/i)).toBeInTheDocument();
        });
    });
    it("displays error messages", ()=>{
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            error: "Invalid credentials"
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        expect(_testutils.screen.getByText("Invalid credentials")).toBeInTheDocument();
        expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
    });
    it("handles keyboard navigation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        // Test that elements can receive focus
        usernameInput.focus();
        expect(usernameInput).toHaveFocus();
        passwordInput.focus();
        expect(passwordInput).toHaveFocus();
        submitButton.focus();
        expect(submitButton).toHaveFocus();
    });
    it("handles form submission with Enter key", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const form = _testutils.screen.getByRole("form") || document.querySelector("form");
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        // Submit the form directly
        if (form) {
            _testutils.fireEvent.submit(form);
            await (0, _testutils.waitFor)(()=>{
                expect(mockLogin).toHaveBeenCalled();
            });
        }
    });
    it("maintains accessibility standards", async ()=>{
        const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        // Check for proper labeling
        expect(_testutils.screen.getByLabelText(/username/i)).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText(/password/i)).toBeInTheDocument();
        // Check for form structure
        expect(container.querySelector("form")).toBeInTheDocument();
        // Run accessibility tests
        await (0, _testutils.runAxeTest)(container);
    });
    it("has proper input types", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        expect(usernameInput).toHaveAttribute("type", "text");
        expect(passwordInput).toHaveAttribute("type", "password");
    });
    it("disables submit button during loading", ()=>{
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            isLoading: true
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const submitButton = _testutils.screen.getByRole("button", {
            name: /signing in/i
        });
        expect(submitButton).toBeDisabled();
        expect(_testutils.screen.getByText("Signing in...")).toBeInTheDocument();
    });
    it("handles network errors gracefully", async ()=>{
        mockLogin.mockResolvedValue(false);
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            error: "Network error"
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Network error")).toBeInTheDocument();
            expect(mockPush).not.toHaveBeenCalled();
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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