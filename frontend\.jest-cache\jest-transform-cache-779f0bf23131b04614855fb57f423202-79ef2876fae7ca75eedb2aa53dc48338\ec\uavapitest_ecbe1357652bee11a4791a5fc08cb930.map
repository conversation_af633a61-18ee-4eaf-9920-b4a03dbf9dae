{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\api\\__tests__\\uav-api.test.ts"], "sourcesContent": ["import { UAVApi, HibernatePodApi, RegionApi } from '../uav-api'\nimport apiClient from '@/lib/api-client'\nimport { createMockUAV, mockApiResponse } from '@/lib/test-utils'\nimport {\n  CreateUAVRequest,\n  UpdateUAVRequest,\n  UAVFilter,\n  PaginationParams,\n  SystemStatistics,\n  HibernatePodStatus,\n  Region,\n} from '@/types/uav'\n\n// Mock the API client\njest.mock('@/lib/api-client')\nconst mockedApiClient = apiClient as jest.Mocked<typeof apiClient>\n\ndescribe('UAVApi', () => {\n  let uavApi: UAVApi\n\n  beforeEach(() => {\n    uavApi = new UAVApi()\n    jest.clearAllMocks()\n  })\n\n  describe('getUAVs', () => {\n    it('should get UAVs without filters', async () => {\n      const mockUAVs = [createMockUAV(), createMockUAV({ id: 2, rfidTag: 'UAV-002' })]\n      mockedApiClient.get.mockResolvedValue(mockUAVs)\n\n      const result = await uavApi.getUAVs()\n\n      expect(result).toEqual(mockUAVs)\n      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/uav/all')\n    })\n\n    it('should get UAVs with filters and pagination', async () => {\n      const filter: UAVFilter = {\n        status: 'AUTHORIZED',\n        operationalStatus: 'READY',\n      }\n      const pagination: PaginationParams = {\n        page: 1,\n        limit: 10,\n        sortBy: 'id',\n        sortOrder: 'desc',\n      }\n\n      const mockUAVs = [createMockUAV()]\n      mockedApiClient.get.mockResolvedValue(mockUAVs)\n\n      const result = await uavApi.getUAVs(filter, pagination)\n\n      expect(result).toEqual(mockUAVs)\n      expect(mockedApiClient.get).toHaveBeenCalledWith(\n        '/api/uav/all?status=AUTHORIZED&operationalStatus=READY&page=1&limit=10&sortBy=id&sortOrder=desc'\n      )\n    })\n\n    it('should handle get UAVs error', async () => {\n      const error = new Error('Failed to fetch UAVs')\n      mockedApiClient.get.mockRejectedValue(error)\n\n      await expect(uavApi.getUAVs()).rejects.toThrow('Failed to fetch UAVs')\n    })\n  })\n\n  describe('getUAVById', () => {\n    it('should get UAV by ID successfully', async () => {\n      const mockUAV = createMockUAV()\n      mockedApiClient.get.mockResolvedValue(mockUAV)\n\n      const result = await uavApi.getUAVById(1)\n\n      expect(result).toEqual(mockUAV)\n      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/uav/1')\n    })\n\n    it('should handle UAV not found', async () => {\n      const error = new Error('UAV not found')\n      mockedApiClient.get.mockRejectedValue(error)\n\n      await expect(uavApi.getUAVById(999)).rejects.toThrow('UAV not found')\n    })\n  })\n\n  describe('createUAV', () => {\n    it('should create UAV successfully', async () => {\n      const createData: CreateUAVRequest = {\n        rfidTag: 'UAV-003',\n        ownerName: 'New Owner',\n        model: 'New Model',\n      }\n\n      const mockResponse = mockApiResponse(createMockUAV({ ...createData, id: 3 }))\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await uavApi.createUAV(createData)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/uav', createData)\n    })\n\n    it('should handle create UAV error', async () => {\n      const createData: CreateUAVRequest = {\n        rfidTag: 'DUPLICATE-TAG',\n        ownerName: 'Owner',\n        model: 'Model',\n      }\n\n      const error = new Error('RFID tag already exists')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(uavApi.createUAV(createData)).rejects.toThrow('RFID tag already exists')\n    })\n  })\n\n  describe('updateUAV', () => {\n    it('should update UAV successfully', async () => {\n      const updateData: UpdateUAVRequest = {\n        ownerName: 'Updated Owner',\n        model: 'Updated Model',\n      }\n\n      const mockResponse = mockApiResponse(createMockUAV({ id: 1, ...updateData }))\n      mockedApiClient.put.mockResolvedValue(mockResponse)\n\n      const result = await uavApi.updateUAV(1, updateData)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.put).toHaveBeenCalledWith('/api/uav/1', updateData)\n    })\n\n    it('should handle update UAV error', async () => {\n      const updateData: UpdateUAVRequest = {\n        ownerName: 'Updated Owner',\n      }\n\n      const error = new Error('UAV not found')\n      mockedApiClient.put.mockRejectedValue(error)\n\n      await expect(uavApi.updateUAV(999, updateData)).rejects.toThrow('UAV not found')\n    })\n  })\n\n  describe('deleteUAV', () => {\n    it('should delete UAV successfully', async () => {\n      const mockResponse = mockApiResponse(null)\n      mockedApiClient.delete.mockResolvedValue(mockResponse)\n\n      const result = await uavApi.deleteUAV(1)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.delete).toHaveBeenCalledWith('/api/uav/1')\n    })\n\n    it('should handle delete UAV error', async () => {\n      const error = new Error('UAV not found')\n      mockedApiClient.delete.mockRejectedValue(error)\n\n      await expect(uavApi.deleteUAV(999)).rejects.toThrow('UAV not found')\n    })\n  })\n\n  describe('updateUAVStatus', () => {\n    it('should update UAV status successfully', async () => {\n      const mockResponse = mockApiResponse({ message: 'Status updated' })\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await uavApi.updateUAVStatus(1)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/uav/1/status')\n    })\n\n    it('should handle update status error', async () => {\n      const error = new Error('UAV not found')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(uavApi.updateUAVStatus(999)).rejects.toThrow('UAV not found')\n    })\n  })\n\n  describe('getSystemStatistics', () => {\n    it('should get system statistics successfully', async () => {\n      const mockStats: SystemStatistics = {\n        totalUAVs: 10,\n        authorizedUAVs: 8,\n        unauthorizedUAVs: 2,\n        activeFlights: 3,\n        hibernatingUAVs: 2,\n        averageBatteryLevel: 75,\n        totalFlightHours: 1000,\n        totalFlightCycles: 500,\n      }\n\n      mockedApiClient.get.mockResolvedValue(mockStats)\n\n      const result = await uavApi.getSystemStatistics()\n\n      expect(result).toEqual(mockStats)\n      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/uav/statistics')\n    })\n\n    it('should handle get statistics error', async () => {\n      const error = new Error('Failed to fetch statistics')\n      mockedApiClient.get.mockRejectedValue(error)\n\n      await expect(uavApi.getSystemStatistics()).rejects.toThrow('Failed to fetch statistics')\n    })\n  })\n\n  describe('bulkUpdateStatus', () => {\n    it('should bulk update status successfully', async () => {\n      const uavIds = [1, 2, 3]\n      const status = 'AUTHORIZED'\n      const mockResponse = mockApiResponse({ updated: 3 })\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await uavApi.bulkUpdateStatus(uavIds, status)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/uav/bulk/status', { uavIds, status })\n    })\n\n    it('should handle bulk update error', async () => {\n      const uavIds = [999, 998]\n      const status = 'AUTHORIZED'\n      const error = new Error('Some UAVs not found')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(uavApi.bulkUpdateStatus(uavIds, status)).rejects.toThrow('Some UAVs not found')\n    })\n  })\n\n  describe('bulkDelete', () => {\n    it('should bulk delete successfully', async () => {\n      const uavIds = [1, 2, 3]\n      const mockResponse = mockApiResponse({ deleted: 3 })\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await uavApi.bulkDelete(uavIds)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/uav/bulk/delete', { uavIds })\n    })\n\n    it('should handle bulk delete error', async () => {\n      const uavIds = [999, 998]\n      const error = new Error('Some UAVs not found')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(uavApi.bulkDelete(uavIds)).rejects.toThrow('Some UAVs not found')\n    })\n  })\n})\n\ndescribe('HibernatePodApi', () => {\n  let hibernatePodApi: HibernatePodApi\n\n  beforeEach(() => {\n    hibernatePodApi = new HibernatePodApi()\n    jest.clearAllMocks()\n  })\n\n  describe('getStatus', () => {\n    it('should get hibernate pod status successfully', async () => {\n      const mockStatus: HibernatePodStatus = {\n        isActive: true,\n        capacity: 10,\n        currentOccupancy: 5,\n        availableSlots: 5,\n        temperature: 20,\n        humidity: 45,\n        powerConsumption: 150,\n        lastMaintenance: '2024-01-01T00:00:00Z',\n      }\n\n      mockedApiClient.get.mockResolvedValue(mockStatus)\n\n      const result = await hibernatePodApi.getStatus()\n\n      expect(result).toEqual(mockStatus)\n      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/hibernate-pod/status')\n    })\n\n    it('should handle get status error', async () => {\n      const error = new Error('Failed to fetch status')\n      mockedApiClient.get.mockRejectedValue(error)\n\n      await expect(hibernatePodApi.getStatus()).rejects.toThrow('Failed to fetch status')\n    })\n  })\n\n  describe('addUAV', () => {\n    it('should add UAV to hibernate pod successfully', async () => {\n      const mockResponse = mockApiResponse({ message: 'UAV added to hibernate pod' })\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await hibernatePodApi.addUAV(1)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/hibernate-pod/add', { uavId: 1 })\n    })\n\n    it('should handle add UAV error', async () => {\n      const error = new Error('Hibernate pod is full')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(hibernatePodApi.addUAV(1)).rejects.toThrow('Hibernate pod is full')\n    })\n  })\n\n  describe('removeUAV', () => {\n    it('should remove UAV from hibernate pod successfully', async () => {\n      const mockResponse = mockApiResponse({ message: 'UAV removed from hibernate pod' })\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await hibernatePodApi.removeUAV(1)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/hibernate-pod/remove', { uavId: 1 })\n    })\n\n    it('should handle remove UAV error', async () => {\n      const error = new Error('UAV not in hibernate pod')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(hibernatePodApi.removeUAV(1)).rejects.toThrow('UAV not in hibernate pod')\n    })\n  })\n\n  describe('getUAVsInPod', () => {\n    it('should get UAVs in hibernate pod successfully', async () => {\n      const mockUAVs = [\n        createMockUAV({ id: 1, inHibernatePod: true }),\n        createMockUAV({ id: 2, inHibernatePod: true }),\n      ]\n      mockedApiClient.get.mockResolvedValue(mockUAVs)\n\n      const result = await hibernatePodApi.getUAVsInPod()\n\n      expect(result).toEqual(mockUAVs)\n      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/hibernate-pod/uavs')\n    })\n\n    it('should handle get UAVs error', async () => {\n      const error = new Error('Failed to fetch UAVs')\n      mockedApiClient.get.mockRejectedValue(error)\n\n      await expect(hibernatePodApi.getUAVsInPod()).rejects.toThrow('Failed to fetch UAVs')\n    })\n  })\n})\n\ndescribe('RegionApi', () => {\n  let regionApi: RegionApi\n\n  beforeEach(() => {\n    regionApi = new RegionApi()\n    jest.clearAllMocks()\n  })\n\n  describe('getRegions', () => {\n    it('should get regions successfully', async () => {\n      const mockRegions: Region[] = [\n        {\n          id: 1,\n          name: 'Region 1',\n          description: 'Test region 1',\n          coordinates: [\n            { latitude: 40.7128, longitude: -74.0060 },\n            { latitude: 40.7589, longitude: -73.9851 },\n          ],\n          isActive: true,\n        },\n      ]\n\n      mockedApiClient.get.mockResolvedValue(mockRegions)\n\n      const result = await regionApi.getRegions()\n\n      expect(result).toEqual(mockRegions)\n      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/regions')\n    })\n\n    it('should handle get regions error', async () => {\n      const error = new Error('Failed to fetch regions')\n      mockedApiClient.get.mockRejectedValue(error)\n\n      await expect(regionApi.getRegions()).rejects.toThrow('Failed to fetch regions')\n    })\n  })\n\n  describe('createRegion', () => {\n    it('should create region successfully', async () => {\n      const regionData = {\n        name: 'New Region',\n        description: 'A new test region',\n        coordinates: [\n          { latitude: 40.7128, longitude: -74.0060 },\n          { latitude: 40.7589, longitude: -73.9851 },\n        ],\n      }\n\n      const mockResponse = mockApiResponse({\n        id: 1,\n        ...regionData,\n        isActive: true,\n      })\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await regionApi.createRegion(regionData)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/regions', regionData)\n    })\n\n    it('should handle create region error', async () => {\n      const regionData = {\n        name: 'Duplicate Region',\n        description: 'A duplicate region',\n        coordinates: [],\n      }\n\n      const error = new Error('Region name already exists')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(regionApi.createRegion(regionData)).rejects.toThrow('Region name already exists')\n    })\n  })\n})\n"], "names": ["jest", "mock", "mockedApiClient", "apiClient", "describe", "uavApi", "beforeEach", "UAVApi", "clearAllMocks", "it", "mockUAVs", "createMockUAV", "id", "rfidTag", "get", "mockResolvedValue", "result", "getUAVs", "expect", "toEqual", "toHaveBeenCalledWith", "filter", "status", "operationalStatus", "pagination", "page", "limit", "sortBy", "sortOrder", "error", "Error", "mockRejectedValue", "rejects", "toThrow", "mockUAV", "getUAVById", "createData", "ownerName", "model", "mockResponse", "mockApiResponse", "post", "createUAV", "updateData", "put", "updateUAV", "delete", "deleteUAV", "message", "updateUAVStatus", "mockStats", "totalUAVs", "authorizedUAVs", "unauthorizedUAVs", "activeFlights", "hibernatingUAVs", "averageBatteryLevel", "totalFlightHours", "totalFlightCycles", "getSystemStatistics", "uavIds", "updated", "bulkUpdateStatus", "deleted", "bulkDelete", "hibernatePodApi", "HibernatePodApi", "mockStatus", "isActive", "capacity", "currentOccupancy", "availableSlots", "temperature", "humidity", "powerConsumption", "lastMaintenance", "getStatus", "addUAV", "uavId", "removeUAV", "inHibernatePod", "getUAVsInPod", "regionApi", "RegionApi", "mockRegions", "name", "description", "coordinates", "latitude", "longitude", "getRegions", "regionData", "createRegion"], "mappings": ";AAaA,sBAAsB;AACtBA,KAAKC,IAAI,CAAC;;;;wBAdyC;kEAC7B;2BACyB;;;;;;AAa/C,MAAMC,kBAAkBC,kBAAS;AAEjCC,SAAS,UAAU;IACjB,IAAIC;IAEJC,WAAW;QACTD,SAAS,IAAIE,cAAM;QACnBP,KAAKQ,aAAa;IACpB;IAEAJ,SAAS,WAAW;QAClBK,GAAG,mCAAmC;YACpC,MAAMC,WAAW;gBAACC,IAAAA,wBAAa;gBAAIA,IAAAA,wBAAa,EAAC;oBAAEC,IAAI;oBAAGC,SAAS;gBAAU;aAAG;YAChFX,gBAAgBY,GAAG,CAACC,iBAAiB,CAACL;YAEtC,MAAMM,SAAS,MAAMX,OAAOY,OAAO;YAEnCC,OAAOF,QAAQG,OAAO,CAACT;YACvBQ,OAAOhB,gBAAgBY,GAAG,EAAEM,oBAAoB,CAAC;QACnD;QAEAX,GAAG,+CAA+C;YAChD,MAAMY,SAAoB;gBACxBC,QAAQ;gBACRC,mBAAmB;YACrB;YACA,MAAMC,aAA+B;gBACnCC,MAAM;gBACNC,OAAO;gBACPC,QAAQ;gBACRC,WAAW;YACb;YAEA,MAAMlB,WAAW;gBAACC,IAAAA,wBAAa;aAAG;YAClCT,gBAAgBY,GAAG,CAACC,iBAAiB,CAACL;YAEtC,MAAMM,SAAS,MAAMX,OAAOY,OAAO,CAACI,QAAQG;YAE5CN,OAAOF,QAAQG,OAAO,CAACT;YACvBQ,OAAOhB,gBAAgBY,GAAG,EAAEM,oBAAoB,CAC9C;QAEJ;QAEAX,GAAG,gCAAgC;YACjC,MAAMoB,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBY,GAAG,CAACiB,iBAAiB,CAACF;YAEtC,MAAMX,OAAOb,OAAOY,OAAO,IAAIe,OAAO,CAACC,OAAO,CAAC;QACjD;IACF;IAEA7B,SAAS,cAAc;QACrBK,GAAG,qCAAqC;YACtC,MAAMyB,UAAUvB,IAAAA,wBAAa;YAC7BT,gBAAgBY,GAAG,CAACC,iBAAiB,CAACmB;YAEtC,MAAMlB,SAAS,MAAMX,OAAO8B,UAAU,CAAC;YAEvCjB,OAAOF,QAAQG,OAAO,CAACe;YACvBhB,OAAOhB,gBAAgBY,GAAG,EAAEM,oBAAoB,CAAC;QACnD;QAEAX,GAAG,+BAA+B;YAChC,MAAMoB,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBY,GAAG,CAACiB,iBAAiB,CAACF;YAEtC,MAAMX,OAAOb,OAAO8B,UAAU,CAAC,MAAMH,OAAO,CAACC,OAAO,CAAC;QACvD;IACF;IAEA7B,SAAS,aAAa;QACpBK,GAAG,kCAAkC;YACnC,MAAM2B,aAA+B;gBACnCvB,SAAS;gBACTwB,WAAW;gBACXC,OAAO;YACT;YAEA,MAAMC,eAAeC,IAAAA,0BAAe,EAAC7B,IAAAA,wBAAa,EAAC;gBAAE,GAAGyB,UAAU;gBAAExB,IAAI;YAAE;YAC1EV,gBAAgBuC,IAAI,CAAC1B,iBAAiB,CAACwB;YAEvC,MAAMvB,SAAS,MAAMX,OAAOqC,SAAS,CAACN;YAEtClB,OAAOF,QAAQG,OAAO,CAACoB;YACvBrB,OAAOhB,gBAAgBuC,IAAI,EAAErB,oBAAoB,CAAC,YAAYgB;QAChE;QAEA3B,GAAG,kCAAkC;YACnC,MAAM2B,aAA+B;gBACnCvB,SAAS;gBACTwB,WAAW;gBACXC,OAAO;YACT;YAEA,MAAMT,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBuC,IAAI,CAACV,iBAAiB,CAACF;YAEvC,MAAMX,OAAOb,OAAOqC,SAAS,CAACN,aAAaJ,OAAO,CAACC,OAAO,CAAC;QAC7D;IACF;IAEA7B,SAAS,aAAa;QACpBK,GAAG,kCAAkC;YACnC,MAAMkC,aAA+B;gBACnCN,WAAW;gBACXC,OAAO;YACT;YAEA,MAAMC,eAAeC,IAAAA,0BAAe,EAAC7B,IAAAA,wBAAa,EAAC;gBAAEC,IAAI;gBAAG,GAAG+B,UAAU;YAAC;YAC1EzC,gBAAgB0C,GAAG,CAAC7B,iBAAiB,CAACwB;YAEtC,MAAMvB,SAAS,MAAMX,OAAOwC,SAAS,CAAC,GAAGF;YAEzCzB,OAAOF,QAAQG,OAAO,CAACoB;YACvBrB,OAAOhB,gBAAgB0C,GAAG,EAAExB,oBAAoB,CAAC,cAAcuB;QACjE;QAEAlC,GAAG,kCAAkC;YACnC,MAAMkC,aAA+B;gBACnCN,WAAW;YACb;YAEA,MAAMR,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgB0C,GAAG,CAACb,iBAAiB,CAACF;YAEtC,MAAMX,OAAOb,OAAOwC,SAAS,CAAC,KAAKF,aAAaX,OAAO,CAACC,OAAO,CAAC;QAClE;IACF;IAEA7B,SAAS,aAAa;QACpBK,GAAG,kCAAkC;YACnC,MAAM8B,eAAeC,IAAAA,0BAAe,EAAC;YACrCtC,gBAAgB4C,MAAM,CAAC/B,iBAAiB,CAACwB;YAEzC,MAAMvB,SAAS,MAAMX,OAAO0C,SAAS,CAAC;YAEtC7B,OAAOF,QAAQG,OAAO,CAACoB;YACvBrB,OAAOhB,gBAAgB4C,MAAM,EAAE1B,oBAAoB,CAAC;QACtD;QAEAX,GAAG,kCAAkC;YACnC,MAAMoB,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgB4C,MAAM,CAACf,iBAAiB,CAACF;YAEzC,MAAMX,OAAOb,OAAO0C,SAAS,CAAC,MAAMf,OAAO,CAACC,OAAO,CAAC;QACtD;IACF;IAEA7B,SAAS,mBAAmB;QAC1BK,GAAG,yCAAyC;YAC1C,MAAM8B,eAAeC,IAAAA,0BAAe,EAAC;gBAAEQ,SAAS;YAAiB;YACjE9C,gBAAgBuC,IAAI,CAAC1B,iBAAiB,CAACwB;YAEvC,MAAMvB,SAAS,MAAMX,OAAO4C,eAAe,CAAC;YAE5C/B,OAAOF,QAAQG,OAAO,CAACoB;YACvBrB,OAAOhB,gBAAgBuC,IAAI,EAAErB,oBAAoB,CAAC;QACpD;QAEAX,GAAG,qCAAqC;YACtC,MAAMoB,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBuC,IAAI,CAACV,iBAAiB,CAACF;YAEvC,MAAMX,OAAOb,OAAO4C,eAAe,CAAC,MAAMjB,OAAO,CAACC,OAAO,CAAC;QAC5D;IACF;IAEA7B,SAAS,uBAAuB;QAC9BK,GAAG,6CAA6C;YAC9C,MAAMyC,YAA8B;gBAClCC,WAAW;gBACXC,gBAAgB;gBAChBC,kBAAkB;gBAClBC,eAAe;gBACfC,iBAAiB;gBACjBC,qBAAqB;gBACrBC,kBAAkB;gBAClBC,mBAAmB;YACrB;YAEAxD,gBAAgBY,GAAG,CAACC,iBAAiB,CAACmC;YAEtC,MAAMlC,SAAS,MAAMX,OAAOsD,mBAAmB;YAE/CzC,OAAOF,QAAQG,OAAO,CAAC+B;YACvBhC,OAAOhB,gBAAgBY,GAAG,EAAEM,oBAAoB,CAAC;QACnD;QAEAX,GAAG,sCAAsC;YACvC,MAAMoB,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBY,GAAG,CAACiB,iBAAiB,CAACF;YAEtC,MAAMX,OAAOb,OAAOsD,mBAAmB,IAAI3B,OAAO,CAACC,OAAO,CAAC;QAC7D;IACF;IAEA7B,SAAS,oBAAoB;QAC3BK,GAAG,0CAA0C;YAC3C,MAAMmD,SAAS;gBAAC;gBAAG;gBAAG;aAAE;YACxB,MAAMtC,SAAS;YACf,MAAMiB,eAAeC,IAAAA,0BAAe,EAAC;gBAAEqB,SAAS;YAAE;YAClD3D,gBAAgBuC,IAAI,CAAC1B,iBAAiB,CAACwB;YAEvC,MAAMvB,SAAS,MAAMX,OAAOyD,gBAAgB,CAACF,QAAQtC;YAErDJ,OAAOF,QAAQG,OAAO,CAACoB;YACvBrB,OAAOhB,gBAAgBuC,IAAI,EAAErB,oBAAoB,CAAC,wBAAwB;gBAAEwC;gBAAQtC;YAAO;QAC7F;QAEAb,GAAG,mCAAmC;YACpC,MAAMmD,SAAS;gBAAC;gBAAK;aAAI;YACzB,MAAMtC,SAAS;YACf,MAAMO,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBuC,IAAI,CAACV,iBAAiB,CAACF;YAEvC,MAAMX,OAAOb,OAAOyD,gBAAgB,CAACF,QAAQtC,SAASU,OAAO,CAACC,OAAO,CAAC;QACxE;IACF;IAEA7B,SAAS,cAAc;QACrBK,GAAG,mCAAmC;YACpC,MAAMmD,SAAS;gBAAC;gBAAG;gBAAG;aAAE;YACxB,MAAMrB,eAAeC,IAAAA,0BAAe,EAAC;gBAAEuB,SAAS;YAAE;YAClD7D,gBAAgBuC,IAAI,CAAC1B,iBAAiB,CAACwB;YAEvC,MAAMvB,SAAS,MAAMX,OAAO2D,UAAU,CAACJ;YAEvC1C,OAAOF,QAAQG,OAAO,CAACoB;YACvBrB,OAAOhB,gBAAgBuC,IAAI,EAAErB,oBAAoB,CAAC,wBAAwB;gBAAEwC;YAAO;QACrF;QAEAnD,GAAG,mCAAmC;YACpC,MAAMmD,SAAS;gBAAC;gBAAK;aAAI;YACzB,MAAM/B,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBuC,IAAI,CAACV,iBAAiB,CAACF;YAEvC,MAAMX,OAAOb,OAAO2D,UAAU,CAACJ,SAAS5B,OAAO,CAACC,OAAO,CAAC;QAC1D;IACF;AACF;AAEA7B,SAAS,mBAAmB;IAC1B,IAAI6D;IAEJ3D,WAAW;QACT2D,kBAAkB,IAAIC,uBAAe;QACrClE,KAAKQ,aAAa;IACpB;IAEAJ,SAAS,aAAa;QACpBK,GAAG,gDAAgD;YACjD,MAAM0D,aAAiC;gBACrCC,UAAU;gBACVC,UAAU;gBACVC,kBAAkB;gBAClBC,gBAAgB;gBAChBC,aAAa;gBACbC,UAAU;gBACVC,kBAAkB;gBAClBC,iBAAiB;YACnB;YAEAzE,gBAAgBY,GAAG,CAACC,iBAAiB,CAACoD;YAEtC,MAAMnD,SAAS,MAAMiD,gBAAgBW,SAAS;YAE9C1D,OAAOF,QAAQG,OAAO,CAACgD;YACvBjD,OAAOhB,gBAAgBY,GAAG,EAAEM,oBAAoB,CAAC;QACnD;QAEAX,GAAG,kCAAkC;YACnC,MAAMoB,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBY,GAAG,CAACiB,iBAAiB,CAACF;YAEtC,MAAMX,OAAO+C,gBAAgBW,SAAS,IAAI5C,OAAO,CAACC,OAAO,CAAC;QAC5D;IACF;IAEA7B,SAAS,UAAU;QACjBK,GAAG,gDAAgD;YACjD,MAAM8B,eAAeC,IAAAA,0BAAe,EAAC;gBAAEQ,SAAS;YAA6B;YAC7E9C,gBAAgBuC,IAAI,CAAC1B,iBAAiB,CAACwB;YAEvC,MAAMvB,SAAS,MAAMiD,gBAAgBY,MAAM,CAAC;YAE5C3D,OAAOF,QAAQG,OAAO,CAACoB;YACvBrB,OAAOhB,gBAAgBuC,IAAI,EAAErB,oBAAoB,CAAC,0BAA0B;gBAAE0D,OAAO;YAAE;QACzF;QAEArE,GAAG,+BAA+B;YAChC,MAAMoB,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBuC,IAAI,CAACV,iBAAiB,CAACF;YAEvC,MAAMX,OAAO+C,gBAAgBY,MAAM,CAAC,IAAI7C,OAAO,CAACC,OAAO,CAAC;QAC1D;IACF;IAEA7B,SAAS,aAAa;QACpBK,GAAG,qDAAqD;YACtD,MAAM8B,eAAeC,IAAAA,0BAAe,EAAC;gBAAEQ,SAAS;YAAiC;YACjF9C,gBAAgBuC,IAAI,CAAC1B,iBAAiB,CAACwB;YAEvC,MAAMvB,SAAS,MAAMiD,gBAAgBc,SAAS,CAAC;YAE/C7D,OAAOF,QAAQG,OAAO,CAACoB;YACvBrB,OAAOhB,gBAAgBuC,IAAI,EAAErB,oBAAoB,CAAC,6BAA6B;gBAAE0D,OAAO;YAAE;QAC5F;QAEArE,GAAG,kCAAkC;YACnC,MAAMoB,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBuC,IAAI,CAACV,iBAAiB,CAACF;YAEvC,MAAMX,OAAO+C,gBAAgBc,SAAS,CAAC,IAAI/C,OAAO,CAACC,OAAO,CAAC;QAC7D;IACF;IAEA7B,SAAS,gBAAgB;QACvBK,GAAG,iDAAiD;YAClD,MAAMC,WAAW;gBACfC,IAAAA,wBAAa,EAAC;oBAAEC,IAAI;oBAAGoE,gBAAgB;gBAAK;gBAC5CrE,IAAAA,wBAAa,EAAC;oBAAEC,IAAI;oBAAGoE,gBAAgB;gBAAK;aAC7C;YACD9E,gBAAgBY,GAAG,CAACC,iBAAiB,CAACL;YAEtC,MAAMM,SAAS,MAAMiD,gBAAgBgB,YAAY;YAEjD/D,OAAOF,QAAQG,OAAO,CAACT;YACvBQ,OAAOhB,gBAAgBY,GAAG,EAAEM,oBAAoB,CAAC;QACnD;QAEAX,GAAG,gCAAgC;YACjC,MAAMoB,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBY,GAAG,CAACiB,iBAAiB,CAACF;YAEtC,MAAMX,OAAO+C,gBAAgBgB,YAAY,IAAIjD,OAAO,CAACC,OAAO,CAAC;QAC/D;IACF;AACF;AAEA7B,SAAS,aAAa;IACpB,IAAI8E;IAEJ5E,WAAW;QACT4E,YAAY,IAAIC,iBAAS;QACzBnF,KAAKQ,aAAa;IACpB;IAEAJ,SAAS,cAAc;QACrBK,GAAG,mCAAmC;YACpC,MAAM2E,cAAwB;gBAC5B;oBACExE,IAAI;oBACJyE,MAAM;oBACNC,aAAa;oBACbC,aAAa;wBACX;4BAAEC,UAAU;4BAASC,WAAW,CAAC;wBAAQ;wBACzC;4BAAED,UAAU;4BAASC,WAAW,CAAC;wBAAQ;qBAC1C;oBACDrB,UAAU;gBACZ;aACD;YAEDlE,gBAAgBY,GAAG,CAACC,iBAAiB,CAACqE;YAEtC,MAAMpE,SAAS,MAAMkE,UAAUQ,UAAU;YAEzCxE,OAAOF,QAAQG,OAAO,CAACiE;YACvBlE,OAAOhB,gBAAgBY,GAAG,EAAEM,oBAAoB,CAAC;QACnD;QAEAX,GAAG,mCAAmC;YACpC,MAAMoB,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBY,GAAG,CAACiB,iBAAiB,CAACF;YAEtC,MAAMX,OAAOgE,UAAUQ,UAAU,IAAI1D,OAAO,CAACC,OAAO,CAAC;QACvD;IACF;IAEA7B,SAAS,gBAAgB;QACvBK,GAAG,qCAAqC;YACtC,MAAMkF,aAAa;gBACjBN,MAAM;gBACNC,aAAa;gBACbC,aAAa;oBACX;wBAAEC,UAAU;wBAASC,WAAW,CAAC;oBAAQ;oBACzC;wBAAED,UAAU;wBAASC,WAAW,CAAC;oBAAQ;iBAC1C;YACH;YAEA,MAAMlD,eAAeC,IAAAA,0BAAe,EAAC;gBACnC5B,IAAI;gBACJ,GAAG+E,UAAU;gBACbvB,UAAU;YACZ;YACAlE,gBAAgBuC,IAAI,CAAC1B,iBAAiB,CAACwB;YAEvC,MAAMvB,SAAS,MAAMkE,UAAUU,YAAY,CAACD;YAE5CzE,OAAOF,QAAQG,OAAO,CAACoB;YACvBrB,OAAOhB,gBAAgBuC,IAAI,EAAErB,oBAAoB,CAAC,gBAAgBuE;QACpE;QAEAlF,GAAG,qCAAqC;YACtC,MAAMkF,aAAa;gBACjBN,MAAM;gBACNC,aAAa;gBACbC,aAAa,EAAE;YACjB;YAEA,MAAM1D,QAAQ,IAAIC,MAAM;YACxB5B,gBAAgBuC,IAAI,CAACV,iBAAiB,CAACF;YAEvC,MAAMX,OAAOgE,UAAUU,YAAY,CAACD,aAAa3D,OAAO,CAACC,OAAO,CAAC;QACnE;IACF;AACF"}