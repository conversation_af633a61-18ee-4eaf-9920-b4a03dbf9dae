7462afd8e8c81533b7fb750efb99ce39
"use strict";
// Mock Next.js navigation
jest.mock("next/navigation", ()=>({
        usePathname: jest.fn()
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _mainnav = require("../main-nav");
const _navigation = require("next/navigation");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock framer-motion
(0, _testutils.mockFramerMotion)();
describe("MainNav Component", ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
        _navigation.usePathname.mockReturnValue("/dashboard");
    });
    it("renders all navigation items", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {}));
        expect(_testutils.screen.getByText("Dashboard")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Management")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Map View")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Hibernate Pod")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Docking Stations")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Battery Monitor")).toBeInTheDocument();
    });
    it("highlights active navigation item", ()=>{
        _navigation.usePathname.mockReturnValue("/uavs");
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {}));
        const uavLink = _testutils.screen.getByRole("link", {
            name: /uav management/i
        });
        expect(uavLink).toHaveClass("bg-primary", "text-primary-foreground");
    });
    it("shows correct icons for each navigation item", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {}));
        // Check that icons are rendered (they would be Lucide React icons)
        const dashboardLink = _testutils.screen.getByRole("link", {
            name: /dashboard/i
        });
        const uavLink = _testutils.screen.getByRole("link", {
            name: /uav management/i
        });
        expect(dashboardLink).toBeInTheDocument();
        expect(uavLink).toBeInTheDocument();
    });
    it("handles navigation item clicks", ()=>{
        const mockOnNavigate = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {
            onNavigate: mockOnNavigate
        }));
        const dashboardLink = _testutils.screen.getByRole("link", {
            name: /dashboard/i
        });
        _testutils.fireEvent.click(dashboardLink);
        expect(mockOnNavigate).toHaveBeenCalledTimes(1);
    });
    it("applies custom className", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {
            className: "custom-nav"
        }));
        const nav = _testutils.screen.getByRole("navigation");
        expect(nav).toHaveClass("custom-nav");
    });
    it("shows descriptions on hover", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {}));
        const dashboardLink = _testutils.screen.getByRole("link", {
            name: /dashboard/i
        });
        _testutils.fireEvent.mouseEnter(dashboardLink);
        expect(_testutils.screen.getByText("System overview and real-time monitoring")).toBeInTheDocument();
    });
    it("handles keyboard navigation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {}));
        const dashboardLink = _testutils.screen.getByRole("link", {
            name: /dashboard/i
        });
        const uavLink = _testutils.screen.getByRole("link", {
            name: /uav management/i
        });
        dashboardLink.focus();
        expect(dashboardLink).toHaveFocus();
        // Test Tab navigation
        _testutils.fireEvent.keyDown(dashboardLink, {
            key: "Tab"
        });
        uavLink.focus();
        expect(uavLink).toHaveFocus();
    });
    it("maintains accessibility standards", async ()=>{
        const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {}));
        // Check for proper navigation role
        expect(_testutils.screen.getByRole("navigation")).toBeInTheDocument();
        // Check for proper link accessibility
        const links = _testutils.screen.getAllByRole("link");
        links.forEach((link)=>{
            expect(link).toHaveAttribute("href");
        });
        // Run accessibility tests
        await (0, _testutils.runAxeTest)(container);
    });
    it("handles active state for nested routes", ()=>{
        _navigation.usePathname.mockReturnValue("/uavs/details/123");
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {}));
        const uavLink = _testutils.screen.getByRole("link", {
            name: /uav management/i
        });
        expect(uavLink).toHaveClass("bg-primary", "text-primary-foreground");
    });
    it("shows proper link hrefs", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {}));
        expect(_testutils.screen.getByRole("link", {
            name: /dashboard/i
        })).toHaveAttribute("href", "/dashboard");
        expect(_testutils.screen.getByRole("link", {
            name: /uav management/i
        })).toHaveAttribute("href", "/uavs");
        expect(_testutils.screen.getByRole("link", {
            name: /map view/i
        })).toHaveAttribute("href", "/map");
        expect(_testutils.screen.getByRole("link", {
            name: /hibernate pod/i
        })).toHaveAttribute("href", "/hibernate-pod");
    });
});
describe("MobileNav Component", ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
        _navigation.usePathname.mockReturnValue("/dashboard");
    });
    it("renders in grid layout", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MobileNav, {}));
        const nav = _testutils.screen.getByRole("navigation");
        expect(nav).toHaveClass("grid", "grid-cols-2", "gap-2", "p-4");
    });
    it("shows limited navigation items", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MobileNav, {}));
        // Should show first 8 items
        expect(_testutils.screen.getByText("Dashboard")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Management")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Map View")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Hibernate Pod")).toBeInTheDocument();
    });
    it("handles mobile navigation clicks", ()=>{
        const mockOnNavigate = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MobileNav, {
            onNavigate: mockOnNavigate
        }));
        const dashboardLink = _testutils.screen.getByRole("link", {
            name: /dashboard/i
        });
        _testutils.fireEvent.click(dashboardLink);
        expect(mockOnNavigate).toHaveBeenCalledTimes(1);
    });
    it("shows icons and text in column layout", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MobileNav, {}));
        const dashboardLink = _testutils.screen.getByRole("link", {
            name: /dashboard/i
        });
        expect(dashboardLink).toHaveClass("flex", "flex-col", "items-center", "justify-center");
    });
    it("applies active state correctly", ()=>{
        _navigation.usePathname.mockReturnValue("/uavs");
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MobileNav, {}));
        const uavLink = _testutils.screen.getByRole("link", {
            name: /uav management/i
        });
        expect(uavLink).toHaveClass("bg-primary", "text-primary-foreground");
    });
    it("handles animation on interaction", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MobileNav, {}));
        const dashboardLink = _testutils.screen.getByRole("link", {
            name: /dashboard/i
        });
        // Test hover animation
        _testutils.fireEvent.mouseEnter(dashboardLink);
        _testutils.fireEvent.mouseLeave(dashboardLink);
        // Test tap animation
        _testutils.fireEvent.mouseDown(dashboardLink);
        _testutils.fireEvent.mouseUp(dashboardLink);
        expect(dashboardLink).toBeInTheDocument();
    });
});
describe("QuickNav Component", ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
        _navigation.usePathname.mockReturnValue("/dashboard");
    });
    it("renders quick access items", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.QuickNav, {}));
        // Should show first 4 items
        expect(_testutils.screen.getByRole("link", {
            name: /dashboard/i
        })).toBeInTheDocument();
        expect(_testutils.screen.getByRole("link", {
            name: /uav management/i
        })).toBeInTheDocument();
        expect(_testutils.screen.getByRole("link", {
            name: /map view/i
        })).toBeInTheDocument();
        expect(_testutils.screen.getByRole("link", {
            name: /hibernate pod/i
        })).toBeInTheDocument();
    });
    it("renders in horizontal layout", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.QuickNav, {}));
        const nav = _testutils.screen.getByRole("navigation");
        expect(nav).toHaveClass("flex", "space-x-1");
    });
    it("applies custom className", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.QuickNav, {
            className: "custom-quick-nav"
        }));
        const nav = _testutils.screen.getByRole("navigation");
        expect(nav).toHaveClass("custom-quick-nav");
    });
    it("shows only icons in compact format", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.QuickNav, {}));
        const dashboardLink = _testutils.screen.getByRole("link", {
            name: /dashboard/i
        });
        expect(dashboardLink).toHaveClass("p-2");
    });
    it("handles active state for quick nav", ()=>{
        _navigation.usePathname.mockReturnValue("/map");
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.QuickNav, {}));
        const mapLink = _testutils.screen.getByRole("link", {
            name: /map view/i
        });
        expect(mapLink).toHaveClass("bg-primary", "text-primary-foreground");
    });
    it("provides tooltips for quick nav items", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.QuickNav, {}));
        const dashboardLink = _testutils.screen.getByRole("link", {
            name: /dashboard/i
        });
        expect(dashboardLink).toHaveAttribute("title", "Dashboard");
    });
    it("maintains accessibility in compact mode", async ()=>{
        const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.QuickNav, {}));
        // Check for proper link accessibility
        const links = _testutils.screen.getAllByRole("link");
        links.forEach((link)=>{
            expect(link).toHaveAttribute("href");
            expect(link).toHaveAttribute("title");
        });
        // Run accessibility tests
        await (0, _testutils.runAxeTest)(container);
    });
    it("handles responsive behavior", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.QuickNav, {}));
        const nav = _testutils.screen.getByRole("navigation");
        expect(nav).toHaveClass("flex", "space-x-1");
        // Links should be compact
        const links = _testutils.screen.getAllByRole("link");
        links.forEach((link)=>{
            expect(link).toHaveClass("p-2");
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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