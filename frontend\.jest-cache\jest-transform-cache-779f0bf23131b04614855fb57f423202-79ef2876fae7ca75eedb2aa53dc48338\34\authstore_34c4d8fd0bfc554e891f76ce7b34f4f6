1a7ea3c15b0cc585bc6303f1fc7e92ac
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    useAuth: function() {
        return useAuth;
    },
    useAuthStore: function() {
        return useAuthStore;
    },
    usePermissions: function() {
        return usePermissions;
    }
});
const _zustand = require("zustand");
const _middleware = require("zustand/middleware");
const _immer = require("zustand/middleware/immer");
const _authapi = require("../api/auth-api");
const _reacthottoast = require("react-hot-toast");
const useAuthStore = (0, _zustand.create)()((0, _middleware.devtools)((0, _middleware.persist)((0, _middleware.subscribeWithSelector)((0, _immer.immer)((set, get)=>({
        // Initial state
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        // Login
        login: async (credentials)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.login(credentials);
                if (response.success && response.data) {
                    set((state)=>{
                        state.user = response.data.user;
                        state.token = response.data.token;
                        state.refreshToken = response.data.refreshToken;
                        state.isAuthenticated = true;
                        state.isLoading = false;
                        state.error = null;
                    });
                    // Set token in API client
                    _authapi.authApi.setAuthToken(response.data.token);
                    _reacthottoast.toast.success("Login successful");
                    return true;
                } else {
                    throw new Error(response.message || "Login failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Login failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                    state.isAuthenticated = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Logout
        logout: async ()=>{
            try {
                await _authapi.authApi.logout();
            } catch (error) {
                console.error("Logout error:", error);
            } finally{
                set((state)=>{
                    state.user = null;
                    state.token = null;
                    state.refreshToken = null;
                    state.isAuthenticated = false;
                    state.error = null;
                });
                // Clear token from API client
                _authapi.authApi.clearAuthToken();
                _reacthottoast.toast.success("Logged out successfully");
            }
        },
        // Register
        register: async (userData)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.register(userData);
                if (response.success && response.data) {
                    set((state)=>{
                        state.user = response.data.user;
                        state.token = response.data.token;
                        state.refreshToken = response.data.refreshToken;
                        state.isAuthenticated = true;
                        state.isLoading = false;
                    });
                    // Store tokens in localStorage
                    localStorage.setItem("token", response.data.token);
                    localStorage.setItem("refreshToken", response.data.refreshToken);
                    _reacthottoast.toast.success("Registration successful. Welcome!");
                    return true;
                } else {
                    throw new Error(response.message || "Registration failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Registration failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Refresh token
        refreshAuthToken: async ()=>{
            const { refreshToken } = get();
            if (!refreshToken) return false;
            try {
                const response = await _authapi.authApi.refreshToken({
                    refreshToken
                });
                if (response.success && response.data) {
                    set((state)=>{
                        state.token = response.data.token;
                        state.refreshToken = response.data.refreshToken;
                        state.error = null;
                    });
                    // Update token in API client
                    _authapi.authApi.setAuthToken(response.data.token);
                    return true;
                } else {
                    // Refresh failed, logout user
                    get().logout();
                    return false;
                }
            } catch (error) {
                console.error("Token refresh failed:", error);
                get().logout();
                return false;
            }
        },
        // Change password
        changePassword: async (passwords)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.changePassword(passwords);
                if (response.success) {
                    set((state)=>{
                        state.isLoading = false;
                    });
                    _reacthottoast.toast.success("Password changed successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Password change failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Password change failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Update profile
        updateProfile: async (userData)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.updateProfile(userData);
                if (response.success && response.data) {
                    set((state)=>{
                        state.user = response.data;
                        state.isLoading = false;
                    });
                    _reacthottoast.toast.success("Profile updated successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Profile update failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Profile update failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Permission checks
        hasPermission: (check)=>{
            const { user } = get();
            if (!user || !user.permissions) return false;
            return user.permissions.some((permission)=>permission.resource === check.resource && permission.action === check.action);
        },
        hasRole: (roleName)=>{
            const { user } = get();
            if (!user || !user.roles) return false;
            return user.roles.some((role)=>role.name === roleName);
        },
        canAccess: (resource, action)=>{
            return get().hasPermission({
                resource,
                action
            });
        },
        // Session management
        checkSession: async ()=>{
            const { token, refreshToken } = get();
            if (!token) {
                set((state)=>{
                    state.isAuthenticated = false;
                });
                return false;
            }
            try {
                // Check if token is still valid
                const isValid = await _authapi.authApi.validateToken();
                if (isValid) {
                    set((state)=>{
                        state.isAuthenticated = true;
                    });
                    return true;
                } else if (refreshToken) {
                    // Try to refresh token
                    return await get().refreshAuthToken();
                } else {
                    // No valid token or refresh token
                    get().logout();
                    return false;
                }
            } catch (error) {
                console.error("Session check failed:", error);
                get().logout();
                return false;
            }
        },
        // Fetch user profile
        fetchUserProfile: async ()=>{
            try {
                const user = await _authapi.authApi.getUserProfile();
                set((state)=>{
                    state.user = user;
                });
            } catch (error) {
                console.error("Failed to fetch user profile:", error);
            }
        },
        // Update last activity
        updateLastActivity: ()=>{
            const { user } = get();
            if (user) {
                set((state)=>{
                    if (state.user) {
                        state.user.lastLogin = new Date().toISOString();
                    }
                });
            }
        },
        // Clear error
        clearError: ()=>{
            set((state)=>{
                state.error = null;
            });
        },
        // Set loading
        setLoading: (loading)=>{
            set((state)=>{
                state.isLoading = loading;
            });
        }
    }))), {
    name: "auth-store",
    partialize: (state)=>({
            user: state.user,
            token: state.token,
            refreshToken: state.refreshToken,
            isAuthenticated: state.isAuthenticated
        })
}), {
    name: "auth-store"
}));
const useAuth = ()=>useAuthStore((state)=>({
            user: state.user,
            isAuthenticated: state.isAuthenticated,
            isLoading: state.isLoading,
            error: state.error
        }));
const usePermissions = ()=>useAuthStore((state)=>({
            hasPermission: state.hasPermission,
            hasRole: state.hasRole,
            canAccess: state.canAccess
        }));
// Auto-refresh token before expiration
let refreshInterval = null;
useAuthStore.subscribe((state)=>state.token, (token)=>{
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
    if (token) {
        // Refresh token every 50 minutes (assuming 60-minute expiration)
        refreshInterval = setInterval(()=>{
            useAuthStore.getState().refreshAuthToken();
        }, 50 * 60 * 1000);
    }
});

//# sourceMappingURL=data:application/json;base64,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