45368f1237ebe5908c3fed85fd77751c
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    useAuth: function() {
        return useAuth;
    },
    useAuthStore: function() {
        return useAuthStore;
    },
    usePermissions: function() {
        return usePermissions;
    }
});
const _zustand = require("zustand");
const _middleware = require("zustand/middleware");
const _immer = require("zustand/middleware/immer");
const _authapi = require("../api/auth-api");
const _reacthottoast = require("react-hot-toast");
const useAuthStore = (0, _zustand.create)()((0, _middleware.devtools)((0, _middleware.persist)((0, _middleware.subscribeWithSelector)((0, _immer.immer)((set, get)=>({
        // Initial state
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        // Login
        login: async (credentials)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.login(credentials);
                if (response.success && response.data) {
                    set((state)=>{
                        state.user = response.data.user;
                        state.token = response.data.token;
                        state.refreshToken = response.data.refreshToken;
                        state.isAuthenticated = true;
                        state.isLoading = false;
                        state.error = null;
                    });
                    // Set token in API client
                    _authapi.authApi.setAuthToken(response.data.token);
                    _reacthottoast.toast.success("Login successful");
                    return true;
                } else {
                    throw new Error(response.message || "Login failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Login failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                    state.isAuthenticated = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Logout
        logout: async ()=>{
            try {
                await _authapi.authApi.logout();
            } catch (error) {
                console.error("Logout error:", error);
            } finally{
                set((state)=>{
                    state.user = null;
                    state.token = null;
                    state.refreshToken = null;
                    state.isAuthenticated = false;
                    state.error = null;
                });
                // Clear token from API client
                _authapi.authApi.clearAuthToken();
                _reacthottoast.toast.success("Logged out successfully");
            }
        },
        // Register
        register: async (userData)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.register(userData);
                if (response.success) {
                    set((state)=>{
                        state.isLoading = false;
                    });
                    _reacthottoast.toast.success("Registration successful. Please log in.");
                    return true;
                } else {
                    throw new Error(response.message || "Registration failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Registration failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Refresh token
        refreshToken: async ()=>{
            const { refreshToken } = get();
            if (!refreshToken) return false;
            try {
                const response = await _authapi.authApi.refreshToken({
                    refreshToken
                });
                if (response.success && response.data) {
                    set((state)=>{
                        state.token = response.data.token;
                        state.refreshToken = response.data.refreshToken;
                        state.error = null;
                    });
                    // Update token in API client
                    _authapi.authApi.setAuthToken(response.data.token);
                    return true;
                } else {
                    // Refresh failed, logout user
                    get().logout();
                    return false;
                }
            } catch (error) {
                console.error("Token refresh failed:", error);
                get().logout();
                return false;
            }
        },
        // Change password
        changePassword: async (passwords)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.changePassword(passwords);
                if (response.success) {
                    set((state)=>{
                        state.isLoading = false;
                    });
                    _reacthottoast.toast.success("Password changed successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Password change failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Password change failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Update profile
        updateProfile: async (userData)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.updateProfile(userData);
                if (response.success && response.data) {
                    set((state)=>{
                        state.user = response.data;
                        state.isLoading = false;
                    });
                    _reacthottoast.toast.success("Profile updated successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Profile update failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Profile update failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Permission checks
        hasPermission: (check)=>{
            const { user } = get();
            if (!user || !user.permissions) return false;
            return user.permissions.some((permission)=>permission.resource === check.resource && permission.action === check.action);
        },
        hasRole: (roleName)=>{
            const { user } = get();
            if (!user || !user.roles) return false;
            return user.roles.some((role)=>role.name === roleName);
        },
        canAccess: (resource, action)=>{
            return get().hasPermission({
                resource,
                action
            });
        },
        // Session management
        checkSession: async ()=>{
            const { token, refreshToken } = get();
            if (!token) {
                set((state)=>{
                    state.isAuthenticated = false;
                });
                return false;
            }
            try {
                // Check if token is still valid
                const isValid = await _authapi.authApi.validateToken();
                if (isValid) {
                    set((state)=>{
                        state.isAuthenticated = true;
                    });
                    return true;
                } else if (refreshToken) {
                    // Try to refresh token
                    return await get().refreshToken();
                } else {
                    // No valid token or refresh token
                    get().logout();
                    return false;
                }
            } catch (error) {
                console.error("Session check failed:", error);
                get().logout();
                return false;
            }
        },
        // Fetch user profile
        fetchUserProfile: async ()=>{
            try {
                const user = await _authapi.authApi.getUserProfile();
                set((state)=>{
                    state.user = user;
                });
            } catch (error) {
                console.error("Failed to fetch user profile:", error);
            }
        },
        // Update last activity
        updateLastActivity: ()=>{
            const { user } = get();
            if (user) {
                set((state)=>{
                    if (state.user) {
                        state.user.lastLogin = new Date().toISOString();
                    }
                });
            }
        },
        // Clear error
        clearError: ()=>{
            set((state)=>{
                state.error = null;
            });
        },
        // Set loading
        setLoading: (loading)=>{
            set((state)=>{
                state.isLoading = loading;
            });
        }
    }))), {
    name: "auth-store",
    partialize: (state)=>({
            user: state.user,
            token: state.token,
            refreshToken: state.refreshToken,
            isAuthenticated: state.isAuthenticated
        })
}), {
    name: "auth-store"
}));
const useAuth = ()=>useAuthStore((state)=>({
            user: state.user,
            isAuthenticated: state.isAuthenticated,
            isLoading: state.isLoading,
            error: state.error
        }));
const usePermissions = ()=>useAuthStore((state)=>({
            hasPermission: state.hasPermission,
            hasRole: state.hasRole,
            canAccess: state.canAccess
        }));
// Auto-refresh token before expiration
let refreshInterval = null;
useAuthStore.subscribe((state)=>state.token, (token)=>{
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
    if (token) {
        // Refresh token every 50 minutes (assuming 60-minute expiration)
        refreshInterval = setInterval(()=>{
            useAuthStore.getState().refreshToken();
        }, 50 * 60 * 1000);
    }
});

//# sourceMappingURL=data:application/json;base64,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