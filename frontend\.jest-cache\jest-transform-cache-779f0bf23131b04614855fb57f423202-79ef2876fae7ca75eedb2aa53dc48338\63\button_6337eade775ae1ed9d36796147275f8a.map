{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        uav: \"bg-uav-primary text-white hover:bg-uav-primary/90\",\n        success: \"bg-green-600 text-white hover:bg-green-700\",\n        warning: \"bg-yellow-600 text-white hover:bg-yellow-700\",\n        danger: \"bg-red-600 text-white hover:bg-red-700\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": ["<PERSON><PERSON>", "buttonVariants", "cva", "variants", "variant", "default", "destructive", "outline", "secondary", "ghost", "link", "uav", "success", "warning", "danger", "size", "sm", "lg", "icon", "defaultVariants", "React", "forwardRef", "className", "<PERSON><PERSON><PERSON><PERSON>", "props", "ref", "Comp", "Slot", "cn", "displayName"], "mappings": ";;;;;;;;;;;IA2DSA,MAAM;eAANA;;IAAQC,cAAc;eAAdA;;;;+DA3DM;2BACF;wCACkB;uBAEpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnB,MAAMA,iBAAiBC,IAAAA,2BAAG,EACxB,0RACA;IACEC,UAAU;QACRC,SAAS;YACPC,SAAS;YACTC,aACE;YACFC,SACE;YACFC,WACE;YACFC,OAAO;YACPC,MAAM;YACNC,KAAK;YACLC,SAAS;YACTC,SAAS;YACTC,QAAQ;QACV;QACAC,MAAM;YACJV,SAAS;YACTW,IAAI;YACJC,IAAI;YACJC,MAAM;QACR;IACF;IACAC,iBAAiB;QACff,SAAS;QACTW,MAAM;IACR;AACF;AASF,MAAMf,uBAASoB,OAAMC,UAAU,CAC7B,CAAC,EAAEC,SAAS,EAAElB,OAAO,EAAEW,IAAI,EAAEQ,UAAU,KAAK,EAAE,GAAGC,OAAO,EAAEC;IACxD,MAAMC,OAAOH,UAAUI,eAAI,GAAG;IAC9B,qBACE,qBAACD;QACCJ,WAAWM,IAAAA,SAAE,EAAC3B,eAAe;YAAEG;YAASW;YAAMO;QAAU;QACxDG,KAAKA;QACJ,GAAGD,KAAK;;AAGf;AAEFxB,OAAO6B,WAAW,GAAG"}