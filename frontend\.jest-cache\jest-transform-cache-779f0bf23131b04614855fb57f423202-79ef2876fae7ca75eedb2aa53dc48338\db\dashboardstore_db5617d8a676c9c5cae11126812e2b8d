33e4e6b2d61185b1f5205e56a1b92f46
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    useAlerts: function() {
        return useAlerts;
    },
    useBatteryStats: function() {
        return useBatteryStats;
    },
    useChartData: function() {
        return useChartData;
    },
    useConnectionStatus: function() {
        return useConnectionStatus;
    },
    useCriticalAlerts: function() {
        return useCriticalAlerts;
    },
    useDashboardMetrics: function() {
        return useDashboardMetrics;
    },
    useDashboardStore: function() {
        return useDashboardStore;
    },
    useFlightActivity: function() {
        return useFlightActivity;
    },
    useHibernatePodMetrics: function() {
        return useHibernatePodMetrics;
    },
    useRecentLocationUpdates: function() {
        return useRecentLocationUpdates;
    },
    useUnacknowledgedAlerts: function() {
        return useUnacknowledgedAlerts;
    }
});
const _zustand = require("zustand");
const _middleware = require("zustand/middleware");
const _immer = require("zustand/middleware/immer");
const useDashboardStore = (0, _zustand.create)()((0, _middleware.devtools)((0, _middleware.subscribeWithSelector)((0, _immer.immer)((set, get)=>({
        // Initial state
        metrics: null,
        flightActivity: null,
        batteryStats: null,
        hibernatePodMetrics: null,
        chartData: null,
        alerts: [],
        recentLocationUpdates: [],
        isConnected: false,
        lastUpdate: null,
        connectionError: null,
        selectedTimeRange: "24h",
        autoRefresh: true,
        refreshInterval: 30,
        showAlerts: true,
        // Update metrics
        updateMetrics: (metrics)=>{
            set((state)=>{
                state.metrics = metrics;
                state.lastUpdate = new Date().toISOString();
            });
        },
        // Update flight activity
        updateFlightActivity: (activity)=>{
            set((state)=>{
                state.flightActivity = activity;
            });
        },
        // Update battery statistics
        updateBatteryStats: (stats)=>{
            set((state)=>{
                state.batteryStats = stats;
            });
        },
        // Update hibernate pod metrics
        updateHibernatePodMetrics: (metrics)=>{
            set((state)=>{
                state.hibernatePodMetrics = metrics;
            });
        },
        // Update chart data
        updateChartData: (data)=>{
            set((state)=>{
                if (state.chartData) {
                    state.chartData = {
                        ...state.chartData,
                        ...data
                    };
                } else {
                    state.chartData = data;
                }
            });
        },
        // Add alert
        addAlert: (alert)=>{
            set((state)=>{
                // Avoid duplicates
                const exists = state.alerts.some((a)=>a.id === alert.id);
                if (!exists) {
                    state.alerts.unshift(alert);
                    // Keep only last 50 alerts
                    if (state.alerts.length > 50) {
                        state.alerts = state.alerts.slice(0, 50);
                    }
                }
            });
        },
        // Remove alert
        removeAlert: (alertId)=>{
            set((state)=>{
                state.alerts = state.alerts.filter((a)=>a.id !== alertId);
            });
        },
        // Acknowledge alert
        acknowledgeAlert: (alertId)=>{
            set((state)=>{
                const alert = state.alerts.find((a)=>a.id === alertId);
                if (alert) {
                    alert.acknowledged = true;
                }
            });
        },
        // Clear all alerts
        clearAlerts: ()=>{
            set((state)=>{
                state.alerts = [];
            });
        },
        // Add location update
        addLocationUpdate: (update)=>{
            set((state)=>{
                state.recentLocationUpdates.unshift(update);
                // Keep only last 100 updates
                if (state.recentLocationUpdates.length > 100) {
                    state.recentLocationUpdates = state.recentLocationUpdates.slice(0, 100);
                }
            });
        },
        // Set connection status
        setConnectionStatus: (connected, error)=>{
            set((state)=>{
                state.isConnected = connected;
                state.connectionError = error || null;
                if (connected) {
                    state.lastUpdate = new Date().toISOString();
                }
            });
        },
        // Update last update timestamp
        updateLastUpdate: ()=>{
            set((state)=>{
                state.lastUpdate = new Date().toISOString();
            });
        },
        // Set time range
        setTimeRange: (range)=>{
            set((state)=>{
                state.selectedTimeRange = range;
            });
            // Trigger data refresh with new time range
            get().refreshData();
        },
        // Set auto refresh
        setAutoRefresh: (enabled)=>{
            set((state)=>{
                state.autoRefresh = enabled;
            });
        },
        // Set refresh interval
        setRefreshInterval: (interval)=>{
            set((state)=>{
                state.refreshInterval = interval;
            });
        },
        // Set show alerts
        setShowAlerts: (show)=>{
            set((state)=>{
                state.showAlerts = show;
            });
        },
        // Fetch dashboard data
        fetchDashboardData: async ()=>{
            try {
                // This would typically call multiple API endpoints
                // For now, we'll simulate the data structure
                const response = await fetch("/api/analytics/dashboard");
                if (response.ok) {
                    const data = await response.json();
                    set((state)=>{
                        if (data.metrics) state.metrics = data.metrics;
                        if (data.flightActivity) state.flightActivity = data.flightActivity;
                        if (data.batteryStats) state.batteryStats = data.batteryStats;
                        if (data.hibernatePodMetrics) state.hibernatePodMetrics = data.hibernatePodMetrics;
                        if (data.chartData) state.chartData = data.chartData;
                        state.lastUpdate = new Date().toISOString();
                        state.connectionError = null;
                    });
                }
            } catch (error) {
                set((state)=>{
                    state.connectionError = error instanceof Error ? error.message : "Failed to fetch dashboard data";
                });
            }
        },
        // Refresh data
        refreshData: async ()=>{
            await get().fetchDashboardData();
        }
    }))), {
    name: "dashboard-store"
}));
const useDashboardMetrics = ()=>useDashboardStore((state)=>state.metrics);
const useFlightActivity = ()=>useDashboardStore((state)=>state.flightActivity);
const useBatteryStats = ()=>useDashboardStore((state)=>state.batteryStats);
const useHibernatePodMetrics = ()=>useDashboardStore((state)=>state.hibernatePodMetrics);
const useChartData = ()=>useDashboardStore((state)=>state.chartData);
const useAlerts = ()=>useDashboardStore((state)=>state.alerts);
const useConnectionStatus = ()=>useDashboardStore((state)=>({
            isConnected: state.isConnected,
            lastUpdate: state.lastUpdate,
            error: state.connectionError
        }));
const useUnacknowledgedAlerts = ()=>useDashboardStore((state)=>state.alerts.filter((alert)=>!alert.acknowledged));
const useCriticalAlerts = ()=>useDashboardStore((state)=>state.alerts.filter((alert)=>alert.type === "CRITICAL" || alert.type === "ERROR"));
const useRecentLocationUpdates = ()=>useDashboardStore((state)=>state.recentLocationUpdates.slice(0, 10));

//# sourceMappingURL=data:application/json;base64,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