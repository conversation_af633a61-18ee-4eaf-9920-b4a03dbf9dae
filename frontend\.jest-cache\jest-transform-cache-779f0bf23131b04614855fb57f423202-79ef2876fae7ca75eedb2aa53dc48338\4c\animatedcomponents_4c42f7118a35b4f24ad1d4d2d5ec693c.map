{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\animated-components.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion, AnimatePresence, HTMLMotionProps } from 'framer-motion'\nimport { cn } from '@/lib/utils'\nimport {\n  pageVariants,\n  cardVariants,\n  staggerContainer,\n  staggerItem,\n  modalVariants,\n  buttonVariants,\n  fadeVariants,\n  slideVariants,\n  getAnimationVariants,\n  getTransition,\n} from '@/lib/animations'\n\n// Animated Page Wrapper\ninterface AnimatedPageProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function AnimatedPage({ children, className, ...props }: AnimatedPageProps) {\n  return (\n    <motion.div\n      variants={getAnimationVariants(pageVariants)}\n      initial=\"initial\"\n      animate=\"animate\"\n      exit=\"exit\"\n      className={cn('w-full', className)}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Animated Card\ninterface AnimatedCardProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n  hover?: boolean\n  tap?: boolean\n}\n\nexport function AnimatedCard({ \n  children, \n  className, \n  hover = true, \n  tap = true, \n  ...props \n}: AnimatedCardProps) {\n  return (\n    <motion.div\n      variants={getAnimationVariants(cardVariants)}\n      initial=\"hidden\"\n      animate=\"visible\"\n      whileHover={hover ? \"hover\" : undefined}\n      whileTap={tap ? \"tap\" : undefined}\n      className={cn('cursor-pointer', className)}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Stagger Container\ninterface StaggerContainerProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n  staggerDelay?: number\n}\n\nexport function StaggerContainer({ \n  children, \n  className, \n  staggerDelay = 0.1,\n  ...props \n}: StaggerContainerProps) {\n  const variants = {\n    ...staggerContainer,\n    visible: {\n      ...staggerContainer.visible,\n      transition: {\n        staggerChildren: staggerDelay,\n        delayChildren: 0.1,\n      },\n    },\n  }\n\n  return (\n    <motion.div\n      variants={getAnimationVariants(variants)}\n      initial=\"hidden\"\n      animate=\"visible\"\n      className={className}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Stagger Item\ninterface StaggerItemProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function StaggerItem({ children, className, ...props }: StaggerItemProps) {\n  return (\n    <motion.div\n      variants={getAnimationVariants(staggerItem)}\n      className={className}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Animated Modal/Dialog\ninterface AnimatedModalProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n  isOpen: boolean\n}\n\nexport function AnimatedModal({ children, className, isOpen, ...props }: AnimatedModalProps) {\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <motion.div\n          variants={getAnimationVariants(modalVariants)}\n          initial=\"hidden\"\n          animate=\"visible\"\n          exit=\"exit\"\n          className={className}\n          {...props}\n        >\n          {children}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  )\n}\n\n// Animated Button\ninterface AnimatedButtonProps extends HTMLMotionProps<'button'> {\n  children: React.ReactNode\n  className?: string\n  disabled?: boolean\n}\n\nexport function AnimatedButton({ \n  children, \n  className, \n  disabled = false, \n  ...props \n}: AnimatedButtonProps) {\n  return (\n    <motion.button\n      variants={getAnimationVariants(buttonVariants)}\n      initial=\"rest\"\n      whileHover={!disabled ? \"hover\" : undefined}\n      whileTap={!disabled ? \"tap\" : undefined}\n      className={className}\n      disabled={disabled}\n      {...props}\n    >\n      {children}\n    </motion.button>\n  )\n}\n\n// Fade In/Out\ninterface FadeProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n  show: boolean\n}\n\nexport function Fade({ children, className, show, ...props }: FadeProps) {\n  return (\n    <AnimatePresence>\n      {show && (\n        <motion.div\n          variants={getAnimationVariants(fadeVariants)}\n          initial=\"hidden\"\n          animate=\"visible\"\n          exit=\"exit\"\n          className={className}\n          {...props}\n        >\n          {children}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  )\n}\n\n// Slide animations\ninterface SlideProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n  direction: 'up' | 'down' | 'left' | 'right'\n  show: boolean\n}\n\nexport function Slide({ children, className, direction, show, ...props }: SlideProps) {\n  return (\n    <AnimatePresence>\n      {show && (\n        <motion.div\n          variants={getAnimationVariants(slideVariants[direction])}\n          initial=\"hidden\"\n          animate=\"visible\"\n          exit=\"exit\"\n          className={className}\n          {...props}\n        >\n          {children}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  )\n}\n\n// Loading Spinner\ninterface AnimatedSpinnerProps extends HTMLMotionProps<'div'> {\n  className?: string\n  size?: number\n}\n\nexport function AnimatedSpinner({ className, size = 24, ...props }: AnimatedSpinnerProps) {\n  return (\n    <motion.div\n      animate={{ rotate: 360 }}\n      transition={{\n        duration: 1,\n        repeat: Infinity,\n        ease: 'linear',\n      }}\n      className={cn('inline-block', className)}\n      style={{ width: size, height: size }}\n      {...props}\n    >\n      <svg\n        className=\"w-full h-full\"\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n      >\n        <circle\n          cx=\"12\"\n          cy=\"12\"\n          r=\"10\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeDasharray=\"32\"\n          strokeDashoffset=\"32\"\n          className=\"opacity-25\"\n        />\n        <circle\n          cx=\"12\"\n          cy=\"12\"\n          r=\"10\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeDasharray=\"32\"\n          strokeDashoffset=\"24\"\n        />\n      </svg>\n    </motion.div>\n  )\n}\n\n// Scale on Hover\ninterface ScaleOnHoverProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n  scale?: number\n}\n\nexport function ScaleOnHover({ \n  children, \n  className, \n  scale = 1.05, \n  ...props \n}: ScaleOnHoverProps) {\n  return (\n    <motion.div\n      whileHover={{ scale }}\n      whileTap={{ scale: 0.95 }}\n      transition={getTransition({ duration: 0.2 })}\n      className={className}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Pulse animation\ninterface PulseProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function Pulse({ children, className, ...props }: PulseProps) {\n  return (\n    <motion.div\n      animate={{\n        scale: [1, 1.05, 1],\n        opacity: [1, 0.8, 1],\n      }}\n      transition={{\n        duration: 2,\n        repeat: Infinity,\n        ease: 'easeInOut',\n      }}\n      className={className}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Bounce animation\ninterface BounceProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n  trigger?: boolean\n}\n\nexport function Bounce({ children, className, trigger = false, ...props }: BounceProps) {\n  return (\n    <motion.div\n      animate={trigger ? {\n        y: [0, -10, 0],\n      } : {}}\n      transition={{\n        duration: 0.5,\n        ease: 'easeOut',\n      }}\n      className={className}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Shake animation\ninterface ShakeProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n  trigger?: boolean\n}\n\nexport function Shake({ children, className, trigger = false, ...props }: ShakeProps) {\n  return (\n    <motion.div\n      animate={trigger ? {\n        x: [0, -10, 10, -10, 10, 0],\n      } : {}}\n      transition={{\n        duration: 0.5,\n        ease: 'easeInOut',\n      }}\n      className={className}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Floating animation\ninterface FloatProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function Float({ children, className, ...props }: FloatProps) {\n  return (\n    <motion.div\n      animate={{\n        y: [0, -5, 0],\n      }}\n      transition={{\n        duration: 3,\n        repeat: Infinity,\n        ease: 'easeInOut',\n      }}\n      className={className}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Glow effect\ninterface GlowProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n  color?: string\n}\n\nexport function Glow({ children, className, color = 'blue', ...props }: GlowProps) {\n  return (\n    <motion.div\n      whileHover={{\n        boxShadow: `0 0 20px rgba(59, 130, 246, 0.5)`,\n      }}\n      transition={{ duration: 0.3 }}\n      className={className}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Magnetic effect\ninterface MagneticProps extends HTMLMotionProps<'div'> {\n  children: React.ReactNode\n  className?: string\n  strength?: number\n}\n\nexport function Magnetic({ children, className, strength = 0.3, ...props }: MagneticProps) {\n  const [mousePosition, setMousePosition] = React.useState({ x: 0, y: 0 })\n  const [isHovered, setIsHovered] = React.useState(false)\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    const rect = e.currentTarget.getBoundingClientRect()\n    const centerX = rect.left + rect.width / 2\n    const centerY = rect.top + rect.height / 2\n\n    setMousePosition({\n      x: (e.clientX - centerX) * strength,\n      y: (e.clientY - centerY) * strength,\n    })\n  }\n\n  return (\n    <motion.div\n      animate={isHovered ? {\n        x: mousePosition.x,\n        y: mousePosition.y,\n      } : {\n        x: 0,\n        y: 0,\n      }}\n      transition={{ type: 'spring', stiffness: 300, damping: 30 }}\n      onMouseMove={handleMouseMove}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n      className={className}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n"], "names": ["AnimatedButton", "AnimatedCard", "AnimatedModal", "AnimatedPage", "AnimatedSpinner", "<PERSON><PERSON><PERSON>", "Fade", "Float", "Glow", "Magnetic", "Pulse", "ScaleOnHover", "Shake", "Slide", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "StaggerItem", "children", "className", "props", "motion", "div", "variants", "getAnimationVariants", "pageVariants", "initial", "animate", "exit", "cn", "hover", "tap", "cardVariants", "whileHover", "undefined", "whileTap", "stagger<PERSON><PERSON><PERSON>", "stagger<PERSON><PERSON><PERSON>", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "staggerItem", "isOpen", "AnimatePresence", "modalVariants", "disabled", "button", "buttonVariants", "show", "fadeVariants", "direction", "slideVariants", "size", "rotate", "duration", "repeat", "Infinity", "ease", "style", "width", "height", "svg", "viewBox", "fill", "xmlns", "circle", "cx", "cy", "r", "stroke", "strokeWidth", "strokeLinecap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "scale", "getTransition", "opacity", "trigger", "y", "x", "color", "boxShadow", "strength", "mousePosition", "setMousePosition", "React", "useState", "isHovered", "setIsHovered", "handleMouseMove", "e", "rect", "currentTarget", "getBoundingClientRect", "centerX", "left", "centerY", "top", "clientX", "clientY", "type", "stiffness", "damping", "onMouseMove", "onMouseEnter", "onMouseLeave"], "mappings": "AAAA;;;;;;;;;;;;IA6JgBA,cAAc;eAAdA;;IA9GAC,YAAY;eAAZA;;IAoFAC,aAAa;eAAbA;;IA3GAC,YAAY;eAAZA;;IAqNAC,eAAe;eAAfA;;IAwGAC,MAAM;eAANA;;IA5JAC,IAAI;eAAJA;;IA6MAC,KAAK;eAALA;;IA0BAC,IAAI;eAAJA;;IAsBAC,QAAQ;eAARA;;IA5HAC,KAAK;eAALA;;IAzBAC,YAAY;eAAZA;;IA6EAC,KAAK;eAALA;;IA1JAC,KAAK;eAALA;;IAxIAC,gBAAgB;eAAhBA;;IAoCAC,WAAW;eAAXA;;;;8DA9GE;8BACuC;uBACtC;4BAYZ;;;;;;AAQA,SAASZ,aAAa,EAAEa,QAAQ,EAAEC,SAAS,EAAE,GAAGC,OAA0B;IAC/E,qBACE,qBAACC,oBAAM,CAACC,GAAG;QACTC,UAAUC,IAAAA,gCAAoB,EAACC,wBAAY;QAC3CC,SAAQ;QACRC,SAAQ;QACRC,MAAK;QACLT,WAAWU,IAAAA,SAAE,EAAC,UAAUV;QACvB,GAAGC,KAAK;kBAERF;;AAGP;AAUO,SAASf,aAAa,EAC3Be,QAAQ,EACRC,SAAS,EACTW,QAAQ,IAAI,EACZC,MAAM,IAAI,EACV,GAAGX,OACe;IAClB,qBACE,qBAACC,oBAAM,CAACC,GAAG;QACTC,UAAUC,IAAAA,gCAAoB,EAACQ,wBAAY;QAC3CN,SAAQ;QACRC,SAAQ;QACRM,YAAYH,QAAQ,UAAUI;QAC9BC,UAAUJ,MAAM,QAAQG;QACxBf,WAAWU,IAAAA,SAAE,EAAC,kBAAkBV;QAC/B,GAAGC,KAAK;kBAERF;;AAGP;AASO,SAASF,iBAAiB,EAC/BE,QAAQ,EACRC,SAAS,EACTiB,eAAe,GAAG,EAClB,GAAGhB,OACmB;IACtB,MAAMG,WAAW;QACf,GAAGc,4BAAgB;QACnBC,SAAS;YACP,GAAGD,4BAAgB,CAACC,OAAO;YAC3BC,YAAY;gBACVC,iBAAiBJ;gBACjBK,eAAe;YACjB;QACF;IACF;IAEA,qBACE,qBAACpB,oBAAM,CAACC,GAAG;QACTC,UAAUC,IAAAA,gCAAoB,EAACD;QAC/BG,SAAQ;QACRC,SAAQ;QACRR,WAAWA;QACV,GAAGC,KAAK;kBAERF;;AAGP;AAQO,SAASD,YAAY,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGC,OAAyB;IAC7E,qBACE,qBAACC,oBAAM,CAACC,GAAG;QACTC,UAAUC,IAAAA,gCAAoB,EAACkB,uBAAW;QAC1CvB,WAAWA;QACV,GAAGC,KAAK;kBAERF;;AAGP;AASO,SAASd,cAAc,EAAEc,QAAQ,EAAEC,SAAS,EAAEwB,MAAM,EAAE,GAAGvB,OAA2B;IACzF,qBACE,qBAACwB,6BAAe;kBACbD,wBACC,qBAACtB,oBAAM,CAACC,GAAG;YACTC,UAAUC,IAAAA,gCAAoB,EAACqB,yBAAa;YAC5CnB,SAAQ;YACRC,SAAQ;YACRC,MAAK;YACLT,WAAWA;YACV,GAAGC,KAAK;sBAERF;;;AAKX;AASO,SAAShB,eAAe,EAC7BgB,QAAQ,EACRC,SAAS,EACT2B,WAAW,KAAK,EAChB,GAAG1B,OACiB;IACpB,qBACE,qBAACC,oBAAM,CAAC0B,MAAM;QACZxB,UAAUC,IAAAA,gCAAoB,EAACwB,0BAAc;QAC7CtB,SAAQ;QACRO,YAAY,CAACa,WAAW,UAAUZ;QAClCC,UAAU,CAACW,WAAW,QAAQZ;QAC9Bf,WAAWA;QACX2B,UAAUA;QACT,GAAG1B,KAAK;kBAERF;;AAGP;AASO,SAASV,KAAK,EAAEU,QAAQ,EAAEC,SAAS,EAAE8B,IAAI,EAAE,GAAG7B,OAAkB;IACrE,qBACE,qBAACwB,6BAAe;kBACbK,sBACC,qBAAC5B,oBAAM,CAACC,GAAG;YACTC,UAAUC,IAAAA,gCAAoB,EAAC0B,wBAAY;YAC3CxB,SAAQ;YACRC,SAAQ;YACRC,MAAK;YACLT,WAAWA;YACV,GAAGC,KAAK;sBAERF;;;AAKX;AAUO,SAASH,MAAM,EAAEG,QAAQ,EAAEC,SAAS,EAAEgC,SAAS,EAAEF,IAAI,EAAE,GAAG7B,OAAmB;IAClF,qBACE,qBAACwB,6BAAe;kBACbK,sBACC,qBAAC5B,oBAAM,CAACC,GAAG;YACTC,UAAUC,IAAAA,gCAAoB,EAAC4B,yBAAa,CAACD,UAAU;YACvDzB,SAAQ;YACRC,SAAQ;YACRC,MAAK;YACLT,WAAWA;YACV,GAAGC,KAAK;sBAERF;;;AAKX;AAQO,SAASZ,gBAAgB,EAAEa,SAAS,EAAEkC,OAAO,EAAE,EAAE,GAAGjC,OAA6B;IACtF,qBACE,qBAACC,oBAAM,CAACC,GAAG;QACTK,SAAS;YAAE2B,QAAQ;QAAI;QACvBf,YAAY;YACVgB,UAAU;YACVC,QAAQC;YACRC,MAAM;QACR;QACAvC,WAAWU,IAAAA,SAAE,EAAC,gBAAgBV;QAC9BwC,OAAO;YAAEC,OAAOP;YAAMQ,QAAQR;QAAK;QAClC,GAAGjC,KAAK;kBAET,cAAA,sBAAC0C;YACC3C,WAAU;YACV4C,SAAQ;YACRC,MAAK;YACLC,OAAM;;8BAEN,qBAACC;oBACCC,IAAG;oBACHC,IAAG;oBACHC,GAAE;oBACFC,QAAO;oBACPC,aAAY;oBACZC,eAAc;oBACdC,iBAAgB;oBAChBC,kBAAiB;oBACjBvD,WAAU;;8BAEZ,qBAAC+C;oBACCC,IAAG;oBACHC,IAAG;oBACHC,GAAE;oBACFC,QAAO;oBACPC,aAAY;oBACZC,eAAc;oBACdC,iBAAgB;oBAChBC,kBAAiB;;;;;AAK3B;AASO,SAAS7D,aAAa,EAC3BK,QAAQ,EACRC,SAAS,EACTwD,QAAQ,IAAI,EACZ,GAAGvD,OACe;IAClB,qBACE,qBAACC,oBAAM,CAACC,GAAG;QACTW,YAAY;YAAE0C;QAAM;QACpBxC,UAAU;YAAEwC,OAAO;QAAK;QACxBpC,YAAYqC,IAAAA,yBAAa,EAAC;YAAErB,UAAU;QAAI;QAC1CpC,WAAWA;QACV,GAAGC,KAAK;kBAERF;;AAGP;AAQO,SAASN,MAAM,EAAEM,QAAQ,EAAEC,SAAS,EAAE,GAAGC,OAAmB;IACjE,qBACE,qBAACC,oBAAM,CAACC,GAAG;QACTK,SAAS;YACPgD,OAAO;gBAAC;gBAAG;gBAAM;aAAE;YACnBE,SAAS;gBAAC;gBAAG;gBAAK;aAAE;QACtB;QACAtC,YAAY;YACVgB,UAAU;YACVC,QAAQC;YACRC,MAAM;QACR;QACAvC,WAAWA;QACV,GAAGC,KAAK;kBAERF;;AAGP;AASO,SAASX,OAAO,EAAEW,QAAQ,EAAEC,SAAS,EAAE2D,UAAU,KAAK,EAAE,GAAG1D,OAAoB;IACpF,qBACE,qBAACC,oBAAM,CAACC,GAAG;QACTK,SAASmD,UAAU;YACjBC,GAAG;gBAAC;gBAAG,CAAC;gBAAI;aAAE;QAChB,IAAI,CAAC;QACLxC,YAAY;YACVgB,UAAU;YACVG,MAAM;QACR;QACAvC,WAAWA;QACV,GAAGC,KAAK;kBAERF;;AAGP;AASO,SAASJ,MAAM,EAAEI,QAAQ,EAAEC,SAAS,EAAE2D,UAAU,KAAK,EAAE,GAAG1D,OAAmB;IAClF,qBACE,qBAACC,oBAAM,CAACC,GAAG;QACTK,SAASmD,UAAU;YACjBE,GAAG;gBAAC;gBAAG,CAAC;gBAAI;gBAAI,CAAC;gBAAI;gBAAI;aAAE;QAC7B,IAAI,CAAC;QACLzC,YAAY;YACVgB,UAAU;YACVG,MAAM;QACR;QACAvC,WAAWA;QACV,GAAGC,KAAK;kBAERF;;AAGP;AAQO,SAAST,MAAM,EAAES,QAAQ,EAAEC,SAAS,EAAE,GAAGC,OAAmB;IACjE,qBACE,qBAACC,oBAAM,CAACC,GAAG;QACTK,SAAS;YACPoD,GAAG;gBAAC;gBAAG,CAAC;gBAAG;aAAE;QACf;QACAxC,YAAY;YACVgB,UAAU;YACVC,QAAQC;YACRC,MAAM;QACR;QACAvC,WAAWA;QACV,GAAGC,KAAK;kBAERF;;AAGP;AASO,SAASR,KAAK,EAAEQ,QAAQ,EAAEC,SAAS,EAAE8D,QAAQ,MAAM,EAAE,GAAG7D,OAAkB;IAC/E,qBACE,qBAACC,oBAAM,CAACC,GAAG;QACTW,YAAY;YACViD,WAAW,CAAC,gCAAgC,CAAC;QAC/C;QACA3C,YAAY;YAAEgB,UAAU;QAAI;QAC5BpC,WAAWA;QACV,GAAGC,KAAK;kBAERF;;AAGP;AASO,SAASP,SAAS,EAAEO,QAAQ,EAAEC,SAAS,EAAEgE,WAAW,GAAG,EAAE,GAAG/D,OAAsB;IACvF,MAAM,CAACgE,eAAeC,iBAAiB,GAAGC,cAAK,CAACC,QAAQ,CAAC;QAAEP,GAAG;QAAGD,GAAG;IAAE;IACtE,MAAM,CAACS,WAAWC,aAAa,GAAGH,cAAK,CAACC,QAAQ,CAAC;IAEjD,MAAMG,kBAAkB,CAACC;QACvB,MAAMC,OAAOD,EAAEE,aAAa,CAACC,qBAAqB;QAClD,MAAMC,UAAUH,KAAKI,IAAI,GAAGJ,KAAKhC,KAAK,GAAG;QACzC,MAAMqC,UAAUL,KAAKM,GAAG,GAAGN,KAAK/B,MAAM,GAAG;QAEzCwB,iBAAiB;YACfL,GAAG,AAACW,CAAAA,EAAEQ,OAAO,GAAGJ,OAAM,IAAKZ;YAC3BJ,GAAG,AAACY,CAAAA,EAAES,OAAO,GAAGH,OAAM,IAAKd;QAC7B;IACF;IAEA,qBACE,qBAAC9D,oBAAM,CAACC,GAAG;QACTK,SAAS6D,YAAY;YACnBR,GAAGI,cAAcJ,CAAC;YAClBD,GAAGK,cAAcL,CAAC;QACpB,IAAI;YACFC,GAAG;YACHD,GAAG;QACL;QACAxC,YAAY;YAAE8D,MAAM;YAAUC,WAAW;YAAKC,SAAS;QAAG;QAC1DC,aAAad;QACbe,cAAc,IAAMhB,aAAa;QACjCiB,cAAc,IAAMjB,aAAa;QACjCtE,WAAWA;QACV,GAAGC,KAAK;kBAERF;;AAGP"}