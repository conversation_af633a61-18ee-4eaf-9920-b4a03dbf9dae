{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\layout\\__tests__\\main-nav.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent } from '@/lib/test-utils'\nimport { MainNav, MobileNav, QuickNav } from '../main-nav'\nimport { usePathname } from 'next/navigation'\nimport { mockFramerMotion, runAxeTest } from '@/lib/test-utils'\n\n// Mock Next.js navigation\njest.mock('next/navigation', () => ({\n  usePathname: jest.fn(),\n}))\n\n// Mock framer-motion\nmockFramerMotion()\n\ndescribe('MainNav Component', () => {\n  beforeEach(() => {\n    jest.clearAllMocks()\n    ;(usePathname as jest.Mock).mockReturnValue('/dashboard')\n  })\n\n  it('renders all navigation items', () => {\n    render(<MainNav />)\n\n    expect(screen.getByText('Dashboard')).toBeInTheDocument()\n    expect(screen.getByText('UAV Management')).toBeInTheDocument()\n    expect(screen.getByText('Map View')).toBeInTheDocument()\n    expect(screen.getByText('Hibernate Pod')).toBeInTheDocument()\n    expect(screen.getByText('Docking Stations')).toBeInTheDocument()\n    expect(screen.getByText('Battery Monitor')).toBeInTheDocument()\n  })\n\n  it('highlights active navigation item', () => {\n    ;(usePathname as jest.Mock).mockReturnValue('/uavs')\n    render(<MainNav />)\n\n    const uavLink = screen.getByRole('link', { name: /uav management/i })\n    expect(uavLink).toHaveClass('bg-primary', 'text-primary-foreground')\n  })\n\n  it('shows correct icons for each navigation item', () => {\n    render(<MainNav />)\n\n    // Check that icons are rendered (they would be Lucide React icons)\n    const dashboardLink = screen.getByRole('link', { name: /dashboard/i })\n    const uavLink = screen.getByRole('link', { name: /uav management/i })\n    \n    expect(dashboardLink).toBeInTheDocument()\n    expect(uavLink).toBeInTheDocument()\n  })\n\n  it('handles navigation item clicks', () => {\n    const mockOnNavigate = jest.fn()\n    render(<MainNav onNavigate={mockOnNavigate} />)\n\n    const dashboardLink = screen.getByRole('link', { name: /dashboard/i })\n    fireEvent.click(dashboardLink)\n\n    expect(mockOnNavigate).toHaveBeenCalledTimes(1)\n  })\n\n  it('applies custom className', () => {\n    render(<MainNav className=\"custom-nav\" />)\n\n    const nav = screen.getByRole('navigation')\n    expect(nav).toHaveClass('custom-nav')\n  })\n\n  it('shows descriptions on hover', () => {\n    render(<MainNav />)\n\n    const dashboardLink = screen.getByRole('link', { name: /dashboard/i })\n    \n    fireEvent.mouseEnter(dashboardLink)\n    expect(screen.getByText('System overview and real-time monitoring')).toBeInTheDocument()\n  })\n\n  it('handles keyboard navigation', () => {\n    render(<MainNav />)\n\n    const dashboardLink = screen.getByRole('link', { name: /dashboard/i })\n    const uavLink = screen.getByRole('link', { name: /uav management/i })\n\n    dashboardLink.focus()\n    expect(dashboardLink).toHaveFocus()\n\n    // Test Tab navigation\n    fireEvent.keyDown(dashboardLink, { key: 'Tab' })\n    uavLink.focus()\n    expect(uavLink).toHaveFocus()\n  })\n\n  it('maintains accessibility standards', async () => {\n    const { container } = render(<MainNav />)\n\n    // Check for proper navigation role\n    expect(screen.getByRole('navigation')).toBeInTheDocument()\n\n    // Check for proper link accessibility\n    const links = screen.getAllByRole('link')\n    links.forEach(link => {\n      expect(link).toHaveAttribute('href')\n    })\n\n    // Run accessibility tests\n    await runAxeTest(container)\n  })\n\n  it('handles active state for nested routes', () => {\n    ;(usePathname as jest.Mock).mockReturnValue('/uavs/details/123')\n    render(<MainNav />)\n\n    const uavLink = screen.getByRole('link', { name: /uav management/i })\n    expect(uavLink).toHaveClass('bg-primary', 'text-primary-foreground')\n  })\n\n  it('shows proper link hrefs', () => {\n    render(<MainNav />)\n\n    expect(screen.getByRole('link', { name: /dashboard/i })).toHaveAttribute('href', '/dashboard')\n    expect(screen.getByRole('link', { name: /uav management/i })).toHaveAttribute('href', '/uavs')\n    expect(screen.getByRole('link', { name: /map view/i })).toHaveAttribute('href', '/map')\n    expect(screen.getByRole('link', { name: /hibernate pod/i })).toHaveAttribute('href', '/hibernate-pod')\n  })\n})\n\ndescribe('MobileNav Component', () => {\n  beforeEach(() => {\n    jest.clearAllMocks()\n    ;(usePathname as jest.Mock).mockReturnValue('/dashboard')\n  })\n\n  it('renders in grid layout', () => {\n    render(<MobileNav />)\n\n    const nav = screen.getByRole('navigation')\n    expect(nav).toHaveClass('grid', 'grid-cols-2', 'gap-2', 'p-4')\n  })\n\n  it('shows limited navigation items', () => {\n    render(<MobileNav />)\n\n    // Should show first 8 items\n    expect(screen.getByText('Dashboard')).toBeInTheDocument()\n    expect(screen.getByText('UAV Management')).toBeInTheDocument()\n    expect(screen.getByText('Map View')).toBeInTheDocument()\n    expect(screen.getByText('Hibernate Pod')).toBeInTheDocument()\n  })\n\n  it('handles mobile navigation clicks', () => {\n    const mockOnNavigate = jest.fn()\n    render(<MobileNav onNavigate={mockOnNavigate} />)\n\n    const dashboardLink = screen.getByRole('link', { name: /dashboard/i })\n    fireEvent.click(dashboardLink)\n\n    expect(mockOnNavigate).toHaveBeenCalledTimes(1)\n  })\n\n  it('shows icons and text in column layout', () => {\n    render(<MobileNav />)\n\n    const dashboardLink = screen.getByRole('link', { name: /dashboard/i })\n    expect(dashboardLink).toHaveClass('flex', 'flex-col', 'items-center', 'justify-center')\n  })\n\n  it('applies active state correctly', () => {\n    ;(usePathname as jest.Mock).mockReturnValue('/uavs')\n    render(<MobileNav />)\n\n    const uavLink = screen.getByRole('link', { name: /uav management/i })\n    expect(uavLink).toHaveClass('bg-primary', 'text-primary-foreground')\n  })\n\n  it('handles animation on interaction', () => {\n    render(<MobileNav />)\n\n    const dashboardLink = screen.getByRole('link', { name: /dashboard/i })\n    \n    // Test hover animation\n    fireEvent.mouseEnter(dashboardLink)\n    fireEvent.mouseLeave(dashboardLink)\n    \n    // Test tap animation\n    fireEvent.mouseDown(dashboardLink)\n    fireEvent.mouseUp(dashboardLink)\n    \n    expect(dashboardLink).toBeInTheDocument()\n  })\n})\n\ndescribe('QuickNav Component', () => {\n  beforeEach(() => {\n    jest.clearAllMocks()\n    ;(usePathname as jest.Mock).mockReturnValue('/dashboard')\n  })\n\n  it('renders quick access items', () => {\n    render(<QuickNav />)\n\n    // Should show first 4 items\n    expect(screen.getByRole('link', { name: /dashboard/i })).toBeInTheDocument()\n    expect(screen.getByRole('link', { name: /uav management/i })).toBeInTheDocument()\n    expect(screen.getByRole('link', { name: /map view/i })).toBeInTheDocument()\n    expect(screen.getByRole('link', { name: /hibernate pod/i })).toBeInTheDocument()\n  })\n\n  it('renders in horizontal layout', () => {\n    render(<QuickNav />)\n\n    const nav = screen.getByRole('navigation')\n    expect(nav).toHaveClass('flex', 'space-x-1')\n  })\n\n  it('applies custom className', () => {\n    render(<QuickNav className=\"custom-quick-nav\" />)\n\n    const nav = screen.getByRole('navigation')\n    expect(nav).toHaveClass('custom-quick-nav')\n  })\n\n  it('shows only icons in compact format', () => {\n    render(<QuickNav />)\n\n    const dashboardLink = screen.getByRole('link', { name: /dashboard/i })\n    expect(dashboardLink).toHaveClass('p-2')\n  })\n\n  it('handles active state for quick nav', () => {\n    ;(usePathname as jest.Mock).mockReturnValue('/map')\n    render(<QuickNav />)\n\n    const mapLink = screen.getByRole('link', { name: /map view/i })\n    expect(mapLink).toHaveClass('bg-primary', 'text-primary-foreground')\n  })\n\n  it('provides tooltips for quick nav items', () => {\n    render(<QuickNav />)\n\n    const dashboardLink = screen.getByRole('link', { name: /dashboard/i })\n    expect(dashboardLink).toHaveAttribute('title', 'Dashboard')\n  })\n\n  it('maintains accessibility in compact mode', async () => {\n    const { container } = render(<QuickNav />)\n\n    // Check for proper link accessibility\n    const links = screen.getAllByRole('link')\n    links.forEach(link => {\n      expect(link).toHaveAttribute('href')\n      expect(link).toHaveAttribute('title')\n    })\n\n    // Run accessibility tests\n    await runAxeTest(container)\n  })\n\n  it('handles responsive behavior', () => {\n    render(<QuickNav />)\n\n    const nav = screen.getByRole('navigation')\n    expect(nav).toHaveClass('flex', 'space-x-1')\n    \n    // Links should be compact\n    const links = screen.getAllByRole('link')\n    links.forEach(link => {\n      expect(link).toHaveClass('p-2')\n    })\n  })\n})\n"], "names": ["jest", "mock", "usePathname", "fn", "mockFramerMotion", "describe", "beforeEach", "clearAllMocks", "mockReturnValue", "it", "render", "MainNav", "expect", "screen", "getByText", "toBeInTheDocument", "uavLink", "getByRole", "name", "toHaveClass", "dashboardLink", "mockOnNavigate", "onNavigate", "fireEvent", "click", "toHaveBeenCalledTimes", "className", "nav", "mouseEnter", "focus", "toHaveFocus", "keyDown", "key", "container", "links", "getAllByRole", "for<PERSON>ach", "link", "toHaveAttribute", "runAxeTest", "MobileNav", "mouseLeave", "mouseDown", "mouseUp", "QuickNav", "mapLink"], "mappings": ";AAMA,0BAA0B;AAC1BA,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC,aAAaF,KAAKG,EAAE;IACtB,CAAA;;;;;8DATkB;2BACwB;yBACG;4BACjB;;;;;;AAQ5B,qBAAqB;AACrBC,IAAAA,2BAAgB;AAEhBC,SAAS,qBAAqB;IAC5BC,WAAW;QACTN,KAAKO,aAAa;QAChBL,uBAAW,CAAeM,eAAe,CAAC;IAC9C;IAEAC,GAAG,gCAAgC;QACjCC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEfC,OAAOC,iBAAM,CAACC,SAAS,CAAC,cAAcC,iBAAiB;QACvDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,aAAaC,iBAAiB;QACtDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC3DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;QAC9DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;IAC/D;IAEAN,GAAG,qCAAqC;QACpCP,uBAAW,CAAeM,eAAe,CAAC;QAC5CE,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEf,MAAMK,UAAUH,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAkB;QACnEN,OAAOI,SAASG,WAAW,CAAC,cAAc;IAC5C;IAEAV,GAAG,gDAAgD;QACjDC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEf,mEAAmE;QACnE,MAAMS,gBAAgBP,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAa;QACpE,MAAMF,UAAUH,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAkB;QAEnEN,OAAOQ,eAAeL,iBAAiB;QACvCH,OAAOI,SAASD,iBAAiB;IACnC;IAEAN,GAAG,kCAAkC;QACnC,MAAMY,iBAAiBrB,KAAKG,EAAE;QAC9BO,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACW,YAAYD;;QAE5B,MAAMD,gBAAgBP,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAa;QACpEK,oBAAS,CAACC,KAAK,CAACJ;QAEhBR,OAAOS,gBAAgBI,qBAAqB,CAAC;IAC/C;IAEAhB,GAAG,4BAA4B;QAC7BC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACe,WAAU;;QAE1B,MAAMC,MAAMd,iBAAM,CAACI,SAAS,CAAC;QAC7BL,OAAOe,KAAKR,WAAW,CAAC;IAC1B;IAEAV,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEf,MAAMS,gBAAgBP,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAa;QAEpEK,oBAAS,CAACK,UAAU,CAACR;QACrBR,OAAOC,iBAAM,CAACC,SAAS,CAAC,6CAA6CC,iBAAiB;IACxF;IAEAN,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEf,MAAMS,gBAAgBP,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAa;QACpE,MAAMF,UAAUH,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAkB;QAEnEE,cAAcS,KAAK;QACnBjB,OAAOQ,eAAeU,WAAW;QAEjC,sBAAsB;QACtBP,oBAAS,CAACQ,OAAO,CAACX,eAAe;YAAEY,KAAK;QAAM;QAC9ChB,QAAQa,KAAK;QACbjB,OAAOI,SAASc,WAAW;IAC7B;IAEArB,GAAG,qCAAqC;QACtC,MAAM,EAAEwB,SAAS,EAAE,GAAGvB,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAErC,mCAAmC;QACnCC,OAAOC,iBAAM,CAACI,SAAS,CAAC,eAAeF,iBAAiB;QAExD,sCAAsC;QACtC,MAAMmB,QAAQrB,iBAAM,CAACsB,YAAY,CAAC;QAClCD,MAAME,OAAO,CAACC,CAAAA;YACZzB,OAAOyB,MAAMC,eAAe,CAAC;QAC/B;QAEA,0BAA0B;QAC1B,MAAMC,IAAAA,qBAAU,EAACN;IACnB;IAEAxB,GAAG,0CAA0C;QACzCP,uBAAW,CAAeM,eAAe,CAAC;QAC5CE,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEf,MAAMK,UAAUH,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAkB;QACnEN,OAAOI,SAASG,WAAW,CAAC,cAAc;IAC5C;IAEAV,GAAG,2BAA2B;QAC5BC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEfC,OAAOC,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAa,IAAIoB,eAAe,CAAC,QAAQ;QACjF1B,OAAOC,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAkB,IAAIoB,eAAe,CAAC,QAAQ;QACtF1B,OAAOC,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAY,IAAIoB,eAAe,CAAC,QAAQ;QAChF1B,OAAOC,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAiB,IAAIoB,eAAe,CAAC,QAAQ;IACvF;AACF;AAEAjC,SAAS,uBAAuB;IAC9BC,WAAW;QACTN,KAAKO,aAAa;QAChBL,uBAAW,CAAeM,eAAe,CAAC;IAC9C;IAEAC,GAAG,0BAA0B;QAC3BC,IAAAA,iBAAM,gBAAC,qBAAC8B,kBAAS;QAEjB,MAAMb,MAAMd,iBAAM,CAACI,SAAS,CAAC;QAC7BL,OAAOe,KAAKR,WAAW,CAAC,QAAQ,eAAe,SAAS;IAC1D;IAEAV,GAAG,kCAAkC;QACnCC,IAAAA,iBAAM,gBAAC,qBAAC8B,kBAAS;QAEjB,4BAA4B;QAC5B5B,OAAOC,iBAAM,CAACC,SAAS,CAAC,cAAcC,iBAAiB;QACvDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACC,SAAS,CAAC,aAAaC,iBAAiB;QACtDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;IAC7D;IAEAN,GAAG,oCAAoC;QACrC,MAAMY,iBAAiBrB,KAAKG,EAAE;QAC9BO,IAAAA,iBAAM,gBAAC,qBAAC8B,kBAAS;YAAClB,YAAYD;;QAE9B,MAAMD,gBAAgBP,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAa;QACpEK,oBAAS,CAACC,KAAK,CAACJ;QAEhBR,OAAOS,gBAAgBI,qBAAqB,CAAC;IAC/C;IAEAhB,GAAG,yCAAyC;QAC1CC,IAAAA,iBAAM,gBAAC,qBAAC8B,kBAAS;QAEjB,MAAMpB,gBAAgBP,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAa;QACpEN,OAAOQ,eAAeD,WAAW,CAAC,QAAQ,YAAY,gBAAgB;IACxE;IAEAV,GAAG,kCAAkC;QACjCP,uBAAW,CAAeM,eAAe,CAAC;QAC5CE,IAAAA,iBAAM,gBAAC,qBAAC8B,kBAAS;QAEjB,MAAMxB,UAAUH,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAkB;QACnEN,OAAOI,SAASG,WAAW,CAAC,cAAc;IAC5C;IAEAV,GAAG,oCAAoC;QACrCC,IAAAA,iBAAM,gBAAC,qBAAC8B,kBAAS;QAEjB,MAAMpB,gBAAgBP,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAa;QAEpE,uBAAuB;QACvBK,oBAAS,CAACK,UAAU,CAACR;QACrBG,oBAAS,CAACkB,UAAU,CAACrB;QAErB,qBAAqB;QACrBG,oBAAS,CAACmB,SAAS,CAACtB;QACpBG,oBAAS,CAACoB,OAAO,CAACvB;QAElBR,OAAOQ,eAAeL,iBAAiB;IACzC;AACF;AAEAV,SAAS,sBAAsB;IAC7BC,WAAW;QACTN,KAAKO,aAAa;QAChBL,uBAAW,CAAeM,eAAe,CAAC;IAC9C;IAEAC,GAAG,8BAA8B;QAC/BC,IAAAA,iBAAM,gBAAC,qBAACkC,iBAAQ;QAEhB,4BAA4B;QAC5BhC,OAAOC,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAa,IAAIH,iBAAiB;QAC1EH,OAAOC,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAkB,IAAIH,iBAAiB;QAC/EH,OAAOC,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAY,IAAIH,iBAAiB;QACzEH,OAAOC,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAiB,IAAIH,iBAAiB;IAChF;IAEAN,GAAG,gCAAgC;QACjCC,IAAAA,iBAAM,gBAAC,qBAACkC,iBAAQ;QAEhB,MAAMjB,MAAMd,iBAAM,CAACI,SAAS,CAAC;QAC7BL,OAAOe,KAAKR,WAAW,CAAC,QAAQ;IAClC;IAEAV,GAAG,4BAA4B;QAC7BC,IAAAA,iBAAM,gBAAC,qBAACkC,iBAAQ;YAAClB,WAAU;;QAE3B,MAAMC,MAAMd,iBAAM,CAACI,SAAS,CAAC;QAC7BL,OAAOe,KAAKR,WAAW,CAAC;IAC1B;IAEAV,GAAG,sCAAsC;QACvCC,IAAAA,iBAAM,gBAAC,qBAACkC,iBAAQ;QAEhB,MAAMxB,gBAAgBP,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAa;QACpEN,OAAOQ,eAAeD,WAAW,CAAC;IACpC;IAEAV,GAAG,sCAAsC;QACrCP,uBAAW,CAAeM,eAAe,CAAC;QAC5CE,IAAAA,iBAAM,gBAAC,qBAACkC,iBAAQ;QAEhB,MAAMC,UAAUhC,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAY;QAC7DN,OAAOiC,SAAS1B,WAAW,CAAC,cAAc;IAC5C;IAEAV,GAAG,yCAAyC;QAC1CC,IAAAA,iBAAM,gBAAC,qBAACkC,iBAAQ;QAEhB,MAAMxB,gBAAgBP,iBAAM,CAACI,SAAS,CAAC,QAAQ;YAAEC,MAAM;QAAa;QACpEN,OAAOQ,eAAekB,eAAe,CAAC,SAAS;IACjD;IAEA7B,GAAG,2CAA2C;QAC5C,MAAM,EAAEwB,SAAS,EAAE,GAAGvB,IAAAA,iBAAM,gBAAC,qBAACkC,iBAAQ;QAEtC,sCAAsC;QACtC,MAAMV,QAAQrB,iBAAM,CAACsB,YAAY,CAAC;QAClCD,MAAME,OAAO,CAACC,CAAAA;YACZzB,OAAOyB,MAAMC,eAAe,CAAC;YAC7B1B,OAAOyB,MAAMC,eAAe,CAAC;QAC/B;QAEA,0BAA0B;QAC1B,MAAMC,IAAAA,qBAAU,EAACN;IACnB;IAEAxB,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACkC,iBAAQ;QAEhB,MAAMjB,MAAMd,iBAAM,CAACI,SAAS,CAAC;QAC7BL,OAAOe,KAAKR,WAAW,CAAC,QAAQ;QAEhC,0BAA0B;QAC1B,MAAMe,QAAQrB,iBAAM,CAACsB,YAAY,CAAC;QAClCD,MAAME,OAAO,CAACC,CAAAA;YACZzB,OAAOyB,MAAMlB,WAAW,CAAC;QAC3B;IACF;AACF"}