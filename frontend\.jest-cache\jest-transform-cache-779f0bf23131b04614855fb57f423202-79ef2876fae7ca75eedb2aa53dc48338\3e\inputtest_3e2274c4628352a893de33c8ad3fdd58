3daeaa6f490819653002544dee8e4d21
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _input = require("../input");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
describe("Input Component", ()=>{
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            placeholder: "Enter text"
        }));
        const input = _testutils.screen.getByPlaceholderText("Enter text");
        expect(input).toBeInTheDocument();
        expect(input).toHaveClass("flex", "h-10", "w-full", "rounded-md", "border");
    });
    it("handles value changes", ()=>{
        const handleChange = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            onChange: handleChange
        }));
        const input = _testutils.screen.getByRole("textbox");
        _testutils.fireEvent.change(input, {
            target: {
                value: "test value"
            }
        });
        expect(handleChange).toHaveBeenCalledTimes(1);
        expect(input).toHaveValue("test value");
    });
    it("supports controlled input", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            value: "initial",
            onChange: ()=>{}
        }));
        let input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveValue("initial");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            value: "updated",
            onChange: ()=>{}
        }));
        input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveValue("updated");
    });
    it("supports uncontrolled input", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            defaultValue: "default"
        }));
        const input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveValue("default");
        _testutils.fireEvent.change(input, {
            target: {
                value: "changed"
            }
        });
        expect(input).toHaveValue("changed");
    });
    it("applies different input types", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "email"
        }));
        let input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveAttribute("type", "email");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "password"
        }));
        input = _testutils.screen.getByLabelText(/password/i) || _testutils.screen.getByDisplayValue("");
        expect(input).toHaveAttribute("type", "password");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "number"
        }));
        input = _testutils.screen.getByRole("spinbutton");
        expect(input).toHaveAttribute("type", "number");
    });
    it("handles disabled state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            disabled: true,
            placeholder: "Disabled input"
        }));
        const input = _testutils.screen.getByPlaceholderText("Disabled input");
        expect(input).toBeDisabled();
        expect(input).toHaveClass("disabled:cursor-not-allowed", "disabled:opacity-50");
    });
    it("handles readonly state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            readOnly: true,
            value: "readonly value"
        }));
        const input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveAttribute("readonly");
        expect(input).toHaveValue("readonly value");
    });
    it("applies custom className", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            className: "custom-input"
        }));
        const input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveClass("custom-input");
    });
    it("forwards ref correctly", ()=>{
        const ref = /*#__PURE__*/ _react.default.createRef();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            ref: ref
        }));
        expect(ref.current).toBeInstanceOf(HTMLInputElement);
    });
    it("supports all HTML input attributes", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            id: "test-input",
            name: "testName",
            placeholder: "Test placeholder",
            maxLength: 50,
            minLength: 5,
            required: true,
            "aria-label": "Test input",
            "data-testid": "test-input"
        }));
        const input = _testutils.screen.getByTestId("test-input");
        expect(input).toHaveAttribute("id", "test-input");
        expect(input).toHaveAttribute("name", "testName");
        expect(input).toHaveAttribute("placeholder", "Test placeholder");
        expect(input).toHaveAttribute("maxLength", "50");
        expect(input).toHaveAttribute("minLength", "5");
        expect(input).toHaveAttribute("required");
        expect(input).toHaveAttribute("aria-label", "Test input");
    });
    it("handles focus and blur events", ()=>{
        const handleFocus = jest.fn();
        const handleBlur = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            onFocus: handleFocus,
            onBlur: handleBlur
        }));
        const input = _testutils.screen.getByRole("textbox");
        _testutils.fireEvent.focus(input);
        expect(handleFocus).toHaveBeenCalledTimes(1);
        expect(input).toHaveFocus();
        _testutils.fireEvent.blur(input);
        expect(handleBlur).toHaveBeenCalledTimes(1);
        expect(input).not.toHaveFocus();
    });
    it("handles keyboard events", ()=>{
        const handleKeyDown = jest.fn();
        const handleKeyUp = jest.fn();
        const handleKeyPress = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            onKeyDown: handleKeyDown,
            onKeyUp: handleKeyUp,
            onKeyPress: handleKeyPress
        }));
        const input = _testutils.screen.getByRole("textbox");
        _testutils.fireEvent.keyDown(input, {
            key: "Enter",
            code: "Enter"
        });
        expect(handleKeyDown).toHaveBeenCalledTimes(1);
        _testutils.fireEvent.keyUp(input, {
            key: "Enter",
            code: "Enter"
        });
        expect(handleKeyUp).toHaveBeenCalledTimes(1);
        _testutils.fireEvent.keyPress(input, {
            key: "a",
            code: "KeyA"
        });
        expect(handleKeyPress).toHaveBeenCalledTimes(1);
    });
    it("supports form validation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("form", {
            "data-testid": "test-form",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
                    type: "email",
                    required: true,
                    pattern: "[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}$",
                    title: "Please enter a valid email address"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                    type: "submit",
                    children: "Submit"
                })
            ]
        }));
        const input = _testutils.screen.getByRole("textbox");
        const form = _testutils.screen.getByTestId("test-form");
        expect(input).toHaveAttribute("required");
        expect(input).toHaveAttribute("pattern");
        expect(input).toHaveAttribute("title");
        // Test invalid input
        _testutils.fireEvent.change(input, {
            target: {
                value: "invalid-email"
            }
        });
        _testutils.fireEvent.submit(form);
        expect(input).toBeInvalid();
    });
    it("handles number input with min/max", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "number",
            min: 0,
            max: 100,
            step: 5,
            defaultValue: 50
        }));
        const input = _testutils.screen.getByRole("spinbutton");
        expect(input).toHaveAttribute("min", "0");
        expect(input).toHaveAttribute("max", "100");
        expect(input).toHaveAttribute("step", "5");
        expect(input).toHaveValue(50);
    });
    it("handles file input", ()=>{
        const handleChange = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "file",
            accept: ".jpg,.png,.pdf",
            multiple: true,
            onChange: handleChange
        }));
        const input = _testutils.screen.getByRole("button", {
            name: /choose files/i
        }) || _testutils.screen.getByLabelText(/file/i) || document.querySelector('input[type="file"]');
        expect(input).toHaveAttribute("type", "file");
        expect(input).toHaveAttribute("accept", ".jpg,.png,.pdf");
        expect(input).toHaveAttribute("multiple");
    });
    it("supports search input with clear functionality", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "search",
            defaultValue: "search term"
        }));
        const input = _testutils.screen.getByRole("searchbox");
        expect(input).toHaveAttribute("type", "search");
        expect(input).toHaveValue("search term");
    });
    it("handles date and time inputs", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "date"
        }));
        let input = _testutils.screen.getByDisplayValue("") || document.querySelector('input[type="date"]');
        expect(input).toHaveAttribute("type", "date");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "time"
        }));
        input = _testutils.screen.getByDisplayValue("") || document.querySelector('input[type="time"]');
        expect(input).toHaveAttribute("type", "time");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            type: "datetime-local"
        }));
        input = _testutils.screen.getByDisplayValue("") || document.querySelector('input[type="datetime-local"]');
        expect(input).toHaveAttribute("type", "datetime-local");
    });
    it("maintains accessibility standards", async ()=>{
        const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                    htmlFor: "accessible-input",
                    children: "Email Address"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
                    id: "accessible-input",
                    type: "email",
                    required: true,
                    "aria-describedby": "email-help"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    id: "email-help",
                    children: "Enter your email address"
                })
            ]
        }));
        const input = _testutils.screen.getByLabelText("Email Address");
        expect(input).toHaveAttribute("aria-describedby", "email-help");
        // Run accessibility tests
        await (0, _testutils.runAxeTest)(container);
    });
    it("handles input with error state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
            "aria-invalid": "true",
            "aria-describedby": "error-message",
            className: "border-red-500"
        }));
        const input = _testutils.screen.getByRole("textbox");
        expect(input).toHaveAttribute("aria-invalid", "true");
        expect(input).toHaveAttribute("aria-describedby", "error-message");
        expect(input).toHaveClass("border-red-500");
    });
    it("supports input groups and addons", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            className: "flex",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                    className: "input-addon",
                    children: "$"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
                    type: "number",
                    placeholder: "0.00",
                    className: "rounded-l-none"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                    className: "input-addon",
                    children: ".00"
                })
            ]
        }));
        const input = _testutils.screen.getByRole("spinbutton");
        expect(input).toHaveClass("rounded-l-none");
        expect(_testutils.screen.getByText("$")).toBeInTheDocument();
        expect(_testutils.screen.getByText(".00")).toBeInTheDocument();
    });
});

//# sourceMappingURL=data:application/json;base64,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