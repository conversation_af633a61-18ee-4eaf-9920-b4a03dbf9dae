e98cb4abd0753018900847b13b5b4879
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _animatedbutton = require("../animated-button");
const _lucidereact = require("lucide-react");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock framer-motion for testing
(0, _testutils.mockFramerMotion)();
describe("AnimatedButton Component", ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            children: "Animated Button"
        }));
        expect(_testutils.screen.getByRole("button", {
            name: /animated button/i
        })).toBeInTheDocument();
    });
    it("handles click events", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            onClick: handleClick,
            children: "Click me"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    it("shows loading state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            loading: true,
            children: "Loading Button"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeDisabled();
        // Check for loading spinner (it's a div with specific styling)
        const spinner = button.querySelector(".border-t-transparent");
        expect(spinner).toBeInTheDocument();
    });
    it("is disabled when disabled prop is true", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            disabled: true,
            children: "Disabled Button"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeDisabled();
    });
    it("does not trigger click when disabled", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            disabled: true,
            onClick: handleClick,
            children: "Disabled"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).not.toHaveBeenCalled();
    });
    it("does not trigger click when loading", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            loading: true,
            onClick: handleClick,
            children: "Loading"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).not.toHaveBeenCalled();
    });
    it("applies custom className", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            className: "custom-class",
            children: "Custom"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("custom-class");
    });
    it("handles ripple effect when enabled", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            ripple: true,
            children: "Ripple Button"
        }));
        const button = _testutils.screen.getByRole("button");
        _testutils.fireEvent.click(button);
        // Ripple effect should be triggered (tested through animation state)
        expect(button).toBeInTheDocument();
    });
    it("respects prefers-reduced-motion", ()=>{
        (0, _testutils.mockPrefersReducedMotion)(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            children: "Reduced Motion"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
    // Animation should be disabled when prefers-reduced-motion is set
    });
    it("supports glow effect", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            glow: true,
            children: "Glow Button"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
    // Glow effect should be applied through CSS classes
    });
    it("supports magnetic effect", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            magnetic: true,
            children: "Magnetic Button"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
    // Magnetic effect should be applied through motion properties
    });
});
describe("FloatingActionButton Component", ()=>{
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.FloatingActionButton, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {
                className: "h-6 w-6"
            })
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
        expect(button).toHaveClass("rounded-full", "w-14", "h-14");
    });
    it("applies correct position classes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.FloatingActionButton, {
            position: "bottom-right",
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {
                className: "h-6 w-6"
            })
        }));
        let container = document.querySelector(".fixed");
        expect(container).toHaveClass("bottom-6", "right-6");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.FloatingActionButton, {
            position: "bottom-left",
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {
                className: "h-6 w-6"
            })
        }));
        container = document.querySelector(".fixed");
        expect(container).toHaveClass("bottom-6", "left-6");
    });
    it("handles click events", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.FloatingActionButton, {
            onClick: handleClick,
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {
                className: "h-6 w-6"
            })
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    it("handles hover interactions", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.FloatingActionButton, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {
                className: "h-6 w-6"
            })
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
        // Test that the button can be hovered
        _testutils.fireEvent.mouseEnter(button);
        _testutils.fireEvent.mouseLeave(button);
    });
});
describe("AnimatedIconButton Component", ()=>{
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {}),
            "aria-label": "Add item"
        }));
        const button = _testutils.screen.getByRole("button", {
            name: /add item/i
        });
        expect(button).toBeInTheDocument();
    });
    it("applies size classes correctly", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {}),
            size: "sm",
            "aria-label": "Small button"
        }));
        let button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("h-8", "w-8");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {}),
            size: "lg",
            "aria-label": "Large button"
        }));
        button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("h-12", "w-12");
    });
    it("applies variant styles correctly", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {}),
            variant: "ghost",
            "aria-label": "Ghost button"
        }));
        let button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("hover:bg-accent");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {}),
            variant: "outline",
            "aria-label": "Outline button"
        }));
        button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("border");
    });
    it("handles click events", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {}),
            onClick: handleClick,
            "aria-label": "Clickable button"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    it("is disabled when disabled prop is true", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {}),
            disabled: true,
            "aria-label": "Disabled button"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeDisabled();
    });
});
describe("ProgressButton Component", ()=>{
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 0,
            children: "Download"
        }));
        expect(_testutils.screen.getByRole("button", {
            name: /download/i
        })).toBeInTheDocument();
    });
    it("shows progress bar when showProgress is true", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 50,
            showProgress: true,
            children: "Downloading..."
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
        // Check for progress bar element (it's a div with specific styling)
        const progressBar = button.querySelector(".bg-white\\/20");
        expect(progressBar).toBeInTheDocument();
    });
    it("shows completion state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 100,
            showProgress: true,
            children: "Completed"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
        expect(button).toHaveTextContent("Completed");
    });
    it("handles click events when not in progress", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 0,
            onClick: handleClick,
            children: "Start"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    it("handles click events normally", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 50,
            onClick: handleClick,
            children: "In Progress"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    it("shows custom content", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 0,
            showProgress: true,
            children: "Download File"
        }));
        expect(_testutils.screen.getByText("Download File")).toBeInTheDocument();
    });
    it("applies correct progress width", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 75,
            showProgress: true,
            children: "Progress"
        }));
        const button = _testutils.screen.getByRole("button");
        const progressBar = button.querySelector(".bg-white\\/20");
        expect(progressBar).toBeInTheDocument();
    });
    it("handles progress animation", async ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 0,
            showProgress: true,
            children: "Start"
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 50,
            showProgress: true,
            children: "Progress"
        }));
        const button = _testutils.screen.getByRole("button");
        const progressBar = button.querySelector(".bg-white\\/20");
        expect(progressBar).toBeInTheDocument();
    });
    it("resets to initial state when progress is 0", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 100,
            showProgress: true,
            children: "Done"
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 0,
            showProgress: false,
            children: "Start Again"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).not.toBeDisabled();
        expect(button).toHaveTextContent("Start Again");
    });
});

//# sourceMappingURL=data:application/json;base64,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