{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\features\\dashboard\\realtime-alerts.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { AnimatedAlert, RealtimeAlerts as AnimatedRealtimeAlerts } from '@/components/ui/animated-alert'\nimport { StaggerContainer, StaggerItem } from '@/components/ui/animated-components'\nimport {\n  AlertTriangle,\n  Bell,\n  CheckCircle,\n  XCircle,\n  Info,\n  Clock,\n  X,\n} from 'lucide-react'\nimport { useAlerts, useDashboardStore } from '@/stores/dashboard-store'\nimport { SystemAlert } from '@/types/uav'\nimport { formatRelativeTime, cn } from '@/lib/utils'\n\n// Mock alerts for demonstration\nconst mockAlerts: SystemAlert[] = [\n  {\n    id: '1',\n    type: 'CRITICAL',\n    title: 'UAV Battery Critical',\n    message: 'UAV-005 battery level is critically low (8%)',\n    uavId: 5,\n    timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n    acknowledged: false,\n  },\n  {\n    id: '2',\n    type: 'WARNING',\n    title: 'Hibernate Pod Near Capacity',\n    message: 'Hibernate pod is at 80% capacity (8/10 slots occupied)',\n    timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),\n    acknowledged: false,\n  },\n  {\n    id: '3',\n    type: 'INFO',\n    title: 'Flight Completed',\n    message: 'UAV-003 has successfully completed mission \"Perimeter Patrol\"',\n    uavId: 3,\n    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n    acknowledged: true,\n  },\n  {\n    id: '4',\n    type: 'ERROR',\n    title: 'Communication Lost',\n    message: 'Lost communication with UAV-007 during flight',\n    uavId: 7,\n    timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),\n    acknowledged: false,\n  },\n  {\n    id: '5',\n    type: 'INFO',\n    title: 'Maintenance Scheduled',\n    message: 'Routine maintenance scheduled for UAV-001 tomorrow at 09:00',\n    uavId: 1,\n    timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),\n    acknowledged: true,\n  },\n]\n\nexport function RealtimeAlerts() {\n  const alerts = useAlerts()\n  const { acknowledgeAlert, removeAlert, addAlert } = useDashboardStore()\n\n  // Initialize with mock data for demonstration\n  useEffect(() => {\n    if (alerts.length === 0) {\n      mockAlerts.forEach(alert => addAlert(alert))\n    }\n  }, [alerts.length, addAlert])\n\n  const getAlertIcon = (type: SystemAlert['type']) => {\n    switch (type) {\n      case 'CRITICAL':\n        return <XCircle className=\"h-4 w-4 text-red-600\" />\n      case 'ERROR':\n        return <AlertTriangle className=\"h-4 w-4 text-red-500\" />\n      case 'WARNING':\n        return <AlertTriangle className=\"h-4 w-4 text-yellow-500\" />\n      case 'INFO':\n        return <Info className=\"h-4 w-4 text-blue-500\" />\n      default:\n        return <Bell className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getAlertVariant = (type: SystemAlert['type']) => {\n    switch (type) {\n      case 'CRITICAL':\n      case 'ERROR':\n        return 'destructive'\n      case 'WARNING':\n        return 'warning'\n      case 'INFO':\n        return 'info'\n      default:\n        return 'secondary'\n    }\n  }\n\n  const handleAcknowledge = (alertId: string) => {\n    acknowledgeAlert(alertId)\n  }\n\n  const handleDismiss = (alertId: string) => {\n    removeAlert(alertId)\n  }\n\n  const unacknowledgedAlerts = alerts.filter(alert => !alert.acknowledged)\n  const acknowledgedAlerts = alerts.filter(alert => alert.acknowledged)\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Unacknowledged Alerts */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <Bell className=\"h-5 w-5\" />\n              <span>Active Alerts</span>\n              {unacknowledgedAlerts.length > 0 && (\n                <Badge variant=\"destructive\" className=\"ml-2\">\n                  {unacknowledgedAlerts.length}\n                </Badge>\n              )}\n            </div>\n          </CardTitle>\n          <CardDescription>\n            Unacknowledged system alerts requiring attention\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {unacknowledgedAlerts.length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              <CheckCircle className=\"h-12 w-12 mx-auto mb-4 text-green-500\" />\n              <p className=\"text-lg font-medium\">All Clear!</p>\n              <p className=\"text-sm\">No active alerts at this time</p>\n            </div>\n          ) : (\n            <ScrollArea className=\"h-80\">\n              <StaggerContainer className=\"space-y-3\">\n                <AnimatePresence>\n                  {unacknowledgedAlerts.map((alert, index) => (\n                    <StaggerItem key={alert.id}>\n                      <motion.div\n                        layout\n                        initial={{ opacity: 0, x: -20, scale: 0.95 }}\n                        animate={{ opacity: 1, x: 0, scale: 1 }}\n                        exit={{ opacity: 0, x: 20, scale: 0.95 }}\n                        transition={{ duration: 0.3, delay: index * 0.1 }}\n                        className={cn(\n                          'p-4 rounded-lg border transition-colors',\n                          alert.type === 'CRITICAL' && 'border-red-200 bg-red-50',\n                          alert.type === 'ERROR' && 'border-red-200 bg-red-50',\n                          alert.type === 'WARNING' && 'border-yellow-200 bg-yellow-50',\n                          alert.type === 'INFO' && 'border-blue-200 bg-blue-50'\n                        )}\n                      >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-start space-x-3 flex-1\">\n                        {getAlertIcon(alert.type)}\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-center space-x-2 mb-1\">\n                            <Badge variant={getAlertVariant(alert.type) as any}>\n                              {alert.type}\n                            </Badge>\n                            <div className=\"flex items-center space-x-1 text-xs text-muted-foreground\">\n                              <Clock className=\"h-3 w-3\" />\n                              <span>{formatRelativeTime(alert.timestamp)}</span>\n                            </div>\n                          </div>\n                          <h4 className=\"font-medium text-sm mb-1\">{alert.title}</h4>\n                          <p className=\"text-sm text-muted-foreground\">{alert.message}</p>\n                          {alert.uavId && (\n                            <p className=\"text-xs text-muted-foreground mt-1\">\n                              Related UAV ID: {alert.uavId}\n                            </p>\n                          )}\n                        </div>\n                      </div>\n                        <div className=\"flex items-center space-x-2 ml-4\">\n                          <motion.div\n                            whileHover={{ scale: 1.05 }}\n                            whileTap={{ scale: 0.95 }}\n                          >\n                            <Button\n                              size=\"sm\"\n                              variant=\"outline\"\n                              onClick={() => handleAcknowledge(alert.id)}\n                            >\n                              Acknowledge\n                            </Button>\n                          </motion.div>\n                          <motion.div\n                            whileHover={{ scale: 1.1, rotate: 90 }}\n                            whileTap={{ scale: 0.9 }}\n                          >\n                            <Button\n                              size=\"sm\"\n                              variant=\"ghost\"\n                              onClick={() => handleDismiss(alert.id)}\n                            >\n                              <X className=\"h-4 w-4\" />\n                            </Button>\n                          </motion.div>\n                        </div>\n                      </div>\n                      </motion.div>\n                    </StaggerItem>\n                  ))}\n                </AnimatePresence>\n              </StaggerContainer>\n            </ScrollArea>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Recent Activity */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Clock className=\"h-5 w-5\" />\n            <span>Recent Activity</span>\n          </CardTitle>\n          <CardDescription>\n            Recently acknowledged alerts and system events\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {acknowledgedAlerts.length === 0 ? (\n            <div className=\"text-center py-4 text-muted-foreground\">\n              <p className=\"text-sm\">No recent activity</p>\n            </div>\n          ) : (\n            <ScrollArea className=\"h-60\">\n              <StaggerContainer className=\"space-y-2\">\n                <AnimatePresence>\n                  {acknowledgedAlerts.slice(0, 10).map((alert, index) => (\n                    <StaggerItem key={alert.id}>\n                      <motion.div\n                        layout\n                        initial={{ opacity: 0, y: 10 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        exit={{ opacity: 0, y: -10 }}\n                        transition={{ duration: 0.2, delay: index * 0.05 }}\n                        className=\"flex items-center justify-between p-3 rounded-lg bg-muted/50\"\n                      >\n                    <div className=\"flex items-center space-x-3\">\n                      {getAlertIcon(alert.type)}\n                      <div>\n                        <p className=\"text-sm font-medium\">{alert.title}</p>\n                        <p className=\"text-xs text-muted-foreground\">\n                          {formatRelativeTime(alert.timestamp)}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        Acknowledged\n                      </Badge>\n                        <motion.div\n                          whileHover={{ scale: 1.1, rotate: 90 }}\n                          whileTap={{ scale: 0.9 }}\n                        >\n                          <Button\n                            size=\"sm\"\n                            variant=\"ghost\"\n                            onClick={() => handleDismiss(alert.id)}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </motion.div>\n                      </div>\n                      </motion.div>\n                    </StaggerItem>\n                  ))}\n                </AnimatePresence>\n              </StaggerContainer>\n            </ScrollArea>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": ["RealtimeAlerts", "mock<PERSON>ler<PERSON>", "id", "type", "title", "message", "uavId", "timestamp", "Date", "now", "toISOString", "acknowledged", "alerts", "useAlerts", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "add<PERSON><PERSON><PERSON>", "useDashboardStore", "useEffect", "length", "for<PERSON>ach", "alert", "getAlertIcon", "XCircle", "className", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Info", "Bell", "getAlertVariant", "handleAcknowledge", "alertId", "handle<PERSON><PERSON><PERSON>", "unacknowledged<PERSON><PERSON><PERSON>", "filter", "<PERSON><PERSON><PERSON><PERSON>", "div", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "span", "Badge", "variant", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "p", "ScrollArea", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AnimatePresence", "map", "index", "StaggerItem", "motion", "layout", "initial", "opacity", "x", "scale", "animate", "exit", "transition", "duration", "delay", "cn", "Clock", "formatRelativeTime", "h4", "whileHover", "whileTap", "<PERSON><PERSON>", "size", "onClick", "rotate", "X", "slice", "y"], "mappings": "AAAA;;;;;+BAuEgBA;;;eAAAA;;;;+DArEiB;8BACO;sBACkC;uBACpD;wBACC;4BACI;oCAEmB;6BASvC;gCACsC;uBAEN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvC,gCAAgC;AAChC,MAAMC,aAA4B;IAChC;QACEC,IAAI;QACJC,MAAM;QACNC,OAAO;QACPC,SAAS;QACTC,OAAO;QACPC,WAAW,IAAIC,KAAKA,KAAKC,GAAG,KAAK,IAAI,KAAK,MAAMC,WAAW;QAC3DC,cAAc;IAChB;IACA;QACET,IAAI;QACJC,MAAM;QACNC,OAAO;QACPC,SAAS;QACTE,WAAW,IAAIC,KAAKA,KAAKC,GAAG,KAAK,KAAK,KAAK,MAAMC,WAAW;QAC5DC,cAAc;IAChB;IACA;QACET,IAAI;QACJC,MAAM;QACNC,OAAO;QACPC,SAAS;QACTC,OAAO;QACPC,WAAW,IAAIC,KAAKA,KAAKC,GAAG,KAAK,KAAK,KAAK,MAAMC,WAAW;QAC5DC,cAAc;IAChB;IACA;QACET,IAAI;QACJC,MAAM;QACNC,OAAO;QACPC,SAAS;QACTC,OAAO;QACPC,WAAW,IAAIC,KAAKA,KAAKC,GAAG,KAAK,KAAK,KAAK,MAAMC,WAAW;QAC5DC,cAAc;IAChB;IACA;QACET,IAAI;QACJC,MAAM;QACNC,OAAO;QACPC,SAAS;QACTC,OAAO;QACPC,WAAW,IAAIC,KAAKA,KAAKC,GAAG,KAAK,KAAK,KAAK,MAAMC,WAAW;QAC5DC,cAAc;IAChB;CACD;AAEM,SAASX;IACd,MAAMY,SAASC,IAAAA,yBAAS;IACxB,MAAM,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,QAAQ,EAAE,GAAGC,IAAAA,iCAAiB;IAErE,8CAA8C;IAC9CC,IAAAA,gBAAS,EAAC;QACR,IAAIN,OAAOO,MAAM,KAAK,GAAG;YACvBlB,WAAWmB,OAAO,CAACC,CAAAA,QAASL,SAASK;QACvC;IACF,GAAG;QAACT,OAAOO,MAAM;QAAEH;KAAS;IAE5B,MAAMM,eAAe,CAACnB;QACpB,OAAQA;YACN,KAAK;gBACH,qBAAO,qBAACoB,oBAAO;oBAACC,WAAU;;YAC5B,KAAK;gBACH,qBAAO,qBAACC,0BAAa;oBAACD,WAAU;;YAClC,KAAK;gBACH,qBAAO,qBAACC,0BAAa;oBAACD,WAAU;;YAClC,KAAK;gBACH,qBAAO,qBAACE,iBAAI;oBAACF,WAAU;;YACzB;gBACE,qBAAO,qBAACG,iBAAI;oBAACH,WAAU;;QAC3B;IACF;IAEA,MAAMI,kBAAkB,CAACzB;QACvB,OAAQA;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM0B,oBAAoB,CAACC;QACzBhB,iBAAiBgB;IACnB;IAEA,MAAMC,gBAAgB,CAACD;QACrBf,YAAYe;IACd;IAEA,MAAME,uBAAuBpB,OAAOqB,MAAM,CAACZ,CAAAA,QAAS,CAACA,MAAMV,YAAY;IACvE,MAAMuB,qBAAqBtB,OAAOqB,MAAM,CAACZ,CAAAA,QAASA,MAAMV,YAAY;IAEpE,qBACE,sBAACwB;QAAIX,WAAU;;0BAEb,sBAACY,UAAI;;kCACH,sBAACC,gBAAU;;0CACT,qBAACC,eAAS;gCAACd,WAAU;0CACnB,cAAA,sBAACW;oCAAIX,WAAU;;sDACb,qBAACG,iBAAI;4CAACH,WAAU;;sDAChB,qBAACe;sDAAK;;wCACLP,qBAAqBb,MAAM,GAAG,mBAC7B,qBAACqB,YAAK;4CAACC,SAAQ;4CAAcjB,WAAU;sDACpCQ,qBAAqBb,MAAM;;;;;0CAKpC,qBAACuB,qBAAe;0CAAC;;;;kCAInB,qBAACC,iBAAW;kCACTX,qBAAqBb,MAAM,KAAK,kBAC/B,sBAACgB;4BAAIX,WAAU;;8CACb,qBAACoB,wBAAW;oCAACpB,WAAU;;8CACvB,qBAACqB;oCAAErB,WAAU;8CAAsB;;8CACnC,qBAACqB;oCAAErB,WAAU;8CAAU;;;2CAGzB,qBAACsB,sBAAU;4BAACtB,WAAU;sCACpB,cAAA,qBAACuB,oCAAgB;gCAACvB,WAAU;0CAC1B,cAAA,qBAACwB,6BAAe;8CACbhB,qBAAqBiB,GAAG,CAAC,CAAC5B,OAAO6B,sBAChC,qBAACC,+BAAW;sDACV,cAAA,qBAACC,oBAAM,CAACjB,GAAG;gDACTkB,MAAM;gDACNC,SAAS;oDAAEC,SAAS;oDAAGC,GAAG,CAAC;oDAAIC,OAAO;gDAAK;gDAC3CC,SAAS;oDAAEH,SAAS;oDAAGC,GAAG;oDAAGC,OAAO;gDAAE;gDACtCE,MAAM;oDAAEJ,SAAS;oDAAGC,GAAG;oDAAIC,OAAO;gDAAK;gDACvCG,YAAY;oDAAEC,UAAU;oDAAKC,OAAOZ,QAAQ;gDAAI;gDAChD1B,WAAWuC,IAAAA,SAAE,EACX,2CACA1C,MAAMlB,IAAI,KAAK,cAAc,4BAC7BkB,MAAMlB,IAAI,KAAK,WAAW,4BAC1BkB,MAAMlB,IAAI,KAAK,aAAa,kCAC5BkB,MAAMlB,IAAI,KAAK,UAAU;0DAG/B,cAAA,sBAACgC;oDAAIX,WAAU;;sEACb,sBAACW;4DAAIX,WAAU;;gEACZF,aAAaD,MAAMlB,IAAI;8EACxB,sBAACgC;oEAAIX,WAAU;;sFACb,sBAACW;4EAAIX,WAAU;;8FACb,qBAACgB,YAAK;oFAACC,SAASb,gBAAgBP,MAAMlB,IAAI;8FACvCkB,MAAMlB,IAAI;;8FAEb,sBAACgC;oFAAIX,WAAU;;sGACb,qBAACwC,kBAAK;4FAACxC,WAAU;;sGACjB,qBAACe;sGAAM0B,IAAAA,yBAAkB,EAAC5C,MAAMd,SAAS;;;;;;sFAG7C,qBAAC2D;4EAAG1C,WAAU;sFAA4BH,MAAMjB,KAAK;;sFACrD,qBAACyC;4EAAErB,WAAU;sFAAiCH,MAAMhB,OAAO;;wEAC1DgB,MAAMf,KAAK,kBACV,sBAACuC;4EAAErB,WAAU;;gFAAqC;gFAC/BH,MAAMf,KAAK;;;;;;;sEAKlC,sBAAC6B;4DAAIX,WAAU;;8EACb,qBAAC4B,oBAAM,CAACjB,GAAG;oEACTgC,YAAY;wEAAEV,OAAO;oEAAK;oEAC1BW,UAAU;wEAAEX,OAAO;oEAAK;8EAExB,cAAA,qBAACY,cAAM;wEACLC,MAAK;wEACL7B,SAAQ;wEACR8B,SAAS,IAAM1C,kBAAkBR,MAAMnB,EAAE;kFAC1C;;;8EAIH,qBAACkD,oBAAM,CAACjB,GAAG;oEACTgC,YAAY;wEAAEV,OAAO;wEAAKe,QAAQ;oEAAG;oEACrCJ,UAAU;wEAAEX,OAAO;oEAAI;8EAEvB,cAAA,qBAACY,cAAM;wEACLC,MAAK;wEACL7B,SAAQ;wEACR8B,SAAS,IAAMxC,cAAcV,MAAMnB,EAAE;kFAErC,cAAA,qBAACuE,cAAC;4EAACjD,WAAU;;;;;;;;;2CA3DLH,MAAMnB,EAAE;;;;;;;0BA2ExC,sBAACkC,UAAI;;kCACH,sBAACC,gBAAU;;0CACT,sBAACC,eAAS;gCAACd,WAAU;;kDACnB,qBAACwC,kBAAK;wCAACxC,WAAU;;kDACjB,qBAACe;kDAAK;;;;0CAER,qBAACG,qBAAe;0CAAC;;;;kCAInB,qBAACC,iBAAW;kCACTT,mBAAmBf,MAAM,KAAK,kBAC7B,qBAACgB;4BAAIX,WAAU;sCACb,cAAA,qBAACqB;gCAAErB,WAAU;0CAAU;;2CAGzB,qBAACsB,sBAAU;4BAACtB,WAAU;sCACpB,cAAA,qBAACuB,oCAAgB;gCAACvB,WAAU;0CAC1B,cAAA,qBAACwB,6BAAe;8CACbd,mBAAmBwC,KAAK,CAAC,GAAG,IAAIzB,GAAG,CAAC,CAAC5B,OAAO6B,sBAC3C,qBAACC,+BAAW;sDACV,cAAA,sBAACC,oBAAM,CAACjB,GAAG;gDACTkB,MAAM;gDACNC,SAAS;oDAAEC,SAAS;oDAAGoB,GAAG;gDAAG;gDAC7BjB,SAAS;oDAAEH,SAAS;oDAAGoB,GAAG;gDAAE;gDAC5BhB,MAAM;oDAAEJ,SAAS;oDAAGoB,GAAG,CAAC;gDAAG;gDAC3Bf,YAAY;oDAAEC,UAAU;oDAAKC,OAAOZ,QAAQ;gDAAK;gDACjD1B,WAAU;;kEAEd,sBAACW;wDAAIX,WAAU;;4DACZF,aAAaD,MAAMlB,IAAI;0EACxB,sBAACgC;;kFACC,qBAACU;wEAAErB,WAAU;kFAAuBH,MAAMjB,KAAK;;kFAC/C,qBAACyC;wEAAErB,WAAU;kFACVyC,IAAAA,yBAAkB,EAAC5C,MAAMd,SAAS;;;;;;kEAIzC,sBAAC4B;wDAAIX,WAAU;;0EACb,qBAACgB,YAAK;gEAACC,SAAQ;gEAAUjB,WAAU;0EAAU;;0EAG3C,qBAAC4B,oBAAM,CAACjB,GAAG;gEACTgC,YAAY;oEAAEV,OAAO;oEAAKe,QAAQ;gEAAG;gEACrCJ,UAAU;oEAAEX,OAAO;gEAAI;0EAEvB,cAAA,qBAACY,cAAM;oEACLC,MAAK;oEACL7B,SAAQ;oEACR8B,SAAS,IAAMxC,cAAcV,MAAMnB,EAAE;8EAErC,cAAA,qBAACuE,cAAC;wEAACjD,WAAU;;;;;;;;2CA/BHH,MAAMnB,EAAE;;;;;;;;;AA8C9C"}