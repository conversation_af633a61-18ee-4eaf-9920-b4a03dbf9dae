da177fef298e050357177806eb36bdfa
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    createQueryString: function() {
        return createQueryString;
    },
    default: function() {
        return _default;
    },
    handleAPIResponse: function() {
        return handleAPIResponse;
    },
    retryRequest: function() {
        return retryRequest;
    }
});
const _axios = /*#__PURE__*/ _interop_require_default(require("axios"));
const _reacthottoast = require("react-hot-toast");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
class APIClient {
    constructor(){
        this.baseURL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080";
        this.client = _axios.default.create({
            baseURL: this.baseURL,
            timeout: 30000,
            headers: {
                "Content-Type": "application/json"
            }
        });
        this.setupInterceptors();
    }
    setupInterceptors() {
        // Request interceptor
        this.client.interceptors.request.use((config)=>{
            // Add auth token if available
            const token = this.getAuthToken();
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
            // Add request timestamp for debugging
            config.metadata = {
                startTime: new Date()
            };
            return config;
        }, (error)=>{
            return Promise.reject(error);
        });
        // Response interceptor
        this.client.interceptors.response.use((response)=>{
            // Log response time in development
            if (process.env.NODE_ENV === "development") {
                const endTime = new Date();
                const startTime = response.config.metadata?.startTime;
                if (startTime) {
                    const duration = endTime.getTime() - startTime.getTime();
                    console.log(`API ${response.config.method?.toUpperCase()} ${response.config.url}: ${duration}ms`);
                }
            }
            return response;
        }, (error)=>{
            this.handleError(error);
            return Promise.reject(error);
        });
    }
    getAuthToken() {
        // Get token from localStorage or cookie
        if (typeof window !== "undefined") {
            return localStorage.getItem("auth_token");
        }
        return null;
    }
    handleError(error) {
        let message = "An unexpected error occurred";
        let status = 500;
        if (error.response) {
            // Server responded with error status
            status = error.response.status;
            message = error.response.data?.message || error.response.statusText;
            switch(status){
                case 401:
                    message = "Authentication required. Please log in.";
                    this.handleAuthError();
                    break;
                case 403:
                    message = "You do not have permission to perform this action.";
                    break;
                case 404:
                    message = "The requested resource was not found.";
                    break;
                case 422:
                    message = "Invalid data provided. Please check your input.";
                    break;
                case 429:
                    message = "Too many requests. Please try again later.";
                    break;
                case 500:
                    message = "Server error. Please try again later.";
                    break;
                case 503:
                    message = "Service temporarily unavailable. Please try again later.";
                    break;
            }
        } else if (error.request) {
            // Network error
            message = "Network error. Please check your connection.";
            status = 0;
        }
        // Show error toast in production, log in development
        if (process.env.NODE_ENV === "production") {
            _reacthottoast.toast.error(message);
        } else {
            console.error("API Error:", error);
            _reacthottoast.toast.error(`${message} (${status})`);
        }
    }
    handleAuthError() {
        // Clear auth token and redirect to login
        if (typeof window !== "undefined") {
            localStorage.removeItem("auth_token");
            // Redirect to login page
            window.location.href = "/login";
        }
    }
    // Generic request methods
    async get(url, config) {
        const response = await this.client.get(url, config);
        return response.data;
    }
    async post(url, data, config) {
        const response = await this.client.post(url, data, config);
        return response.data;
    }
    async put(url, data, config) {
        const response = await this.client.put(url, data, config);
        return response.data;
    }
    async patch(url, data, config) {
        const response = await this.client.patch(url, data, config);
        return response.data;
    }
    async delete(url, config) {
        const response = await this.client.delete(url, config);
        return response.data;
    }
    // File upload method
    async uploadFile(url, file, onProgress) {
        const formData = new FormData();
        formData.append("file", file);
        const config = {
            headers: {
                "Content-Type": "multipart/form-data"
            },
            onUploadProgress: (progressEvent)=>{
                if (onProgress && progressEvent.total) {
                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);
                    onProgress(progress);
                }
            }
        };
        const response = await this.client.post(url, formData, config);
        return response.data;
    }
    // Batch request method
    async batch(requests) {
        try {
            const results = await Promise.allSettled(requests.map((req)=>req()));
            return results.map((result, index)=>{
                if (result.status === "fulfilled") {
                    return result.value;
                } else {
                    console.error(`Batch request ${index} failed:`, result.reason);
                    throw result.reason;
                }
            });
        } catch (error) {
            console.error("Batch request failed:", error);
            throw error;
        }
    }
    // Health check method
    async healthCheck() {
        try {
            await this.get("/actuator/health");
            return true;
        } catch (error) {
            return false;
        }
    }
    // Set auth token
    setAuthToken(token) {
        if (typeof window !== "undefined") {
            localStorage.setItem("auth_token", token);
        }
    }
    // Clear auth token
    clearAuthToken() {
        if (typeof window !== "undefined") {
            localStorage.removeItem("auth_token");
        }
    }
    // Get base URL
    getBaseURL() {
        return this.baseURL;
    }
    // Cancel all pending requests
    cancelAllRequests() {
        // Implementation would depend on tracking active requests
        // For now, we'll create a new axios instance
        this.client = _axios.default.create({
            baseURL: this.baseURL,
            timeout: 30000,
            headers: {
                "Content-Type": "application/json"
            }
        });
        this.setupInterceptors();
    }
}
// Create singleton instance
const apiClient = new APIClient();
const _default = apiClient;
const handleAPIResponse = (response)=>{
    if (!response.success) {
        throw new Error(response.message || "API request failed");
    }
    return response.data;
};
const createQueryString = (params)=>{
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value])=>{
        if (value !== undefined && value !== null && value !== "") {
            if (Array.isArray(value)) {
                value.forEach((item)=>searchParams.append(key, String(item)));
            } else {
                searchParams.append(key, String(value));
            }
        }
    });
    return searchParams.toString();
};
const retryRequest = async (requestFn, maxRetries = 3, delay = 1000)=>{
    let lastError;
    for(let i = 0; i <= maxRetries; i++){
        try {
            return await requestFn();
        } catch (error) {
            lastError = error;
            if (i === maxRetries) {
                break;
            }
            // Wait before retrying
            await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));
        }
    }
    throw lastError;
};

//# sourceMappingURL=data:application/json;base64,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