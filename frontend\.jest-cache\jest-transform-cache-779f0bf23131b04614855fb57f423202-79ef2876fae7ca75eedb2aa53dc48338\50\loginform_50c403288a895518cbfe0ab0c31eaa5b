ec475ee222695be7dcff25f06f307444
"use client";
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "LoginForm", {
    enumerable: true,
    get: function() {
        return LoginForm;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _navigation = require("next/navigation");
const _reacthookform = require("react-hook-form");
const _zod = require("@hookform/resolvers/zod");
const _zod1 = require("zod");
const _link = /*#__PURE__*/ _interop_require_default(require("next/link"));
const _button = require("../ui/button");
const _input = require("../ui/input");
const _label = require("../ui/label");
const _checkbox = require("../ui/checkbox");
const _card = require("../ui/card");
const _alert = require("../ui/alert");
const _lucidereact = require("lucide-react");
const _authstore = require("../../stores/auth-store");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const loginSchema = _zod1.z.object({
    username: _zod1.z.string().min(1, "Username is required"),
    password: _zod1.z.string().min(1, "Password is required"),
    rememberMe: _zod1.z.boolean().optional()
});
function LoginForm({ redirectTo = "/dashboard", onSuccess }) {
    const router = (0, _navigation.useRouter)();
    const { login, isLoading, error, clearError } = (0, _authstore.useAuthStore)();
    const [showPassword, setShowPassword] = (0, _react.useState)(false);
    const { register, handleSubmit, setValue, watch, formState: { errors } } = (0, _reacthookform.useForm)({
        resolver: (0, _zod.zodResolver)(loginSchema),
        defaultValues: {
            username: "",
            password: "",
            rememberMe: false
        }
    });
    const onSubmit = async (data)=>{
        clearError();
        const loginData = {
            username: data.username,
            password: data.password,
            rememberMe: data.rememberMe
        };
        const success = await login(loginData);
        if (success) {
            if (onSuccess) {
                onSuccess();
            } else {
                router.push(redirectTo);
            }
        }
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
        className: "min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",
        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.Card, {
            className: "w-full max-w-md",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.CardHeader, {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "flex justify-center mb-4",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                className: "p-3 bg-primary/10 rounded-full",
                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Shield, {
                                    className: "h-8 w-8 text-primary"
                                })
                            })
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardTitle, {
                            className: "text-2xl font-orbitron",
                            children: "UAV Control System"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardDescription, {
                            children: "Sign in to access the UAV management dashboard"
                        })
                    ]
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.CardContent, {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("form", {
                            onSubmit: handleSubmit(onSubmit),
                            className: "space-y-4",
                            children: [
                                error && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_alert.Alert, {
                                    variant: "destructive",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                                            className: "h-4 w-4"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_alert.AlertDescription, {
                                            children: error
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_label.Label, {
                                            htmlFor: "username",
                                            children: "Username"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
                                            id: "username",
                                            type: "text",
                                            placeholder: "Enter your username",
                                            ...register("username"),
                                            className: errors.username ? "border-red-500" : "",
                                            disabled: isLoading
                                        }),
                                        errors.username && /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-sm text-red-500",
                                            children: errors.username.message
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_label.Label, {
                                            htmlFor: "password",
                                            children: "Password"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "relative",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
                                                    id: "password",
                                                    type: showPassword ? "text" : "password",
                                                    placeholder: "Enter your password",
                                                    ...register("password"),
                                                    className: errors.password ? "border-red-500 pr-10" : "pr-10",
                                                    disabled: isLoading
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
                                                    type: "button",
                                                    variant: "ghost",
                                                    size: "sm",
                                                    className: "absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",
                                                    onClick: ()=>setShowPassword(!showPassword),
                                                    disabled: isLoading,
                                                    "aria-label": showPassword ? "Hide password" : "Show password",
                                                    children: showPassword ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.EyeOff, {
                                                        className: "h-4 w-4 text-muted-foreground"
                                                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Eye, {
                                                        className: "h-4 w-4 text-muted-foreground"
                                                    })
                                                })
                                            ]
                                        }),
                                        errors.password && /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-sm text-red-500",
                                            children: errors.password.message
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_checkbox.Checkbox, {
                                                    id: "rememberMe",
                                                    checked: watch("rememberMe"),
                                                    onCheckedChange: (checked)=>setValue("rememberMe", !!checked),
                                                    disabled: isLoading
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_label.Label, {
                                                    htmlFor: "rememberMe",
                                                    className: "text-sm",
                                                    children: "Remember me"
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_link.default, {
                                            href: "/auth/forgot-password",
                                            className: "text-sm text-primary hover:underline",
                                            children: "Forgot password?"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
                                    type: "submit",
                                    className: "w-full",
                                    disabled: isLoading,
                                    children: isLoading ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Loader2, {
                                                className: "mr-2 h-4 w-4 animate-spin"
                                            }),
                                            "Signing in..."
                                        ]
                                    }) : "Sign In"
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "mt-6 text-center",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                className: "text-sm text-muted-foreground",
                                children: [
                                    "Don't have an account?",
                                    " ",
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_link.default, {
                                        href: "/auth/register",
                                        className: "text-primary hover:underline font-medium",
                                        children: "Sign up"
                                    })
                                ]
                            })
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "mt-4 pt-4 border-t",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: "Secure access to UAV management system"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "flex justify-center items-center mt-2 space-x-4 text-xs text-muted-foreground",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                children: "\uD83D\uDD12 SSL Encrypted"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                children: "\uD83D\uDEE1️ Multi-factor Auth"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                children: "\uD83D\uDCCA Audit Logged"
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    ]
                })
            ]
        })
    });
}

//# sourceMappingURL=data:application/json;base64,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