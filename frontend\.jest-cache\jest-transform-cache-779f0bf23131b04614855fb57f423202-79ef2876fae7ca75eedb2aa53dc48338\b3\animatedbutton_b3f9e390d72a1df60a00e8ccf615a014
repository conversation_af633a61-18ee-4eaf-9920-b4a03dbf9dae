9e367d4085736bb7bf81654e97597b40
"use client";
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AnimatedButton: function() {
        return AnimatedButton;
    },
    AnimatedIconButton: function() {
        return AnimatedIconButton;
    },
    FloatingActionButton: function() {
        return FloatingActionButton;
    },
    ProgressButton: function() {
        return ProgressButton;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _framermotion = require("framer-motion");
const _button = require("./button");
const _utils = require("../../lib/utils");
const _animations = require("../../lib/animations");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function AnimatedButton({ children, className, disabled = false, loading = false, ripple = false, glow = false, magnetic = false, onClick, ...props }) {
    const [isClicked, setIsClicked] = _react.default.useState(false);
    const [ripplePosition, setRipplePosition] = _react.default.useState({
        x: 0,
        y: 0
    });
    const handleClick = (e)=>{
        if (disabled || loading) return;
        if (ripple) {
            const rect = e.currentTarget.getBoundingClientRect();
            setRipplePosition({
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            });
            setIsClicked(true);
            setTimeout(()=>setIsClicked(false), 600);
        }
        onClick?.(e);
    };
    const buttonVariant = {
        rest: {
            scale: 1
        },
        hover: {
            scale: disabled ? 1 : 1.05,
            boxShadow: glow ? "0 0 20px rgba(59, 130, 246, 0.5)" : undefined
        },
        tap: {
            scale: disabled ? 1 : 0.95
        }
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        variants: (0, _animations.getAnimationVariants)(buttonVariant),
        initial: "rest",
        whileHover: !disabled ? "hover" : undefined,
        whileTap: !disabled ? "tap" : undefined,
        className: "relative inline-block",
        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_button.Button, {
            className: (0, _utils.cn)("relative overflow-hidden", loading && "cursor-wait", className),
            disabled: disabled || loading,
            onClick: handleClick,
            ...props,
            children: [
                loading && /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                    initial: {
                        opacity: 0
                    },
                    animate: {
                        opacity: 1
                    },
                    className: "absolute inset-0 flex items-center justify-center bg-current/10",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                        animate: {
                            rotate: 360
                        },
                        transition: {
                            duration: 1,
                            repeat: Infinity,
                            ease: "linear"
                        },
                        className: "w-4 h-4 border-2 border-current border-t-transparent rounded-full"
                    })
                }),
                ripple && isClicked && /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                    initial: {
                        scale: 0,
                        opacity: 0.5
                    },
                    animate: {
                        scale: 4,
                        opacity: 0
                    },
                    transition: {
                        duration: 0.6,
                        ease: "easeOut"
                    },
                    className: "absolute bg-white rounded-full pointer-events-none",
                    style: {
                        left: ripplePosition.x - 10,
                        top: ripplePosition.y - 10,
                        width: 20,
                        height: 20
                    }
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                    animate: loading ? {
                        opacity: 0.5
                    } : {
                        opacity: 1
                    },
                    className: "flex items-center space-x-2",
                    children: children
                })
            ]
        })
    });
}
function FloatingActionButton({ children, className, position = "bottom-right", ...props }) {
    const positionClasses = {
        "bottom-right": "fixed bottom-6 right-6",
        "bottom-left": "fixed bottom-6 left-6",
        "top-right": "fixed top-6 right-6",
        "top-left": "fixed top-6 left-6"
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
        initial: {
            scale: 0,
            opacity: 0
        },
        animate: {
            scale: 1,
            opacity: 1
        },
        exit: {
            scale: 0,
            opacity: 0
        },
        whileHover: {
            scale: 1.1
        },
        whileTap: {
            scale: 0.9
        },
        className: (0, _utils.cn)(positionClasses[position], "z-50"),
        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(AnimatedButton, {
            className: (0, _utils.cn)("rounded-full w-14 h-14 shadow-lg", className),
            glow: true,
            ripple: true,
            ...props,
            children: children
        })
    });
}
function AnimatedIconButton({ icon, label, showLabel = false, className, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(AnimatedButton, {
        className: (0, _utils.cn)("relative group", !showLabel && "w-10 h-10 p-0", className),
        ...props,
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                whileHover: {
                    rotate: 15
                },
                transition: {
                    duration: 0.2
                },
                children: icon
            }),
            label && showLabel && /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                className: "ml-2",
                children: label
            }),
            label && !showLabel && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_framermotion.motion.div, {
                initial: {
                    opacity: 0,
                    scale: 0.8,
                    y: 10
                },
                whileHover: {
                    opacity: 1,
                    scale: 1,
                    y: 0
                },
                className: "absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap pointer-events-none",
                children: [
                    label,
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"
                    })
                ]
            })
        ]
    });
}
function ProgressButton({ progress = 0, showProgress = false, children, className, ...props }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(AnimatedButton, {
        className: (0, _utils.cn)("relative overflow-hidden", className),
        ...props,
        children: [
            showProgress && /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                initial: {
                    width: 0
                },
                animate: {
                    width: `${progress}%`
                },
                transition: {
                    duration: 0.3
                },
                className: "absolute left-0 top-0 h-full bg-white/20 pointer-events-none"
            }),
            children
        ]
    });
}

//# sourceMappingURL=data:application/json;base64,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