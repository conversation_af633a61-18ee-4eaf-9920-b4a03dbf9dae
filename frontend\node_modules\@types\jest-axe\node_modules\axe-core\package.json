{"name": "axe-core", "description": "Accessibility engine for automated Web UI testing", "version": "3.5.6", "license": "MPL-2.0", "engines": {"node": ">=4"}, "contributors": [{"name": "<PERSON>", "organization": "Deque Systems, Inc.", "url": "http://deque.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "organization": "Deque Systems, Inc.", "url": "http://deque.com/"}, {"name": "<PERSON><PERSON><PERSON>", "organization": "Deque Systems, Inc.", "url": "http://deque.com/"}, {"name": "<PERSON><PERSON>", "organization": "Deque Systems, Inc.", "url": "http://deque.com/"}, {"name": "<PERSON><PERSON>", "organization": "Deque Systems, Inc.", "url": "http://deque.com/"}], "homepage": "https://www.deque.com/axe/", "repository": {"type": "git", "url": "https://github.com/dequelabs/axe-core.git"}, "keywords": ["Accessibility", "a11y", "testing", "unit", "tdd", "bdd", "axe"], "main": "axe.js", "typings": "axe.d.ts", "standard-version": {"scripts": {"postbump": "npm ci && npm run sri-update"}}, "scripts": {"start": "npm run develop", "develop": "grunt dev --force", "api-docs": "jsdoc --configure .jsdoc.json", "imports-gen": "node ./build/imports-generator", "build": "grunt", "eslint": "eslint --color --format stylish '{lib,test,build,doc}/**/*.js' 'Gruntfile.js'", "test:headless": "node ./build/test/headless", "test": "tsc && grunt test", "test:examples": "node ./doc/examples/test-examples", "test:locales": "mocha test/test-locales.js", "test:rule-help-version": "mocha test/test-rule-help-version.js", "version": "echo \"use 'npm run release' to bump axe-core version\" && exit 1", "prepublishOnly": "grunt build && grunt file-exists", "release": "standard-version -a", "rule-gen": "node build/rule-generator", "next-release": "standard-version --scripts.prebump=./build/next-version.js --skip.commit=true --skip.tag=true", "sri-update": "grunt build && node build/sri-update && git add sri-history.json", "sri-validate": "node build/sri-update --validate", "fmt": "prettier --write *.{json,md,js} **/*.ts './{.github,build,doc,lib,test}/**/*.{json,md,js,ts,html}'"}, "devDependencies": {"@babel/core": "^7.5.4", "@babel/plugin-proposal-object-rest-spread": "^7.5.4", "@babel/polyfill": "^7.4.4", "@babel/preset-env": "^7.5.4", "@deque/dot": "^1.1.5", "aria-query": "^3.0.0", "axios": "^0.19.0", "babelify": "^10.0.0", "blanket": "~1.2.3", "browserify": "^16.2.3", "chai": "~4.2.0", "clone": "~2.1.1", "core-js": "^3.2.1", "css-selector-parser": "^1.3.0", "derequire": "^2.0.6", "emoji-regex": "8.0.0", "es6-promise": "^4.2.6", "eslint": "^6.1.0", "eslint-config-prettier": "^6.2.0", "execa": "^2.0.2", "fs-extra": "^8.0.1", "globby": "^10.0.0", "grunt": "^1.0.3", "grunt-babel": "^8.0.0", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^2.0.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-parallel": "^0.5.1", "grunt-run": "^0.8.1", "html-entities": "^1.2.0", "husky": "^3.0.0", "jquery": "^3.0.0", "jsdoc": "^3.5.5", "lint-staged": "^9.2.1", "make-dir": "^3.0.0", "markdown-table": "^1.1.2", "memoizee": "^0.4.14", "minami": "^1.2.3", "mkdirp": "^0.5.1", "mocha": "^5.2.0", "mocha-headless-chrome": "^2.0.3", "prettier": "^1.17.1", "puppeteer": "^2.0.0", "revalidator": "~0.3.1", "selenium-webdriver": "~3.6.0", "sinon": "^7.5.0", "sri-toolbox": "^0.2.0", "standard-version": "^7.0.0", "typescript": "^3.5.3", "uglify-js": "^3.4.4", "weakmap-polyfill": "^2.0.0"}, "lint-staged": {"*.{md,json,ts,html}": ["prettier --write", "git add"], "*.js": ["eslint --fix", "prettier --write", "git add"]}}