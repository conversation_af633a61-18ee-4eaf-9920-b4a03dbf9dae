1a44b9f96f20d09ad52af5622bf2516c
"use strict";
// Mock react-leaflet components (already mocked in jest.setup.js)
// Mock framer-motion
jest.mock("framer-motion", ()=>({
        motion: {
            div: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    ...props,
                    children: children
                })
        },
        AnimatePresence: ({ children })=>children
    }));
// Mock the animated map components
jest.mock("../animated-map-components", ()=>({
        AnimatedUAVMarker: ({ uav, onSelect })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `uav-marker-${uav.id}`,
                onClick: ()=>onSelect(uav),
                children: [
                    "UAV Marker: ",
                    uav.rfidTag
                ]
            }),
        AnimatedGeofence: ({ region })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `geofence-${region.id}`,
                children: [
                    "Geofence: ",
                    region.name
                ]
            }),
        AnimatedFlightPath: ({ path })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "flight-path",
                children: "Flight Path"
            }),
        AnimatedDockingStation: ({ station, onSelect })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `docking-station-${station.id}`,
                onClick: ()=>onSelect(station),
                children: [
                    "Docking Station: ",
                    station.name
                ]
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../../lib/test-utils");
const _interactivemap = /*#__PURE__*/ _interop_require_default(require("../interactive-map"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
describe("InteractiveMap Component", ()=>{
    const mockUAVs = [
        (0, _testutils.createMockUAV)({
            id: 1,
            rfidTag: "UAV-001",
            status: "AUTHORIZED",
            operationalStatus: "ACTIVE",
            location: {
                latitude: 40.7128,
                longitude: -74.0060
            }
        }),
        (0, _testutils.createMockUAV)({
            id: 2,
            rfidTag: "UAV-002",
            status: "AUTHORIZED",
            operationalStatus: "READY",
            location: {
                latitude: 40.7589,
                longitude: -73.9851
            }
        })
    ];
    const mockDockingStations = [
        (0, _testutils.createMockDockingStation)({
            id: 1,
            name: "Station Alpha",
            location: {
                latitude: 40.7505,
                longitude: -73.9934
            },
            status: "AVAILABLE"
        })
    ];
    const mockRegions = [
        {
            id: 1,
            name: "Zone A",
            description: "Authorized zone A",
            coordinates: [
                {
                    latitude: 40.7000,
                    longitude: -74.0200
                },
                {
                    latitude: 40.7200,
                    longitude: -74.0200
                },
                {
                    latitude: 40.7200,
                    longitude: -73.9800
                },
                {
                    latitude: 40.7000,
                    longitude: -73.9800
                }
            ],
            isActive: true
        }
    ];
    const defaultProps = {
        uavs: mockUAVs,
        dockingStations: mockDockingStations,
        regions: mockRegions,
        center: {
            latitude: 40.7128,
            longitude: -74.0060
        },
        zoom: 12,
        onUAVSelect: jest.fn(),
        onStationSelect: jest.fn(),
        onMapClick: jest.fn()
    };
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("tile-layer")).toBeInTheDocument();
    });
    it("renders UAV markers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("uav-marker-2")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Marker: UAV-001")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Marker: UAV-002")).toBeInTheDocument();
    });
    it("renders docking station markers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("docking-station-1")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Docking Station: Station Alpha")).toBeInTheDocument();
    });
    it("renders geofences for regions", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("geofence-1")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Geofence: Zone A")).toBeInTheDocument();
    });
    it("handles UAV selection", ()=>{
        const onUAVSelect = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onUAVSelect: onUAVSelect
        }));
        const uavMarker = _testutils.screen.getByTestId("uav-marker-1");
        _testutils.fireEvent.click(uavMarker);
        expect(onUAVSelect).toHaveBeenCalledWith(mockUAVs[0]);
    });
    it("handles docking station selection", ()=>{
        const onStationSelect = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onStationSelect: onStationSelect
        }));
        const stationMarker = _testutils.screen.getByTestId("docking-station-1");
        _testutils.fireEvent.click(stationMarker);
        expect(onStationSelect).toHaveBeenCalledWith(mockDockingStations[0]);
    });
    it("highlights selected UAV", ()=>{
        const selectedUAV = mockUAVs[0];
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            selectedUAV: selectedUAV
        }));
        const selectedMarker = _testutils.screen.getByTestId("uav-marker-1");
        expect(selectedMarker).toBeInTheDocument();
    // The selected state would be passed to the AnimatedUAVMarker component
    });
    it("shows flight paths when enabled", ()=>{
        const flightPaths = [
            {
                id: 1,
                uavId: 1,
                coordinates: [
                    {
                        latitude: 40.7128,
                        longitude: -74.0060
                    },
                    {
                        latitude: 40.7589,
                        longitude: -73.9851
                    }
                ],
                timestamp: new Date().toISOString()
            }
        ];
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            flightPaths: flightPaths,
            showFlightPaths: true
        }));
        expect(_testutils.screen.getByTestId("flight-path")).toBeInTheDocument();
    });
    it("filters UAVs by status", ()=>{
        const filteredProps = {
            ...defaultProps,
            uavs: mockUAVs.filter((uav)=>uav.operationalStatus === "ACTIVE")
        };
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...filteredProps
        }));
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-2")).not.toBeInTheDocument();
    });
    it("updates map center when prop changes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        const newCenter = {
            latitude: 41.8781,
            longitude: -87.6298
        };
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            center: newCenter
        }));
        // Map center update would be handled by the MapContainer component
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    });
    it("updates zoom level when prop changes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            zoom: 15
        }));
        // Zoom update would be handled by the MapContainer component
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    });
    it("handles empty UAV list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-1")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-2")).not.toBeInTheDocument();
    });
    it("handles empty docking stations list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            dockingStations: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("docking-station-1")).not.toBeInTheDocument();
    });
    it("handles empty regions list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            regions: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("geofence-1")).not.toBeInTheDocument();
    });
    it("shows loading state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            loading: true
        }));
        expect(_testutils.screen.getByTestId("map-loading")).toBeInTheDocument();
    });
    it("shows error state", ()=>{
        const errorMessage = "Failed to load map data";
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            error: errorMessage
        }));
        expect(_testutils.screen.getByText(errorMessage)).toBeInTheDocument();
        expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
    });
    it("supports different map layers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            mapLayer: "satellite"
        }));
        // Different tile layer would be rendered
        expect(_testutils.screen.getByTestId("tile-layer")).toBeInTheDocument();
    });
    it("handles real-time updates", async ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        const updatedUAVs = [
            ...mockUAVs,
            (0, _testutils.createMockUAV)({
                id: 3,
                rfidTag: "UAV-003",
                location: {
                    latitude: 40.7300,
                    longitude: -74.0000
                }
            })
        ];
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: updatedUAVs
        }));
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByTestId("uav-marker-3")).toBeInTheDocument();
        });
    });
    it("handles UAV location updates", ()=>{
        const updatedUAVs = mockUAVs.map((uav)=>uav.id === 1 ? {
                ...uav,
                location: {
                    latitude: 40.7200,
                    longitude: -74.0100
                }
            } : uav);
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: updatedUAVs
        }));
        // Updated location would be reflected in the marker position
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
    });
    it("supports clustering for many UAVs", ()=>{
        const manyUAVs = Array.from({
            length: 50
        }, (_, i)=>(0, _testutils.createMockUAV)({
                id: i + 1,
                rfidTag: `UAV-${(i + 1).toString().padStart(3, "0")}`,
                location: {
                    latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
                    longitude: -74.0060 + (Math.random() - 0.5) * 0.1
                }
            }));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: manyUAVs,
            enableClustering: true
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    // Clustering would be handled by the map library
    });
    it("handles map interaction events", ()=>{
        const onMapClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onMapClick: onMapClick
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        _testutils.fireEvent.click(mapContainer);
        // Map click would be handled by the MapContainer component
        expect(mapContainer).toBeInTheDocument();
    });
    it("supports custom map controls", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            showZoomControl: true,
            showScaleControl: true,
            showFullscreenControl: true
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    // Custom controls would be rendered as part of the map
    });
    it("handles responsive design", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            className: "h-96 w-full"
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        expect(mapContainer.parentElement).toHaveClass("h-96", "w-full");
    });
    it("supports accessibility features", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            "aria-label": "UAV tracking map",
            role: "application"
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        expect(mapContainer).toHaveAttribute("aria-label", "UAV tracking map");
        expect(mapContainer).toHaveAttribute("role", "application");
    });
});

//# sourceMappingURL=data:application/json;base64,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