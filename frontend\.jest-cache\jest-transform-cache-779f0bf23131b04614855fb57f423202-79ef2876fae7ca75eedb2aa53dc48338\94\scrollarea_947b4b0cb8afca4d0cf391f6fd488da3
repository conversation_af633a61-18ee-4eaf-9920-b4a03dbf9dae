d963a1f1a007188ed4cbe056eae3f04a
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    ScrollArea: function() {
        return ScrollArea;
    },
    ScrollBar: function() {
        return ScrollBar;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _reactscrollarea = /*#__PURE__*/ _interop_require_wildcard(require("@radix-ui/react-scroll-area"));
const _utils = require("../../lib/utils");
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const ScrollArea = /*#__PURE__*/ _react.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)(_reactscrollarea.Root, {
        ref: ref,
        className: (0, _utils.cn)("relative overflow-hidden", className),
        ...props,
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_reactscrollarea.Viewport, {
                className: "h-full w-full rounded-[inherit]",
                children: children
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(ScrollBar, {}),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_reactscrollarea.Corner, {})
        ]
    }));
ScrollArea.displayName = _reactscrollarea.Root.displayName;
const ScrollBar = /*#__PURE__*/ _react.forwardRef(({ className, orientation = "vertical", ...props }, ref)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_reactscrollarea.ScrollAreaScrollbar, {
        ref: ref,
        orientation: orientation,
        className: (0, _utils.cn)("flex touch-none select-none transition-colors", orientation === "vertical" && "h-full w-2.5 border-l border-l-transparent p-[1px]", orientation === "horizontal" && "h-2.5 flex-col border-t border-t-transparent p-[1px]", className),
        ...props,
        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_reactscrollarea.ScrollAreaThumb, {
            className: "relative flex-1 rounded-full bg-border"
        })
    }));
ScrollBar.displayName = _reactscrollarea.ScrollAreaScrollbar.displayName;

//# sourceMappingURL=data:application/json;base64,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