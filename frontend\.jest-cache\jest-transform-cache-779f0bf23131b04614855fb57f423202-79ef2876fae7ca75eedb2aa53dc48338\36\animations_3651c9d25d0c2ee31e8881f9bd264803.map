{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\lib\\animations.ts"], "sourcesContent": ["import { Variants, Transition } from 'framer-motion'\n\n// Animation durations\nexport const ANIMATION_DURATION = {\n  fast: 0.2,\n  normal: 0.3,\n  slow: 0.5,\n  page: 0.4,\n  modal: 0.3,\n  hover: 0.2,\n} as const\n\n// Easing functions\nexport const EASING = {\n  easeOut: [0.0, 0.0, 0.2, 1],\n  easeIn: [0.4, 0.0, 1, 1],\n  easeInOut: [0.4, 0.0, 0.2, 1],\n  spring: { type: 'spring', damping: 25, stiffness: 300 },\n  springBouncy: { type: 'spring', damping: 15, stiffness: 400 },\n} as const\n\n// Common transitions\nexport const transitions: Record<string, Transition> = {\n  default: {\n    duration: ANIMATION_DURATION.normal,\n    ease: EASING.easeOut,\n  },\n  fast: {\n    duration: ANIMATION_DURATION.fast,\n    ease: EASING.easeOut,\n  },\n  slow: {\n    duration: ANIMATION_DURATION.slow,\n    ease: EASING.easeOut,\n  },\n  spring: EASING.spring,\n  springBouncy: EASING.springBouncy,\n}\n\n// Page transition variants\nexport const pageVariants: Variants = {\n  initial: {\n    opacity: 0,\n    y: 20,\n    scale: 0.98,\n  },\n  animate: {\n    opacity: 1,\n    y: 0,\n    scale: 1,\n    transition: {\n      duration: ANIMATION_DURATION.page,\n      ease: EASING.easeOut,\n    },\n  },\n  exit: {\n    opacity: 0,\n    y: -20,\n    scale: 0.98,\n    transition: {\n      duration: ANIMATION_DURATION.fast,\n      ease: EASING.easeIn,\n    },\n  },\n}\n\n// Card animation variants\nexport const cardVariants: Variants = {\n  hidden: {\n    opacity: 0,\n    y: 20,\n    scale: 0.95,\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    scale: 1,\n    transition: transitions.default,\n  },\n  hover: {\n    y: -2,\n    scale: 1.02,\n    transition: transitions.fast,\n  },\n  tap: {\n    scale: 0.98,\n    transition: transitions.fast,\n  },\n}\n\n// Stagger container variants\nexport const staggerContainer: Variants = {\n  hidden: {\n    opacity: 0,\n  },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.1,\n      delayChildren: 0.1,\n    },\n  },\n}\n\n// Stagger item variants\nexport const staggerItem: Variants = {\n  hidden: {\n    opacity: 0,\n    y: 20,\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: transitions.default,\n  },\n}\n\n// Modal/Dialog variants\nexport const modalVariants: Variants = {\n  hidden: {\n    opacity: 0,\n    scale: 0.95,\n    y: 20,\n  },\n  visible: {\n    opacity: 1,\n    scale: 1,\n    y: 0,\n    transition: {\n      duration: ANIMATION_DURATION.modal,\n      ease: EASING.easeOut,\n    },\n  },\n  exit: {\n    opacity: 0,\n    scale: 0.95,\n    y: 20,\n    transition: {\n      duration: ANIMATION_DURATION.fast,\n      ease: EASING.easeIn,\n    },\n  },\n}\n\n// Sidebar variants\nexport const sidebarVariants: Variants = {\n  collapsed: {\n    width: 64,\n    transition: {\n      duration: ANIMATION_DURATION.normal,\n      ease: EASING.easeInOut,\n    },\n  },\n  expanded: {\n    width: 256,\n    transition: {\n      duration: ANIMATION_DURATION.normal,\n      ease: EASING.easeInOut,\n    },\n  },\n}\n\n// Button hover variants\nexport const buttonVariants: Variants = {\n  rest: {\n    scale: 1,\n  },\n  hover: {\n    scale: 1.05,\n    transition: transitions.fast,\n  },\n  tap: {\n    scale: 0.95,\n    transition: transitions.fast,\n  },\n}\n\n// Loading spinner variants\nexport const spinnerVariants: Variants = {\n  animate: {\n    rotate: 360,\n    transition: {\n      duration: 1,\n      repeat: Infinity,\n      ease: 'linear',\n    },\n  },\n}\n\n// Fade variants\nexport const fadeVariants: Variants = {\n  hidden: {\n    opacity: 0,\n  },\n  visible: {\n    opacity: 1,\n    transition: transitions.default,\n  },\n  exit: {\n    opacity: 0,\n    transition: transitions.fast,\n  },\n}\n\n// Slide variants\nexport const slideVariants = {\n  up: {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0, transition: transitions.default },\n    exit: { opacity: 0, y: -20, transition: transitions.fast },\n  },\n  down: {\n    hidden: { opacity: 0, y: -20 },\n    visible: { opacity: 1, y: 0, transition: transitions.default },\n    exit: { opacity: 0, y: 20, transition: transitions.fast },\n  },\n  left: {\n    hidden: { opacity: 0, x: 20 },\n    visible: { opacity: 1, x: 0, transition: transitions.default },\n    exit: { opacity: 0, x: -20, transition: transitions.fast },\n  },\n  right: {\n    hidden: { opacity: 0, x: -20 },\n    visible: { opacity: 1, x: 0, transition: transitions.default },\n    exit: { opacity: 0, x: 20, transition: transitions.fast },\n  },\n}\n\n// Alert/notification variants\nexport const alertVariants: Variants = {\n  hidden: {\n    opacity: 0,\n    x: 100,\n    scale: 0.95,\n  },\n  visible: {\n    opacity: 1,\n    x: 0,\n    scale: 1,\n    transition: {\n      duration: ANIMATION_DURATION.normal,\n      ease: EASING.easeOut,\n    },\n  },\n  exit: {\n    opacity: 0,\n    x: 100,\n    scale: 0.95,\n    transition: {\n      duration: ANIMATION_DURATION.fast,\n      ease: EASING.easeIn,\n    },\n  },\n}\n\n// Map marker variants\nexport const markerVariants: Variants = {\n  hidden: {\n    scale: 0,\n    opacity: 0,\n  },\n  visible: {\n    scale: 1,\n    opacity: 1,\n    transition: transitions.springBouncy,\n  },\n  hover: {\n    scale: 1.2,\n    transition: transitions.fast,\n  },\n  selected: {\n    scale: 1.3,\n    transition: transitions.spring,\n  },\n}\n\n// Utility function to check if user prefers reduced motion\nexport const prefersReducedMotion = () => {\n  if (typeof window === 'undefined') return false\n  return window.matchMedia('(prefers-reduced-motion: reduce)').matches\n}\n\n// Utility function to get animation variants based on user preference\nexport const getAnimationVariants = (variants: Variants): Variants => {\n  if (prefersReducedMotion()) {\n    // Return simplified variants for reduced motion\n    return Object.keys(variants).reduce((acc, key) => {\n      acc[key] = { opacity: variants[key]?.opacity || 1 }\n      return acc\n    }, {} as Variants)\n  }\n  return variants\n}\n\n// Utility function to get transition based on user preference\nexport const getTransition = (transition: Transition): Transition => {\n  if (prefersReducedMotion()) {\n    return { duration: 0.01 }\n  }\n  return transition\n}\n"], "names": ["ANIMATION_DURATION", "EASING", "alertVariants", "buttonVariants", "cardVariants", "fadeVariants", "getAnimationVariants", "getTransition", "markerVariants", "modalVariants", "pageVariants", "prefersReducedMotion", "sidebarVariants", "slideVariants", "spinnerVariants", "stagger<PERSON><PERSON><PERSON>", "staggerItem", "transitions", "fast", "normal", "slow", "page", "modal", "hover", "easeOut", "easeIn", "easeInOut", "spring", "type", "damping", "stiffness", "springBouncy", "default", "duration", "ease", "initial", "opacity", "y", "scale", "animate", "transition", "exit", "hidden", "visible", "tap", "stagger<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collapsed", "width", "expanded", "rest", "rotate", "repeat", "Infinity", "up", "down", "left", "x", "right", "selected", "window", "matchMedia", "matches", "variants", "Object", "keys", "reduce", "acc", "key"], "mappings": ";;;;;;;;;;;IAGaA,kBAAkB;eAAlBA;;IAUAC,MAAM;eAANA;;IAwNAC,aAAa;eAAbA;;IAlEAC,cAAc;eAAdA;;IAhGAC,YAAY;eAAZA;;IA2HAC,YAAY;eAAZA;;IA6FAC,oBAAoB;eAApBA;;IAYAC,aAAa;eAAbA;;IAvCAC,cAAc;eAAdA;;IA1IAC,aAAa;eAAbA;;IA9EAC,YAAY;eAAZA;;IA6OAC,oBAAoB;eAApBA;;IApIAC,eAAe;eAAfA;;IA4DAC,aAAa;eAAbA;;IA3BAC,eAAe;eAAfA;;IAvFAC,gBAAgB;eAAhBA;;IAcAC,WAAW;eAAXA;;IAnFAC,WAAW;eAAXA;;;AAnBN,MAAMjB,qBAAqB;IAChCkB,MAAM;IACNC,QAAQ;IACRC,MAAM;IACNC,MAAM;IACNC,OAAO;IACPC,OAAO;AACT;AAGO,MAAMtB,SAAS;IACpBuB,SAAS;QAAC;QAAK;QAAK;QAAK;KAAE;IAC3BC,QAAQ;QAAC;QAAK;QAAK;QAAG;KAAE;IACxBC,WAAW;QAAC;QAAK;QAAK;QAAK;KAAE;IAC7BC,QAAQ;QAAEC,MAAM;QAAUC,SAAS;QAAIC,WAAW;IAAI;IACtDC,cAAc;QAAEH,MAAM;QAAUC,SAAS;QAAIC,WAAW;IAAI;AAC9D;AAGO,MAAMb,cAA0C;IACrDe,SAAS;QACPC,UAAUjC,mBAAmBmB,MAAM;QACnCe,MAAMjC,OAAOuB,OAAO;IACtB;IACAN,MAAM;QACJe,UAAUjC,mBAAmBkB,IAAI;QACjCgB,MAAMjC,OAAOuB,OAAO;IACtB;IACAJ,MAAM;QACJa,UAAUjC,mBAAmBoB,IAAI;QACjCc,MAAMjC,OAAOuB,OAAO;IACtB;IACAG,QAAQ1B,OAAO0B,MAAM;IACrBI,cAAc9B,OAAO8B,YAAY;AACnC;AAGO,MAAMrB,eAAyB;IACpCyB,SAAS;QACPC,SAAS;QACTC,GAAG;QACHC,OAAO;IACT;IACAC,SAAS;QACPH,SAAS;QACTC,GAAG;QACHC,OAAO;QACPE,YAAY;YACVP,UAAUjC,mBAAmBqB,IAAI;YACjCa,MAAMjC,OAAOuB,OAAO;QACtB;IACF;IACAiB,MAAM;QACJL,SAAS;QACTC,GAAG,CAAC;QACJC,OAAO;QACPE,YAAY;YACVP,UAAUjC,mBAAmBkB,IAAI;YACjCgB,MAAMjC,OAAOwB,MAAM;QACrB;IACF;AACF;AAGO,MAAMrB,eAAyB;IACpCsC,QAAQ;QACNN,SAAS;QACTC,GAAG;QACHC,OAAO;IACT;IACAK,SAAS;QACPP,SAAS;QACTC,GAAG;QACHC,OAAO;QACPE,YAAYvB,YAAYe,OAAO;IACjC;IACAT,OAAO;QACLc,GAAG,CAAC;QACJC,OAAO;QACPE,YAAYvB,YAAYC,IAAI;IAC9B;IACA0B,KAAK;QACHN,OAAO;QACPE,YAAYvB,YAAYC,IAAI;IAC9B;AACF;AAGO,MAAMH,mBAA6B;IACxC2B,QAAQ;QACNN,SAAS;IACX;IACAO,SAAS;QACPP,SAAS;QACTI,YAAY;YACVK,iBAAiB;YACjBC,eAAe;QACjB;IACF;AACF;AAGO,MAAM9B,cAAwB;IACnC0B,QAAQ;QACNN,SAAS;QACTC,GAAG;IACL;IACAM,SAAS;QACPP,SAAS;QACTC,GAAG;QACHG,YAAYvB,YAAYe,OAAO;IACjC;AACF;AAGO,MAAMvB,gBAA0B;IACrCiC,QAAQ;QACNN,SAAS;QACTE,OAAO;QACPD,GAAG;IACL;IACAM,SAAS;QACPP,SAAS;QACTE,OAAO;QACPD,GAAG;QACHG,YAAY;YACVP,UAAUjC,mBAAmBsB,KAAK;YAClCY,MAAMjC,OAAOuB,OAAO;QACtB;IACF;IACAiB,MAAM;QACJL,SAAS;QACTE,OAAO;QACPD,GAAG;QACHG,YAAY;YACVP,UAAUjC,mBAAmBkB,IAAI;YACjCgB,MAAMjC,OAAOwB,MAAM;QACrB;IACF;AACF;AAGO,MAAMb,kBAA4B;IACvCmC,WAAW;QACTC,OAAO;QACPR,YAAY;YACVP,UAAUjC,mBAAmBmB,MAAM;YACnCe,MAAMjC,OAAOyB,SAAS;QACxB;IACF;IACAuB,UAAU;QACRD,OAAO;QACPR,YAAY;YACVP,UAAUjC,mBAAmBmB,MAAM;YACnCe,MAAMjC,OAAOyB,SAAS;QACxB;IACF;AACF;AAGO,MAAMvB,iBAA2B;IACtC+C,MAAM;QACJZ,OAAO;IACT;IACAf,OAAO;QACLe,OAAO;QACPE,YAAYvB,YAAYC,IAAI;IAC9B;IACA0B,KAAK;QACHN,OAAO;QACPE,YAAYvB,YAAYC,IAAI;IAC9B;AACF;AAGO,MAAMJ,kBAA4B;IACvCyB,SAAS;QACPY,QAAQ;QACRX,YAAY;YACVP,UAAU;YACVmB,QAAQC;YACRnB,MAAM;QACR;IACF;AACF;AAGO,MAAM7B,eAAyB;IACpCqC,QAAQ;QACNN,SAAS;IACX;IACAO,SAAS;QACPP,SAAS;QACTI,YAAYvB,YAAYe,OAAO;IACjC;IACAS,MAAM;QACJL,SAAS;QACTI,YAAYvB,YAAYC,IAAI;IAC9B;AACF;AAGO,MAAML,gBAAgB;IAC3ByC,IAAI;QACFZ,QAAQ;YAAEN,SAAS;YAAGC,GAAG;QAAG;QAC5BM,SAAS;YAAEP,SAAS;YAAGC,GAAG;YAAGG,YAAYvB,YAAYe,OAAO;QAAC;QAC7DS,MAAM;YAAEL,SAAS;YAAGC,GAAG,CAAC;YAAIG,YAAYvB,YAAYC,IAAI;QAAC;IAC3D;IACAqC,MAAM;QACJb,QAAQ;YAAEN,SAAS;YAAGC,GAAG,CAAC;QAAG;QAC7BM,SAAS;YAAEP,SAAS;YAAGC,GAAG;YAAGG,YAAYvB,YAAYe,OAAO;QAAC;QAC7DS,MAAM;YAAEL,SAAS;YAAGC,GAAG;YAAIG,YAAYvB,YAAYC,IAAI;QAAC;IAC1D;IACAsC,MAAM;QACJd,QAAQ;YAAEN,SAAS;YAAGqB,GAAG;QAAG;QAC5Bd,SAAS;YAAEP,SAAS;YAAGqB,GAAG;YAAGjB,YAAYvB,YAAYe,OAAO;QAAC;QAC7DS,MAAM;YAAEL,SAAS;YAAGqB,GAAG,CAAC;YAAIjB,YAAYvB,YAAYC,IAAI;QAAC;IAC3D;IACAwC,OAAO;QACLhB,QAAQ;YAAEN,SAAS;YAAGqB,GAAG,CAAC;QAAG;QAC7Bd,SAAS;YAAEP,SAAS;YAAGqB,GAAG;YAAGjB,YAAYvB,YAAYe,OAAO;QAAC;QAC7DS,MAAM;YAAEL,SAAS;YAAGqB,GAAG;YAAIjB,YAAYvB,YAAYC,IAAI;QAAC;IAC1D;AACF;AAGO,MAAMhB,gBAA0B;IACrCwC,QAAQ;QACNN,SAAS;QACTqB,GAAG;QACHnB,OAAO;IACT;IACAK,SAAS;QACPP,SAAS;QACTqB,GAAG;QACHnB,OAAO;QACPE,YAAY;YACVP,UAAUjC,mBAAmBmB,MAAM;YACnCe,MAAMjC,OAAOuB,OAAO;QACtB;IACF;IACAiB,MAAM;QACJL,SAAS;QACTqB,GAAG;QACHnB,OAAO;QACPE,YAAY;YACVP,UAAUjC,mBAAmBkB,IAAI;YACjCgB,MAAMjC,OAAOwB,MAAM;QACrB;IACF;AACF;AAGO,MAAMjB,iBAA2B;IACtCkC,QAAQ;QACNJ,OAAO;QACPF,SAAS;IACX;IACAO,SAAS;QACPL,OAAO;QACPF,SAAS;QACTI,YAAYvB,YAAYc,YAAY;IACtC;IACAR,OAAO;QACLe,OAAO;QACPE,YAAYvB,YAAYC,IAAI;IAC9B;IACAyC,UAAU;QACRrB,OAAO;QACPE,YAAYvB,YAAYU,MAAM;IAChC;AACF;AAGO,MAAMhB,uBAAuB;IAClC,IAAI,OAAOiD,WAAW,aAAa,OAAO;IAC1C,OAAOA,OAAOC,UAAU,CAAC,oCAAoCC,OAAO;AACtE;AAGO,MAAMxD,uBAAuB,CAACyD;IACnC,IAAIpD,wBAAwB;QAC1B,gDAAgD;QAChD,OAAOqD,OAAOC,IAAI,CAACF,UAAUG,MAAM,CAAC,CAACC,KAAKC;YACxCD,GAAG,CAACC,IAAI,GAAG;gBAAEhC,SAAS2B,QAAQ,CAACK,IAAI,EAAEhC,WAAW;YAAE;YAClD,OAAO+B;QACT,GAAG,CAAC;IACN;IACA,OAAOJ;AACT;AAGO,MAAMxD,gBAAgB,CAACiC;IAC5B,IAAI7B,wBAAwB;QAC1B,OAAO;YAAEsB,UAAU;QAAK;IAC1B;IACA,OAAOO;AACT"}