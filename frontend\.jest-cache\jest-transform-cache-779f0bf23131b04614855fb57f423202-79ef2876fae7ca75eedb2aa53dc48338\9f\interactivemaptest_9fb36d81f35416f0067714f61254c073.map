{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\features\\map\\__tests__\\interactive-map.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { InteractiveMap } from '../interactive-map'\nimport { createMockUAV, createMockDockingStation } from '@/lib/test-utils'\nimport { UAV, DockingStation } from '@/types/uav'\n\n// Mock react-leaflet components (already mocked in jest.setup.js)\n// Mock framer-motion\njest.mock('framer-motion', () => ({\n  motion: {\n    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,\n  },\n  AnimatePresence: ({ children }: any) => children,\n}))\n\n// Mock the animated map components\njest.mock('../animated-map-components', () => ({\n  AnimatedUAVMarker: ({ uav, onSelect }: any) => (\n    <div \n      data-testid={`uav-marker-${uav.id}`}\n      onClick={() => onSelect(uav)}\n    >\n      UAV Marker: {uav.rfidTag}\n    </div>\n  ),\n  AnimatedGeofence: ({ region }: any) => (\n    <div data-testid={`geofence-${region.id}`}>\n      Geofence: {region.name}\n    </div>\n  ),\n  AnimatedFlightPath: ({ path }: any) => (\n    <div data-testid=\"flight-path\">\n      Flight Path\n    </div>\n  ),\n  AnimatedDockingStation: ({ station, onSelect }: any) => (\n    <div \n      data-testid={`docking-station-${station.id}`}\n      onClick={() => onSelect(station)}\n    >\n      Docking Station: {station.name}\n    </div>\n  ),\n}))\n\ndescribe('InteractiveMap Component', () => {\n  const mockUAVs: UAV[] = [\n    createMockUAV({\n      id: 1,\n      rfidTag: 'UAV-001',\n      status: 'AUTHORIZED',\n      operationalStatus: 'ACTIVE',\n      location: { latitude: 40.7128, longitude: -74.0060 },\n    }),\n    createMockUAV({\n      id: 2,\n      rfidTag: 'UAV-002',\n      status: 'AUTHORIZED',\n      operationalStatus: 'READY',\n      location: { latitude: 40.7589, longitude: -73.9851 },\n    }),\n  ]\n\n  const mockDockingStations: DockingStation[] = [\n    createMockDockingStation({\n      id: 1,\n      name: 'Station Alpha',\n      location: { latitude: 40.7505, longitude: -73.9934 },\n      status: 'AVAILABLE',\n    }),\n  ]\n\n  const mockRegions = [\n    {\n      id: 1,\n      name: 'Zone A',\n      description: 'Authorized zone A',\n      coordinates: [\n        { latitude: 40.7000, longitude: -74.0200 },\n        { latitude: 40.7200, longitude: -74.0200 },\n        { latitude: 40.7200, longitude: -73.9800 },\n        { latitude: 40.7000, longitude: -73.9800 },\n      ],\n      isActive: true,\n    },\n  ]\n\n  const defaultProps = {\n    uavs: mockUAVs,\n    dockingStations: mockDockingStations,\n    regions: mockRegions,\n    center: { latitude: 40.7128, longitude: -74.0060 },\n    zoom: 12,\n    onUAVSelect: jest.fn(),\n    onStationSelect: jest.fn(),\n    onMapClick: jest.fn(),\n  }\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n  })\n\n  it('renders correctly', () => {\n    render(<InteractiveMap {...defaultProps} />)\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    expect(screen.getByTestId('tile-layer')).toBeInTheDocument()\n  })\n\n  it('renders UAV markers', () => {\n    render(<InteractiveMap {...defaultProps} />)\n    \n    expect(screen.getByTestId('uav-marker-1')).toBeInTheDocument()\n    expect(screen.getByTestId('uav-marker-2')).toBeInTheDocument()\n    expect(screen.getByText('UAV Marker: UAV-001')).toBeInTheDocument()\n    expect(screen.getByText('UAV Marker: UAV-002')).toBeInTheDocument()\n  })\n\n  it('renders docking station markers', () => {\n    render(<InteractiveMap {...defaultProps} />)\n    \n    expect(screen.getByTestId('docking-station-1')).toBeInTheDocument()\n    expect(screen.getByText('Docking Station: Station Alpha')).toBeInTheDocument()\n  })\n\n  it('renders geofences for regions', () => {\n    render(<InteractiveMap {...defaultProps} />)\n    \n    expect(screen.getByTestId('geofence-1')).toBeInTheDocument()\n    expect(screen.getByText('Geofence: Zone A')).toBeInTheDocument()\n  })\n\n  it('handles UAV selection', () => {\n    const onUAVSelect = jest.fn()\n    render(<InteractiveMap {...defaultProps} onUAVSelect={onUAVSelect} />)\n    \n    const uavMarker = screen.getByTestId('uav-marker-1')\n    fireEvent.click(uavMarker)\n    \n    expect(onUAVSelect).toHaveBeenCalledWith(mockUAVs[0])\n  })\n\n  it('handles docking station selection', () => {\n    const onStationSelect = jest.fn()\n    render(<InteractiveMap {...defaultProps} onStationSelect={onStationSelect} />)\n    \n    const stationMarker = screen.getByTestId('docking-station-1')\n    fireEvent.click(stationMarker)\n    \n    expect(onStationSelect).toHaveBeenCalledWith(mockDockingStations[0])\n  })\n\n  it('highlights selected UAV', () => {\n    const selectedUAV = mockUAVs[0]\n    render(<InteractiveMap {...defaultProps} selectedUAV={selectedUAV} />)\n    \n    const selectedMarker = screen.getByTestId('uav-marker-1')\n    expect(selectedMarker).toBeInTheDocument()\n    // The selected state would be passed to the AnimatedUAVMarker component\n  })\n\n  it('shows flight paths when enabled', () => {\n    const flightPaths = [\n      {\n        id: 1,\n        uavId: 1,\n        coordinates: [\n          { latitude: 40.7128, longitude: -74.0060 },\n          { latitude: 40.7589, longitude: -73.9851 },\n        ],\n        timestamp: new Date().toISOString(),\n      },\n    ]\n    \n    render(\n      <InteractiveMap \n        {...defaultProps} \n        flightPaths={flightPaths}\n        showFlightPaths={true}\n      />\n    )\n    \n    expect(screen.getByTestId('flight-path')).toBeInTheDocument()\n  })\n\n  it('filters UAVs by status', () => {\n    const filteredProps = {\n      ...defaultProps,\n      uavs: mockUAVs.filter(uav => uav.operationalStatus === 'ACTIVE'),\n    }\n    \n    render(<InteractiveMap {...filteredProps} />)\n    \n    expect(screen.getByTestId('uav-marker-1')).toBeInTheDocument()\n    expect(screen.queryByTestId('uav-marker-2')).not.toBeInTheDocument()\n  })\n\n  it('updates map center when prop changes', () => {\n    const { rerender } = render(<InteractiveMap {...defaultProps} />)\n    \n    const newCenter = { latitude: 41.8781, longitude: -87.6298 }\n    rerender(<InteractiveMap {...defaultProps} center={newCenter} />)\n    \n    // Map center update would be handled by the MapContainer component\n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n  })\n\n  it('updates zoom level when prop changes', () => {\n    const { rerender } = render(<InteractiveMap {...defaultProps} />)\n    \n    rerender(<InteractiveMap {...defaultProps} zoom={15} />)\n    \n    // Zoom update would be handled by the MapContainer component\n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n  })\n\n  it('handles empty UAV list', () => {\n    render(<InteractiveMap {...defaultProps} uavs={[]} />)\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    expect(screen.queryByTestId('uav-marker-1')).not.toBeInTheDocument()\n    expect(screen.queryByTestId('uav-marker-2')).not.toBeInTheDocument()\n  })\n\n  it('handles empty docking stations list', () => {\n    render(<InteractiveMap {...defaultProps} dockingStations={[]} />)\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    expect(screen.queryByTestId('docking-station-1')).not.toBeInTheDocument()\n  })\n\n  it('handles empty regions list', () => {\n    render(<InteractiveMap {...defaultProps} regions={[]} />)\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    expect(screen.queryByTestId('geofence-1')).not.toBeInTheDocument()\n  })\n\n  it('shows loading state', () => {\n    render(<InteractiveMap {...defaultProps} loading={true} />)\n    \n    expect(screen.getByTestId('map-loading')).toBeInTheDocument()\n  })\n\n  it('shows error state', () => {\n    const errorMessage = 'Failed to load map data'\n    render(<InteractiveMap {...defaultProps} error={errorMessage} />)\n    \n    expect(screen.getByText(errorMessage)).toBeInTheDocument()\n    expect(screen.getByRole('alert')).toBeInTheDocument()\n  })\n\n  it('supports different map layers', () => {\n    render(<InteractiveMap {...defaultProps} mapLayer=\"satellite\" />)\n    \n    // Different tile layer would be rendered\n    expect(screen.getByTestId('tile-layer')).toBeInTheDocument()\n  })\n\n  it('handles real-time updates', async () => {\n    const { rerender } = render(<InteractiveMap {...defaultProps} />)\n    \n    const updatedUAVs = [\n      ...mockUAVs,\n      createMockUAV({\n        id: 3,\n        rfidTag: 'UAV-003',\n        location: { latitude: 40.7300, longitude: -74.0000 },\n      }),\n    ]\n    \n    rerender(<InteractiveMap {...defaultProps} uavs={updatedUAVs} />)\n    \n    await waitFor(() => {\n      expect(screen.getByTestId('uav-marker-3')).toBeInTheDocument()\n    })\n  })\n\n  it('handles UAV location updates', () => {\n    const updatedUAVs = mockUAVs.map(uav => \n      uav.id === 1 \n        ? { ...uav, location: { latitude: 40.7200, longitude: -74.0100 } }\n        : uav\n    )\n    \n    const { rerender } = render(<InteractiveMap {...defaultProps} />)\n    rerender(<InteractiveMap {...defaultProps} uavs={updatedUAVs} />)\n    \n    // Updated location would be reflected in the marker position\n    expect(screen.getByTestId('uav-marker-1')).toBeInTheDocument()\n  })\n\n  it('supports clustering for many UAVs', () => {\n    const manyUAVs = Array.from({ length: 50 }, (_, i) => \n      createMockUAV({\n        id: i + 1,\n        rfidTag: `UAV-${(i + 1).toString().padStart(3, '0')}`,\n        location: { \n          latitude: 40.7128 + (Math.random() - 0.5) * 0.1, \n          longitude: -74.0060 + (Math.random() - 0.5) * 0.1 \n        },\n      })\n    )\n    \n    render(<InteractiveMap {...defaultProps} uavs={manyUAVs} enableClustering={true} />)\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    // Clustering would be handled by the map library\n  })\n\n  it('handles map interaction events', () => {\n    const onMapClick = jest.fn()\n    render(<InteractiveMap {...defaultProps} onMapClick={onMapClick} />)\n    \n    const mapContainer = screen.getByTestId('map-container')\n    fireEvent.click(mapContainer)\n    \n    // Map click would be handled by the MapContainer component\n    expect(mapContainer).toBeInTheDocument()\n  })\n\n  it('supports custom map controls', () => {\n    render(\n      <InteractiveMap \n        {...defaultProps} \n        showZoomControl={true}\n        showScaleControl={true}\n        showFullscreenControl={true}\n      />\n    )\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    // Custom controls would be rendered as part of the map\n  })\n\n  it('handles responsive design', () => {\n    render(<InteractiveMap {...defaultProps} className=\"h-96 w-full\" />)\n    \n    const mapContainer = screen.getByTestId('map-container')\n    expect(mapContainer.parentElement).toHaveClass('h-96', 'w-full')\n  })\n\n  it('supports accessibility features', () => {\n    render(\n      <InteractiveMap \n        {...defaultProps} \n        aria-label=\"UAV tracking map\"\n        role=\"application\"\n      />\n    )\n    \n    const mapContainer = screen.getByTestId('map-container')\n    expect(mapContainer).toHaveAttribute('aria-label', 'UAV tracking map')\n    expect(mapContainer).toHaveAttribute('role', 'application')\n  })\n})\n"], "names": ["jest", "mock", "motion", "div", "children", "props", "AnimatePresence", "AnimatedUAVMarker", "uav", "onSelect", "data-testid", "id", "onClick", "rfidTag", "AnimatedGeofence", "region", "name", "AnimatedFlightPath", "path", "AnimatedDockingStation", "station", "describe", "mockUAVs", "createMockUAV", "status", "operationalStatus", "location", "latitude", "longitude", "mockDockingStations", "createMockDockingStation", "mockRegions", "description", "coordinates", "isActive", "defaultProps", "uavs", "dockingStations", "regions", "center", "zoom", "onUAVSelect", "fn", "onStationSelect", "onMapClick", "beforeEach", "clearAllMocks", "it", "render", "InteractiveMap", "expect", "screen", "getByTestId", "toBeInTheDocument", "getByText", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireEvent", "click", "toHaveBeenCalledWith", "stationMarker", "selectedUAV", "<PERSON><PERSON><PERSON><PERSON>", "flightPaths", "uavId", "timestamp", "Date", "toISOString", "showFlightPaths", "filteredProps", "filter", "queryByTestId", "not", "rerender", "newCenter", "loading", "errorMessage", "error", "getByRole", "map<PERSON>ayer", "updatedUAVs", "waitFor", "map", "manyUAVs", "Array", "from", "length", "_", "i", "toString", "padStart", "Math", "random", "enableClustering", "mapContainer", "showZoomControl", "showScaleControl", "showFullscreenControl", "className", "parentElement", "toHaveClass", "aria-label", "role", "toHaveAttribute"], "mappings": ";AAMA,kEAAkE;AAClE,qBAAqB;AACrBA,KAAKC,IAAI,CAAC,iBAAiB,IAAO,CAAA;QAChCC,QAAQ;YACNC,KAAK,CAAC,EAAEC,QAAQ,EAAE,GAAGC,OAAY,iBAAK,qBAACF;oBAAK,GAAGE,KAAK;8BAAGD;;QACzD;QACAE,iBAAiB,CAAC,EAAEF,QAAQ,EAAO,GAAKA;IAC1C,CAAA;AAEA,mCAAmC;AACnCJ,KAAKC,IAAI,CAAC,8BAA8B,IAAO,CAAA;QAC7CM,mBAAmB,CAAC,EAAEC,GAAG,EAAEC,QAAQ,EAAO,iBACxC,sBAACN;gBACCO,eAAa,CAAC,WAAW,EAAEF,IAAIG,EAAE,CAAC,CAAC;gBACnCC,SAAS,IAAMH,SAASD;;oBACzB;oBACcA,IAAIK,OAAO;;;QAG5BC,kBAAkB,CAAC,EAAEC,MAAM,EAAO,iBAChC,sBAACZ;gBAAIO,eAAa,CAAC,SAAS,EAAEK,OAAOJ,EAAE,CAAC,CAAC;;oBAAE;oBAC9BI,OAAOC,IAAI;;;QAG1BC,oBAAoB,CAAC,EAAEC,IAAI,EAAO,iBAChC,qBAACf;gBAAIO,eAAY;0BAAc;;QAIjCS,wBAAwB,CAAC,EAAEC,OAAO,EAAEX,QAAQ,EAAO,iBACjD,sBAACN;gBACCO,eAAa,CAAC,gBAAgB,EAAEU,QAAQT,EAAE,CAAC,CAAC;gBAC5CC,SAAS,IAAMH,SAASW;;oBACzB;oBACmBA,QAAQJ,IAAI;;;IAGpC,CAAA;;;;;8DA3CkB;2BACiC;gCACpB;;;;;;AA2C/BK,SAAS,4BAA4B;IACnC,MAAMC,WAAkB;QACtBC,IAAAA,wBAAa,EAAC;YACZZ,IAAI;YACJE,SAAS;YACTW,QAAQ;YACRC,mBAAmB;YACnBC,UAAU;gBAAEC,UAAU;gBAASC,WAAW,CAAC;YAAQ;QACrD;QACAL,IAAAA,wBAAa,EAAC;YACZZ,IAAI;YACJE,SAAS;YACTW,QAAQ;YACRC,mBAAmB;YACnBC,UAAU;gBAAEC,UAAU;gBAASC,WAAW,CAAC;YAAQ;QACrD;KACD;IAED,MAAMC,sBAAwC;QAC5CC,IAAAA,mCAAwB,EAAC;YACvBnB,IAAI;YACJK,MAAM;YACNU,UAAU;gBAAEC,UAAU;gBAASC,WAAW,CAAC;YAAQ;YACnDJ,QAAQ;QACV;KACD;IAED,MAAMO,cAAc;QAClB;YACEpB,IAAI;YACJK,MAAM;YACNgB,aAAa;YACbC,aAAa;gBACX;oBAAEN,UAAU;oBAASC,WAAW,CAAC;gBAAQ;gBACzC;oBAAED,UAAU;oBAASC,WAAW,CAAC;gBAAQ;gBACzC;oBAAED,UAAU;oBAASC,WAAW,CAAC;gBAAQ;gBACzC;oBAAED,UAAU;oBAASC,WAAW,CAAC;gBAAQ;aAC1C;YACDM,UAAU;QACZ;KACD;IAED,MAAMC,eAAe;QACnBC,MAAMd;QACNe,iBAAiBR;QACjBS,SAASP;QACTQ,QAAQ;YAAEZ,UAAU;YAASC,WAAW,CAAC;QAAQ;QACjDY,MAAM;QACNC,aAAazC,KAAK0C,EAAE;QACpBC,iBAAiB3C,KAAK0C,EAAE;QACxBE,YAAY5C,KAAK0C,EAAE;IACrB;IAEAG,WAAW;QACT7C,KAAK8C,aAAa;IACpB;IAEAC,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;;QAEvCe,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;QAC7DH,OAAOC,iBAAM,CAACC,WAAW,CAAC,eAAeC,iBAAiB;IAC5D;IAEAN,GAAG,uBAAuB;QACxBC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;;QAEvCe,OAAOC,iBAAM,CAACC,WAAW,CAAC,iBAAiBC,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACC,WAAW,CAAC,iBAAiBC,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACG,SAAS,CAAC,wBAAwBD,iBAAiB;QACjEH,OAAOC,iBAAM,CAACG,SAAS,CAAC,wBAAwBD,iBAAiB;IACnE;IAEAN,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;;QAEvCe,OAAOC,iBAAM,CAACC,WAAW,CAAC,sBAAsBC,iBAAiB;QACjEH,OAAOC,iBAAM,CAACG,SAAS,CAAC,mCAAmCD,iBAAiB;IAC9E;IAEAN,GAAG,iCAAiC;QAClCC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;;QAEvCe,OAAOC,iBAAM,CAACC,WAAW,CAAC,eAAeC,iBAAiB;QAC1DH,OAAOC,iBAAM,CAACG,SAAS,CAAC,qBAAqBD,iBAAiB;IAChE;IAEAN,GAAG,yBAAyB;QAC1B,MAAMN,cAAczC,KAAK0C,EAAE;QAC3BM,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAEM,aAAaA;;QAEtD,MAAMc,YAAYJ,iBAAM,CAACC,WAAW,CAAC;QACrCI,oBAAS,CAACC,KAAK,CAACF;QAEhBL,OAAOT,aAAaiB,oBAAoB,CAACpC,QAAQ,CAAC,EAAE;IACtD;IAEAyB,GAAG,qCAAqC;QACtC,MAAMJ,kBAAkB3C,KAAK0C,EAAE;QAC/BM,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAEQ,iBAAiBA;;QAE1D,MAAMgB,gBAAgBR,iBAAM,CAACC,WAAW,CAAC;QACzCI,oBAAS,CAACC,KAAK,CAACE;QAEhBT,OAAOP,iBAAiBe,oBAAoB,CAAC7B,mBAAmB,CAAC,EAAE;IACrE;IAEAkB,GAAG,2BAA2B;QAC5B,MAAMa,cAActC,QAAQ,CAAC,EAAE;QAC/B0B,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAEyB,aAAaA;;QAEtD,MAAMC,iBAAiBV,iBAAM,CAACC,WAAW,CAAC;QAC1CF,OAAOW,gBAAgBR,iBAAiB;IACxC,wEAAwE;IAC1E;IAEAN,GAAG,mCAAmC;QACpC,MAAMe,cAAc;YAClB;gBACEnD,IAAI;gBACJoD,OAAO;gBACP9B,aAAa;oBACX;wBAAEN,UAAU;wBAASC,WAAW,CAAC;oBAAQ;oBACzC;wBAAED,UAAU;wBAASC,WAAW,CAAC;oBAAQ;iBAC1C;gBACDoC,WAAW,IAAIC,OAAOC,WAAW;YACnC;SACD;QAEDlB,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;YACZ,GAAGd,YAAY;YAChB2B,aAAaA;YACbK,iBAAiB;;QAIrBjB,OAAOC,iBAAM,CAACC,WAAW,CAAC,gBAAgBC,iBAAiB;IAC7D;IAEAN,GAAG,0BAA0B;QAC3B,MAAMqB,gBAAgB;YACpB,GAAGjC,YAAY;YACfC,MAAMd,SAAS+C,MAAM,CAAC7D,CAAAA,MAAOA,IAAIiB,iBAAiB,KAAK;QACzD;QAEAuB,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGmB,aAAa;;QAExClB,OAAOC,iBAAM,CAACC,WAAW,CAAC,iBAAiBC,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACmB,aAAa,CAAC,iBAAiBC,GAAG,CAAClB,iBAAiB;IACpE;IAEAN,GAAG,wCAAwC;QACzC,MAAM,EAAEyB,QAAQ,EAAE,GAAGxB,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;;QAE5D,MAAMsC,YAAY;YAAE9C,UAAU;YAASC,WAAW,CAAC;QAAQ;QAC3D4C,uBAAS,qBAACvB,8BAAc;YAAE,GAAGd,YAAY;YAAEI,QAAQkC;;QAEnD,mEAAmE;QACnEvB,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;IAC/D;IAEAN,GAAG,wCAAwC;QACzC,MAAM,EAAEyB,QAAQ,EAAE,GAAGxB,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;;QAE5DqC,uBAAS,qBAACvB,8BAAc;YAAE,GAAGd,YAAY;YAAEK,MAAM;;QAEjD,6DAA6D;QAC7DU,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;IAC/D;IAEAN,GAAG,0BAA0B;QAC3BC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAEC,MAAM,EAAE;;QAEjDc,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;QAC7DH,OAAOC,iBAAM,CAACmB,aAAa,CAAC,iBAAiBC,GAAG,CAAClB,iBAAiB;QAClEH,OAAOC,iBAAM,CAACmB,aAAa,CAAC,iBAAiBC,GAAG,CAAClB,iBAAiB;IACpE;IAEAN,GAAG,uCAAuC;QACxCC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAEE,iBAAiB,EAAE;;QAE5Da,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;QAC7DH,OAAOC,iBAAM,CAACmB,aAAa,CAAC,sBAAsBC,GAAG,CAAClB,iBAAiB;IACzE;IAEAN,GAAG,8BAA8B;QAC/BC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAEG,SAAS,EAAE;;QAEpDY,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;QAC7DH,OAAOC,iBAAM,CAACmB,aAAa,CAAC,eAAeC,GAAG,CAAClB,iBAAiB;IAClE;IAEAN,GAAG,uBAAuB;QACxBC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAEuC,SAAS;;QAElDxB,OAAOC,iBAAM,CAACC,WAAW,CAAC,gBAAgBC,iBAAiB;IAC7D;IAEAN,GAAG,qBAAqB;QACtB,MAAM4B,eAAe;QACrB3B,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAEyC,OAAOD;;QAEhDzB,OAAOC,iBAAM,CAACG,SAAS,CAACqB,eAAetB,iBAAiB;QACxDH,OAAOC,iBAAM,CAAC0B,SAAS,CAAC,UAAUxB,iBAAiB;IACrD;IAEAN,GAAG,iCAAiC;QAClCC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAE2C,UAAS;;QAElD,yCAAyC;QACzC5B,OAAOC,iBAAM,CAACC,WAAW,CAAC,eAAeC,iBAAiB;IAC5D;IAEAN,GAAG,6BAA6B;QAC9B,MAAM,EAAEyB,QAAQ,EAAE,GAAGxB,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;;QAE5D,MAAM4C,cAAc;eACfzD;YACHC,IAAAA,wBAAa,EAAC;gBACZZ,IAAI;gBACJE,SAAS;gBACTa,UAAU;oBAAEC,UAAU;oBAASC,WAAW,CAAC;gBAAQ;YACrD;SACD;QAED4C,uBAAS,qBAACvB,8BAAc;YAAE,GAAGd,YAAY;YAAEC,MAAM2C;;QAEjD,MAAMC,IAAAA,kBAAO,EAAC;YACZ9B,OAAOC,iBAAM,CAACC,WAAW,CAAC,iBAAiBC,iBAAiB;QAC9D;IACF;IAEAN,GAAG,gCAAgC;QACjC,MAAMgC,cAAczD,SAAS2D,GAAG,CAACzE,CAAAA,MAC/BA,IAAIG,EAAE,KAAK,IACP;gBAAE,GAAGH,GAAG;gBAAEkB,UAAU;oBAAEC,UAAU;oBAASC,WAAW,CAAC;gBAAQ;YAAE,IAC/DpB;QAGN,MAAM,EAAEgE,QAAQ,EAAE,GAAGxB,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;;QAC5DqC,uBAAS,qBAACvB,8BAAc;YAAE,GAAGd,YAAY;YAAEC,MAAM2C;;QAEjD,6DAA6D;QAC7D7B,OAAOC,iBAAM,CAACC,WAAW,CAAC,iBAAiBC,iBAAiB;IAC9D;IAEAN,GAAG,qCAAqC;QACtC,MAAMmC,WAAWC,MAAMC,IAAI,CAAC;YAAEC,QAAQ;QAAG,GAAG,CAACC,GAAGC,IAC9ChE,IAAAA,wBAAa,EAAC;gBACZZ,IAAI4E,IAAI;gBACR1E,SAAS,CAAC,IAAI,EAAE,AAAC0E,CAAAA,IAAI,CAAA,EAAGC,QAAQ,GAAGC,QAAQ,CAAC,GAAG,KAAK,CAAC;gBACrD/D,UAAU;oBACRC,UAAU,UAAU,AAAC+D,CAAAA,KAAKC,MAAM,KAAK,GAAE,IAAK;oBAC5C/D,WAAW,CAAC,UAAU,AAAC8D,CAAAA,KAAKC,MAAM,KAAK,GAAE,IAAK;gBAChD;YACF;QAGF3C,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAEC,MAAM8C;YAAUU,kBAAkB;;QAE3E1C,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;IAC7D,iDAAiD;IACnD;IAEAN,GAAG,kCAAkC;QACnC,MAAMH,aAAa5C,KAAK0C,EAAE;QAC1BM,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAES,YAAYA;;QAErD,MAAMiD,eAAe1C,iBAAM,CAACC,WAAW,CAAC;QACxCI,oBAAS,CAACC,KAAK,CAACoC;QAEhB,2DAA2D;QAC3D3C,OAAO2C,cAAcxC,iBAAiB;IACxC;IAEAN,GAAG,gCAAgC;QACjCC,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;YACZ,GAAGd,YAAY;YAChB2D,iBAAiB;YACjBC,kBAAkB;YAClBC,uBAAuB;;QAI3B9C,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;IAC7D,uDAAuD;IACzD;IAEAN,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAACC,8BAAc;YAAE,GAAGd,YAAY;YAAE8D,WAAU;;QAEnD,MAAMJ,eAAe1C,iBAAM,CAACC,WAAW,CAAC;QACxCF,OAAO2C,aAAaK,aAAa,EAAEC,WAAW,CAAC,QAAQ;IACzD;IAEApD,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBACJ,qBAACC,8BAAc;YACZ,GAAGd,YAAY;YAChBiE,cAAW;YACXC,MAAK;;QAIT,MAAMR,eAAe1C,iBAAM,CAACC,WAAW,CAAC;QACxCF,OAAO2C,cAAcS,eAAe,CAAC,cAAc;QACnDpD,OAAO2C,cAAcS,eAAe,CAAC,QAAQ;IAC/C;AACF"}