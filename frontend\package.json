{"name": "uav-management-frontend", "version": "1.0.0", "description": "Next.js frontend for UAV Docking Management System", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:production": "NODE_ENV=production next build && next export", "start": "next start", "start:production": "NODE_ENV=production next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false", "test:performance": "jest --testPathPattern=performance", "e2e": "playwright test", "e2e:ui": "playwright test --ui", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "clean": "rm -rf .next out node_modules/.cache", "precommit": "npm run lint:fix && npm run type-check"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.59.20", "@tanstack/react-query-devtools": "^5.83.0", "@types/leaflet": "^1.9.14", "@types/node": "^22.9.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "critters": "^0.0.23", "date-fns": "^4.1.0", "framer-motion": "^11.11.17", "immer": "^10.1.1", "leaflet": "^1.9.4", "lucide-react": "^0.454.0", "next": "^14.2.15", "postcss": "^8.4.49", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.4.1", "react-leaflet": "^4.2.1", "recharts": "^2.13.3", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.3", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@playwright/test": "^1.48.2", "@storybook/addon-essentials": "^8.4.2", "@storybook/addon-interactions": "^8.4.2", "@storybook/addon-links": "^8.4.2", "@storybook/blocks": "^8.4.2", "@storybook/nextjs": "^8.4.2", "@storybook/react": "^8.4.2", "@storybook/test": "^8.4.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/jest-axe": "^3.5.9", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "eslint": "^8.57.1", "eslint-config-next": "^14.2.15", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "jest": "^29.7.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "storybook": "^8.4.2", "webpack-bundle-analyzer": "^4.10.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}