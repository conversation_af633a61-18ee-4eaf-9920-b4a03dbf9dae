0c6f712f394ef94474d074a93c57851a
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    useAuth: function() {
        return useAuth;
    },
    useAuthStore: function() {
        return useAuthStore;
    },
    usePermissions: function() {
        return usePermissions;
    }
});
const _zustand = require("zustand");
const _middleware = require("zustand/middleware");
const _immer = require("zustand/middleware/immer");
const _authapi = require("../api/auth-api");
const _reacthottoast = require("react-hot-toast");
const useAuthStore = (0, _zustand.create)()((0, _middleware.devtools)((0, _middleware.persist)((0, _middleware.subscribeWithSelector)((0, _immer.immer)((set, get)=>({
        // Initial state
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        // Login
        login: async (credentials)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.login(credentials);
                if (response.success && response.data) {
                    set((state)=>{
                        state.user = response.data.user;
                        state.token = response.data.token;
                        state.refreshToken = response.data.refreshToken;
                        state.isAuthenticated = true;
                        state.isLoading = false;
                        state.error = null;
                    });
                    // Set token in API client
                    _authapi.authApi.setAuthToken(response.data.token);
                    _reacthottoast.toast.success("Login successful");
                    return true;
                } else {
                    throw new Error(response.message || "Login failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Login failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                    state.isAuthenticated = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Logout
        logout: async ()=>{
            try {
                await _authapi.authApi.logout();
            } catch (error) {
                console.error("Logout error:", error);
            } finally{
                set((state)=>{
                    state.user = null;
                    state.token = null;
                    state.refreshToken = null;
                    state.isAuthenticated = false;
                    state.error = null;
                });
                // Clear token from API client
                _authapi.authApi.clearAuthToken();
                _reacthottoast.toast.success("Logged out successfully");
            }
        },
        // Register
        register: async (userData)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.register(userData);
                if (response.success) {
                    set((state)=>{
                        state.isLoading = false;
                    });
                    _reacthottoast.toast.success("Registration successful. Please log in.");
                    return true;
                } else {
                    throw new Error(response.message || "Registration failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Registration failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Refresh token
        refreshAuthToken: async ()=>{
            const { refreshToken } = get();
            if (!refreshToken) return false;
            try {
                const response = await _authapi.authApi.refreshToken({
                    refreshToken
                });
                if (response.success && response.data) {
                    set((state)=>{
                        state.token = response.data.token;
                        state.refreshToken = response.data.refreshToken;
                        state.error = null;
                    });
                    // Update token in API client
                    _authapi.authApi.setAuthToken(response.data.token);
                    return true;
                } else {
                    // Refresh failed, logout user
                    get().logout();
                    return false;
                }
            } catch (error) {
                console.error("Token refresh failed:", error);
                get().logout();
                return false;
            }
        },
        // Change password
        changePassword: async (passwords)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.changePassword(passwords);
                if (response.success) {
                    set((state)=>{
                        state.isLoading = false;
                    });
                    _reacthottoast.toast.success("Password changed successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Password change failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Password change failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Update profile
        updateProfile: async (userData)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.updateProfile(userData);
                if (response.success && response.data) {
                    set((state)=>{
                        state.user = response.data;
                        state.isLoading = false;
                    });
                    _reacthottoast.toast.success("Profile updated successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Profile update failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Profile update failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Permission checks
        hasPermission: (check)=>{
            const { user } = get();
            if (!user || !user.permissions) return false;
            return user.permissions.some((permission)=>permission.resource === check.resource && permission.action === check.action);
        },
        hasRole: (roleName)=>{
            const { user } = get();
            if (!user || !user.roles) return false;
            return user.roles.some((role)=>role.name === roleName);
        },
        canAccess: (resource, action)=>{
            return get().hasPermission({
                resource,
                action
            });
        },
        // Session management
        checkSession: async ()=>{
            const { token, refreshToken } = get();
            if (!token) {
                set((state)=>{
                    state.isAuthenticated = false;
                });
                return false;
            }
            try {
                // Check if token is still valid
                const isValid = await _authapi.authApi.validateToken();
                if (isValid) {
                    set((state)=>{
                        state.isAuthenticated = true;
                    });
                    return true;
                } else if (refreshToken) {
                    // Try to refresh token
                    return await get().refreshAuthToken();
                } else {
                    // No valid token or refresh token
                    get().logout();
                    return false;
                }
            } catch (error) {
                console.error("Session check failed:", error);
                get().logout();
                return false;
            }
        },
        // Fetch user profile
        fetchUserProfile: async ()=>{
            try {
                const user = await _authapi.authApi.getUserProfile();
                set((state)=>{
                    state.user = user;
                });
            } catch (error) {
                console.error("Failed to fetch user profile:", error);
            }
        },
        // Update last activity
        updateLastActivity: ()=>{
            const { user } = get();
            if (user) {
                set((state)=>{
                    if (state.user) {
                        state.user.lastLogin = new Date().toISOString();
                    }
                });
            }
        },
        // Clear error
        clearError: ()=>{
            set((state)=>{
                state.error = null;
            });
        },
        // Set loading
        setLoading: (loading)=>{
            set((state)=>{
                state.isLoading = loading;
            });
        }
    }))), {
    name: "auth-store",
    partialize: (state)=>({
            user: state.user,
            token: state.token,
            refreshToken: state.refreshToken,
            isAuthenticated: state.isAuthenticated
        })
}), {
    name: "auth-store"
}));
const useAuth = ()=>useAuthStore((state)=>({
            user: state.user,
            isAuthenticated: state.isAuthenticated,
            isLoading: state.isLoading,
            error: state.error
        }));
const usePermissions = ()=>useAuthStore((state)=>({
            hasPermission: state.hasPermission,
            hasRole: state.hasRole,
            canAccess: state.canAccess
        }));
// Auto-refresh token before expiration
let refreshInterval = null;
useAuthStore.subscribe((state)=>state.token, (token)=>{
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
    if (token) {
        // Refresh token every 50 minutes (assuming 60-minute expiration)
        refreshInterval = setInterval(()=>{
            useAuthStore.getState().refreshAuthToken();
        }, 50 * 60 * 1000);
    }
});

//# sourceMappingURL=data:application/json;base64,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