f8d32b386e0fa843f53853897d70068c
"use strict";
// Mock Next.js router
jest.mock("next/navigation", ()=>({
        useRouter: jest.fn()
    }));
// Mock the auth store
jest.mock("@/stores/auth-store");
// Mock react-hot-toast
jest.mock("react-hot-toast", ()=>({
        toast: {
            success: jest.fn(),
            error: jest.fn()
        }
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _loginform = require("../login-form");
const _authstore = require("../../../stores/auth-store");
const _navigation = require("next/navigation");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockUseAuthStore = _authstore.useAuthStore;
describe("LoginForm Component", ()=>{
    const mockPush = jest.fn();
    const mockLogin = jest.fn();
    const mockClearError = jest.fn();
    beforeEach(()=>{
        jest.clearAllMocks();
        _navigation.useRouter.mockReturnValue({
            push: mockPush,
            replace: jest.fn(),
            prefetch: jest.fn()
        });
        // Mock auth store
        mockUseAuthStore.mockReturnValue({
            login: mockLogin,
            isLoading: false,
            error: null,
            clearError: mockClearError,
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            logout: jest.fn(),
            register: jest.fn(),
            refreshToken: jest.fn(),
            changePassword: jest.fn(),
            updateProfile: jest.fn(),
            hasPermission: jest.fn(),
            hasRole: jest.fn(),
            canAccess: jest.fn(),
            checkSession: jest.fn(),
            setLoading: jest.fn(),
            fetchUserProfile: jest.fn(),
            updateLastActivity: jest.fn()
        });
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        expect(_testutils.screen.getByText("UAV Control System")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Sign in to access the UAV management dashboard")).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText(/username/i)).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText(/password/i)).toBeInTheDocument();
        expect(_testutils.screen.getByRole("button", {
            name: /sign in/i
        })).toBeInTheDocument();
    });
    it("handles form submission with valid data", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockClearError).toHaveBeenCalled();
            expect(mockLogin).toHaveBeenCalledWith({
                username: "testuser",
                password: "password123",
                rememberMe: false
            });
        });
    });
    it("redirects to dashboard on successful login", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockPush).toHaveBeenCalledWith("/dashboard");
        });
    });
    it("calls onSuccess callback when provided", async ()=>{
        const mockOnSuccess = jest.fn();
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {
            onSuccess: mockOnSuccess
        }));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockOnSuccess).toHaveBeenCalled();
            expect(mockPush).not.toHaveBeenCalled();
        });
    });
    it("redirects to custom path when provided", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {
            redirectTo: "/custom-path"
        }));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockPush).toHaveBeenCalledWith("/custom-path");
        });
    });
    it("handles remember me checkbox", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const rememberMeCheckbox = _testutils.screen.getByLabelText(/remember me/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(rememberMeCheckbox);
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockLogin).toHaveBeenCalledWith({
                username: "testuser",
                password: "password123",
                rememberMe: true
            });
        });
    });
    it("toggles password visibility", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const toggleButton = _testutils.screen.getByRole("button", {
            name: /toggle password visibility/i
        });
        expect(passwordInput).toHaveAttribute("type", "password");
        _testutils.fireEvent.click(toggleButton);
        expect(passwordInput).toHaveAttribute("type", "text");
        _testutils.fireEvent.click(toggleButton);
        expect(passwordInput).toHaveAttribute("type", "password");
    });
    it("displays loading state during login", ()=>{
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            isLoading: true
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const submitButton = _testutils.screen.getByRole("button", {
            name: /signing in/i
        });
        expect(submitButton).toBeDisabled();
        expect(_testutils.screen.getByTestId("loading-spinner")).toBeInTheDocument();
    });
    it("displays error message when login fails", ()=>{
        const errorMessage = "Invalid credentials";
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            error: errorMessage
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        expect(_testutils.screen.getByText(errorMessage)).toBeInTheDocument();
        expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
    });
    it("validates required fields", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText(/username is required/i)).toBeInTheDocument();
            expect(_testutils.screen.getByText(/password is required/i)).toBeInTheDocument();
        });
        expect(mockLogin).not.toHaveBeenCalled();
    });
    it("validates username format", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "ab"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText(/username must be at least 3 characters/i)).toBeInTheDocument();
        });
    });
    it("validates password format", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText(/password must be at least 6 characters/i)).toBeInTheDocument();
        });
    });
    it("clears error when user starts typing", ()=>{
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            error: "Some error"
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        expect(_testutils.screen.getByText("Some error")).toBeInTheDocument();
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "test"
            }
        });
        expect(mockClearError).toHaveBeenCalled();
    });
    it("handles keyboard navigation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        usernameInput.focus();
        expect(usernameInput).toHaveFocus();
        _testutils.fireEvent.keyDown(usernameInput, {
            key: "Tab"
        });
        expect(passwordInput).toHaveFocus();
        _testutils.fireEvent.keyDown(passwordInput, {
            key: "Tab"
        });
    // Should focus on remember me checkbox or submit button
    });
    it("handles Enter key submission", async ()=>{
        mockLogin.mockResolvedValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.keyDown(passwordInput, {
            key: "Enter",
            code: "Enter"
        });
        await (0, _testutils.waitFor)(()=>{
            expect(mockLogin).toHaveBeenCalled();
        });
    });
    it("maintains accessibility standards", async ()=>{
        const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        // Check for proper labeling
        expect(_testutils.screen.getByLabelText(/username/i)).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText(/password/i)).toBeInTheDocument();
        // Check for form structure
        expect(_testutils.screen.getByRole("form") || container.querySelector("form")).toBeInTheDocument();
        // Run accessibility tests
        await (0, _testutils.runAxeTest)(container);
    });
    it("supports autofill and autocomplete", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        expect(usernameInput).toHaveAttribute("autoComplete", "username");
        expect(passwordInput).toHaveAttribute("autoComplete", "current-password");
    });
    it("prevents multiple submissions", async ()=>{
        mockLogin.mockImplementation(()=>new Promise((resolve)=>setTimeout(()=>resolve(true), 100)));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        _testutils.fireEvent.click(submitButton);
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(mockLogin).toHaveBeenCalledTimes(1);
        });
    });
    it("handles network errors gracefully", async ()=>{
        mockLogin.mockResolvedValue(false);
        mockUseAuthStore.mockReturnValue({
            ...mockUseAuthStore(),
            error: "Network error"
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_loginform.LoginForm, {}));
        const usernameInput = _testutils.screen.getByLabelText(/username/i);
        const passwordInput = _testutils.screen.getByLabelText(/password/i);
        const submitButton = _testutils.screen.getByRole("button", {
            name: /sign in/i
        });
        _testutils.fireEvent.change(usernameInput, {
            target: {
                value: "testuser"
            }
        });
        _testutils.fireEvent.change(passwordInput, {
            target: {
                value: "password123"
            }
        });
        _testutils.fireEvent.click(submitButton);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Network error")).toBeInTheDocument();
            expect(mockPush).not.toHaveBeenCalled();
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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