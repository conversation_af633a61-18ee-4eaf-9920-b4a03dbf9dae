{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\__tests__\\animations.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { mockFramerMotion, mockPrefersReducedMotion } from '@/lib/test-utils'\n\n// Mock framer-motion\nmockFramerMotion()\n\n// Test component that uses animations\nconst AnimatedTestComponent = ({ children }: { children: React.ReactNode }) => (\n  <div data-testid=\"animated-component\" className=\"animate-fade-in\">\n    {children}\n  </div>\n)\n\ndescribe('Animation System Tests', () => {\n  beforeEach(() => {\n    jest.clearAllMocks()\n    // Reset prefers-reduced-motion\n    mockPrefersReducedMotion(false)\n  })\n\n  describe('Framer Motion Integration', () => {\n    it('renders motion components correctly', () => {\n      render(\n        <AnimatedTestComponent>\n          <div>Animated content</div>\n        </AnimatedTestComponent>\n      )\n\n      expect(screen.getByTestId('animated-component')).toBeInTheDocument()\n      expect(screen.getByText('Animated content')).toBeInTheDocument()\n    })\n\n    it('handles motion variants', () => {\n      const MotionComponent = () => (\n        <div\n          data-testid=\"motion-div\"\n          style={{\n            opacity: 1,\n            transform: 'translateY(0px)',\n          }}\n        >\n          Motion content\n        </div>\n      )\n\n      render(<MotionComponent />)\n      \n      const motionDiv = screen.getByTestId('motion-div')\n      expect(motionDiv).toHaveStyle('opacity: 1')\n      expect(motionDiv).toHaveStyle('transform: translateY(0px)')\n    })\n\n    it('handles animation presence', () => {\n      const AnimatePresenceComponent = ({ show }: { show: boolean }) => (\n        <div data-testid=\"presence-container\">\n          {show && (\n            <div data-testid=\"animated-item\">\n              Animated item\n            </div>\n          )}\n        </div>\n      )\n\n      const { rerender } = render(<AnimatePresenceComponent show={true} />)\n      expect(screen.getByTestId('animated-item')).toBeInTheDocument()\n\n      rerender(<AnimatePresenceComponent show={false} />)\n      expect(screen.queryByTestId('animated-item')).not.toBeInTheDocument()\n    })\n\n    it('handles stagger animations', () => {\n      const StaggerComponent = () => (\n        <div data-testid=\"stagger-container\">\n          {[1, 2, 3].map(i => (\n            <div key={i} data-testid={`stagger-item-${i}`}>\n              Item {i}\n            </div>\n          ))}\n        </div>\n      )\n\n      render(<StaggerComponent />)\n      \n      expect(screen.getByTestId('stagger-item-1')).toBeInTheDocument()\n      expect(screen.getByTestId('stagger-item-2')).toBeInTheDocument()\n      expect(screen.getByTestId('stagger-item-3')).toBeInTheDocument()\n    })\n  })\n\n  describe('Prefers Reduced Motion', () => {\n    it('respects prefers-reduced-motion setting', () => {\n      mockPrefersReducedMotion(true)\n      \n      render(\n        <AnimatedTestComponent>\n          <div>Reduced motion content</div>\n        </AnimatedTestComponent>\n      )\n\n      const component = screen.getByTestId('animated-component')\n      expect(component).toBeInTheDocument()\n      // Animation should be disabled or reduced\n    })\n\n    it('provides full animations when prefers-reduced-motion is false', () => {\n      mockPrefersReducedMotion(false)\n      \n      render(\n        <AnimatedTestComponent>\n          <div>Full animation content</div>\n        </AnimatedTestComponent>\n      )\n\n      const component = screen.getByTestId('animated-component')\n      expect(component).toBeInTheDocument()\n      // Full animations should be enabled\n    })\n\n    it('handles media query changes', () => {\n      // Start with reduced motion\n      mockPrefersReducedMotion(true)\n      \n      const { rerender } = render(\n        <AnimatedTestComponent>\n          <div>Content</div>\n        </AnimatedTestComponent>\n      )\n\n      // Change to full motion\n      mockPrefersReducedMotion(false)\n      rerender(\n        <AnimatedTestComponent>\n          <div>Content</div>\n        </AnimatedTestComponent>\n      )\n\n      expect(screen.getByText('Content')).toBeInTheDocument()\n    })\n  })\n\n  describe('Animation Performance', () => {\n    it('handles rapid animation triggers', async () => {\n      const RapidAnimationComponent = () => {\n        const [count, setCount] = React.useState(0)\n        \n        return (\n          <div>\n            <button \n              onClick={() => setCount(c => c + 1)}\n              data-testid=\"trigger-button\"\n            >\n              Trigger Animation\n            </button>\n            <div \n              data-testid=\"animated-counter\"\n              style={{ transform: `scale(${1 + count * 0.1})` }}\n            >\n              Count: {count}\n            </div>\n          </div>\n        )\n      }\n\n      render(<RapidAnimationComponent />)\n      \n      const button = screen.getByTestId('trigger-button')\n      const counter = screen.getByTestId('animated-counter')\n\n      // Rapidly trigger animations\n      for (let i = 0; i < 10; i++) {\n        fireEvent.click(button)\n      }\n\n      await waitFor(() => {\n        expect(screen.getByText('Count: 10')).toBeInTheDocument()\n      })\n\n      expect(counter).toHaveStyle('transform: scale(2)')\n    })\n\n    it('handles concurrent animations', () => {\n      const ConcurrentAnimationComponent = () => (\n        <div>\n          <div \n            data-testid=\"animation-1\"\n            style={{ opacity: 0.5, transform: 'translateX(10px)' }}\n          >\n            Animation 1\n          </div>\n          <div \n            data-testid=\"animation-2\"\n            style={{ opacity: 0.8, transform: 'translateY(20px)' }}\n          >\n            Animation 2\n          </div>\n        </div>\n      )\n\n      render(<ConcurrentAnimationComponent />)\n      \n      const anim1 = screen.getByTestId('animation-1')\n      const anim2 = screen.getByTestId('animation-2')\n\n      expect(anim1).toHaveStyle('opacity: 0.5')\n      expect(anim2).toHaveStyle('opacity: 0.8')\n    })\n\n    it('cleans up animations on unmount', () => {\n      const AnimationComponent = () => (\n        <div data-testid=\"cleanup-component\">\n          Animated component\n        </div>\n      )\n\n      const { unmount } = render(<AnimationComponent />)\n      \n      expect(screen.getByTestId('cleanup-component')).toBeInTheDocument()\n      \n      // Unmount should not cause errors\n      expect(() => unmount()).not.toThrow()\n    })\n  })\n\n  describe('Animation Timing', () => {\n    it('uses appropriate animation durations', () => {\n      const TimedAnimationComponent = () => (\n        <div \n          data-testid=\"timed-animation\"\n          style={{ \n            transition: 'all 0.3s ease-out',\n            opacity: 1 \n          }}\n        >\n          Timed content\n        </div>\n      )\n\n      render(<TimedAnimationComponent />)\n      \n      const component = screen.getByTestId('timed-animation')\n      expect(component).toHaveStyle('transition: all 0.3s ease-out')\n    })\n\n    it('handles different easing functions', () => {\n      const EasingComponent = () => (\n        <div>\n          <div \n            data-testid=\"ease-out\"\n            style={{ transition: 'transform 0.2s ease-out' }}\n          >\n            Ease Out\n          </div>\n          <div \n            data-testid=\"ease-in\"\n            style={{ transition: 'transform 0.2s ease-in' }}\n          >\n            Ease In\n          </div>\n        </div>\n      )\n\n      render(<EasingComponent />)\n      \n      expect(screen.getByTestId('ease-out')).toHaveStyle('transition: transform 0.2s ease-out')\n      expect(screen.getByTestId('ease-in')).toHaveStyle('transition: transform 0.2s ease-in')\n    })\n\n    it('handles animation delays', () => {\n      const DelayedAnimationComponent = () => (\n        <div \n          data-testid=\"delayed-animation\"\n          style={{ \n            transition: 'opacity 0.3s ease-out',\n            transitionDelay: '0.1s'\n          }}\n        >\n          Delayed content\n        </div>\n      )\n\n      render(<DelayedAnimationComponent />)\n      \n      const component = screen.getByTestId('delayed-animation')\n      expect(component).toHaveStyle('transition-delay: 0.1s')\n    })\n  })\n\n  describe('Interactive Animations', () => {\n    it('handles hover animations', () => {\n      const HoverComponent = () => {\n        const [isHovered, setIsHovered] = React.useState(false)\n        \n        return (\n          <div\n            data-testid=\"hover-component\"\n            onMouseEnter={() => setIsHovered(true)}\n            onMouseLeave={() => setIsHovered(false)}\n            style={{\n              transform: isHovered ? 'scale(1.05)' : 'scale(1)',\n              transition: 'transform 0.2s ease-out'\n            }}\n          >\n            Hover me\n          </div>\n        )\n      }\n\n      render(<HoverComponent />)\n      \n      const component = screen.getByTestId('hover-component')\n      \n      expect(component).toHaveStyle('transform: scale(1)')\n      \n      fireEvent.mouseEnter(component)\n      expect(component).toHaveStyle('transform: scale(1.05)')\n      \n      fireEvent.mouseLeave(component)\n      expect(component).toHaveStyle('transform: scale(1)')\n    })\n\n    it('handles click animations', () => {\n      const ClickComponent = () => {\n        const [isPressed, setIsPressed] = React.useState(false)\n        \n        return (\n          <button\n            data-testid=\"click-component\"\n            onMouseDown={() => setIsPressed(true)}\n            onMouseUp={() => setIsPressed(false)}\n            style={{\n              transform: isPressed ? 'scale(0.95)' : 'scale(1)',\n              transition: 'transform 0.1s ease-out'\n            }}\n          >\n            Click me\n          </button>\n        )\n      }\n\n      render(<ClickComponent />)\n      \n      const button = screen.getByTestId('click-component')\n      \n      expect(button).toHaveStyle('transform: scale(1)')\n      \n      fireEvent.mouseDown(button)\n      expect(button).toHaveStyle('transform: scale(0.95)')\n      \n      fireEvent.mouseUp(button)\n      expect(button).toHaveStyle('transform: scale(1)')\n    })\n\n    it('handles focus animations', () => {\n      const FocusComponent = () => {\n        const [isFocused, setIsFocused] = React.useState(false)\n        \n        return (\n          <input\n            data-testid=\"focus-component\"\n            onFocus={() => setIsFocused(true)}\n            onBlur={() => setIsFocused(false)}\n            style={{\n              borderColor: isFocused ? '#3b82f6' : '#d1d5db',\n              transition: 'border-color 0.2s ease-out'\n            }}\n            placeholder=\"Focus me\"\n          />\n        )\n      }\n\n      render(<FocusComponent />)\n      \n      const input = screen.getByTestId('focus-component')\n      \n      expect(input).toHaveStyle('border-color: #d1d5db')\n      \n      fireEvent.focus(input)\n      expect(input).toHaveStyle('border-color: #3b82f6')\n      \n      fireEvent.blur(input)\n      expect(input).toHaveStyle('border-color: #d1d5db')\n    })\n  })\n\n  describe('Animation Error Handling', () => {\n    it('handles animation errors gracefully', () => {\n      const ErrorProneComponent = () => {\n        try {\n          return (\n            <div data-testid=\"error-prone\">\n              Animation content\n            </div>\n          )\n        } catch (error) {\n          return (\n            <div data-testid=\"error-fallback\">\n              Fallback content\n            </div>\n          )\n        }\n      }\n\n      render(<ErrorProneComponent />)\n      \n      // Should render without throwing\n      expect(screen.getByTestId('error-prone')).toBeInTheDocument()\n    })\n\n    it('provides fallbacks for unsupported animations', () => {\n      const FallbackComponent = () => (\n        <div \n          data-testid=\"fallback-component\"\n          style={{\n            // Fallback for unsupported properties\n            transform: 'translateX(0px)',\n            opacity: 1\n          }}\n        >\n          Fallback animation\n        </div>\n      )\n\n      render(<FallbackComponent />)\n      \n      const component = screen.getByTestId('fallback-component')\n      expect(component).toHaveStyle('opacity: 1')\n    })\n  })\n})\n"], "names": ["mockFramerMotion", "AnimatedTestComponent", "children", "div", "data-testid", "className", "describe", "beforeEach", "jest", "clearAllMocks", "mockPrefersReducedMotion", "it", "render", "expect", "screen", "getByTestId", "toBeInTheDocument", "getByText", "MotionComponent", "style", "opacity", "transform", "motionDiv", "toHaveStyle", "AnimatePresenceComponent", "show", "rerender", "queryByTestId", "not", "StaggerComponent", "map", "i", "component", "RapidAnimationComponent", "count", "setCount", "React", "useState", "button", "onClick", "c", "counter", "fireEvent", "click", "waitFor", "ConcurrentAnimationComponent", "anim1", "anim2", "AnimationComponent", "unmount", "toThrow", "TimedAnimationComponent", "transition", "EasingComponent", "DelayedAnimationComponent", "transitionDelay", "HoverComponent", "isHovered", "setIsHovered", "onMouseEnter", "onMouseLeave", "mouseEnter", "mouseLeave", "ClickComponent", "isPressed", "setIsPressed", "onMouseDown", "onMouseUp", "mouseDown", "mouseUp", "FocusComponent", "isFocused", "setIsFocused", "input", "onFocus", "onBlur", "borderColor", "placeholder", "focus", "blur", "ErrorProneComponent", "error", "FallbackComponent"], "mappings": ";;;;;8DAAkB;2BACiC;;;;;;AAGnD,qBAAqB;AACrBA,IAAAA,2BAAgB;AAEhB,sCAAsC;AACtC,MAAMC,wBAAwB,CAAC,EAAEC,QAAQ,EAAiC,iBACxE,qBAACC;QAAIC,eAAY;QAAqBC,WAAU;kBAC7CH;;AAILI,SAAS,0BAA0B;IACjCC,WAAW;QACTC,KAAKC,aAAa;QAClB,+BAA+B;QAC/BC,IAAAA,mCAAwB,EAAC;IAC3B;IAEAJ,SAAS,6BAA6B;QACpCK,GAAG,uCAAuC;YACxCC,IAAAA,iBAAM,gBACJ,qBAACX;0BACC,cAAA,qBAACE;8BAAI;;;YAITU,OAAOC,iBAAM,CAACC,WAAW,CAAC,uBAAuBC,iBAAiB;YAClEH,OAAOC,iBAAM,CAACG,SAAS,CAAC,qBAAqBD,iBAAiB;QAChE;QAEAL,GAAG,2BAA2B;YAC5B,MAAMO,kBAAkB,kBACtB,qBAACf;oBACCC,eAAY;oBACZe,OAAO;wBACLC,SAAS;wBACTC,WAAW;oBACb;8BACD;;YAKHT,IAAAA,iBAAM,gBAAC,qBAACM;YAER,MAAMI,YAAYR,iBAAM,CAACC,WAAW,CAAC;YACrCF,OAAOS,WAAWC,WAAW,CAAC;YAC9BV,OAAOS,WAAWC,WAAW,CAAC;QAChC;QAEAZ,GAAG,8BAA8B;YAC/B,MAAMa,2BAA2B,CAAC,EAAEC,IAAI,EAAqB,iBAC3D,qBAACtB;oBAAIC,eAAY;8BACdqB,sBACC,qBAACtB;wBAAIC,eAAY;kCAAgB;;;YAOvC,MAAM,EAAEsB,QAAQ,EAAE,GAAGd,IAAAA,iBAAM,gBAAC,qBAACY;gBAAyBC,MAAM;;YAC5DZ,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;YAE7DU,uBAAS,qBAACF;gBAAyBC,MAAM;;YACzCZ,OAAOC,iBAAM,CAACa,aAAa,CAAC,kBAAkBC,GAAG,CAACZ,iBAAiB;QACrE;QAEAL,GAAG,8BAA8B;YAC/B,MAAMkB,mBAAmB,kBACvB,qBAAC1B;oBAAIC,eAAY;8BACd;wBAAC;wBAAG;wBAAG;qBAAE,CAAC0B,GAAG,CAACC,CAAAA,kBACb,sBAAC5B;4BAAYC,eAAa,CAAC,aAAa,EAAE2B,EAAE,CAAC;;gCAAE;gCACvCA;;2BADEA;;YAOhBnB,IAAAA,iBAAM,gBAAC,qBAACiB;YAERhB,OAAOC,iBAAM,CAACC,WAAW,CAAC,mBAAmBC,iBAAiB;YAC9DH,OAAOC,iBAAM,CAACC,WAAW,CAAC,mBAAmBC,iBAAiB;YAC9DH,OAAOC,iBAAM,CAACC,WAAW,CAAC,mBAAmBC,iBAAiB;QAChE;IACF;IAEAV,SAAS,0BAA0B;QACjCK,GAAG,2CAA2C;YAC5CD,IAAAA,mCAAwB,EAAC;YAEzBE,IAAAA,iBAAM,gBACJ,qBAACX;0BACC,cAAA,qBAACE;8BAAI;;;YAIT,MAAM6B,YAAYlB,iBAAM,CAACC,WAAW,CAAC;YACrCF,OAAOmB,WAAWhB,iBAAiB;QACnC,0CAA0C;QAC5C;QAEAL,GAAG,iEAAiE;YAClED,IAAAA,mCAAwB,EAAC;YAEzBE,IAAAA,iBAAM,gBACJ,qBAACX;0BACC,cAAA,qBAACE;8BAAI;;;YAIT,MAAM6B,YAAYlB,iBAAM,CAACC,WAAW,CAAC;YACrCF,OAAOmB,WAAWhB,iBAAiB;QACnC,oCAAoC;QACtC;QAEAL,GAAG,+BAA+B;YAChC,4BAA4B;YAC5BD,IAAAA,mCAAwB,EAAC;YAEzB,MAAM,EAAEgB,QAAQ,EAAE,GAAGd,IAAAA,iBAAM,gBACzB,qBAACX;0BACC,cAAA,qBAACE;8BAAI;;;YAIT,wBAAwB;YACxBO,IAAAA,mCAAwB,EAAC;YACzBgB,uBACE,qBAACzB;0BACC,cAAA,qBAACE;8BAAI;;;YAITU,OAAOC,iBAAM,CAACG,SAAS,CAAC,YAAYD,iBAAiB;QACvD;IACF;IAEAV,SAAS,yBAAyB;QAChCK,GAAG,oCAAoC;YACrC,MAAMsB,0BAA0B;gBAC9B,MAAM,CAACC,OAAOC,SAAS,GAAGC,cAAK,CAACC,QAAQ,CAAC;gBAEzC,qBACE,sBAAClC;;sCACC,qBAACmC;4BACCC,SAAS,IAAMJ,SAASK,CAAAA,IAAKA,IAAI;4BACjCpC,eAAY;sCACb;;sCAGD,sBAACD;4BACCC,eAAY;4BACZe,OAAO;gCAAEE,WAAW,CAAC,MAAM,EAAE,IAAIa,QAAQ,IAAI,CAAC,CAAC;4BAAC;;gCACjD;gCACSA;;;;;YAIhB;YAEAtB,IAAAA,iBAAM,gBAAC,qBAACqB;YAER,MAAMK,SAASxB,iBAAM,CAACC,WAAW,CAAC;YAClC,MAAM0B,UAAU3B,iBAAM,CAACC,WAAW,CAAC;YAEnC,6BAA6B;YAC7B,IAAK,IAAIgB,IAAI,GAAGA,IAAI,IAAIA,IAAK;gBAC3BW,oBAAS,CAACC,KAAK,CAACL;YAClB;YAEA,MAAMM,IAAAA,kBAAO,EAAC;gBACZ/B,OAAOC,iBAAM,CAACG,SAAS,CAAC,cAAcD,iBAAiB;YACzD;YAEAH,OAAO4B,SAASlB,WAAW,CAAC;QAC9B;QAEAZ,GAAG,iCAAiC;YAClC,MAAMkC,+BAA+B,kBACnC,sBAAC1C;;sCACC,qBAACA;4BACCC,eAAY;4BACZe,OAAO;gCAAEC,SAAS;gCAAKC,WAAW;4BAAmB;sCACtD;;sCAGD,qBAAClB;4BACCC,eAAY;4BACZe,OAAO;gCAAEC,SAAS;gCAAKC,WAAW;4BAAmB;sCACtD;;;;YAMLT,IAAAA,iBAAM,gBAAC,qBAACiC;YAER,MAAMC,QAAQhC,iBAAM,CAACC,WAAW,CAAC;YACjC,MAAMgC,QAAQjC,iBAAM,CAACC,WAAW,CAAC;YAEjCF,OAAOiC,OAAOvB,WAAW,CAAC;YAC1BV,OAAOkC,OAAOxB,WAAW,CAAC;QAC5B;QAEAZ,GAAG,mCAAmC;YACpC,MAAMqC,qBAAqB,kBACzB,qBAAC7C;oBAAIC,eAAY;8BAAoB;;YAKvC,MAAM,EAAE6C,OAAO,EAAE,GAAGrC,IAAAA,iBAAM,gBAAC,qBAACoC;YAE5BnC,OAAOC,iBAAM,CAACC,WAAW,CAAC,sBAAsBC,iBAAiB;YAEjE,kCAAkC;YAClCH,OAAO,IAAMoC,WAAWrB,GAAG,CAACsB,OAAO;QACrC;IACF;IAEA5C,SAAS,oBAAoB;QAC3BK,GAAG,wCAAwC;YACzC,MAAMwC,0BAA0B,kBAC9B,qBAAChD;oBACCC,eAAY;oBACZe,OAAO;wBACLiC,YAAY;wBACZhC,SAAS;oBACX;8BACD;;YAKHR,IAAAA,iBAAM,gBAAC,qBAACuC;YAER,MAAMnB,YAAYlB,iBAAM,CAACC,WAAW,CAAC;YACrCF,OAAOmB,WAAWT,WAAW,CAAC;QAChC;QAEAZ,GAAG,sCAAsC;YACvC,MAAM0C,kBAAkB,kBACtB,sBAAClD;;sCACC,qBAACA;4BACCC,eAAY;4BACZe,OAAO;gCAAEiC,YAAY;4BAA0B;sCAChD;;sCAGD,qBAACjD;4BACCC,eAAY;4BACZe,OAAO;gCAAEiC,YAAY;4BAAyB;sCAC/C;;;;YAMLxC,IAAAA,iBAAM,gBAAC,qBAACyC;YAERxC,OAAOC,iBAAM,CAACC,WAAW,CAAC,aAAaQ,WAAW,CAAC;YACnDV,OAAOC,iBAAM,CAACC,WAAW,CAAC,YAAYQ,WAAW,CAAC;QACpD;QAEAZ,GAAG,4BAA4B;YAC7B,MAAM2C,4BAA4B,kBAChC,qBAACnD;oBACCC,eAAY;oBACZe,OAAO;wBACLiC,YAAY;wBACZG,iBAAiB;oBACnB;8BACD;;YAKH3C,IAAAA,iBAAM,gBAAC,qBAAC0C;YAER,MAAMtB,YAAYlB,iBAAM,CAACC,WAAW,CAAC;YACrCF,OAAOmB,WAAWT,WAAW,CAAC;QAChC;IACF;IAEAjB,SAAS,0BAA0B;QACjCK,GAAG,4BAA4B;YAC7B,MAAM6C,iBAAiB;gBACrB,MAAM,CAACC,WAAWC,aAAa,GAAGtB,cAAK,CAACC,QAAQ,CAAC;gBAEjD,qBACE,qBAAClC;oBACCC,eAAY;oBACZuD,cAAc,IAAMD,aAAa;oBACjCE,cAAc,IAAMF,aAAa;oBACjCvC,OAAO;wBACLE,WAAWoC,YAAY,gBAAgB;wBACvCL,YAAY;oBACd;8BACD;;YAIL;YAEAxC,IAAAA,iBAAM,gBAAC,qBAAC4C;YAER,MAAMxB,YAAYlB,iBAAM,CAACC,WAAW,CAAC;YAErCF,OAAOmB,WAAWT,WAAW,CAAC;YAE9BmB,oBAAS,CAACmB,UAAU,CAAC7B;YACrBnB,OAAOmB,WAAWT,WAAW,CAAC;YAE9BmB,oBAAS,CAACoB,UAAU,CAAC9B;YACrBnB,OAAOmB,WAAWT,WAAW,CAAC;QAChC;QAEAZ,GAAG,4BAA4B;YAC7B,MAAMoD,iBAAiB;gBACrB,MAAM,CAACC,WAAWC,aAAa,GAAG7B,cAAK,CAACC,QAAQ,CAAC;gBAEjD,qBACE,qBAACC;oBACClC,eAAY;oBACZ8D,aAAa,IAAMD,aAAa;oBAChCE,WAAW,IAAMF,aAAa;oBAC9B9C,OAAO;wBACLE,WAAW2C,YAAY,gBAAgB;wBACvCZ,YAAY;oBACd;8BACD;;YAIL;YAEAxC,IAAAA,iBAAM,gBAAC,qBAACmD;YAER,MAAMzB,SAASxB,iBAAM,CAACC,WAAW,CAAC;YAElCF,OAAOyB,QAAQf,WAAW,CAAC;YAE3BmB,oBAAS,CAAC0B,SAAS,CAAC9B;YACpBzB,OAAOyB,QAAQf,WAAW,CAAC;YAE3BmB,oBAAS,CAAC2B,OAAO,CAAC/B;YAClBzB,OAAOyB,QAAQf,WAAW,CAAC;QAC7B;QAEAZ,GAAG,4BAA4B;YAC7B,MAAM2D,iBAAiB;gBACrB,MAAM,CAACC,WAAWC,aAAa,GAAGpC,cAAK,CAACC,QAAQ,CAAC;gBAEjD,qBACE,qBAACoC;oBACCrE,eAAY;oBACZsE,SAAS,IAAMF,aAAa;oBAC5BG,QAAQ,IAAMH,aAAa;oBAC3BrD,OAAO;wBACLyD,aAAaL,YAAY,YAAY;wBACrCnB,YAAY;oBACd;oBACAyB,aAAY;;YAGlB;YAEAjE,IAAAA,iBAAM,gBAAC,qBAAC0D;YAER,MAAMG,QAAQ3D,iBAAM,CAACC,WAAW,CAAC;YAEjCF,OAAO4D,OAAOlD,WAAW,CAAC;YAE1BmB,oBAAS,CAACoC,KAAK,CAACL;YAChB5D,OAAO4D,OAAOlD,WAAW,CAAC;YAE1BmB,oBAAS,CAACqC,IAAI,CAACN;YACf5D,OAAO4D,OAAOlD,WAAW,CAAC;QAC5B;IACF;IAEAjB,SAAS,4BAA4B;QACnCK,GAAG,uCAAuC;YACxC,MAAMqE,sBAAsB;gBAC1B,IAAI;oBACF,qBACE,qBAAC7E;wBAAIC,eAAY;kCAAc;;gBAInC,EAAE,OAAO6E,OAAO;oBACd,qBACE,qBAAC9E;wBAAIC,eAAY;kCAAiB;;gBAItC;YACF;YAEAQ,IAAAA,iBAAM,gBAAC,qBAACoE;YAER,iCAAiC;YACjCnE,OAAOC,iBAAM,CAACC,WAAW,CAAC,gBAAgBC,iBAAiB;QAC7D;QAEAL,GAAG,iDAAiD;YAClD,MAAMuE,oBAAoB,kBACxB,qBAAC/E;oBACCC,eAAY;oBACZe,OAAO;wBACL,sCAAsC;wBACtCE,WAAW;wBACXD,SAAS;oBACX;8BACD;;YAKHR,IAAAA,iBAAM,gBAAC,qBAACsE;YAER,MAAMlD,YAAYlB,iBAAM,CAACC,WAAW,CAAC;YACrCF,OAAOmB,WAAWT,WAAW,CAAC;QAChC;IACF;AACF"}