6965257c3bafd6bbef63d6195b364daa
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../lib/test-utils");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock framer-motion
(0, _testutils.mockFramerMotion)();
// Test component that uses animations
const AnimatedTestComponent = ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
        "data-testid": "animated-component",
        className: "animate-fade-in",
        children: children
    });
describe("Animation System Tests", ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
        // Reset prefers-reduced-motion
        (0, _testutils.mockPrefersReducedMotion)(false);
    });
    describe("Framer Motion Integration", ()=>{
        it("renders motion components correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AnimatedTestComponent, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Animated content"
                })
            }));
            expect(_testutils.screen.getByTestId("animated-component")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Animated content")).toBeInTheDocument();
        });
        it("handles motion variants", ()=>{
            const MotionComponent = ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "motion-div",
                    style: {
                        opacity: 1,
                        transform: "translateY(0px)"
                    },
                    children: "Motion content"
                });
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(MotionComponent, {}));
            const motionDiv = _testutils.screen.getByTestId("motion-div");
            expect(motionDiv).toHaveStyle("opacity: 1");
            expect(motionDiv).toHaveStyle("transform: translateY(0px)");
        });
        it("handles animation presence", ()=>{
            const AnimatePresenceComponent = ({ show })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "presence-container",
                    children: show && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        "data-testid": "animated-item",
                        children: "Animated item"
                    })
                });
            const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AnimatePresenceComponent, {
                show: true
            }));
            expect(_testutils.screen.getByTestId("animated-item")).toBeInTheDocument();
            rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(AnimatePresenceComponent, {
                show: false
            }));
            expect(_testutils.screen.queryByTestId("animated-item")).not.toBeInTheDocument();
        });
        it("handles stagger animations", ()=>{
            const StaggerComponent = ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "stagger-container",
                    children: [
                        1,
                        2,
                        3
                    ].map((i)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            "data-testid": `stagger-item-${i}`,
                            children: [
                                "Item ",
                                i
                            ]
                        }, i))
                });
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(StaggerComponent, {}));
            expect(_testutils.screen.getByTestId("stagger-item-1")).toBeInTheDocument();
            expect(_testutils.screen.getByTestId("stagger-item-2")).toBeInTheDocument();
            expect(_testutils.screen.getByTestId("stagger-item-3")).toBeInTheDocument();
        });
    });
    describe("Prefers Reduced Motion", ()=>{
        it("respects prefers-reduced-motion setting", ()=>{
            (0, _testutils.mockPrefersReducedMotion)(true);
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AnimatedTestComponent, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Reduced motion content"
                })
            }));
            const component = _testutils.screen.getByTestId("animated-component");
            expect(component).toBeInTheDocument();
        // Animation should be disabled or reduced
        });
        it("provides full animations when prefers-reduced-motion is false", ()=>{
            (0, _testutils.mockPrefersReducedMotion)(false);
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AnimatedTestComponent, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Full animation content"
                })
            }));
            const component = _testutils.screen.getByTestId("animated-component");
            expect(component).toBeInTheDocument();
        // Full animations should be enabled
        });
        it("handles media query changes", ()=>{
            // Start with reduced motion
            (0, _testutils.mockPrefersReducedMotion)(true);
            const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AnimatedTestComponent, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Content"
                })
            }));
            // Change to full motion
            (0, _testutils.mockPrefersReducedMotion)(false);
            rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(AnimatedTestComponent, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Content"
                })
            }));
            expect(_testutils.screen.getByText("Content")).toBeInTheDocument();
        });
    });
    describe("Animation Performance", ()=>{
        it("handles rapid animation triggers", async ()=>{
            const RapidAnimationComponent = ()=>{
                const [count, setCount] = _react.default.useState(0);
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            onClick: ()=>setCount((c)=>c + 1),
                            "data-testid": "trigger-button",
                            children: "Trigger Animation"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            "data-testid": "animated-counter",
                            style: {
                                transform: `scale(${1 + count * 0.1})`
                            },
                            children: [
                                "Count: ",
                                count
                            ]
                        })
                    ]
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(RapidAnimationComponent, {}));
            const button = _testutils.screen.getByTestId("trigger-button");
            const counter = _testutils.screen.getByTestId("animated-counter");
            // Rapidly trigger animations
            for(let i = 0; i < 10; i++){
                _testutils.fireEvent.click(button);
            }
            await (0, _testutils.waitFor)(()=>{
                expect(_testutils.screen.getByText("Count: 10")).toBeInTheDocument();
            });
            expect(counter).toHaveStyle("transform: scale(2)");
        });
        it("handles concurrent animations", ()=>{
            const ConcurrentAnimationComponent = ()=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            "data-testid": "animation-1",
                            style: {
                                opacity: 0.5,
                                transform: "translateX(10px)"
                            },
                            children: "Animation 1"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            "data-testid": "animation-2",
                            style: {
                                opacity: 0.8,
                                transform: "translateY(20px)"
                            },
                            children: "Animation 2"
                        })
                    ]
                });
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(ConcurrentAnimationComponent, {}));
            const anim1 = _testutils.screen.getByTestId("animation-1");
            const anim2 = _testutils.screen.getByTestId("animation-2");
            expect(anim1).toHaveStyle("opacity: 0.5");
            expect(anim2).toHaveStyle("opacity: 0.8");
        });
        it("cleans up animations on unmount", ()=>{
            const AnimationComponent = ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "cleanup-component",
                    children: "Animated component"
                });
            const { unmount } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AnimationComponent, {}));
            expect(_testutils.screen.getByTestId("cleanup-component")).toBeInTheDocument();
            // Unmount should not cause errors
            expect(()=>unmount()).not.toThrow();
        });
    });
    describe("Animation Timing", ()=>{
        it("uses appropriate animation durations", ()=>{
            const TimedAnimationComponent = ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "timed-animation",
                    style: {
                        transition: "all 0.3s ease-out",
                        opacity: 1
                    },
                    children: "Timed content"
                });
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TimedAnimationComponent, {}));
            const component = _testutils.screen.getByTestId("timed-animation");
            expect(component).toHaveStyle("transition: all 0.3s ease-out");
        });
        it("handles different easing functions", ()=>{
            const EasingComponent = ()=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            "data-testid": "ease-out",
                            style: {
                                transition: "transform 0.2s ease-out"
                            },
                            children: "Ease Out"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            "data-testid": "ease-in",
                            style: {
                                transition: "transform 0.2s ease-in"
                            },
                            children: "Ease In"
                        })
                    ]
                });
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(EasingComponent, {}));
            expect(_testutils.screen.getByTestId("ease-out")).toHaveStyle("transition: transform 0.2s ease-out");
            expect(_testutils.screen.getByTestId("ease-in")).toHaveStyle("transition: transform 0.2s ease-in");
        });
        it("handles animation delays", ()=>{
            const DelayedAnimationComponent = ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "delayed-animation",
                    style: {
                        transition: "opacity 0.3s ease-out",
                        transitionDelay: "0.1s"
                    },
                    children: "Delayed content"
                });
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(DelayedAnimationComponent, {}));
            const component = _testutils.screen.getByTestId("delayed-animation");
            expect(component).toHaveStyle("transition-delay: 0.1s");
        });
    });
    describe("Interactive Animations", ()=>{
        it("handles hover animations", ()=>{
            const HoverComponent = ()=>{
                const [isHovered, setIsHovered] = _react.default.useState(false);
                return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "hover-component",
                    onMouseEnter: ()=>setIsHovered(true),
                    onMouseLeave: ()=>setIsHovered(false),
                    style: {
                        transform: isHovered ? "scale(1.05)" : "scale(1)",
                        transition: "transform 0.2s ease-out"
                    },
                    children: "Hover me"
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(HoverComponent, {}));
            const component = _testutils.screen.getByTestId("hover-component");
            expect(component).toHaveStyle("transform: scale(1)");
            _testutils.fireEvent.mouseEnter(component);
            expect(component).toHaveStyle("transform: scale(1.05)");
            _testutils.fireEvent.mouseLeave(component);
            expect(component).toHaveStyle("transform: scale(1)");
        });
        it("handles click animations", ()=>{
            const ClickComponent = ()=>{
                const [isPressed, setIsPressed] = _react.default.useState(false);
                return /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                    "data-testid": "click-component",
                    onMouseDown: ()=>setIsPressed(true),
                    onMouseUp: ()=>setIsPressed(false),
                    style: {
                        transform: isPressed ? "scale(0.95)" : "scale(1)",
                        transition: "transform 0.1s ease-out"
                    },
                    children: "Click me"
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(ClickComponent, {}));
            const button = _testutils.screen.getByTestId("click-component");
            expect(button).toHaveStyle("transform: scale(1)");
            _testutils.fireEvent.mouseDown(button);
            expect(button).toHaveStyle("transform: scale(0.95)");
            _testutils.fireEvent.mouseUp(button);
            expect(button).toHaveStyle("transform: scale(1)");
        });
        it("handles focus animations", ()=>{
            const FocusComponent = ()=>{
                const [isFocused, setIsFocused] = _react.default.useState(false);
                return /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                    "data-testid": "focus-component",
                    onFocus: ()=>setIsFocused(true),
                    onBlur: ()=>setIsFocused(false),
                    style: {
                        borderColor: isFocused ? "#3b82f6" : "#d1d5db",
                        transition: "border-color 0.2s ease-out"
                    },
                    placeholder: "Focus me"
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(FocusComponent, {}));
            const input = _testutils.screen.getByTestId("focus-component");
            expect(input).toHaveStyle("border-color: #d1d5db");
            _testutils.fireEvent.focus(input);
            expect(input).toHaveStyle("border-color: #3b82f6");
            _testutils.fireEvent.blur(input);
            expect(input).toHaveStyle("border-color: #d1d5db");
        });
    });
    describe("Animation Error Handling", ()=>{
        it("handles animation errors gracefully", ()=>{
            const ErrorProneComponent = ()=>{
                try {
                    return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        "data-testid": "error-prone",
                        children: "Animation content"
                    });
                } catch (error) {
                    return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        "data-testid": "error-fallback",
                        children: "Fallback content"
                    });
                }
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(ErrorProneComponent, {}));
            // Should render without throwing
            expect(_testutils.screen.getByTestId("error-prone")).toBeInTheDocument();
        });
        it("provides fallbacks for unsupported animations", ()=>{
            const FallbackComponent = ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "fallback-component",
                    style: {
                        // Fallback for unsupported properties
                        transform: "translateX(0px)",
                        opacity: 1
                    },
                    children: "Fallback animation"
                });
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(FallbackComponent, {}));
            const component = _testutils.screen.getByTestId("fallback-component");
            expect(component).toHaveStyle("opacity: 1");
        });
    });
});

//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFxEYUNodWFuZ0JhY2tlbmRcXGZyb250ZW5kXFxzcmNcXF9fdGVzdHNfX1xcYW5pbWF0aW9ucy50ZXN0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyByZW5kZXIsIHNjcmVlbiwgZmlyZUV2ZW50LCB3YWl0Rm9yIH0gZnJvbSAnQC9saWIvdGVzdC11dGlscydcbmltcG9ydCB7IG1vY2tGcmFtZXJNb3Rpb24sIG1vY2tQcmVmZXJzUmVkdWNlZE1vdGlvbiB9IGZyb20gJ0AvbGliL3Rlc3QtdXRpbHMnXG5cbi8vIE1vY2sgZnJhbWVyLW1vdGlvblxubW9ja0ZyYW1lck1vdGlvbigpXG5cbi8vIFRlc3QgY29tcG9uZW50IHRoYXQgdXNlcyBhbmltYXRpb25zXG5jb25zdCBBbmltYXRlZFRlc3RDb21wb25lbnQgPSAoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkgPT4gKFxuICA8ZGl2IGRhdGEtdGVzdGlkPVwiYW5pbWF0ZWQtY29tcG9uZW50XCIgY2xhc3NOYW1lPVwiYW5pbWF0ZS1mYWRlLWluXCI+XG4gICAge2NoaWxkcmVufVxuICA8L2Rpdj5cbilcblxuZGVzY3JpYmUoJ0FuaW1hdGlvbiBTeXN0ZW0gVGVzdHMnLCAoKSA9PiB7XG4gIGJlZm9yZUVhY2goKCkgPT4ge1xuICAgIGplc3QuY2xlYXJBbGxNb2NrcygpXG4gICAgLy8gUmVzZXQgcHJlZmVycy1yZWR1Y2VkLW1vdGlvblxuICAgIG1vY2tQcmVmZXJzUmVkdWNlZE1vdGlvbihmYWxzZSlcbiAgfSlcblxuICBkZXNjcmliZSgnRnJhbWVyIE1vdGlvbiBJbnRlZ3JhdGlvbicsICgpID0+IHtcbiAgICBpdCgncmVuZGVycyBtb3Rpb24gY29tcG9uZW50cyBjb3JyZWN0bHknLCAoKSA9PiB7XG4gICAgICByZW5kZXIoXG4gICAgICAgIDxBbmltYXRlZFRlc3RDb21wb25lbnQ+XG4gICAgICAgICAgPGRpdj5BbmltYXRlZCBjb250ZW50PC9kaXY+XG4gICAgICAgIDwvQW5pbWF0ZWRUZXN0Q29tcG9uZW50PlxuICAgICAgKVxuXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdhbmltYXRlZC1jb21wb25lbnQnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0FuaW1hdGVkIGNvbnRlbnQnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIH0pXG5cbiAgICBpdCgnaGFuZGxlcyBtb3Rpb24gdmFyaWFudHMnLCAoKSA9PiB7XG4gICAgICBjb25zdCBNb3Rpb25Db21wb25lbnQgPSAoKSA9PiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBkYXRhLXRlc3RpZD1cIm1vdGlvbi1kaXZcIlxuICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICBvcGFjaXR5OiAxLFxuICAgICAgICAgICAgdHJhbnNmb3JtOiAndHJhbnNsYXRlWSgwcHgpJyxcbiAgICAgICAgICB9fVxuICAgICAgICA+XG4gICAgICAgICAgTW90aW9uIGNvbnRlbnRcbiAgICAgICAgPC9kaXY+XG4gICAgICApXG5cbiAgICAgIHJlbmRlcig8TW90aW9uQ29tcG9uZW50IC8+KVxuICAgICAgXG4gICAgICBjb25zdCBtb3Rpb25EaXYgPSBzY3JlZW4uZ2V0QnlUZXN0SWQoJ21vdGlvbi1kaXYnKVxuICAgICAgZXhwZWN0KG1vdGlvbkRpdikudG9IYXZlU3R5bGUoJ29wYWNpdHk6IDEnKVxuICAgICAgZXhwZWN0KG1vdGlvbkRpdikudG9IYXZlU3R5bGUoJ3RyYW5zZm9ybTogdHJhbnNsYXRlWSgwcHgpJylcbiAgICB9KVxuXG4gICAgaXQoJ2hhbmRsZXMgYW5pbWF0aW9uIHByZXNlbmNlJywgKCkgPT4ge1xuICAgICAgY29uc3QgQW5pbWF0ZVByZXNlbmNlQ29tcG9uZW50ID0gKHsgc2hvdyB9OiB7IHNob3c6IGJvb2xlYW4gfSkgPT4gKFxuICAgICAgICA8ZGl2IGRhdGEtdGVzdGlkPVwicHJlc2VuY2UtY29udGFpbmVyXCI+XG4gICAgICAgICAge3Nob3cgJiYgKFxuICAgICAgICAgICAgPGRpdiBkYXRhLXRlc3RpZD1cImFuaW1hdGVkLWl0ZW1cIj5cbiAgICAgICAgICAgICAgQW5pbWF0ZWQgaXRlbVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApXG5cbiAgICAgIGNvbnN0IHsgcmVyZW5kZXIgfSA9IHJlbmRlcig8QW5pbWF0ZVByZXNlbmNlQ29tcG9uZW50IHNob3c9e3RydWV9IC8+KVxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnYW5pbWF0ZWQtaXRlbScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG5cbiAgICAgIHJlcmVuZGVyKDxBbmltYXRlUHJlc2VuY2VDb21wb25lbnQgc2hvdz17ZmFsc2V9IC8+KVxuICAgICAgZXhwZWN0KHNjcmVlbi5xdWVyeUJ5VGVzdElkKCdhbmltYXRlZC1pdGVtJykpLm5vdC50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgfSlcblxuICAgIGl0KCdoYW5kbGVzIHN0YWdnZXIgYW5pbWF0aW9ucycsICgpID0+IHtcbiAgICAgIGNvbnN0IFN0YWdnZXJDb21wb25lbnQgPSAoKSA9PiAoXG4gICAgICAgIDxkaXYgZGF0YS10ZXN0aWQ9XCJzdGFnZ2VyLWNvbnRhaW5lclwiPlxuICAgICAgICAgIHtbMSwgMiwgM10ubWFwKGkgPT4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2l9IGRhdGEtdGVzdGlkPXtgc3RhZ2dlci1pdGVtLSR7aX1gfT5cbiAgICAgICAgICAgICAgSXRlbSB7aX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIClcblxuICAgICAgcmVuZGVyKDxTdGFnZ2VyQ29tcG9uZW50IC8+KVxuICAgICAgXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdzdGFnZ2VyLWl0ZW0tMScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdzdGFnZ2VyLWl0ZW0tMicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdzdGFnZ2VyLWl0ZW0tMycpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgfSlcbiAgfSlcblxuICBkZXNjcmliZSgnUHJlZmVycyBSZWR1Y2VkIE1vdGlvbicsICgpID0+IHtcbiAgICBpdCgncmVzcGVjdHMgcHJlZmVycy1yZWR1Y2VkLW1vdGlvbiBzZXR0aW5nJywgKCkgPT4ge1xuICAgICAgbW9ja1ByZWZlcnNSZWR1Y2VkTW90aW9uKHRydWUpXG4gICAgICBcbiAgICAgIHJlbmRlcihcbiAgICAgICAgPEFuaW1hdGVkVGVzdENvbXBvbmVudD5cbiAgICAgICAgICA8ZGl2PlJlZHVjZWQgbW90aW9uIGNvbnRlbnQ8L2Rpdj5cbiAgICAgICAgPC9BbmltYXRlZFRlc3RDb21wb25lbnQ+XG4gICAgICApXG5cbiAgICAgIGNvbnN0IGNvbXBvbmVudCA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnYW5pbWF0ZWQtY29tcG9uZW50JylcbiAgICAgIGV4cGVjdChjb21wb25lbnQpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICAgIC8vIEFuaW1hdGlvbiBzaG91bGQgYmUgZGlzYWJsZWQgb3IgcmVkdWNlZFxuICAgIH0pXG5cbiAgICBpdCgncHJvdmlkZXMgZnVsbCBhbmltYXRpb25zIHdoZW4gcHJlZmVycy1yZWR1Y2VkLW1vdGlvbiBpcyBmYWxzZScsICgpID0+IHtcbiAgICAgIG1vY2tQcmVmZXJzUmVkdWNlZE1vdGlvbihmYWxzZSlcbiAgICAgIFxuICAgICAgcmVuZGVyKFxuICAgICAgICA8QW5pbWF0ZWRUZXN0Q29tcG9uZW50PlxuICAgICAgICAgIDxkaXY+RnVsbCBhbmltYXRpb24gY29udGVudDwvZGl2PlxuICAgICAgICA8L0FuaW1hdGVkVGVzdENvbXBvbmVudD5cbiAgICAgIClcblxuICAgICAgY29uc3QgY29tcG9uZW50ID0gc2NyZWVuLmdldEJ5VGVzdElkKCdhbmltYXRlZC1jb21wb25lbnQnKVxuICAgICAgZXhwZWN0KGNvbXBvbmVudCkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgICAgLy8gRnVsbCBhbmltYXRpb25zIHNob3VsZCBiZSBlbmFibGVkXG4gICAgfSlcblxuICAgIGl0KCdoYW5kbGVzIG1lZGlhIHF1ZXJ5IGNoYW5nZXMnLCAoKSA9PiB7XG4gICAgICAvLyBTdGFydCB3aXRoIHJlZHVjZWQgbW90aW9uXG4gICAgICBtb2NrUHJlZmVyc1JlZHVjZWRNb3Rpb24odHJ1ZSlcbiAgICAgIFxuICAgICAgY29uc3QgeyByZXJlbmRlciB9ID0gcmVuZGVyKFxuICAgICAgICA8QW5pbWF0ZWRUZXN0Q29tcG9uZW50PlxuICAgICAgICAgIDxkaXY+Q29udGVudDwvZGl2PlxuICAgICAgICA8L0FuaW1hdGVkVGVzdENvbXBvbmVudD5cbiAgICAgIClcblxuICAgICAgLy8gQ2hhbmdlIHRvIGZ1bGwgbW90aW9uXG4gICAgICBtb2NrUHJlZmVyc1JlZHVjZWRNb3Rpb24oZmFsc2UpXG4gICAgICByZXJlbmRlcihcbiAgICAgICAgPEFuaW1hdGVkVGVzdENvbXBvbmVudD5cbiAgICAgICAgICA8ZGl2PkNvbnRlbnQ8L2Rpdj5cbiAgICAgICAgPC9BbmltYXRlZFRlc3RDb21wb25lbnQ+XG4gICAgICApXG5cbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdDb250ZW50JykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICB9KVxuICB9KVxuXG4gIGRlc2NyaWJlKCdBbmltYXRpb24gUGVyZm9ybWFuY2UnLCAoKSA9PiB7XG4gICAgaXQoJ2hhbmRsZXMgcmFwaWQgYW5pbWF0aW9uIHRyaWdnZXJzJywgYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgUmFwaWRBbmltYXRpb25Db21wb25lbnQgPSAoKSA9PiB7XG4gICAgICAgIGNvbnN0IFtjb3VudCwgc2V0Q291bnRdID0gUmVhY3QudXNlU3RhdGUoMClcbiAgICAgICAgXG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldENvdW50KGMgPT4gYyArIDEpfVxuICAgICAgICAgICAgICBkYXRhLXRlc3RpZD1cInRyaWdnZXItYnV0dG9uXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgVHJpZ2dlciBBbmltYXRpb25cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgICAgZGF0YS10ZXN0aWQ9XCJhbmltYXRlZC1jb3VudGVyXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3sgdHJhbnNmb3JtOiBgc2NhbGUoJHsxICsgY291bnQgKiAwLjF9KWAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgQ291bnQ6IHtjb3VudH1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApXG4gICAgICB9XG5cbiAgICAgIHJlbmRlcig8UmFwaWRBbmltYXRpb25Db21wb25lbnQgLz4pXG4gICAgICBcbiAgICAgIGNvbnN0IGJ1dHRvbiA9IHNjcmVlbi5nZXRCeVRlc3RJZCgndHJpZ2dlci1idXR0b24nKVxuICAgICAgY29uc3QgY291bnRlciA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnYW5pbWF0ZWQtY291bnRlcicpXG5cbiAgICAgIC8vIFJhcGlkbHkgdHJpZ2dlciBhbmltYXRpb25zXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IDEwOyBpKyspIHtcbiAgICAgICAgZmlyZUV2ZW50LmNsaWNrKGJ1dHRvbilcbiAgICAgIH1cblxuICAgICAgYXdhaXQgd2FpdEZvcigoKSA9PiB7XG4gICAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdDb3VudDogMTAnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgICAgfSlcblxuICAgICAgZXhwZWN0KGNvdW50ZXIpLnRvSGF2ZVN0eWxlKCd0cmFuc2Zvcm06IHNjYWxlKDIpJylcbiAgICB9KVxuXG4gICAgaXQoJ2hhbmRsZXMgY29uY3VycmVudCBhbmltYXRpb25zJywgKCkgPT4ge1xuICAgICAgY29uc3QgQ29uY3VycmVudEFuaW1hdGlvbkNvbXBvbmVudCA9ICgpID0+IChcbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgZGF0YS10ZXN0aWQ9XCJhbmltYXRpb24tMVwiXG4gICAgICAgICAgICBzdHlsZT17eyBvcGFjaXR5OiAwLjUsIHRyYW5zZm9ybTogJ3RyYW5zbGF0ZVgoMTBweCknIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgQW5pbWF0aW9uIDFcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgZGF0YS10ZXN0aWQ9XCJhbmltYXRpb24tMlwiXG4gICAgICAgICAgICBzdHlsZT17eyBvcGFjaXR5OiAwLjgsIHRyYW5zZm9ybTogJ3RyYW5zbGF0ZVkoMjBweCknIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgQW5pbWF0aW9uIDJcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApXG5cbiAgICAgIHJlbmRlcig8Q29uY3VycmVudEFuaW1hdGlvbkNvbXBvbmVudCAvPilcbiAgICAgIFxuICAgICAgY29uc3QgYW5pbTEgPSBzY3JlZW4uZ2V0QnlUZXN0SWQoJ2FuaW1hdGlvbi0xJylcbiAgICAgIGNvbnN0IGFuaW0yID0gc2NyZWVuLmdldEJ5VGVzdElkKCdhbmltYXRpb24tMicpXG5cbiAgICAgIGV4cGVjdChhbmltMSkudG9IYXZlU3R5bGUoJ29wYWNpdHk6IDAuNScpXG4gICAgICBleHBlY3QoYW5pbTIpLnRvSGF2ZVN0eWxlKCdvcGFjaXR5OiAwLjgnKVxuICAgIH0pXG5cbiAgICBpdCgnY2xlYW5zIHVwIGFuaW1hdGlvbnMgb24gdW5tb3VudCcsICgpID0+IHtcbiAgICAgIGNvbnN0IEFuaW1hdGlvbkNvbXBvbmVudCA9ICgpID0+IChcbiAgICAgICAgPGRpdiBkYXRhLXRlc3RpZD1cImNsZWFudXAtY29tcG9uZW50XCI+XG4gICAgICAgICAgQW5pbWF0ZWQgY29tcG9uZW50XG4gICAgICAgIDwvZGl2PlxuICAgICAgKVxuXG4gICAgICBjb25zdCB7IHVubW91bnQgfSA9IHJlbmRlcig8QW5pbWF0aW9uQ29tcG9uZW50IC8+KVxuICAgICAgXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdjbGVhbnVwLWNvbXBvbmVudCcpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgICBcbiAgICAgIC8vIFVubW91bnQgc2hvdWxkIG5vdCBjYXVzZSBlcnJvcnNcbiAgICAgIGV4cGVjdCgoKSA9PiB1bm1vdW50KCkpLm5vdC50b1Rocm93KClcbiAgICB9KVxuICB9KVxuXG4gIGRlc2NyaWJlKCdBbmltYXRpb24gVGltaW5nJywgKCkgPT4ge1xuICAgIGl0KCd1c2VzIGFwcHJvcHJpYXRlIGFuaW1hdGlvbiBkdXJhdGlvbnMnLCAoKSA9PiB7XG4gICAgICBjb25zdCBUaW1lZEFuaW1hdGlvbkNvbXBvbmVudCA9ICgpID0+IChcbiAgICAgICAgPGRpdiBcbiAgICAgICAgICBkYXRhLXRlc3RpZD1cInRpbWVkLWFuaW1hdGlvblwiXG4gICAgICAgICAgc3R5bGU9e3sgXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuM3MgZWFzZS1vdXQnLFxuICAgICAgICAgICAgb3BhY2l0eTogMSBcbiAgICAgICAgICB9fVxuICAgICAgICA+XG4gICAgICAgICAgVGltZWQgY29udGVudFxuICAgICAgICA8L2Rpdj5cbiAgICAgIClcblxuICAgICAgcmVuZGVyKDxUaW1lZEFuaW1hdGlvbkNvbXBvbmVudCAvPilcbiAgICAgIFxuICAgICAgY29uc3QgY29tcG9uZW50ID0gc2NyZWVuLmdldEJ5VGVzdElkKCd0aW1lZC1hbmltYXRpb24nKVxuICAgICAgZXhwZWN0KGNvbXBvbmVudCkudG9IYXZlU3R5bGUoJ3RyYW5zaXRpb246IGFsbCAwLjNzIGVhc2Utb3V0JylcbiAgICB9KVxuXG4gICAgaXQoJ2hhbmRsZXMgZGlmZmVyZW50IGVhc2luZyBmdW5jdGlvbnMnLCAoKSA9PiB7XG4gICAgICBjb25zdCBFYXNpbmdDb21wb25lbnQgPSAoKSA9PiAoXG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgIGRhdGEtdGVzdGlkPVwiZWFzZS1vdXRcIlxuICAgICAgICAgICAgc3R5bGU9e3sgdHJhbnNpdGlvbjogJ3RyYW5zZm9ybSAwLjJzIGVhc2Utb3V0JyB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIEVhc2UgT3V0XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgIGRhdGEtdGVzdGlkPVwiZWFzZS1pblwiXG4gICAgICAgICAgICBzdHlsZT17eyB0cmFuc2l0aW9uOiAndHJhbnNmb3JtIDAuMnMgZWFzZS1pbicgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICBFYXNlIEluXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKVxuXG4gICAgICByZW5kZXIoPEVhc2luZ0NvbXBvbmVudCAvPilcbiAgICAgIFxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnZWFzZS1vdXQnKSkudG9IYXZlU3R5bGUoJ3RyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzIGVhc2Utb3V0JylcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXN0SWQoJ2Vhc2UtaW4nKSkudG9IYXZlU3R5bGUoJ3RyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzIGVhc2UtaW4nKVxuICAgIH0pXG5cbiAgICBpdCgnaGFuZGxlcyBhbmltYXRpb24gZGVsYXlzJywgKCkgPT4ge1xuICAgICAgY29uc3QgRGVsYXllZEFuaW1hdGlvbkNvbXBvbmVudCA9ICgpID0+IChcbiAgICAgICAgPGRpdiBcbiAgICAgICAgICBkYXRhLXRlc3RpZD1cImRlbGF5ZWQtYW5pbWF0aW9uXCJcbiAgICAgICAgICBzdHlsZT17eyBcbiAgICAgICAgICAgIHRyYW5zaXRpb246ICdvcGFjaXR5IDAuM3MgZWFzZS1vdXQnLFxuICAgICAgICAgICAgdHJhbnNpdGlvbkRlbGF5OiAnMC4xcydcbiAgICAgICAgICB9fVxuICAgICAgICA+XG4gICAgICAgICAgRGVsYXllZCBjb250ZW50XG4gICAgICAgIDwvZGl2PlxuICAgICAgKVxuXG4gICAgICByZW5kZXIoPERlbGF5ZWRBbmltYXRpb25Db21wb25lbnQgLz4pXG4gICAgICBcbiAgICAgIGNvbnN0IGNvbXBvbmVudCA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnZGVsYXllZC1hbmltYXRpb24nKVxuICAgICAgZXhwZWN0KGNvbXBvbmVudCkudG9IYXZlU3R5bGUoJ3RyYW5zaXRpb24tZGVsYXk6IDAuMXMnKVxuICAgIH0pXG4gIH0pXG5cbiAgZGVzY3JpYmUoJ0ludGVyYWN0aXZlIEFuaW1hdGlvbnMnLCAoKSA9PiB7XG4gICAgaXQoJ2hhbmRsZXMgaG92ZXIgYW5pbWF0aW9ucycsICgpID0+IHtcbiAgICAgIGNvbnN0IEhvdmVyQ29tcG9uZW50ID0gKCkgPT4ge1xuICAgICAgICBjb25zdCBbaXNIb3ZlcmVkLCBzZXRJc0hvdmVyZWRdID0gUmVhY3QudXNlU3RhdGUoZmFsc2UpXG4gICAgICAgIFxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGRhdGEtdGVzdGlkPVwiaG92ZXItY29tcG9uZW50XCJcbiAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KCkgPT4gc2V0SXNIb3ZlcmVkKHRydWUpfVxuICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoKSA9PiBzZXRJc0hvdmVyZWQoZmFsc2UpfVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiBpc0hvdmVyZWQgPyAnc2NhbGUoMS4wNSknIDogJ3NjYWxlKDEpJyxcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ3RyYW5zZm9ybSAwLjJzIGVhc2Utb3V0J1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICBIb3ZlciBtZVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApXG4gICAgICB9XG5cbiAgICAgIHJlbmRlcig8SG92ZXJDb21wb25lbnQgLz4pXG4gICAgICBcbiAgICAgIGNvbnN0IGNvbXBvbmVudCA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnaG92ZXItY29tcG9uZW50JylcbiAgICAgIFxuICAgICAgZXhwZWN0KGNvbXBvbmVudCkudG9IYXZlU3R5bGUoJ3RyYW5zZm9ybTogc2NhbGUoMSknKVxuICAgICAgXG4gICAgICBmaXJlRXZlbnQubW91c2VFbnRlcihjb21wb25lbnQpXG4gICAgICBleHBlY3QoY29tcG9uZW50KS50b0hhdmVTdHlsZSgndHJhbnNmb3JtOiBzY2FsZSgxLjA1KScpXG4gICAgICBcbiAgICAgIGZpcmVFdmVudC5tb3VzZUxlYXZlKGNvbXBvbmVudClcbiAgICAgIGV4cGVjdChjb21wb25lbnQpLnRvSGF2ZVN0eWxlKCd0cmFuc2Zvcm06IHNjYWxlKDEpJylcbiAgICB9KVxuXG4gICAgaXQoJ2hhbmRsZXMgY2xpY2sgYW5pbWF0aW9ucycsICgpID0+IHtcbiAgICAgIGNvbnN0IENsaWNrQ29tcG9uZW50ID0gKCkgPT4ge1xuICAgICAgICBjb25zdCBbaXNQcmVzc2VkLCBzZXRJc1ByZXNzZWRdID0gUmVhY3QudXNlU3RhdGUoZmFsc2UpXG4gICAgICAgIFxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIGRhdGEtdGVzdGlkPVwiY2xpY2stY29tcG9uZW50XCJcbiAgICAgICAgICAgIG9uTW91c2VEb3duPXsoKSA9PiBzZXRJc1ByZXNzZWQodHJ1ZSl9XG4gICAgICAgICAgICBvbk1vdXNlVXA9eygpID0+IHNldElzUHJlc3NlZChmYWxzZSl9XG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICB0cmFuc2Zvcm06IGlzUHJlc3NlZCA/ICdzY2FsZSgwLjk1KScgOiAnc2NhbGUoMSknLFxuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAndHJhbnNmb3JtIDAuMXMgZWFzZS1vdXQnXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIENsaWNrIG1lXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIClcbiAgICAgIH1cblxuICAgICAgcmVuZGVyKDxDbGlja0NvbXBvbmVudCAvPilcbiAgICAgIFxuICAgICAgY29uc3QgYnV0dG9uID0gc2NyZWVuLmdldEJ5VGVzdElkKCdjbGljay1jb21wb25lbnQnKVxuICAgICAgXG4gICAgICBleHBlY3QoYnV0dG9uKS50b0hhdmVTdHlsZSgndHJhbnNmb3JtOiBzY2FsZSgxKScpXG4gICAgICBcbiAgICAgIGZpcmVFdmVudC5tb3VzZURvd24oYnV0dG9uKVxuICAgICAgZXhwZWN0KGJ1dHRvbikudG9IYXZlU3R5bGUoJ3RyYW5zZm9ybTogc2NhbGUoMC45NSknKVxuICAgICAgXG4gICAgICBmaXJlRXZlbnQubW91c2VVcChidXR0b24pXG4gICAgICBleHBlY3QoYnV0dG9uKS50b0hhdmVTdHlsZSgndHJhbnNmb3JtOiBzY2FsZSgxKScpXG4gICAgfSlcblxuICAgIGl0KCdoYW5kbGVzIGZvY3VzIGFuaW1hdGlvbnMnLCAoKSA9PiB7XG4gICAgICBjb25zdCBGb2N1c0NvbXBvbmVudCA9ICgpID0+IHtcbiAgICAgICAgY29uc3QgW2lzRm9jdXNlZCwgc2V0SXNGb2N1c2VkXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKVxuICAgICAgICBcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgIGRhdGEtdGVzdGlkPVwiZm9jdXMtY29tcG9uZW50XCJcbiAgICAgICAgICAgIG9uRm9jdXM9eygpID0+IHNldElzRm9jdXNlZCh0cnVlKX1cbiAgICAgICAgICAgIG9uQmx1cj17KCkgPT4gc2V0SXNGb2N1c2VkKGZhbHNlKX1cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJvcmRlckNvbG9yOiBpc0ZvY3VzZWQgPyAnIzNiODJmNicgOiAnI2QxZDVkYicsXG4gICAgICAgICAgICAgIHRyYW5zaXRpb246ICdib3JkZXItY29sb3IgMC4ycyBlYXNlLW91dCdcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkZvY3VzIG1lXCJcbiAgICAgICAgICAvPlxuICAgICAgICApXG4gICAgICB9XG5cbiAgICAgIHJlbmRlcig8Rm9jdXNDb21wb25lbnQgLz4pXG4gICAgICBcbiAgICAgIGNvbnN0IGlucHV0ID0gc2NyZWVuLmdldEJ5VGVzdElkKCdmb2N1cy1jb21wb25lbnQnKVxuICAgICAgXG4gICAgICBleHBlY3QoaW5wdXQpLnRvSGF2ZVN0eWxlKCdib3JkZXItY29sb3I6ICNkMWQ1ZGInKVxuICAgICAgXG4gICAgICBmaXJlRXZlbnQuZm9jdXMoaW5wdXQpXG4gICAgICBleHBlY3QoaW5wdXQpLnRvSGF2ZVN0eWxlKCdib3JkZXItY29sb3I6ICMzYjgyZjYnKVxuICAgICAgXG4gICAgICBmaXJlRXZlbnQuYmx1cihpbnB1dClcbiAgICAgIGV4cGVjdChpbnB1dCkudG9IYXZlU3R5bGUoJ2JvcmRlci1jb2xvcjogI2QxZDVkYicpXG4gICAgfSlcbiAgfSlcblxuICBkZXNjcmliZSgnQW5pbWF0aW9uIEVycm9yIEhhbmRsaW5nJywgKCkgPT4ge1xuICAgIGl0KCdoYW5kbGVzIGFuaW1hdGlvbiBlcnJvcnMgZ3JhY2VmdWxseScsICgpID0+IHtcbiAgICAgIGNvbnN0IEVycm9yUHJvbmVDb21wb25lbnQgPSAoKSA9PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxkaXYgZGF0YS10ZXN0aWQ9XCJlcnJvci1wcm9uZVwiPlxuICAgICAgICAgICAgICBBbmltYXRpb24gY29udGVudFxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8ZGl2IGRhdGEtdGVzdGlkPVwiZXJyb3ItZmFsbGJhY2tcIj5cbiAgICAgICAgICAgICAgRmFsbGJhY2sgY29udGVudFxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJlbmRlcig8RXJyb3JQcm9uZUNvbXBvbmVudCAvPilcbiAgICAgIFxuICAgICAgLy8gU2hvdWxkIHJlbmRlciB3aXRob3V0IHRocm93aW5nXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdlcnJvci1wcm9uZScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgfSlcblxuICAgIGl0KCdwcm92aWRlcyBmYWxsYmFja3MgZm9yIHVuc3VwcG9ydGVkIGFuaW1hdGlvbnMnLCAoKSA9PiB7XG4gICAgICBjb25zdCBGYWxsYmFja0NvbXBvbmVudCA9ICgpID0+IChcbiAgICAgICAgPGRpdiBcbiAgICAgICAgICBkYXRhLXRlc3RpZD1cImZhbGxiYWNrLWNvbXBvbmVudFwiXG4gICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgIC8vIEZhbGxiYWNrIGZvciB1bnN1cHBvcnRlZCBwcm9wZXJ0aWVzXG4gICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGVYKDBweCknLFxuICAgICAgICAgICAgb3BhY2l0eTogMVxuICAgICAgICAgIH19XG4gICAgICAgID5cbiAgICAgICAgICBGYWxsYmFjayBhbmltYXRpb25cbiAgICAgICAgPC9kaXY+XG4gICAgICApXG5cbiAgICAgIHJlbmRlcig8RmFsbGJhY2tDb21wb25lbnQgLz4pXG4gICAgICBcbiAgICAgIGNvbnN0IGNvbXBvbmVudCA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnZmFsbGJhY2stY29tcG9uZW50JylcbiAgICAgIGV4cGVjdChjb21wb25lbnQpLnRvSGF2ZVN0eWxlKCdvcGFjaXR5OiAxJylcbiAgICB9KVxuICB9KVxufSlcbiJdLCJuYW1lcyI6WyJtb2NrRnJhbWVyTW90aW9uIiwiQW5pbWF0ZWRUZXN0Q29tcG9uZW50IiwiY2hpbGRyZW4iLCJkaXYiLCJkYXRhLXRlc3RpZCIsImNsYXNzTmFtZSIsImRlc2NyaWJlIiwiYmVmb3JlRWFjaCIsImplc3QiLCJjbGVhckFsbE1vY2tzIiwibW9ja1ByZWZlcnNSZWR1Y2VkTW90aW9uIiwiaXQiLCJyZW5kZXIiLCJleHBlY3QiLCJzY3JlZW4iLCJnZXRCeVRlc3RJZCIsInRvQmVJblRoZURvY3VtZW50IiwiZ2V0QnlUZXh0IiwiTW90aW9uQ29tcG9uZW50Iiwic3R5bGUiLCJvcGFjaXR5IiwidHJhbnNmb3JtIiwibW90aW9uRGl2IiwidG9IYXZlU3R5bGUiLCJBbmltYXRlUHJlc2VuY2VDb21wb25lbnQiLCJzaG93IiwicmVyZW5kZXIiLCJxdWVyeUJ5VGVzdElkIiwibm90IiwiU3RhZ2dlckNvbXBvbmVudCIsIm1hcCIsImkiLCJjb21wb25lbnQiLCJSYXBpZEFuaW1hdGlvbkNvbXBvbmVudCIsImNvdW50Iiwic2V0Q291bnQiLCJSZWFjdCIsInVzZVN0YXRlIiwiYnV0dG9uIiwib25DbGljayIsImMiLCJjb3VudGVyIiwiZmlyZUV2ZW50IiwiY2xpY2siLCJ3YWl0Rm9yIiwiQ29uY3VycmVudEFuaW1hdGlvbkNvbXBvbmVudCIsImFuaW0xIiwiYW5pbTIiLCJBbmltYXRpb25Db21wb25lbnQiLCJ1bm1vdW50IiwidG9UaHJvdyIsIlRpbWVkQW5pbWF0aW9uQ29tcG9uZW50IiwidHJhbnNpdGlvbiIsIkVhc2luZ0NvbXBvbmVudCIsIkRlbGF5ZWRBbmltYXRpb25Db21wb25lbnQiLCJ0cmFuc2l0aW9uRGVsYXkiLCJIb3ZlckNvbXBvbmVudCIsImlzSG92ZXJlZCIsInNldElzSG92ZXJlZCIsIm9uTW91c2VFbnRlciIsIm9uTW91c2VMZWF2ZSIsIm1vdXNlRW50ZXIiLCJtb3VzZUxlYXZlIiwiQ2xpY2tDb21wb25lbnQiLCJpc1ByZXNzZWQiLCJzZXRJc1ByZXNzZWQiLCJvbk1vdXNlRG93biIsIm9uTW91c2VVcCIsIm1vdXNlRG93biIsIm1vdXNlVXAiLCJGb2N1c0NvbXBvbmVudCIsImlzRm9jdXNlZCIsInNldElzRm9jdXNlZCIsImlucHV0Iiwib25Gb2N1cyIsIm9uQmx1ciIsImJvcmRlckNvbG9yIiwicGxhY2Vob2xkZXIiLCJmb2N1cyIsImJsdXIiLCJFcnJvclByb25lQ29tcG9uZW50IiwiZXJyb3IiLCJGYWxsYmFja0NvbXBvbmVudCJdLCJtYXBwaW5ncyI6Ijs7Ozs7OERBQWtCOzJCQUNpQzs7Ozs7O0FBR25ELHFCQUFxQjtBQUNyQkEsSUFBQUEsMkJBQWdCO0FBRWhCLHNDQUFzQztBQUN0QyxNQUFNQyx3QkFBd0IsQ0FBQyxFQUFFQyxRQUFRLEVBQWlDLGlCQUN4RSxxQkFBQ0M7UUFBSUMsZUFBWTtRQUFxQkMsV0FBVTtrQkFDN0NIOztBQUlMSSxTQUFTLDBCQUEwQjtJQUNqQ0MsV0FBVztRQUNUQyxLQUFLQyxhQUFhO1FBQ2xCLCtCQUErQjtRQUMvQkMsSUFBQUEsbUNBQXdCLEVBQUM7SUFDM0I7SUFFQUosU0FBUyw2QkFBNkI7UUFDcENLLEdBQUcsdUNBQXVDO1lBQ3hDQyxJQUFBQSxpQkFBTSxnQkFDSixxQkFBQ1g7MEJBQ0MsY0FBQSxxQkFBQ0U7OEJBQUk7OztZQUlUVSxPQUFPQyxpQkFBTSxDQUFDQyxXQUFXLENBQUMsdUJBQXVCQyxpQkFBaUI7WUFDbEVILE9BQU9DLGlCQUFNLENBQUNHLFNBQVMsQ0FBQyxxQkFBcUJELGlCQUFpQjtRQUNoRTtRQUVBTCxHQUFHLDJCQUEyQjtZQUM1QixNQUFNTyxrQkFBa0Isa0JBQ3RCLHFCQUFDZjtvQkFDQ0MsZUFBWTtvQkFDWmUsT0FBTzt3QkFDTEMsU0FBUzt3QkFDVEMsV0FBVztvQkFDYjs4QkFDRDs7WUFLSFQsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNNO1lBRVIsTUFBTUksWUFBWVIsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDO1lBQ3JDRixPQUFPUyxXQUFXQyxXQUFXLENBQUM7WUFDOUJWLE9BQU9TLFdBQVdDLFdBQVcsQ0FBQztRQUNoQztRQUVBWixHQUFHLDhCQUE4QjtZQUMvQixNQUFNYSwyQkFBMkIsQ0FBQyxFQUFFQyxJQUFJLEVBQXFCLGlCQUMzRCxxQkFBQ3RCO29CQUFJQyxlQUFZOzhCQUNkcUIsc0JBQ0MscUJBQUN0Qjt3QkFBSUMsZUFBWTtrQ0FBZ0I7OztZQU92QyxNQUFNLEVBQUVzQixRQUFRLEVBQUUsR0FBR2QsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNZO2dCQUF5QkMsTUFBTTs7WUFDNURaLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxrQkFBa0JDLGlCQUFpQjtZQUU3RFUsdUJBQVMscUJBQUNGO2dCQUF5QkMsTUFBTTs7WUFDekNaLE9BQU9DLGlCQUFNLENBQUNhLGFBQWEsQ0FBQyxrQkFBa0JDLEdBQUcsQ0FBQ1osaUJBQWlCO1FBQ3JFO1FBRUFMLEdBQUcsOEJBQThCO1lBQy9CLE1BQU1rQixtQkFBbUIsa0JBQ3ZCLHFCQUFDMUI7b0JBQUlDLGVBQVk7OEJBQ2Q7d0JBQUM7d0JBQUc7d0JBQUc7cUJBQUUsQ0FBQzBCLEdBQUcsQ0FBQ0MsQ0FBQUEsa0JBQ2Isc0JBQUM1Qjs0QkFBWUMsZUFBYSxDQUFDLGFBQWEsRUFBRTJCLEVBQUUsQ0FBQzs7Z0NBQUU7Z0NBQ3ZDQTs7MkJBREVBOztZQU9oQm5CLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDaUI7WUFFUmhCLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxtQkFBbUJDLGlCQUFpQjtZQUM5REgsT0FBT0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLG1CQUFtQkMsaUJBQWlCO1lBQzlESCxPQUFPQyxpQkFBTSxDQUFDQyxXQUFXLENBQUMsbUJBQW1CQyxpQkFBaUI7UUFDaEU7SUFDRjtJQUVBVixTQUFTLDBCQUEwQjtRQUNqQ0ssR0FBRywyQ0FBMkM7WUFDNUNELElBQUFBLG1DQUF3QixFQUFDO1lBRXpCRSxJQUFBQSxpQkFBTSxnQkFDSixxQkFBQ1g7MEJBQ0MsY0FBQSxxQkFBQ0U7OEJBQUk7OztZQUlULE1BQU02QixZQUFZbEIsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDO1lBQ3JDRixPQUFPbUIsV0FBV2hCLGlCQUFpQjtRQUNuQywwQ0FBMEM7UUFDNUM7UUFFQUwsR0FBRyxpRUFBaUU7WUFDbEVELElBQUFBLG1DQUF3QixFQUFDO1lBRXpCRSxJQUFBQSxpQkFBTSxnQkFDSixxQkFBQ1g7MEJBQ0MsY0FBQSxxQkFBQ0U7OEJBQUk7OztZQUlULE1BQU02QixZQUFZbEIsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDO1lBQ3JDRixPQUFPbUIsV0FBV2hCLGlCQUFpQjtRQUNuQyxvQ0FBb0M7UUFDdEM7UUFFQUwsR0FBRywrQkFBK0I7WUFDaEMsNEJBQTRCO1lBQzVCRCxJQUFBQSxtQ0FBd0IsRUFBQztZQUV6QixNQUFNLEVBQUVnQixRQUFRLEVBQUUsR0FBR2QsSUFBQUEsaUJBQU0sZ0JBQ3pCLHFCQUFDWDswQkFDQyxjQUFBLHFCQUFDRTs4QkFBSTs7O1lBSVQsd0JBQXdCO1lBQ3hCTyxJQUFBQSxtQ0FBd0IsRUFBQztZQUN6QmdCLHVCQUNFLHFCQUFDekI7MEJBQ0MsY0FBQSxxQkFBQ0U7OEJBQUk7OztZQUlUVSxPQUFPQyxpQkFBTSxDQUFDRyxTQUFTLENBQUMsWUFBWUQsaUJBQWlCO1FBQ3ZEO0lBQ0Y7SUFFQVYsU0FBUyx5QkFBeUI7UUFDaENLLEdBQUcsb0NBQW9DO1lBQ3JDLE1BQU1zQiwwQkFBMEI7Z0JBQzlCLE1BQU0sQ0FBQ0MsT0FBT0MsU0FBUyxHQUFHQyxjQUFLLENBQUNDLFFBQVEsQ0FBQztnQkFFekMscUJBQ0Usc0JBQUNsQzs7c0NBQ0MscUJBQUNtQzs0QkFDQ0MsU0FBUyxJQUFNSixTQUFTSyxDQUFBQSxJQUFLQSxJQUFJOzRCQUNqQ3BDLGVBQVk7c0NBQ2I7O3NDQUdELHNCQUFDRDs0QkFDQ0MsZUFBWTs0QkFDWmUsT0FBTztnQ0FBRUUsV0FBVyxDQUFDLE1BQU0sRUFBRSxJQUFJYSxRQUFRLElBQUksQ0FBQyxDQUFDOzRCQUFDOztnQ0FDakQ7Z0NBQ1NBOzs7OztZQUloQjtZQUVBdEIsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNxQjtZQUVSLE1BQU1LLFNBQVN4QixpQkFBTSxDQUFDQyxXQUFXLENBQUM7WUFDbEMsTUFBTTBCLFVBQVUzQixpQkFBTSxDQUFDQyxXQUFXLENBQUM7WUFFbkMsNkJBQTZCO1lBQzdCLElBQUssSUFBSWdCLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUFLO2dCQUMzQlcsb0JBQVMsQ0FBQ0MsS0FBSyxDQUFDTDtZQUNsQjtZQUVBLE1BQU1NLElBQUFBLGtCQUFPLEVBQUM7Z0JBQ1ovQixPQUFPQyxpQkFBTSxDQUFDRyxTQUFTLENBQUMsY0FBY0QsaUJBQWlCO1lBQ3pEO1lBRUFILE9BQU80QixTQUFTbEIsV0FBVyxDQUFDO1FBQzlCO1FBRUFaLEdBQUcsaUNBQWlDO1lBQ2xDLE1BQU1rQywrQkFBK0Isa0JBQ25DLHNCQUFDMUM7O3NDQUNDLHFCQUFDQTs0QkFDQ0MsZUFBWTs0QkFDWmUsT0FBTztnQ0FBRUMsU0FBUztnQ0FBS0MsV0FBVzs0QkFBbUI7c0NBQ3REOztzQ0FHRCxxQkFBQ2xCOzRCQUNDQyxlQUFZOzRCQUNaZSxPQUFPO2dDQUFFQyxTQUFTO2dDQUFLQyxXQUFXOzRCQUFtQjtzQ0FDdEQ7Ozs7WUFNTFQsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNpQztZQUVSLE1BQU1DLFFBQVFoQyxpQkFBTSxDQUFDQyxXQUFXLENBQUM7WUFDakMsTUFBTWdDLFFBQVFqQyxpQkFBTSxDQUFDQyxXQUFXLENBQUM7WUFFakNGLE9BQU9pQyxPQUFPdkIsV0FBVyxDQUFDO1lBQzFCVixPQUFPa0MsT0FBT3hCLFdBQVcsQ0FBQztRQUM1QjtRQUVBWixHQUFHLG1DQUFtQztZQUNwQyxNQUFNcUMscUJBQXFCLGtCQUN6QixxQkFBQzdDO29CQUFJQyxlQUFZOzhCQUFvQjs7WUFLdkMsTUFBTSxFQUFFNkMsT0FBTyxFQUFFLEdBQUdyQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ29DO1lBRTVCbkMsT0FBT0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLHNCQUFzQkMsaUJBQWlCO1lBRWpFLGtDQUFrQztZQUNsQ0gsT0FBTyxJQUFNb0MsV0FBV3JCLEdBQUcsQ0FBQ3NCLE9BQU87UUFDckM7SUFDRjtJQUVBNUMsU0FBUyxvQkFBb0I7UUFDM0JLLEdBQUcsd0NBQXdDO1lBQ3pDLE1BQU13QywwQkFBMEIsa0JBQzlCLHFCQUFDaEQ7b0JBQ0NDLGVBQVk7b0JBQ1plLE9BQU87d0JBQ0xpQyxZQUFZO3dCQUNaaEMsU0FBUztvQkFDWDs4QkFDRDs7WUFLSFIsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUN1QztZQUVSLE1BQU1uQixZQUFZbEIsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDO1lBQ3JDRixPQUFPbUIsV0FBV1QsV0FBVyxDQUFDO1FBQ2hDO1FBRUFaLEdBQUcsc0NBQXNDO1lBQ3ZDLE1BQU0wQyxrQkFBa0Isa0JBQ3RCLHNCQUFDbEQ7O3NDQUNDLHFCQUFDQTs0QkFDQ0MsZUFBWTs0QkFDWmUsT0FBTztnQ0FBRWlDLFlBQVk7NEJBQTBCO3NDQUNoRDs7c0NBR0QscUJBQUNqRDs0QkFDQ0MsZUFBWTs0QkFDWmUsT0FBTztnQ0FBRWlDLFlBQVk7NEJBQXlCO3NDQUMvQzs7OztZQU1MeEMsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUN5QztZQUVSeEMsT0FBT0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLGFBQWFRLFdBQVcsQ0FBQztZQUNuRFYsT0FBT0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLFlBQVlRLFdBQVcsQ0FBQztRQUNwRDtRQUVBWixHQUFHLDRCQUE0QjtZQUM3QixNQUFNMkMsNEJBQTRCLGtCQUNoQyxxQkFBQ25EO29CQUNDQyxlQUFZO29CQUNaZSxPQUFPO3dCQUNMaUMsWUFBWTt3QkFDWkcsaUJBQWlCO29CQUNuQjs4QkFDRDs7WUFLSDNDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDMEM7WUFFUixNQUFNdEIsWUFBWWxCLGlCQUFNLENBQUNDLFdBQVcsQ0FBQztZQUNyQ0YsT0FBT21CLFdBQVdULFdBQVcsQ0FBQztRQUNoQztJQUNGO0lBRUFqQixTQUFTLDBCQUEwQjtRQUNqQ0ssR0FBRyw0QkFBNEI7WUFDN0IsTUFBTTZDLGlCQUFpQjtnQkFDckIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUd0QixjQUFLLENBQUNDLFFBQVEsQ0FBQztnQkFFakQscUJBQ0UscUJBQUNsQztvQkFDQ0MsZUFBWTtvQkFDWnVELGNBQWMsSUFBTUQsYUFBYTtvQkFDakNFLGNBQWMsSUFBTUYsYUFBYTtvQkFDakN2QyxPQUFPO3dCQUNMRSxXQUFXb0MsWUFBWSxnQkFBZ0I7d0JBQ3ZDTCxZQUFZO29CQUNkOzhCQUNEOztZQUlMO1lBRUF4QyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQzRDO1lBRVIsTUFBTXhCLFlBQVlsQixpQkFBTSxDQUFDQyxXQUFXLENBQUM7WUFFckNGLE9BQU9tQixXQUFXVCxXQUFXLENBQUM7WUFFOUJtQixvQkFBUyxDQUFDbUIsVUFBVSxDQUFDN0I7WUFDckJuQixPQUFPbUIsV0FBV1QsV0FBVyxDQUFDO1lBRTlCbUIsb0JBQVMsQ0FBQ29CLFVBQVUsQ0FBQzlCO1lBQ3JCbkIsT0FBT21CLFdBQVdULFdBQVcsQ0FBQztRQUNoQztRQUVBWixHQUFHLDRCQUE0QjtZQUM3QixNQUFNb0QsaUJBQWlCO2dCQUNyQixNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBRzdCLGNBQUssQ0FBQ0MsUUFBUSxDQUFDO2dCQUVqRCxxQkFDRSxxQkFBQ0M7b0JBQ0NsQyxlQUFZO29CQUNaOEQsYUFBYSxJQUFNRCxhQUFhO29CQUNoQ0UsV0FBVyxJQUFNRixhQUFhO29CQUM5QjlDLE9BQU87d0JBQ0xFLFdBQVcyQyxZQUFZLGdCQUFnQjt3QkFDdkNaLFlBQVk7b0JBQ2Q7OEJBQ0Q7O1lBSUw7WUFFQXhDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDbUQ7WUFFUixNQUFNekIsU0FBU3hCLGlCQUFNLENBQUNDLFdBQVcsQ0FBQztZQUVsQ0YsT0FBT3lCLFFBQVFmLFdBQVcsQ0FBQztZQUUzQm1CLG9CQUFTLENBQUMwQixTQUFTLENBQUM5QjtZQUNwQnpCLE9BQU95QixRQUFRZixXQUFXLENBQUM7WUFFM0JtQixvQkFBUyxDQUFDMkIsT0FBTyxDQUFDL0I7WUFDbEJ6QixPQUFPeUIsUUFBUWYsV0FBVyxDQUFDO1FBQzdCO1FBRUFaLEdBQUcsNEJBQTRCO1lBQzdCLE1BQU0yRCxpQkFBaUI7Z0JBQ3JCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHcEMsY0FBSyxDQUFDQyxRQUFRLENBQUM7Z0JBRWpELHFCQUNFLHFCQUFDb0M7b0JBQ0NyRSxlQUFZO29CQUNac0UsU0FBUyxJQUFNRixhQUFhO29CQUM1QkcsUUFBUSxJQUFNSCxhQUFhO29CQUMzQnJELE9BQU87d0JBQ0x5RCxhQUFhTCxZQUFZLFlBQVk7d0JBQ3JDbkIsWUFBWTtvQkFDZDtvQkFDQXlCLGFBQVk7O1lBR2xCO1lBRUFqRSxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQzBEO1lBRVIsTUFBTUcsUUFBUTNELGlCQUFNLENBQUNDLFdBQVcsQ0FBQztZQUVqQ0YsT0FBTzRELE9BQU9sRCxXQUFXLENBQUM7WUFFMUJtQixvQkFBUyxDQUFDb0MsS0FBSyxDQUFDTDtZQUNoQjVELE9BQU80RCxPQUFPbEQsV0FBVyxDQUFDO1lBRTFCbUIsb0JBQVMsQ0FBQ3FDLElBQUksQ0FBQ047WUFDZjVELE9BQU80RCxPQUFPbEQsV0FBVyxDQUFDO1FBQzVCO0lBQ0Y7SUFFQWpCLFNBQVMsNEJBQTRCO1FBQ25DSyxHQUFHLHVDQUF1QztZQUN4QyxNQUFNcUUsc0JBQXNCO2dCQUMxQixJQUFJO29CQUNGLHFCQUNFLHFCQUFDN0U7d0JBQUlDLGVBQVk7a0NBQWM7O2dCQUluQyxFQUFFLE9BQU82RSxPQUFPO29CQUNkLHFCQUNFLHFCQUFDOUU7d0JBQUlDLGVBQVk7a0NBQWlCOztnQkFJdEM7WUFDRjtZQUVBUSxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ29FO1lBRVIsaUNBQWlDO1lBQ2pDbkUsT0FBT0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLGdCQUFnQkMsaUJBQWlCO1FBQzdEO1FBRUFMLEdBQUcsaURBQWlEO1lBQ2xELE1BQU11RSxvQkFBb0Isa0JBQ3hCLHFCQUFDL0U7b0JBQ0NDLGVBQVk7b0JBQ1plLE9BQU87d0JBQ0wsc0NBQXNDO3dCQUN0Q0UsV0FBVzt3QkFDWEQsU0FBUztvQkFDWDs4QkFDRDs7WUFLSFIsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNzRTtZQUVSLE1BQU1sRCxZQUFZbEIsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDO1lBQ3JDRixPQUFPbUIsV0FBV1QsV0FBVyxDQUFDO1FBQ2hDO0lBQ0Y7QUFDRiJ9