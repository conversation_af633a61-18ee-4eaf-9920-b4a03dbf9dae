{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\__tests__\\button.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent } from '@/lib/test-utils'\nimport { Button } from '../button'\n\ndescribe('Button Component', () => {\n  it('renders correctly', () => {\n    render(<Button>Click me</Button>)\n    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument()\n  })\n\n  it('handles click events', () => {\n    const handleClick = jest.fn()\n    render(<Button onClick={handleClick}>Click me</Button>)\n    \n    fireEvent.click(screen.getByRole('button'))\n    expect(handleClick).toHaveBeenCalledTimes(1)\n  })\n\n  it('applies variant styles correctly', () => {\n    const { rerender } = render(<Button variant=\"destructive\">Delete</Button>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toHaveClass('bg-destructive')\n    \n    rerender(<Button variant=\"outline\">Outline</Button>)\n    expect(button).toHaveClass('border')\n  })\n\n  it('applies size styles correctly', () => {\n    const { rerender } = render(<Button size=\"sm\">Small</Button>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toHaveClass('h-9')\n    \n    rerender(<Button size=\"lg\">Large</Button>)\n    expect(button).toHaveClass('h-11')\n  })\n\n  it('is disabled when disabled prop is true', () => {\n    render(<Button disabled>Disabled</Button>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toBeDisabled()\n    expect(button).toHaveClass('disabled:pointer-events-none')\n  })\n\n  it('renders as child component when asChild is true', () => {\n    render(\n      <Button asChild>\n        <a href=\"/test\">Link Button</a>\n      </Button>\n    )\n    \n    const link = screen.getByRole('link')\n    expect(link).toBeInTheDocument()\n    expect(link).toHaveAttribute('href', '/test')\n  })\n\n  it('forwards ref correctly', () => {\n    const ref = React.createRef<HTMLButtonElement>()\n    render(<Button ref={ref}>Button with ref</Button>)\n    \n    expect(ref.current).toBeInstanceOf(HTMLButtonElement)\n  })\n\n  it('applies custom className', () => {\n    render(<Button className=\"custom-class\">Custom</Button>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toHaveClass('custom-class')\n  })\n\n  it('supports UAV-specific variant', () => {\n    render(<Button variant=\"uav\">UAV Button</Button>)\n    const button = screen.getByRole('button')\n    \n    expect(button).toHaveClass('bg-uav-primary')\n  })\n\n  it('supports success, warning, and danger variants', () => {\n    const { rerender } = render(<Button variant=\"success\">Success</Button>)\n    let button = screen.getByRole('button')\n    expect(button).toHaveClass('bg-green-600')\n    \n    rerender(<Button variant=\"warning\">Warning</Button>)\n    button = screen.getByRole('button')\n    expect(button).toHaveClass('bg-yellow-600')\n    \n    rerender(<Button variant=\"danger\">Danger</Button>)\n    button = screen.getByRole('button')\n    expect(button).toHaveClass('bg-red-600')\n  })\n})\n"], "names": ["describe", "it", "render", "<PERSON><PERSON>", "expect", "screen", "getByRole", "name", "toBeInTheDocument", "handleClick", "jest", "fn", "onClick", "fireEvent", "click", "toHaveBeenCalledTimes", "rerender", "variant", "button", "toHaveClass", "size", "disabled", "toBeDisabled", "<PERSON><PERSON><PERSON><PERSON>", "a", "href", "link", "toHaveAttribute", "ref", "React", "createRef", "current", "toBeInstanceOf", "HTMLButtonElement", "className"], "mappings": ";;;;;8DAAkB;2BACwB;wBACnB;;;;;;AAEvBA,SAAS,oBAAoB;IAC3BC,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;sBAAC;;QACfC,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAY,IAAIC,iBAAiB;IAC7E;IAEAP,GAAG,wBAAwB;QACzB,MAAMQ,cAAcC,KAAKC,EAAE;QAC3BT,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;YAACS,SAASH;sBAAa;;QAErCI,oBAAS,CAACC,KAAK,CAACT,iBAAM,CAACC,SAAS,CAAC;QACjCF,OAAOK,aAAaM,qBAAqB,CAAC;IAC5C;IAEAd,GAAG,oCAAoC;QACrC,MAAM,EAAEe,QAAQ,EAAE,GAAGd,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;YAACc,SAAQ;sBAAc;;QAC1D,MAAMC,SAASb,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOc,QAAQC,WAAW,CAAC;QAE3BH,uBAAS,qBAACb,cAAM;YAACc,SAAQ;sBAAU;;QACnCb,OAAOc,QAAQC,WAAW,CAAC;IAC7B;IAEAlB,GAAG,iCAAiC;QAClC,MAAM,EAAEe,QAAQ,EAAE,GAAGd,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;YAACiB,MAAK;sBAAK;;QAC9C,MAAMF,SAASb,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOc,QAAQC,WAAW,CAAC;QAE3BH,uBAAS,qBAACb,cAAM;YAACiB,MAAK;sBAAK;;QAC3BhB,OAAOc,QAAQC,WAAW,CAAC;IAC7B;IAEAlB,GAAG,0CAA0C;QAC3CC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;YAACkB,QAAQ;sBAAC;;QACxB,MAAMH,SAASb,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOc,QAAQI,YAAY;QAC3BlB,OAAOc,QAAQC,WAAW,CAAC;IAC7B;IAEAlB,GAAG,mDAAmD;QACpDC,IAAAA,iBAAM,gBACJ,qBAACC,cAAM;YAACoB,OAAO;sBACb,cAAA,qBAACC;gBAAEC,MAAK;0BAAQ;;;QAIpB,MAAMC,OAAOrB,iBAAM,CAACC,SAAS,CAAC;QAC9BF,OAAOsB,MAAMlB,iBAAiB;QAC9BJ,OAAOsB,MAAMC,eAAe,CAAC,QAAQ;IACvC;IAEA1B,GAAG,0BAA0B;QAC3B,MAAM2B,oBAAMC,cAAK,CAACC,SAAS;QAC3B5B,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;YAACyB,KAAKA;sBAAK;;QAEzBxB,OAAOwB,IAAIG,OAAO,EAAEC,cAAc,CAACC;IACrC;IAEAhC,GAAG,4BAA4B;QAC7BC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;YAAC+B,WAAU;sBAAe;;QACxC,MAAMhB,SAASb,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOc,QAAQC,WAAW,CAAC;IAC7B;IAEAlB,GAAG,iCAAiC;QAClCC,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;YAACc,SAAQ;sBAAM;;QAC7B,MAAMC,SAASb,iBAAM,CAACC,SAAS,CAAC;QAEhCF,OAAOc,QAAQC,WAAW,CAAC;IAC7B;IAEAlB,GAAG,kDAAkD;QACnD,MAAM,EAAEe,QAAQ,EAAE,GAAGd,IAAAA,iBAAM,gBAAC,qBAACC,cAAM;YAACc,SAAQ;sBAAU;;QACtD,IAAIC,SAASb,iBAAM,CAACC,SAAS,CAAC;QAC9BF,OAAOc,QAAQC,WAAW,CAAC;QAE3BH,uBAAS,qBAACb,cAAM;YAACc,SAAQ;sBAAU;;QACnCC,SAASb,iBAAM,CAACC,SAAS,CAAC;QAC1BF,OAAOc,QAAQC,WAAW,CAAC;QAE3BH,uBAAS,qBAACb,cAAM;YAACc,SAAQ;sBAAS;;QAClCC,SAASb,iBAAM,CAACC,SAAS,CAAC;QAC1BF,OAAOc,QAAQC,WAAW,CAAC;IAC7B;AACF"}