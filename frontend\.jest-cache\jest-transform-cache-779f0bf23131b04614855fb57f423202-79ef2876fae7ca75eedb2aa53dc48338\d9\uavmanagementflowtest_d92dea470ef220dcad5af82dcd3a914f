90aa87c08e6b4a126f3e185afafc85f6
"use strict";
// Mock the API
jest.mock("@/api/uav-api");
// Mock react-hot-toast
jest.mock("react-hot-toast", ()=>({
        toast: {
            success: jest.fn(),
            error: jest.fn()
        }
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../lib/test-utils");
const _uavstore = require("../../stores/uav-store");
const _uavapi = require("../../api/uav-api");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockedUAVApi = _uavapi.uavApi;
// Integration test component for UAV management
const UAVManagementTestComponent = ()=>{
    const { uavs, selectedUAV, loading: isLoading, error, fetchUAVs, setSelectedUAV, updateUAVStatus, createUAV, updateUAV, deleteUAV } = (0, _uavstore.useUAVStore)();
    const [showCreateForm, setShowCreateForm] = _react.default.useState(false);
    const [formData, setFormData] = _react.default.useState({
        rfidTag: "",
        ownerName: "",
        model: ""
    });
    _react.default.useEffect(()=>{
        fetchUAVs();
    }, [
        fetchUAVs
    ]);
    const handleCreateUAV = async (e)=>{
        e.preventDefault();
        const success = await createUAV(formData);
        if (success) {
            setShowCreateForm(false);
            setFormData({
                rfidTag: "",
                ownerName: "",
                model: ""
            });
        }
    };
    const handleUpdateStatus = async (uavId)=>{
        await updateUAVStatus(uavId);
    };
    const handleDeleteUAV = async (uavId)=>{
        if (window.confirm("Are you sure you want to delete this UAV?")) {
            await deleteUAV(uavId);
        }
    };
    if (isLoading && uavs.length === 0) {
        return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
            children: "Loading UAVs..."
        });
    }
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                children: "UAV Management"
            }),
            error && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                role: "alert",
                children: error
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        onClick: ()=>setShowCreateForm(true),
                        children: "Add New UAV"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        onClick: ()=>fetchUAVs(),
                        disabled: isLoading,
                        children: isLoading ? "Refreshing..." : "Refresh"
                    })
                ]
            }),
            showCreateForm && /*#__PURE__*/ (0, _jsxruntime.jsxs)("form", {
                onSubmit: handleCreateUAV,
                "data-testid": "create-form",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                        children: "Create New UAV"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                                htmlFor: "rfidTag",
                                children: "RFID Tag"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                                id: "rfidTag",
                                type: "text",
                                value: formData.rfidTag,
                                onChange: (e)=>setFormData((prev)=>({
                                            ...prev,
                                            rfidTag: e.target.value
                                        })),
                                required: true
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                                htmlFor: "ownerName",
                                children: "Owner Name"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                                id: "ownerName",
                                type: "text",
                                value: formData.ownerName,
                                onChange: (e)=>setFormData((prev)=>({
                                            ...prev,
                                            ownerName: e.target.value
                                        })),
                                required: true
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                                htmlFor: "model",
                                children: "Model"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                                id: "model",
                                type: "text",
                                value: formData.model,
                                onChange: (e)=>setFormData((prev)=>({
                                            ...prev,
                                            model: e.target.value
                                        })),
                                required: true
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        type: "submit",
                        disabled: isLoading,
                        children: isLoading ? "Creating..." : "Create UAV"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        type: "button",
                        onClick: ()=>setShowCreateForm(false),
                        children: "Cancel"
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "uav-list",
                children: uavs.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                    children: "No UAVs found"
                }) : uavs.map((uav)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        "data-testid": `uav-item-${uav.id}`,
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                children: uav.rfidTag
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                children: [
                                    "Owner: ",
                                    uav.ownerName
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                children: [
                                    "Model: ",
                                    uav.model
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                children: [
                                    "Status: ",
                                    uav.status
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                children: [
                                    "Operational Status: ",
                                    uav.operationalStatus
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                        onClick: ()=>setSelectedUAV(uav),
                                        children: selectedUAV?.id === uav.id ? "Selected" : "Select"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                        onClick: ()=>handleUpdateStatus(uav.id),
                                        children: "Update Status"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                        onClick: ()=>handleDeleteUAV(uav.id),
                                        children: "Delete"
                                    })
                                ]
                            })
                        ]
                    }, uav.id))
            }),
            selectedUAV && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": "selected-uav-details",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                        children: "Selected UAV Details"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "ID: ",
                            selectedUAV.id
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "RFID: ",
                            selectedUAV.rfidTag
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "Owner: ",
                            selectedUAV.ownerName
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "Model: ",
                            selectedUAV.model
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "Flight Hours: ",
                            selectedUAV.totalFlightHours
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        children: [
                            "Flight Cycles: ",
                            selectedUAV.totalFlightCycles
                        ]
                    })
                ]
            })
        ]
    });
};
describe("UAV Management Flow Integration Tests", ()=>{
    const mockUAVs = [
        (0, _testutils.createMockUAV)({
            id: 1,
            rfidTag: "UAV-001",
            ownerName: "John Doe",
            model: "Quadcopter X1",
            status: "AUTHORIZED",
            operationalStatus: "READY"
        }),
        (0, _testutils.createMockUAV)({
            id: 2,
            rfidTag: "UAV-002",
            ownerName: "Jane Smith",
            model: "Fixed Wing Y2",
            status: "PENDING",
            operationalStatus: "MAINTENANCE"
        })
    ];
    beforeEach(()=>{
        jest.clearAllMocks();
        // Reset UAV store
        _uavstore.useUAVStore.setState({
            uavs: [],
            selectedUAV: null,
            loading: false,
            error: null,
            filter: {},
            pagination: {
                page: 1,
                limit: 10,
                sortBy: "id",
                sortOrder: "desc"
            },
            searchQuery: "",
            regions: [],
            systemStats: null,
            hibernatePodStatus: null
        });
        // Mock window.confirm
        window.confirm = jest.fn();
    });
    it("loads and displays UAVs on mount", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Should show loading initially
        expect(_testutils.screen.getByText("Loading UAVs...")).toBeInTheDocument();
        // Wait for UAVs to load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
            expect(_testutils.screen.getByText("UAV-002")).toBeInTheDocument();
        });
        // Should display UAV details
        expect(_testutils.screen.getByText("Owner: John Doe")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Model: Quadcopter X1")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Status: AUTHORIZED")).toBeInTheDocument();
        // Verify API was called
        expect(mockedUAVApi.getUAVs).toHaveBeenCalled();
    });
    it("handles UAV selection", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for UAVs to load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
        });
        // Select first UAV
        const selectButton = _testutils.screen.getAllByText("Select")[0];
        _testutils.fireEvent.click(selectButton);
        // Should show selected UAV details
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByTestId("selected-uav-details")).toBeInTheDocument();
            expect(_testutils.screen.getByText("ID: 1")).toBeInTheDocument();
            expect(_testutils.screen.getByText("RFID: UAV-001")).toBeInTheDocument();
        });
        // Button should show as selected
        expect(_testutils.screen.getByText("Selected")).toBeInTheDocument();
    });
    it("creates new UAV successfully", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue([]);
        const newUAV = (0, _testutils.createMockUAV)({
            id: 3,
            rfidTag: "UAV-003",
            ownerName: "New Owner",
            model: "New Model"
        });
        mockedUAVApi.createUAV.mockResolvedValue({
            success: true,
            data: newUAV,
            message: "UAV created successfully"
        });
        // Mock updated list after creation
        mockedUAVApi.getUAVs.mockResolvedValueOnce([]).mockResolvedValueOnce([
            newUAV
        ]);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for initial load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("No UAVs found")).toBeInTheDocument();
        });
        // Open create form
        _testutils.fireEvent.click(_testutils.screen.getByText("Add New UAV"));
        expect(_testutils.screen.getByTestId("create-form")).toBeInTheDocument();
        // Fill in form
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("RFID Tag"), {
            target: {
                value: "UAV-003"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Owner Name"), {
            target: {
                value: "New Owner"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Model"), {
            target: {
                value: "New Model"
            }
        });
        // Submit form
        _testutils.fireEvent.click(_testutils.screen.getByRole("button", {
            name: "Create UAV"
        }));
        // Wait for creation to complete
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-003")).toBeInTheDocument();
        });
        // Form should be hidden
        expect(_testutils.screen.queryByTestId("create-form")).not.toBeInTheDocument();
        // Verify API was called
        expect(mockedUAVApi.createUAV).toHaveBeenCalledWith({
            rfidTag: "UAV-003",
            ownerName: "New Owner",
            model: "New Model"
        });
    });
    it("handles UAV creation failure", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue([]);
        mockedUAVApi.createUAV.mockRejectedValue(new Error("RFID tag already exists"));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for initial load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("No UAVs found")).toBeInTheDocument();
        });
        // Open create form and fill it
        _testutils.fireEvent.click(_testutils.screen.getByText("Add New UAV"));
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("RFID Tag"), {
            target: {
                value: "DUPLICATE-TAG"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Owner Name"), {
            target: {
                value: "Owner"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Model"), {
            target: {
                value: "Model"
            }
        });
        // Submit form
        _testutils.fireEvent.click(_testutils.screen.getByRole("button", {
            name: "Create UAV"
        }));
        // Wait for error
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
            expect(_testutils.screen.getByText("RFID tag already exists")).toBeInTheDocument();
        });
        // Form should still be visible
        expect(_testutils.screen.getByTestId("create-form")).toBeInTheDocument();
    });
    it("updates UAV status", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        mockedUAVApi.updateUAVStatus.mockResolvedValue({
            success: true,
            message: "Status updated"
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for UAVs to load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
        });
        // Click update status for first UAV
        const updateButtons = _testutils.screen.getAllByText("Update Status");
        _testutils.fireEvent.click(updateButtons[0]);
        // Wait for update to complete
        await (0, _testutils.waitFor)(()=>{
            expect(mockedUAVApi.updateUAVStatus).toHaveBeenCalledWith(1);
        });
    });
    it("deletes UAV with confirmation", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        mockedUAVApi.deleteUAV.mockResolvedValue({
            success: true,
            message: "UAV deleted"
        });
        // Mock updated list after deletion
        mockedUAVApi.getUAVs.mockResolvedValueOnce(mockUAVs).mockResolvedValueOnce([
            mockUAVs[1]
        ]);
        window.confirm.mockReturnValue(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for UAVs to load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
        });
        // Click delete for first UAV
        const deleteButtons = _testutils.screen.getAllByText("Delete");
        _testutils.fireEvent.click(deleteButtons[0]);
        // Should show confirmation
        expect(window.confirm).toHaveBeenCalledWith("Are you sure you want to delete this UAV?");
        // Wait for deletion to complete
        await (0, _testutils.waitFor)(()=>{
            expect(mockedUAVApi.deleteUAV).toHaveBeenCalledWith(1);
        });
    });
    it("cancels deletion when user declines confirmation", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        window.confirm.mockReturnValue(false);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for UAVs to load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
        });
        // Click delete for first UAV
        const deleteButtons = _testutils.screen.getAllByText("Delete");
        _testutils.fireEvent.click(deleteButtons[0]);
        // Should show confirmation but not call delete API
        expect(window.confirm).toHaveBeenCalled();
        expect(mockedUAVApi.deleteUAV).not.toHaveBeenCalled();
    });
    it("handles refresh functionality", async ()=>{
        mockedUAVApi.getUAVs.mockResolvedValue(mockUAVs);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for initial load
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("UAV-001")).toBeInTheDocument();
        });
        // Clear previous calls
        mockedUAVApi.getUAVs.mockClear();
        // Click refresh
        _testutils.fireEvent.click(_testutils.screen.getByText("Refresh"));
        // Should show loading state
        expect(_testutils.screen.getByText("Refreshing...")).toBeInTheDocument();
        // Wait for refresh to complete
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Refresh")).toBeInTheDocument();
        });
        // Verify API was called again
        expect(mockedUAVApi.getUAVs).toHaveBeenCalled();
    });
    it("handles API errors gracefully", async ()=>{
        mockedUAVApi.getUAVs.mockRejectedValue(new Error("Network error"));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(UAVManagementTestComponent, {}));
        // Wait for error to appear
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Network error")).toBeInTheDocument();
        });
        // Should not show loading state
        expect(_testutils.screen.queryByText("Loading UAVs...")).not.toBeInTheDocument();
    });
});

//# sourceMappingURL=data:application/json;base64,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