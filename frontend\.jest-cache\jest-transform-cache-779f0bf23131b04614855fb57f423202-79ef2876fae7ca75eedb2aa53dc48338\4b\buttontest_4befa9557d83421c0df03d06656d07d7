067a76d2895999b0b8658b7c4289ca16
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _button = require("../button");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
describe("Button Component", ()=>{
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            children: "Click me"
        }));
        expect(_testutils.screen.getByRole("button", {
            name: /click me/i
        })).toBeInTheDocument();
    });
    it("handles click events", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            onClick: handleClick,
            children: "Click me"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    it("applies variant styles correctly", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            variant: "destructive",
            children: "Delete"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("bg-destructive");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            variant: "outline",
            children: "Outline"
        }));
        expect(button).toHaveClass("border");
    });
    it("applies size styles correctly", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            size: "sm",
            children: "Small"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("h-9");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            size: "lg",
            children: "Large"
        }));
        expect(button).toHaveClass("h-11");
    });
    it("is disabled when disabled prop is true", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            disabled: true,
            children: "Disabled"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeDisabled();
        expect(button).toHaveClass("disabled:pointer-events-none");
    });
    it("renders as child component when asChild is true", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            asChild: true,
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("a", {
                href: "/test",
                children: "Link Button"
            })
        }));
        const link = _testutils.screen.getByRole("link");
        expect(link).toBeInTheDocument();
        expect(link).toHaveAttribute("href", "/test");
    });
    it("forwards ref correctly", ()=>{
        const ref = /*#__PURE__*/ _react.default.createRef();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            ref: ref,
            children: "Button with ref"
        }));
        expect(ref.current).toBeInstanceOf(HTMLButtonElement);
    });
    it("applies custom className", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            className: "custom-class",
            children: "Custom"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("custom-class");
    });
    it("supports UAV-specific variant", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            variant: "uav",
            children: "UAV Button"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("bg-uav-primary");
    });
    it("supports success, warning, and danger variants", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            variant: "success",
            children: "Success"
        }));
        let button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("bg-green-600");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            variant: "warning",
            children: "Warning"
        }));
        button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("bg-yellow-600");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
            variant: "danger",
            children: "Danger"
        }));
        button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("bg-red-600");
    });
});

//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFxEYUNodWFuZ0JhY2tlbmRcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxfX3Rlc3RzX19cXGJ1dHRvbi50ZXN0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyByZW5kZXIsIHNjcmVlbiwgZmlyZUV2ZW50IH0gZnJvbSAnQC9saWIvdGVzdC11dGlscydcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJy4uL2J1dHRvbidcblxuZGVzY3JpYmUoJ0J1dHRvbiBDb21wb25lbnQnLCAoKSA9PiB7XG4gIGl0KCdyZW5kZXJzIGNvcnJlY3RseScsICgpID0+IHtcbiAgICByZW5kZXIoPEJ1dHRvbj5DbGljayBtZTwvQnV0dG9uPilcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJywgeyBuYW1lOiAvY2xpY2sgbWUvaSB9KSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdoYW5kbGVzIGNsaWNrIGV2ZW50cycsICgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVDbGljayA9IGplc3QuZm4oKVxuICAgIHJlbmRlcig8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUNsaWNrfT5DbGljayBtZTwvQnV0dG9uPilcbiAgICBcbiAgICBmaXJlRXZlbnQuY2xpY2soc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJykpXG4gICAgZXhwZWN0KGhhbmRsZUNsaWNrKS50b0hhdmVCZWVuQ2FsbGVkVGltZXMoMSlcbiAgfSlcblxuICBpdCgnYXBwbGllcyB2YXJpYW50IHN0eWxlcyBjb3JyZWN0bHknLCAoKSA9PiB7XG4gICAgY29uc3QgeyByZXJlbmRlciB9ID0gcmVuZGVyKDxCdXR0b24gdmFyaWFudD1cImRlc3RydWN0aXZlXCI+RGVsZXRlPC9CdXR0b24+KVxuICAgIGNvbnN0IGJ1dHRvbiA9IHNjcmVlbi5nZXRCeVJvbGUoJ2J1dHRvbicpXG4gICAgXG4gICAgZXhwZWN0KGJ1dHRvbikudG9IYXZlQ2xhc3MoJ2JnLWRlc3RydWN0aXZlJylcbiAgICBcbiAgICByZXJlbmRlcig8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCI+T3V0bGluZTwvQnV0dG9uPilcbiAgICBleHBlY3QoYnV0dG9uKS50b0hhdmVDbGFzcygnYm9yZGVyJylcbiAgfSlcblxuICBpdCgnYXBwbGllcyBzaXplIHN0eWxlcyBjb3JyZWN0bHknLCAoKSA9PiB7XG4gICAgY29uc3QgeyByZXJlbmRlciB9ID0gcmVuZGVyKDxCdXR0b24gc2l6ZT1cInNtXCI+U21hbGw8L0J1dHRvbj4pXG4gICAgY29uc3QgYnV0dG9uID0gc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJylcbiAgICBcbiAgICBleHBlY3QoYnV0dG9uKS50b0hhdmVDbGFzcygnaC05JylcbiAgICBcbiAgICByZXJlbmRlcig8QnV0dG9uIHNpemU9XCJsZ1wiPkxhcmdlPC9CdXR0b24+KVxuICAgIGV4cGVjdChidXR0b24pLnRvSGF2ZUNsYXNzKCdoLTExJylcbiAgfSlcblxuICBpdCgnaXMgZGlzYWJsZWQgd2hlbiBkaXNhYmxlZCBwcm9wIGlzIHRydWUnLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxCdXR0b24gZGlzYWJsZWQ+RGlzYWJsZWQ8L0J1dHRvbj4pXG4gICAgY29uc3QgYnV0dG9uID0gc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJylcbiAgICBcbiAgICBleHBlY3QoYnV0dG9uKS50b0JlRGlzYWJsZWQoKVxuICAgIGV4cGVjdChidXR0b24pLnRvSGF2ZUNsYXNzKCdkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lJylcbiAgfSlcblxuICBpdCgncmVuZGVycyBhcyBjaGlsZCBjb21wb25lbnQgd2hlbiBhc0NoaWxkIGlzIHRydWUnLCAoKSA9PiB7XG4gICAgcmVuZGVyKFxuICAgICAgPEJ1dHRvbiBhc0NoaWxkPlxuICAgICAgICA8YSBocmVmPVwiL3Rlc3RcIj5MaW5rIEJ1dHRvbjwvYT5cbiAgICAgIDwvQnV0dG9uPlxuICAgIClcbiAgICBcbiAgICBjb25zdCBsaW5rID0gc2NyZWVuLmdldEJ5Um9sZSgnbGluaycpXG4gICAgZXhwZWN0KGxpbmspLnRvQmVJblRoZURvY3VtZW50KClcbiAgICBleHBlY3QobGluaykudG9IYXZlQXR0cmlidXRlKCdocmVmJywgJy90ZXN0JylcbiAgfSlcblxuICBpdCgnZm9yd2FyZHMgcmVmIGNvcnJlY3RseScsICgpID0+IHtcbiAgICBjb25zdCByZWYgPSBSZWFjdC5jcmVhdGVSZWY8SFRNTEJ1dHRvbkVsZW1lbnQ+KClcbiAgICByZW5kZXIoPEJ1dHRvbiByZWY9e3JlZn0+QnV0dG9uIHdpdGggcmVmPC9CdXR0b24+KVxuICAgIFxuICAgIGV4cGVjdChyZWYuY3VycmVudCkudG9CZUluc3RhbmNlT2YoSFRNTEJ1dHRvbkVsZW1lbnQpXG4gIH0pXG5cbiAgaXQoJ2FwcGxpZXMgY3VzdG9tIGNsYXNzTmFtZScsICgpID0+IHtcbiAgICByZW5kZXIoPEJ1dHRvbiBjbGFzc05hbWU9XCJjdXN0b20tY2xhc3NcIj5DdXN0b208L0J1dHRvbj4pXG4gICAgY29uc3QgYnV0dG9uID0gc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJylcbiAgICBcbiAgICBleHBlY3QoYnV0dG9uKS50b0hhdmVDbGFzcygnY3VzdG9tLWNsYXNzJylcbiAgfSlcblxuICBpdCgnc3VwcG9ydHMgVUFWLXNwZWNpZmljIHZhcmlhbnQnLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxCdXR0b24gdmFyaWFudD1cInVhdlwiPlVBViBCdXR0b248L0J1dHRvbj4pXG4gICAgY29uc3QgYnV0dG9uID0gc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJylcbiAgICBcbiAgICBleHBlY3QoYnV0dG9uKS50b0hhdmVDbGFzcygnYmctdWF2LXByaW1hcnknKVxuICB9KVxuXG4gIGl0KCdzdXBwb3J0cyBzdWNjZXNzLCB3YXJuaW5nLCBhbmQgZGFuZ2VyIHZhcmlhbnRzJywgKCkgPT4ge1xuICAgIGNvbnN0IHsgcmVyZW5kZXIgfSA9IHJlbmRlcig8QnV0dG9uIHZhcmlhbnQ9XCJzdWNjZXNzXCI+U3VjY2VzczwvQnV0dG9uPilcbiAgICBsZXQgYnV0dG9uID0gc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJylcbiAgICBleHBlY3QoYnV0dG9uKS50b0hhdmVDbGFzcygnYmctZ3JlZW4tNjAwJylcbiAgICBcbiAgICByZXJlbmRlcig8QnV0dG9uIHZhcmlhbnQ9XCJ3YXJuaW5nXCI+V2FybmluZzwvQnV0dG9uPilcbiAgICBidXR0b24gPSBzY3JlZW4uZ2V0QnlSb2xlKCdidXR0b24nKVxuICAgIGV4cGVjdChidXR0b24pLnRvSGF2ZUNsYXNzKCdiZy15ZWxsb3ctNjAwJylcbiAgICBcbiAgICByZXJlbmRlcig8QnV0dG9uIHZhcmlhbnQ9XCJkYW5nZXJcIj5EYW5nZXI8L0J1dHRvbj4pXG4gICAgYnV0dG9uID0gc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJylcbiAgICBleHBlY3QoYnV0dG9uKS50b0hhdmVDbGFzcygnYmctcmVkLTYwMCcpXG4gIH0pXG59KVxuIl0sIm5hbWVzIjpbImRlc2NyaWJlIiwiaXQiLCJyZW5kZXIiLCJCdXR0b24iLCJleHBlY3QiLCJzY3JlZW4iLCJnZXRCeVJvbGUiLCJuYW1lIiwidG9CZUluVGhlRG9jdW1lbnQiLCJoYW5kbGVDbGljayIsImplc3QiLCJmbiIsIm9uQ2xpY2siLCJmaXJlRXZlbnQiLCJjbGljayIsInRvSGF2ZUJlZW5DYWxsZWRUaW1lcyIsInJlcmVuZGVyIiwidmFyaWFudCIsImJ1dHRvbiIsInRvSGF2ZUNsYXNzIiwic2l6ZSIsImRpc2FibGVkIiwidG9CZURpc2FibGVkIiwiYXNDaGlsZCIsImEiLCJocmVmIiwibGluayIsInRvSGF2ZUF0dHJpYnV0ZSIsInJlZiIsIlJlYWN0IiwiY3JlYXRlUmVmIiwiY3VycmVudCIsInRvQmVJbnN0YW5jZU9mIiwiSFRNTEJ1dHRvbkVsZW1lbnQiLCJjbGFzc05hbWUiXSwibWFwcGluZ3MiOiI7Ozs7OzhEQUFrQjsyQkFDd0I7d0JBQ25COzs7Ozs7QUFFdkJBLFNBQVMsb0JBQW9CO0lBQzNCQyxHQUFHLHFCQUFxQjtRQUN0QkMsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLGNBQU07c0JBQUM7O1FBQ2ZDLE9BQU9DLGlCQUFNLENBQUNDLFNBQVMsQ0FBQyxVQUFVO1lBQUVDLE1BQU07UUFBWSxJQUFJQyxpQkFBaUI7SUFDN0U7SUFFQVAsR0FBRyx3QkFBd0I7UUFDekIsTUFBTVEsY0FBY0MsS0FBS0MsRUFBRTtRQUMzQlQsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLGNBQU07WUFBQ1MsU0FBU0g7c0JBQWE7O1FBRXJDSSxvQkFBUyxDQUFDQyxLQUFLLENBQUNULGlCQUFNLENBQUNDLFNBQVMsQ0FBQztRQUNqQ0YsT0FBT0ssYUFBYU0scUJBQXFCLENBQUM7SUFDNUM7SUFFQWQsR0FBRyxvQ0FBb0M7UUFDckMsTUFBTSxFQUFFZSxRQUFRLEVBQUUsR0FBR2QsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLGNBQU07WUFBQ2MsU0FBUTtzQkFBYzs7UUFDMUQsTUFBTUMsU0FBU2IsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDO1FBRWhDRixPQUFPYyxRQUFRQyxXQUFXLENBQUM7UUFFM0JILHVCQUFTLHFCQUFDYixjQUFNO1lBQUNjLFNBQVE7c0JBQVU7O1FBQ25DYixPQUFPYyxRQUFRQyxXQUFXLENBQUM7SUFDN0I7SUFFQWxCLEdBQUcsaUNBQWlDO1FBQ2xDLE1BQU0sRUFBRWUsUUFBUSxFQUFFLEdBQUdkLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyxjQUFNO1lBQUNpQixNQUFLO3NCQUFLOztRQUM5QyxNQUFNRixTQUFTYixpQkFBTSxDQUFDQyxTQUFTLENBQUM7UUFFaENGLE9BQU9jLFFBQVFDLFdBQVcsQ0FBQztRQUUzQkgsdUJBQVMscUJBQUNiLGNBQU07WUFBQ2lCLE1BQUs7c0JBQUs7O1FBQzNCaEIsT0FBT2MsUUFBUUMsV0FBVyxDQUFDO0lBQzdCO0lBRUFsQixHQUFHLDBDQUEwQztRQUMzQ0MsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLGNBQU07WUFBQ2tCLFFBQVE7c0JBQUM7O1FBQ3hCLE1BQU1ILFNBQVNiLGlCQUFNLENBQUNDLFNBQVMsQ0FBQztRQUVoQ0YsT0FBT2MsUUFBUUksWUFBWTtRQUMzQmxCLE9BQU9jLFFBQVFDLFdBQVcsQ0FBQztJQUM3QjtJQUVBbEIsR0FBRyxtREFBbUQ7UUFDcERDLElBQUFBLGlCQUFNLGdCQUNKLHFCQUFDQyxjQUFNO1lBQUNvQixPQUFPO3NCQUNiLGNBQUEscUJBQUNDO2dCQUFFQyxNQUFLOzBCQUFROzs7UUFJcEIsTUFBTUMsT0FBT3JCLGlCQUFNLENBQUNDLFNBQVMsQ0FBQztRQUM5QkYsT0FBT3NCLE1BQU1sQixpQkFBaUI7UUFDOUJKLE9BQU9zQixNQUFNQyxlQUFlLENBQUMsUUFBUTtJQUN2QztJQUVBMUIsR0FBRywwQkFBMEI7UUFDM0IsTUFBTTJCLG9CQUFNQyxjQUFLLENBQUNDLFNBQVM7UUFDM0I1QixJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsY0FBTTtZQUFDeUIsS0FBS0E7c0JBQUs7O1FBRXpCeEIsT0FBT3dCLElBQUlHLE9BQU8sRUFBRUMsY0FBYyxDQUFDQztJQUNyQztJQUVBaEMsR0FBRyw0QkFBNEI7UUFDN0JDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyxjQUFNO1lBQUMrQixXQUFVO3NCQUFlOztRQUN4QyxNQUFNaEIsU0FBU2IsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDO1FBRWhDRixPQUFPYyxRQUFRQyxXQUFXLENBQUM7SUFDN0I7SUFFQWxCLEdBQUcsaUNBQWlDO1FBQ2xDQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsY0FBTTtZQUFDYyxTQUFRO3NCQUFNOztRQUM3QixNQUFNQyxTQUFTYixpQkFBTSxDQUFDQyxTQUFTLENBQUM7UUFFaENGLE9BQU9jLFFBQVFDLFdBQVcsQ0FBQztJQUM3QjtJQUVBbEIsR0FBRyxrREFBa0Q7UUFDbkQsTUFBTSxFQUFFZSxRQUFRLEVBQUUsR0FBR2QsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLGNBQU07WUFBQ2MsU0FBUTtzQkFBVTs7UUFDdEQsSUFBSUMsU0FBU2IsaUJBQU0sQ0FBQ0MsU0FBUyxDQUFDO1FBQzlCRixPQUFPYyxRQUFRQyxXQUFXLENBQUM7UUFFM0JILHVCQUFTLHFCQUFDYixjQUFNO1lBQUNjLFNBQVE7c0JBQVU7O1FBQ25DQyxTQUFTYixpQkFBTSxDQUFDQyxTQUFTLENBQUM7UUFDMUJGLE9BQU9jLFFBQVFDLFdBQVcsQ0FBQztRQUUzQkgsdUJBQVMscUJBQUNiLGNBQU07WUFBQ2MsU0FBUTtzQkFBUzs7UUFDbENDLFNBQVNiLGlCQUFNLENBQUNDLFNBQVMsQ0FBQztRQUMxQkYsT0FBT2MsUUFBUUMsV0FBVyxDQUFDO0lBQzdCO0FBQ0YifQ==