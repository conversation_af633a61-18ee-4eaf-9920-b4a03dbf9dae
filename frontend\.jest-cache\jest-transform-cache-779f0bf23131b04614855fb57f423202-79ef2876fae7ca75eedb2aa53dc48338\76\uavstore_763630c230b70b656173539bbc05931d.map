{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\stores\\uav-store.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools, subscribeWithSelector } from 'zustand/middleware';\nimport { immer } from 'zustand/middleware/immer';\nimport {\n  UAV,\n  UAVFilter,\n  PaginationParams,\n  SystemStatistics,\n  HibernatePodStatus,\n  Region,\n  CreateUAVRequest,\n  UpdateUAVRequest,\n} from '@/types/uav';\nimport { uavApi, hibernatePodApi, regionApi } from '@/api/uav-api';\nimport { toast } from 'react-hot-toast';\n\ninterface UAVState {\n  // Data\n  uavs: UAV[];\n  selectedUAV: UAV | null;\n  regions: Region[];\n  systemStats: SystemStatistics | null;\n  hibernatePodStatus: HibernatePodStatus | null;\n\n  // UI State\n  loading: boolean;\n  error: string | null;\n  filter: UAVFilter;\n  pagination: PaginationParams;\n  searchQuery: string;\n\n  // Actions\n  fetchUAVs: () => Promise<void>;\n  fetchUAVById: (id: number) => Promise<void>;\n  createUAV: (uav: CreateUAVRequest) => Promise<boolean>;\n  updateUAV: (id: number, uav: Partial<UpdateUAVRequest>) => Promise<boolean>;\n  deleteUAV: (id: number) => Promise<boolean>;\n  updateUAVStatus: (id: number) => Promise<boolean>;\n  \n  // Region actions\n  fetchRegions: () => Promise<void>;\n  addRegionToUAV: (uavId: number, regionId: number) => Promise<boolean>;\n  removeRegionFromUAV: (uavId: number, regionId: number) => Promise<boolean>;\n  \n  // Hibernate pod actions\n  fetchHibernatePodStatus: () => Promise<void>;\n  addToHibernatePod: (uavId: number) => Promise<boolean>;\n  removeFromHibernatePod: (uavId: number) => Promise<boolean>;\n  \n  // Statistics\n  fetchSystemStats: () => Promise<void>;\n  \n  // UI actions\n  setFilter: (filter: Partial<UAVFilter>) => void;\n  setPagination: (pagination: Partial<PaginationParams>) => void;\n  setSearchQuery: (query: string) => void;\n  setSelectedUAV: (uav: UAV | null) => void;\n  clearError: () => void;\n  \n  // Bulk actions\n  bulkUpdateStatus: (uavIds: number[], status: string) => Promise<boolean>;\n  bulkDelete: (uavIds: number[]) => Promise<boolean>;\n  \n  // Real-time updates\n  updateUAVInStore: (uav: UAV) => void;\n  removeUAVFromStore: (uavId: number) => void;\n  updateHibernatePodInStore: (status: HibernatePodStatus) => void;\n}\n\nexport const useUAVStore = create<UAVState>()(\n  devtools(\n    subscribeWithSelector(\n      immer((set, get) => ({\n        // Initial state\n        uavs: [],\n        selectedUAV: null,\n        regions: [],\n        systemStats: null,\n        hibernatePodStatus: null,\n        loading: false,\n        error: null,\n        filter: {},\n        pagination: { page: 1, limit: 10, sortBy: 'id', sortOrder: 'desc' },\n        searchQuery: '',\n\n        // Fetch UAVs\n        fetchUAVs: async () => {\n          set((state) => {\n            state.loading = true;\n            state.error = null;\n          });\n\n          try {\n            const { filter, pagination, searchQuery } = get();\n            const finalFilter = searchQuery ? { ...filter, search: searchQuery } : filter;\n            const uavs = await uavApi.getUAVs(finalFilter, pagination);\n            \n            set((state) => {\n              state.uavs = uavs;\n              state.loading = false;\n            });\n          } catch (error) {\n            set((state) => {\n              state.error = error instanceof Error ? error.message : 'Failed to fetch UAVs';\n              state.loading = false;\n            });\n          }\n        },\n\n        // Fetch UAV by ID\n        fetchUAVById: async (id: number) => {\n          set((state) => {\n            state.loading = true;\n            state.error = null;\n          });\n\n          try {\n            const uav = await uavApi.getUAVById(id);\n            set((state) => {\n              state.selectedUAV = uav;\n              state.loading = false;\n            });\n          } catch (error) {\n            set((state) => {\n              state.error = error instanceof Error ? error.message : 'Failed to fetch UAV';\n              state.loading = false;\n            });\n          }\n        },\n\n        // Create UAV\n        createUAV: async (uav: CreateUAVRequest) => {\n          set((state) => {\n            state.loading = true;\n            state.error = null;\n          });\n\n          try {\n            const response = await uavApi.createUAV(uav);\n            if (response.success && response.data) {\n              set((state) => {\n                state.uavs.unshift(response.data!);\n                state.loading = false;\n              });\n              toast.success('UAV created successfully');\n              return true;\n            } else {\n              throw new Error(response.message || 'Failed to create UAV');\n            }\n          } catch (error) {\n            const message = error instanceof Error ? error.message : 'Failed to create UAV';\n            set((state) => {\n              state.error = message;\n              state.loading = false;\n            });\n            toast.error(message);\n            return false;\n          }\n        },\n\n        // Update UAV\n        updateUAV: async (id: number, uav: Partial<UpdateUAVRequest>) => {\n          set((state) => {\n            state.loading = true;\n            state.error = null;\n          });\n\n          try {\n            const response = await uavApi.updateUAV(id, uav);\n            if (response.success && response.data) {\n              set((state) => {\n                const index = state.uavs.findIndex(u => u.id === id);\n                if (index !== -1) {\n                  state.uavs[index] = response.data!;\n                }\n                if (state.selectedUAV?.id === id) {\n                  state.selectedUAV = response.data!;\n                }\n                state.loading = false;\n              });\n              toast.success('UAV updated successfully');\n              return true;\n            } else {\n              throw new Error(response.message || 'Failed to update UAV');\n            }\n          } catch (error) {\n            const message = error instanceof Error ? error.message : 'Failed to update UAV';\n            set((state) => {\n              state.error = message;\n              state.loading = false;\n            });\n            toast.error(message);\n            return false;\n          }\n        },\n\n        // Delete UAV\n        deleteUAV: async (id: number) => {\n          set((state) => {\n            state.loading = true;\n            state.error = null;\n          });\n\n          try {\n            const response = await uavApi.deleteUAV(id);\n            if (response.success) {\n              set((state) => {\n                state.uavs = state.uavs.filter(u => u.id !== id);\n                if (state.selectedUAV?.id === id) {\n                  state.selectedUAV = null;\n                }\n                state.loading = false;\n              });\n              toast.success('UAV deleted successfully');\n              return true;\n            } else {\n              throw new Error(response.message || 'Failed to delete UAV');\n            }\n          } catch (error) {\n            const message = error instanceof Error ? error.message : 'Failed to delete UAV';\n            set((state) => {\n              state.error = message;\n              state.loading = false;\n            });\n            toast.error(message);\n            return false;\n          }\n        },\n\n        // Update UAV status\n        updateUAVStatus: async (id: number) => {\n          try {\n            const response = await uavApi.updateUAVStatus(id);\n            if (response.success) {\n              set((state) => {\n                const index = state.uavs.findIndex(u => u.id === id);\n                if (index !== -1) {\n                  // Toggle status\n                  const currentStatus = state.uavs[index].status;\n                  state.uavs[index].status = currentStatus === 'AUTHORIZED' ? 'UNAUTHORIZED' : 'AUTHORIZED';\n                }\n              });\n              toast.success('UAV status updated successfully');\n              return true;\n            } else {\n              throw new Error(response.message || 'Failed to update UAV status');\n            }\n          } catch (error) {\n            const message = error instanceof Error ? error.message : 'Failed to update UAV status';\n            toast.error(message);\n            return false;\n          }\n        },\n\n        // Fetch regions\n        fetchRegions: async () => {\n          try {\n            const regions = await regionApi.getRegions();\n            set((state) => {\n              state.regions = regions;\n            });\n          } catch (error) {\n            console.error('Failed to fetch regions:', error);\n          }\n        },\n\n        // Add region to UAV\n        addRegionToUAV: async (uavId: number, regionId: number) => {\n          try {\n            const response = await uavApi.addRegionToUAV(uavId, regionId);\n            if (response.success && response.data) {\n              set((state) => {\n                const index = state.uavs.findIndex(u => u.id === uavId);\n                if (index !== -1) {\n                  state.uavs[index] = response.data!;\n                }\n              });\n              toast.success('Region added to UAV successfully');\n              return true;\n            } else {\n              throw new Error(response.message || 'Failed to add region to UAV');\n            }\n          } catch (error) {\n            const message = error instanceof Error ? error.message : 'Failed to add region to UAV';\n            toast.error(message);\n            return false;\n          }\n        },\n\n        // Remove region from UAV\n        removeRegionFromUAV: async (uavId: number, regionId: number) => {\n          try {\n            const response = await uavApi.removeRegionFromUAV(uavId, regionId);\n            if (response.success) {\n              set((state) => {\n                const index = state.uavs.findIndex(u => u.id === uavId);\n                if (index !== -1) {\n                  state.uavs[index].regions = state.uavs[index].regions.filter(r => r.id !== regionId);\n                }\n              });\n              toast.success('Region removed from UAV successfully');\n              return true;\n            } else {\n              throw new Error(response.message || 'Failed to remove region from UAV');\n            }\n          } catch (error) {\n            const message = error instanceof Error ? error.message : 'Failed to remove region from UAV';\n            toast.error(message);\n            return false;\n          }\n        },\n\n        // Fetch hibernate pod status\n        fetchHibernatePodStatus: async () => {\n          try {\n            const status = await hibernatePodApi.getStatus();\n            set((state) => {\n              state.hibernatePodStatus = status;\n            });\n          } catch (error) {\n            console.error('Failed to fetch hibernate pod status:', error);\n          }\n        },\n\n        // Add to hibernate pod\n        addToHibernatePod: async (uavId: number) => {\n          try {\n            const response = await hibernatePodApi.addUAV(uavId);\n            if (response.success) {\n              set((state) => {\n                const index = state.uavs.findIndex(u => u.id === uavId);\n                if (index !== -1) {\n                  state.uavs[index].inHibernatePod = true;\n                  state.uavs[index].operationalStatus = 'HIBERNATING';\n                }\n              });\n              // Refresh hibernate pod status\n              get().fetchHibernatePodStatus();\n              toast.success('UAV added to hibernate pod successfully');\n              return true;\n            } else {\n              throw new Error(response.message || 'Failed to add UAV to hibernate pod');\n            }\n          } catch (error) {\n            const message = error instanceof Error ? error.message : 'Failed to add UAV to hibernate pod';\n            toast.error(message);\n            return false;\n          }\n        },\n\n        // Remove from hibernate pod\n        removeFromHibernatePod: async (uavId: number) => {\n          try {\n            const response = await hibernatePodApi.removeUAV(uavId);\n            if (response.success) {\n              set((state) => {\n                const index = state.uavs.findIndex(u => u.id === uavId);\n                if (index !== -1) {\n                  state.uavs[index].inHibernatePod = false;\n                  state.uavs[index].operationalStatus = 'READY';\n                }\n              });\n              // Refresh hibernate pod status\n              get().fetchHibernatePodStatus();\n              toast.success('UAV removed from hibernate pod successfully');\n              return true;\n            } else {\n              throw new Error(response.message || 'Failed to remove UAV from hibernate pod');\n            }\n          } catch (error) {\n            const message = error instanceof Error ? error.message : 'Failed to remove UAV from hibernate pod';\n            toast.error(message);\n            return false;\n          }\n        },\n\n        // Fetch system statistics\n        fetchSystemStats: async () => {\n          try {\n            const stats = await uavApi.getSystemStatistics();\n            set((state) => {\n              state.systemStats = stats;\n            });\n          } catch (error) {\n            console.error('Failed to fetch system statistics:', error);\n          }\n        },\n\n        // UI actions\n        setFilter: (filter: Partial<UAVFilter>) => {\n          set((state) => {\n            state.filter = { ...state.filter, ...filter };\n          });\n        },\n\n        setPagination: (pagination: Partial<PaginationParams>) => {\n          set((state) => {\n            state.pagination = { ...state.pagination, ...pagination };\n          });\n        },\n\n        setSearchQuery: (query: string) => {\n          set((state) => {\n            state.searchQuery = query;\n          });\n        },\n\n        setSelectedUAV: (uav: UAV | null) => {\n          set((state) => {\n            state.selectedUAV = uav;\n          });\n        },\n\n        clearError: () => {\n          set((state) => {\n            state.error = null;\n          });\n        },\n\n        // Bulk actions\n        bulkUpdateStatus: async (uavIds: number[], status: string) => {\n          try {\n            const response = await uavApi.bulkUpdateStatus(uavIds, status);\n            if (response.success) {\n              set((state) => {\n                uavIds.forEach(id => {\n                  const index = state.uavs.findIndex(u => u.id === id);\n                  if (index !== -1) {\n                    state.uavs[index].status = status as any;\n                  }\n                });\n              });\n              toast.success(`${uavIds.length} UAVs updated successfully`);\n              return true;\n            } else {\n              throw new Error(response.message || 'Failed to update UAVs');\n            }\n          } catch (error) {\n            const message = error instanceof Error ? error.message : 'Failed to update UAVs';\n            toast.error(message);\n            return false;\n          }\n        },\n\n        bulkDelete: async (uavIds: number[]) => {\n          try {\n            const response = await uavApi.bulkDelete(uavIds);\n            if (response.success) {\n              set((state) => {\n                state.uavs = state.uavs.filter(u => !uavIds.includes(u.id));\n              });\n              toast.success(`${uavIds.length} UAVs deleted successfully`);\n              return true;\n            } else {\n              throw new Error(response.message || 'Failed to delete UAVs');\n            }\n          } catch (error) {\n            const message = error instanceof Error ? error.message : 'Failed to delete UAVs';\n            toast.error(message);\n            return false;\n          }\n        },\n\n        // Real-time updates\n        updateUAVInStore: (uav: UAV) => {\n          set((state) => {\n            const index = state.uavs.findIndex(u => u.id === uav.id);\n            if (index !== -1) {\n              state.uavs[index] = uav;\n            } else {\n              state.uavs.unshift(uav);\n            }\n            if (state.selectedUAV?.id === uav.id) {\n              state.selectedUAV = uav;\n            }\n          });\n        },\n\n        removeUAVFromStore: (uavId: number) => {\n          set((state) => {\n            state.uavs = state.uavs.filter(u => u.id !== uavId);\n            if (state.selectedUAV?.id === uavId) {\n              state.selectedUAV = null;\n            }\n          });\n        },\n\n        updateHibernatePodInStore: (status: HibernatePodStatus) => {\n          set((state) => {\n            state.hibernatePodStatus = status;\n          });\n        },\n      }))\n    ),\n    {\n      name: 'uav-store',\n    }\n  )\n);\n"], "names": ["useUAVStore", "create", "devtools", "subscribeWithSelector", "immer", "set", "get", "uavs", "selectedUAV", "regions", "systemStats", "hibernatePodStatus", "loading", "error", "filter", "pagination", "page", "limit", "sortBy", "sortOrder", "searchQuery", "fetchUAVs", "state", "finalFilter", "search", "uavApi", "getUAVs", "Error", "message", "fetchUAVById", "id", "uav", "getUAVById", "createUAV", "response", "success", "data", "unshift", "toast", "updateUAV", "index", "findIndex", "u", "deleteUAV", "updateUAVStatus", "currentStatus", "status", "fetchRegions", "regionApi", "getRegions", "console", "addRegionToUAV", "uavId", "regionId", "removeRegionFromUAV", "r", "fetchHibernatePodStatus", "hibernatePodApi", "getStatus", "addToHibernatePod", "addUAV", "inHibernatePod", "operationalStatus", "removeFromHibernatePod", "removeUAV", "fetchSystemStats", "stats", "getSystemStatistics", "setFilter", "setPagination", "setSearch<PERSON>uery", "query", "setSelectedUAV", "clearError", "bulkUpdateStatus", "uavIds", "for<PERSON>ach", "length", "bulkDelete", "includes", "updateUAVInStore", "removeUAVFromStore", "updateHibernatePodInStore", "name"], "mappings": ";;;;+BAqEaA;;;eAAAA;;;yBArEU;4BACyB;uBAC1B;wBAW6B;+BAC7B;AAuDf,MAAMA,cAAcC,IAAAA,eAAM,IAC/BC,IAAAA,oBAAQ,EACNC,IAAAA,iCAAqB,EACnBC,IAAAA,YAAK,EAAC,CAACC,KAAKC,MAAS,CAAA;QACnB,gBAAgB;QAChBC,MAAM,EAAE;QACRC,aAAa;QACbC,SAAS,EAAE;QACXC,aAAa;QACbC,oBAAoB;QACpBC,SAAS;QACTC,OAAO;QACPC,QAAQ,CAAC;QACTC,YAAY;YAAEC,MAAM;YAAGC,OAAO;YAAIC,QAAQ;YAAMC,WAAW;QAAO;QAClEC,aAAa;QAEb,aAAa;QACbC,WAAW;YACThB,IAAI,CAACiB;gBACHA,MAAMV,OAAO,GAAG;gBAChBU,MAAMT,KAAK,GAAG;YAChB;YAEA,IAAI;gBACF,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEK,WAAW,EAAE,GAAGd;gBAC5C,MAAMiB,cAAcH,cAAc;oBAAE,GAAGN,MAAM;oBAAEU,QAAQJ;gBAAY,IAAIN;gBACvE,MAAMP,OAAO,MAAMkB,cAAM,CAACC,OAAO,CAACH,aAAaR;gBAE/CV,IAAI,CAACiB;oBACHA,MAAMf,IAAI,GAAGA;oBACbe,MAAMV,OAAO,GAAG;gBAClB;YACF,EAAE,OAAOC,OAAO;gBACdR,IAAI,CAACiB;oBACHA,MAAMT,KAAK,GAAGA,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;oBACvDN,MAAMV,OAAO,GAAG;gBAClB;YACF;QACF;QAEA,kBAAkB;QAClBiB,cAAc,OAAOC;YACnBzB,IAAI,CAACiB;gBACHA,MAAMV,OAAO,GAAG;gBAChBU,MAAMT,KAAK,GAAG;YAChB;YAEA,IAAI;gBACF,MAAMkB,MAAM,MAAMN,cAAM,CAACO,UAAU,CAACF;gBACpCzB,IAAI,CAACiB;oBACHA,MAAMd,WAAW,GAAGuB;oBACpBT,MAAMV,OAAO,GAAG;gBAClB;YACF,EAAE,OAAOC,OAAO;gBACdR,IAAI,CAACiB;oBACHA,MAAMT,KAAK,GAAGA,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;oBACvDN,MAAMV,OAAO,GAAG;gBAClB;YACF;QACF;QAEA,aAAa;QACbqB,WAAW,OAAOF;YAChB1B,IAAI,CAACiB;gBACHA,MAAMV,OAAO,GAAG;gBAChBU,MAAMT,KAAK,GAAG;YAChB;YAEA,IAAI;gBACF,MAAMqB,WAAW,MAAMT,cAAM,CAACQ,SAAS,CAACF;gBACxC,IAAIG,SAASC,OAAO,IAAID,SAASE,IAAI,EAAE;oBACrC/B,IAAI,CAACiB;wBACHA,MAAMf,IAAI,CAAC8B,OAAO,CAACH,SAASE,IAAI;wBAChCd,MAAMV,OAAO,GAAG;oBAClB;oBACA0B,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAIR,MAAMO,SAASN,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOf,OAAO;gBACd,MAAMe,UAAUf,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;gBACzDvB,IAAI,CAACiB;oBACHA,MAAMT,KAAK,GAAGe;oBACdN,MAAMV,OAAO,GAAG;gBAClB;gBACA0B,oBAAK,CAACzB,KAAK,CAACe;gBACZ,OAAO;YACT;QACF;QAEA,aAAa;QACbW,WAAW,OAAOT,IAAYC;YAC5B1B,IAAI,CAACiB;gBACHA,MAAMV,OAAO,GAAG;gBAChBU,MAAMT,KAAK,GAAG;YAChB;YAEA,IAAI;gBACF,MAAMqB,WAAW,MAAMT,cAAM,CAACc,SAAS,CAACT,IAAIC;gBAC5C,IAAIG,SAASC,OAAO,IAAID,SAASE,IAAI,EAAE;oBACrC/B,IAAI,CAACiB;wBACH,MAAMkB,QAAQlB,MAAMf,IAAI,CAACkC,SAAS,CAACC,CAAAA,IAAKA,EAAEZ,EAAE,KAAKA;wBACjD,IAAIU,UAAU,CAAC,GAAG;4BAChBlB,MAAMf,IAAI,CAACiC,MAAM,GAAGN,SAASE,IAAI;wBACnC;wBACA,IAAId,MAAMd,WAAW,EAAEsB,OAAOA,IAAI;4BAChCR,MAAMd,WAAW,GAAG0B,SAASE,IAAI;wBACnC;wBACAd,MAAMV,OAAO,GAAG;oBAClB;oBACA0B,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAIR,MAAMO,SAASN,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOf,OAAO;gBACd,MAAMe,UAAUf,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;gBACzDvB,IAAI,CAACiB;oBACHA,MAAMT,KAAK,GAAGe;oBACdN,MAAMV,OAAO,GAAG;gBAClB;gBACA0B,oBAAK,CAACzB,KAAK,CAACe;gBACZ,OAAO;YACT;QACF;QAEA,aAAa;QACbe,WAAW,OAAOb;YAChBzB,IAAI,CAACiB;gBACHA,MAAMV,OAAO,GAAG;gBAChBU,MAAMT,KAAK,GAAG;YAChB;YAEA,IAAI;gBACF,MAAMqB,WAAW,MAAMT,cAAM,CAACkB,SAAS,CAACb;gBACxC,IAAII,SAASC,OAAO,EAAE;oBACpB9B,IAAI,CAACiB;wBACHA,MAAMf,IAAI,GAAGe,MAAMf,IAAI,CAACO,MAAM,CAAC4B,CAAAA,IAAKA,EAAEZ,EAAE,KAAKA;wBAC7C,IAAIR,MAAMd,WAAW,EAAEsB,OAAOA,IAAI;4BAChCR,MAAMd,WAAW,GAAG;wBACtB;wBACAc,MAAMV,OAAO,GAAG;oBAClB;oBACA0B,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAIR,MAAMO,SAASN,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOf,OAAO;gBACd,MAAMe,UAAUf,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;gBACzDvB,IAAI,CAACiB;oBACHA,MAAMT,KAAK,GAAGe;oBACdN,MAAMV,OAAO,GAAG;gBAClB;gBACA0B,oBAAK,CAACzB,KAAK,CAACe;gBACZ,OAAO;YACT;QACF;QAEA,oBAAoB;QACpBgB,iBAAiB,OAAOd;YACtB,IAAI;gBACF,MAAMI,WAAW,MAAMT,cAAM,CAACmB,eAAe,CAACd;gBAC9C,IAAII,SAASC,OAAO,EAAE;oBACpB9B,IAAI,CAACiB;wBACH,MAAMkB,QAAQlB,MAAMf,IAAI,CAACkC,SAAS,CAACC,CAAAA,IAAKA,EAAEZ,EAAE,KAAKA;wBACjD,IAAIU,UAAU,CAAC,GAAG;4BAChB,gBAAgB;4BAChB,MAAMK,gBAAgBvB,MAAMf,IAAI,CAACiC,MAAM,CAACM,MAAM;4BAC9CxB,MAAMf,IAAI,CAACiC,MAAM,CAACM,MAAM,GAAGD,kBAAkB,eAAe,iBAAiB;wBAC/E;oBACF;oBACAP,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAIR,MAAMO,SAASN,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOf,OAAO;gBACd,MAAMe,UAAUf,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;gBACzDU,oBAAK,CAACzB,KAAK,CAACe;gBACZ,OAAO;YACT;QACF;QAEA,gBAAgB;QAChBmB,cAAc;YACZ,IAAI;gBACF,MAAMtC,UAAU,MAAMuC,iBAAS,CAACC,UAAU;gBAC1C5C,IAAI,CAACiB;oBACHA,MAAMb,OAAO,GAAGA;gBAClB;YACF,EAAE,OAAOI,OAAO;gBACdqC,QAAQrC,KAAK,CAAC,4BAA4BA;YAC5C;QACF;QAEA,oBAAoB;QACpBsC,gBAAgB,OAAOC,OAAeC;YACpC,IAAI;gBACF,MAAMnB,WAAW,MAAMT,cAAM,CAAC0B,cAAc,CAACC,OAAOC;gBACpD,IAAInB,SAASC,OAAO,IAAID,SAASE,IAAI,EAAE;oBACrC/B,IAAI,CAACiB;wBACH,MAAMkB,QAAQlB,MAAMf,IAAI,CAACkC,SAAS,CAACC,CAAAA,IAAKA,EAAEZ,EAAE,KAAKsB;wBACjD,IAAIZ,UAAU,CAAC,GAAG;4BAChBlB,MAAMf,IAAI,CAACiC,MAAM,GAAGN,SAASE,IAAI;wBACnC;oBACF;oBACAE,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAIR,MAAMO,SAASN,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOf,OAAO;gBACd,MAAMe,UAAUf,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;gBACzDU,oBAAK,CAACzB,KAAK,CAACe;gBACZ,OAAO;YACT;QACF;QAEA,yBAAyB;QACzB0B,qBAAqB,OAAOF,OAAeC;YACzC,IAAI;gBACF,MAAMnB,WAAW,MAAMT,cAAM,CAAC6B,mBAAmB,CAACF,OAAOC;gBACzD,IAAInB,SAASC,OAAO,EAAE;oBACpB9B,IAAI,CAACiB;wBACH,MAAMkB,QAAQlB,MAAMf,IAAI,CAACkC,SAAS,CAACC,CAAAA,IAAKA,EAAEZ,EAAE,KAAKsB;wBACjD,IAAIZ,UAAU,CAAC,GAAG;4BAChBlB,MAAMf,IAAI,CAACiC,MAAM,CAAC/B,OAAO,GAAGa,MAAMf,IAAI,CAACiC,MAAM,CAAC/B,OAAO,CAACK,MAAM,CAACyC,CAAAA,IAAKA,EAAEzB,EAAE,KAAKuB;wBAC7E;oBACF;oBACAf,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAIR,MAAMO,SAASN,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOf,OAAO;gBACd,MAAMe,UAAUf,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;gBACzDU,oBAAK,CAACzB,KAAK,CAACe;gBACZ,OAAO;YACT;QACF;QAEA,6BAA6B;QAC7B4B,yBAAyB;YACvB,IAAI;gBACF,MAAMV,SAAS,MAAMW,uBAAe,CAACC,SAAS;gBAC9CrD,IAAI,CAACiB;oBACHA,MAAMX,kBAAkB,GAAGmC;gBAC7B;YACF,EAAE,OAAOjC,OAAO;gBACdqC,QAAQrC,KAAK,CAAC,yCAAyCA;YACzD;QACF;QAEA,uBAAuB;QACvB8C,mBAAmB,OAAOP;YACxB,IAAI;gBACF,MAAMlB,WAAW,MAAMuB,uBAAe,CAACG,MAAM,CAACR;gBAC9C,IAAIlB,SAASC,OAAO,EAAE;oBACpB9B,IAAI,CAACiB;wBACH,MAAMkB,QAAQlB,MAAMf,IAAI,CAACkC,SAAS,CAACC,CAAAA,IAAKA,EAAEZ,EAAE,KAAKsB;wBACjD,IAAIZ,UAAU,CAAC,GAAG;4BAChBlB,MAAMf,IAAI,CAACiC,MAAM,CAACqB,cAAc,GAAG;4BACnCvC,MAAMf,IAAI,CAACiC,MAAM,CAACsB,iBAAiB,GAAG;wBACxC;oBACF;oBACA,+BAA+B;oBAC/BxD,MAAMkD,uBAAuB;oBAC7BlB,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAIR,MAAMO,SAASN,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOf,OAAO;gBACd,MAAMe,UAAUf,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;gBACzDU,oBAAK,CAACzB,KAAK,CAACe;gBACZ,OAAO;YACT;QACF;QAEA,4BAA4B;QAC5BmC,wBAAwB,OAAOX;YAC7B,IAAI;gBACF,MAAMlB,WAAW,MAAMuB,uBAAe,CAACO,SAAS,CAACZ;gBACjD,IAAIlB,SAASC,OAAO,EAAE;oBACpB9B,IAAI,CAACiB;wBACH,MAAMkB,QAAQlB,MAAMf,IAAI,CAACkC,SAAS,CAACC,CAAAA,IAAKA,EAAEZ,EAAE,KAAKsB;wBACjD,IAAIZ,UAAU,CAAC,GAAG;4BAChBlB,MAAMf,IAAI,CAACiC,MAAM,CAACqB,cAAc,GAAG;4BACnCvC,MAAMf,IAAI,CAACiC,MAAM,CAACsB,iBAAiB,GAAG;wBACxC;oBACF;oBACA,+BAA+B;oBAC/BxD,MAAMkD,uBAAuB;oBAC7BlB,oBAAK,CAACH,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAIR,MAAMO,SAASN,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOf,OAAO;gBACd,MAAMe,UAAUf,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;gBACzDU,oBAAK,CAACzB,KAAK,CAACe;gBACZ,OAAO;YACT;QACF;QAEA,0BAA0B;QAC1BqC,kBAAkB;YAChB,IAAI;gBACF,MAAMC,QAAQ,MAAMzC,cAAM,CAAC0C,mBAAmB;gBAC9C9D,IAAI,CAACiB;oBACHA,MAAMZ,WAAW,GAAGwD;gBACtB;YACF,EAAE,OAAOrD,OAAO;gBACdqC,QAAQrC,KAAK,CAAC,sCAAsCA;YACtD;QACF;QAEA,aAAa;QACbuD,WAAW,CAACtD;YACVT,IAAI,CAACiB;gBACHA,MAAMR,MAAM,GAAG;oBAAE,GAAGQ,MAAMR,MAAM;oBAAE,GAAGA,MAAM;gBAAC;YAC9C;QACF;QAEAuD,eAAe,CAACtD;YACdV,IAAI,CAACiB;gBACHA,MAAMP,UAAU,GAAG;oBAAE,GAAGO,MAAMP,UAAU;oBAAE,GAAGA,UAAU;gBAAC;YAC1D;QACF;QAEAuD,gBAAgB,CAACC;YACflE,IAAI,CAACiB;gBACHA,MAAMF,WAAW,GAAGmD;YACtB;QACF;QAEAC,gBAAgB,CAACzC;YACf1B,IAAI,CAACiB;gBACHA,MAAMd,WAAW,GAAGuB;YACtB;QACF;QAEA0C,YAAY;YACVpE,IAAI,CAACiB;gBACHA,MAAMT,KAAK,GAAG;YAChB;QACF;QAEA,eAAe;QACf6D,kBAAkB,OAAOC,QAAkB7B;YACzC,IAAI;gBACF,MAAMZ,WAAW,MAAMT,cAAM,CAACiD,gBAAgB,CAACC,QAAQ7B;gBACvD,IAAIZ,SAASC,OAAO,EAAE;oBACpB9B,IAAI,CAACiB;wBACHqD,OAAOC,OAAO,CAAC9C,CAAAA;4BACb,MAAMU,QAAQlB,MAAMf,IAAI,CAACkC,SAAS,CAACC,CAAAA,IAAKA,EAAEZ,EAAE,KAAKA;4BACjD,IAAIU,UAAU,CAAC,GAAG;gCAChBlB,MAAMf,IAAI,CAACiC,MAAM,CAACM,MAAM,GAAGA;4BAC7B;wBACF;oBACF;oBACAR,oBAAK,CAACH,OAAO,CAAC,CAAC,EAAEwC,OAAOE,MAAM,CAAC,0BAA0B,CAAC;oBAC1D,OAAO;gBACT,OAAO;oBACL,MAAM,IAAIlD,MAAMO,SAASN,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOf,OAAO;gBACd,MAAMe,UAAUf,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;gBACzDU,oBAAK,CAACzB,KAAK,CAACe;gBACZ,OAAO;YACT;QACF;QAEAkD,YAAY,OAAOH;YACjB,IAAI;gBACF,MAAMzC,WAAW,MAAMT,cAAM,CAACqD,UAAU,CAACH;gBACzC,IAAIzC,SAASC,OAAO,EAAE;oBACpB9B,IAAI,CAACiB;wBACHA,MAAMf,IAAI,GAAGe,MAAMf,IAAI,CAACO,MAAM,CAAC4B,CAAAA,IAAK,CAACiC,OAAOI,QAAQ,CAACrC,EAAEZ,EAAE;oBAC3D;oBACAQ,oBAAK,CAACH,OAAO,CAAC,CAAC,EAAEwC,OAAOE,MAAM,CAAC,0BAA0B,CAAC;oBAC1D,OAAO;gBACT,OAAO;oBACL,MAAM,IAAIlD,MAAMO,SAASN,OAAO,IAAI;gBACtC;YACF,EAAE,OAAOf,OAAO;gBACd,MAAMe,UAAUf,iBAAiBc,QAAQd,MAAMe,OAAO,GAAG;gBACzDU,oBAAK,CAACzB,KAAK,CAACe;gBACZ,OAAO;YACT;QACF;QAEA,oBAAoB;QACpBoD,kBAAkB,CAACjD;YACjB1B,IAAI,CAACiB;gBACH,MAAMkB,QAAQlB,MAAMf,IAAI,CAACkC,SAAS,CAACC,CAAAA,IAAKA,EAAEZ,EAAE,KAAKC,IAAID,EAAE;gBACvD,IAAIU,UAAU,CAAC,GAAG;oBAChBlB,MAAMf,IAAI,CAACiC,MAAM,GAAGT;gBACtB,OAAO;oBACLT,MAAMf,IAAI,CAAC8B,OAAO,CAACN;gBACrB;gBACA,IAAIT,MAAMd,WAAW,EAAEsB,OAAOC,IAAID,EAAE,EAAE;oBACpCR,MAAMd,WAAW,GAAGuB;gBACtB;YACF;QACF;QAEAkD,oBAAoB,CAAC7B;YACnB/C,IAAI,CAACiB;gBACHA,MAAMf,IAAI,GAAGe,MAAMf,IAAI,CAACO,MAAM,CAAC4B,CAAAA,IAAKA,EAAEZ,EAAE,KAAKsB;gBAC7C,IAAI9B,MAAMd,WAAW,EAAEsB,OAAOsB,OAAO;oBACnC9B,MAAMd,WAAW,GAAG;gBACtB;YACF;QACF;QAEA0E,2BAA2B,CAACpC;YAC1BzC,IAAI,CAACiB;gBACHA,MAAMX,kBAAkB,GAAGmC;YAC7B;QACF;IACF,CAAA,KAEF;IACEqC,MAAM;AACR"}