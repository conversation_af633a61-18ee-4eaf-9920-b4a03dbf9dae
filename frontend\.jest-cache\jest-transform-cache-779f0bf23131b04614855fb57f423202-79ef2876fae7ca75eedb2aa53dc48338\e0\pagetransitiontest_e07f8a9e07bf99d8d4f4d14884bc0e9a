78bf67eadd3ef7533b2c8c64c4b0f5d3
"use strict";
// Mock Next.js navigation
jest.mock("next/navigation", ()=>({
        usePathname: jest.fn()
    }));
// Mock animations
jest.mock("@/lib/animations", ()=>({
        pageVariants: {
            initial: {
                opacity: 0,
                y: 20,
                scale: 0.98
            },
            animate: {
                opacity: 1,
                y: 0,
                scale: 1
            },
            exit: {
                opacity: 0,
                y: -20,
                scale: 0.98
            }
        },
        getAnimationVariants: jest.fn((variants)=>variants)
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _pagetransition = require("../page-transition");
const _navigation = require("next/navigation");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock framer-motion
(0, _testutils.mockFramerMotion)();
describe("PageTransition Component", ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
        _navigation.usePathname.mockReturnValue("/dashboard");
    });
    it("renders children correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "page-content",
                children: "Page Content"
            })
        }));
        expect(_testutils.screen.getByTestId("page-content")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Page Content")).toBeInTheDocument();
    });
    it("wraps content in motion div", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Content"
            })
        }));
        // The motion.div should be present (mocked as regular div)
        const wrapper = _testutils.screen.getByText("Content").parentElement;
        expect(wrapper).toHaveClass("w-full", "h-full");
    });
    it("uses pathname as key for animations", ()=>{
        _navigation.usePathname.mockReturnValue("/dashboard");
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Dashboard Content"
            })
        }));
        expect(_testutils.screen.getByText("Dashboard Content")).toBeInTheDocument();
        _navigation.usePathname.mockReturnValue("/uavs");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "UAV Content"
            })
        }));
        expect(_testutils.screen.getByText("UAV Content")).toBeInTheDocument();
    });
    it("applies correct animation variants", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Animated Content"
            })
        }));
        // The motion div should have the correct props (tested through mocked framer-motion)
        const content = _testutils.screen.getByText("Animated Content");
        expect(content).toBeInTheDocument();
    });
    it("handles multiple children", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)(_pagetransition.PageTransition, {
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "First Child"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Second Child"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                    children: "Third Child"
                })
            ]
        }));
        expect(_testutils.screen.getByText("First Child")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Second Child")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Third Child")).toBeInTheDocument();
    });
    it("handles complex nested content", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("header", {
                        children: "Page Header"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("main", {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("section", {
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                                    children: "Page Title"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    children: "Page description"
                                })
                            ]
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("footer", {
                        children: "Page Footer"
                    })
                ]
            })
        }));
        expect(_testutils.screen.getByText("Page Header")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Page Title")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Page description")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Page Footer")).toBeInTheDocument();
    });
    it("respects prefers-reduced-motion", ()=>{
        (0, _testutils.mockPrefersReducedMotion)(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Reduced Motion Content"
            })
        }));
        expect(_testutils.screen.getByText("Reduced Motion Content")).toBeInTheDocument();
    // Animation should be disabled when prefers-reduced-motion is set
    });
    it("handles pathname changes correctly", ()=>{
        const pathnames = [
            "/dashboard",
            "/uavs",
            "/map",
            "/hibernate-pod"
        ];
        pathnames.forEach((pathname, index)=>{
            _navigation.usePathname.mockReturnValue(pathname);
            const { unmount } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        "Content for ",
                        pathname
                    ]
                })
            }));
            expect(_testutils.screen.getByText(`Content for ${pathname}`)).toBeInTheDocument();
            unmount();
        });
    });
    it("maintains full width and height", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "content",
                children: "Content"
            })
        }));
        const wrapper = _testutils.screen.getByTestId("content").parentElement;
        expect(wrapper).toHaveClass("w-full", "h-full");
    });
    it("handles empty children gracefully", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: null
        }));
        // Should not throw error and should render the wrapper
        const wrapper = document.querySelector(".w-full.h-full");
        expect(wrapper).toBeInTheDocument();
    });
    it("handles string children", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: "Simple text content"
        }));
        expect(_testutils.screen.getByText("Simple text content")).toBeInTheDocument();
    });
    it("handles React fragments", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        children: "Fragment Child 1"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        children: "Fragment Child 2"
                    })
                ]
            })
        }));
        expect(_testutils.screen.getByText("Fragment Child 1")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Fragment Child 2")).toBeInTheDocument();
    });
    it("preserves component state during transitions", ()=>{
        const TestComponent = ()=>{
            const [count, setCount] = _react.default.useState(0);
            return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                        children: [
                            "Count: ",
                            count
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        onClick: ()=>setCount((c)=>c + 1),
                        children: "Increment"
                    })
                ]
            });
        };
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(TestComponent, {})
        }));
        const button = _testutils.screen.getByText("Increment");
        const countDisplay = _testutils.screen.getByText("Count: 0");
        expect(countDisplay).toBeInTheDocument();
        // Click button to change state
        button.click();
        expect(_testutils.screen.getByText("Count: 1")).toBeInTheDocument();
    });
    it("handles conditional rendering", ()=>{
        const ConditionalComponent = ({ show })=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
                children: show ? /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Visible Content"
                }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Hidden Content"
                })
            });
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(ConditionalComponent, {
            show: true
        }));
        expect(_testutils.screen.getByText("Visible Content")).toBeInTheDocument();
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(ConditionalComponent, {
            show: false
        }));
        expect(_testutils.screen.getByText("Hidden Content")).toBeInTheDocument();
    });
    it("works with different pathname formats", ()=>{
        const pathnames = [
            "/",
            "/dashboard",
            "/uavs/123",
            "/map?filter=active",
            "/hibernate-pod#section1"
        ];
        pathnames.forEach((pathname)=>{
            _navigation.usePathname.mockReturnValue(pathname);
            const { unmount } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        "Content for ",
                        pathname
                    ]
                })
            }));
            expect(_testutils.screen.getByText(`Content for ${pathname}`)).toBeInTheDocument();
            unmount();
        });
    });
    it("handles rapid pathname changes", ()=>{
        _navigation.usePathname.mockReturnValue("/dashboard");
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Dashboard"
            })
        }));
        _navigation.usePathname.mockReturnValue("/uavs");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "UAVs"
            })
        }));
        _navigation.usePathname.mockReturnValue("/map");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Map"
            })
        }));
        expect(_testutils.screen.getByText("Map")).toBeInTheDocument();
    });
    it("maintains accessibility during transitions", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_pagetransition.PageTransition, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                role: "main",
                "aria-label": "Page content",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                        children: "Accessible Page"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        children: "Accessible Button"
                    })
                ]
            })
        }));
        expect(_testutils.screen.getByRole("main")).toBeInTheDocument();
        expect(_testutils.screen.getByRole("button")).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText("Page content")).toBeInTheDocument();
    });
});

//# sourceMappingURL=data:application/json;base64,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