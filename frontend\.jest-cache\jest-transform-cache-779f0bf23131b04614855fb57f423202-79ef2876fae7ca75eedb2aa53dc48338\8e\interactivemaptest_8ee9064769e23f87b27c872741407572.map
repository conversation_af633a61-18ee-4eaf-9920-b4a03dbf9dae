{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\features\\map\\__tests__\\interactive-map.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport InteractiveMap from '../interactive-map'\nimport { createMockUAV, createMockDockingStation } from '@/lib/test-utils'\nimport { UAV, DockingStation } from '@/types/uav'\n\n// Mock react-leaflet components (already mocked in jest.setup.js)\n// Mock framer-motion\njest.mock('framer-motion', () => ({\n  motion: {\n    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,\n  },\n  AnimatePresence: ({ children }: any) => children,\n}))\n\n// Mock the animated map components\njest.mock('../animated-map-components', () => ({\n  AnimatedUAVMarker: ({ uav, onSelect }: any) => (\n    <div \n      data-testid={`uav-marker-${uav.id}`}\n      onClick={() => onSelect(uav)}\n    >\n      UAV Marker: {uav.rfidTag}\n    </div>\n  ),\n  AnimatedGeofence: ({ name, center, radius, color, type }: any) => (\n    <div data-testid={`geofence-${name}`}>\n      Geofence: {name}\n    </div>\n  ),\n  AnimatedFlightPath: ({ path }: any) => (\n    <div data-testid=\"flight-path\">\n      Flight Path\n    </div>\n  ),\n  AnimatedDockingStation: ({ name, position, status, capacity, occupied }: any) => (\n    <div\n      data-testid={`docking-station-${name}`}\n    >\n      Docking Station: {name}\n    </div>\n  ),\n}))\n\ndescribe('InteractiveMap Component', () => {\n  const mockUAVs: UAV[] = [\n    createMockUAV({\n      id: 1,\n      rfidTag: 'UAV-001',\n      status: 'AUTHORIZED',\n      operationalStatus: 'ACTIVE',\n      location: { latitude: 40.7128, longitude: -74.0060 },\n    }),\n    createMockUAV({\n      id: 2,\n      rfidTag: 'UAV-002',\n      status: 'AUTHORIZED',\n      operationalStatus: 'READY',\n      location: { latitude: 40.7589, longitude: -73.9851 },\n    }),\n  ]\n\n  const mockDockingStations: DockingStation[] = [\n    createMockDockingStation({\n      id: 1,\n      name: 'Station Alpha',\n      location: { latitude: 40.7505, longitude: -73.9934 },\n      status: 'AVAILABLE',\n    }),\n  ]\n\n  const mockRegions = [\n    {\n      id: 1,\n      name: 'Zone A',\n      description: 'Authorized zone A',\n      coordinates: [\n        { latitude: 40.7000, longitude: -74.0200 },\n        { latitude: 40.7200, longitude: -74.0200 },\n        { latitude: 40.7200, longitude: -73.9800 },\n        { latitude: 40.7000, longitude: -73.9800 },\n      ],\n      isActive: true,\n    },\n  ]\n\n  const defaultProps = {\n    uavs: mockUAVs,\n    selectedUAV: null,\n    center: [40.7128, -74.0060] as [number, number],\n    zoom: 12,\n    layers: {\n      uavs: true,\n      geofences: true,\n      dockingStations: true,\n      flightPaths: true,\n      weather: false,\n    },\n    onUAVSelect: jest.fn(),\n  }\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n  })\n\n  it('renders correctly', () => {\n    render(<InteractiveMap {...defaultProps} />)\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    expect(screen.getByTestId('tile-layer')).toBeInTheDocument()\n  })\n\n  it('renders UAV markers', () => {\n    render(<InteractiveMap {...defaultProps} />)\n    \n    expect(screen.getByTestId('uav-marker-1')).toBeInTheDocument()\n    expect(screen.getByTestId('uav-marker-2')).toBeInTheDocument()\n    expect(screen.getByText('UAV Marker: UAV-001')).toBeInTheDocument()\n    expect(screen.getByText('UAV Marker: UAV-002')).toBeInTheDocument()\n  })\n\n  it('renders docking station markers', () => {\n    render(<InteractiveMap {...defaultProps} />)\n\n    expect(screen.getByTestId('docking-station-Station Alpha')).toBeInTheDocument()\n    expect(screen.getByText('Docking Station: Station Alpha')).toBeInTheDocument()\n  })\n\n  it('renders geofences for regions', () => {\n    render(<InteractiveMap {...defaultProps} />)\n\n    expect(screen.getByTestId('geofence-Restricted Zone A')).toBeInTheDocument()\n    expect(screen.getByText('Geofence: Restricted Zone A')).toBeInTheDocument()\n  })\n\n  it('handles UAV selection', () => {\n    const onUAVSelect = jest.fn()\n    render(<InteractiveMap {...defaultProps} onUAVSelect={onUAVSelect} />)\n    \n    const uavMarker = screen.getByTestId('uav-marker-1')\n    fireEvent.click(uavMarker)\n    \n    expect(onUAVSelect).toHaveBeenCalledWith(mockUAVs[0])\n  })\n\n  it('handles docking station selection', () => {\n    const onStationSelect = jest.fn()\n    render(<InteractiveMap {...defaultProps} onStationSelect={onStationSelect} />)\n\n    const stationMarker = screen.getByTestId('docking-station-Station Alpha')\n    fireEvent.click(stationMarker)\n\n    // Note: The mock component doesn't have click handler, so this test needs to be adjusted\n    expect(screen.getByText('Docking Station: Station Alpha')).toBeInTheDocument()\n  })\n\n  it('highlights selected UAV', () => {\n    const selectedUAV = mockUAVs[0]\n    render(<InteractiveMap {...defaultProps} selectedUAV={selectedUAV} />)\n    \n    const selectedMarker = screen.getByTestId('uav-marker-1')\n    expect(selectedMarker).toBeInTheDocument()\n    // The selected state would be passed to the AnimatedUAVMarker component\n  })\n\n  it('shows flight paths when enabled', () => {\n    const flightPaths = [\n      {\n        id: 1,\n        uavId: 1,\n        coordinates: [\n          { latitude: 40.7128, longitude: -74.0060 },\n          { latitude: 40.7589, longitude: -73.9851 },\n        ],\n        timestamp: new Date().toISOString(),\n      },\n    ]\n    \n    render(\n      <InteractiveMap \n        {...defaultProps} \n        flightPaths={flightPaths}\n        showFlightPaths={true}\n      />\n    )\n    \n    expect(screen.getByTestId('flight-path')).toBeInTheDocument()\n  })\n\n  it('filters UAVs by status', () => {\n    const filteredProps = {\n      ...defaultProps,\n      uavs: mockUAVs.filter(uav => uav.operationalStatus === 'ACTIVE'),\n    }\n    \n    render(<InteractiveMap {...filteredProps} />)\n    \n    expect(screen.getByTestId('uav-marker-1')).toBeInTheDocument()\n    expect(screen.queryByTestId('uav-marker-2')).not.toBeInTheDocument()\n  })\n\n  it('updates map center when prop changes', () => {\n    const { rerender } = render(<InteractiveMap {...defaultProps} />)\n    \n    const newCenter = { latitude: 41.8781, longitude: -87.6298 }\n    rerender(<InteractiveMap {...defaultProps} center={newCenter} />)\n    \n    // Map center update would be handled by the MapContainer component\n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n  })\n\n  it('updates zoom level when prop changes', () => {\n    const { rerender } = render(<InteractiveMap {...defaultProps} />)\n    \n    rerender(<InteractiveMap {...defaultProps} zoom={15} />)\n    \n    // Zoom update would be handled by the MapContainer component\n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n  })\n\n  it('handles empty UAV list', () => {\n    render(<InteractiveMap {...defaultProps} uavs={[]} />)\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    expect(screen.queryByTestId('uav-marker-1')).not.toBeInTheDocument()\n    expect(screen.queryByTestId('uav-marker-2')).not.toBeInTheDocument()\n  })\n\n  it('handles empty docking stations list', () => {\n    render(<InteractiveMap {...defaultProps} dockingStations={[]} />)\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    expect(screen.queryByTestId('docking-station-1')).not.toBeInTheDocument()\n  })\n\n  it('handles empty regions list', () => {\n    render(<InteractiveMap {...defaultProps} regions={[]} />)\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    expect(screen.queryByTestId('geofence-1')).not.toBeInTheDocument()\n  })\n\n  it('shows loading state', () => {\n    render(<InteractiveMap {...defaultProps} loading={true} />)\n    \n    expect(screen.getByTestId('map-loading')).toBeInTheDocument()\n  })\n\n  it('shows error state', () => {\n    const errorMessage = 'Failed to load map data'\n    render(<InteractiveMap {...defaultProps} error={errorMessage} />)\n    \n    expect(screen.getByText(errorMessage)).toBeInTheDocument()\n    expect(screen.getByRole('alert')).toBeInTheDocument()\n  })\n\n  it('supports different map layers', () => {\n    render(<InteractiveMap {...defaultProps} mapLayer=\"satellite\" />)\n    \n    // Different tile layer would be rendered\n    expect(screen.getByTestId('tile-layer')).toBeInTheDocument()\n  })\n\n  it('handles real-time updates', async () => {\n    const { rerender } = render(<InteractiveMap {...defaultProps} />)\n    \n    const updatedUAVs = [\n      ...mockUAVs,\n      createMockUAV({\n        id: 3,\n        rfidTag: 'UAV-003',\n        location: { latitude: 40.7300, longitude: -74.0000 },\n      }),\n    ]\n    \n    rerender(<InteractiveMap {...defaultProps} uavs={updatedUAVs} />)\n    \n    await waitFor(() => {\n      expect(screen.getByTestId('uav-marker-3')).toBeInTheDocument()\n    })\n  })\n\n  it('handles UAV location updates', () => {\n    const updatedUAVs = mockUAVs.map(uav => \n      uav.id === 1 \n        ? { ...uav, location: { latitude: 40.7200, longitude: -74.0100 } }\n        : uav\n    )\n    \n    const { rerender } = render(<InteractiveMap {...defaultProps} />)\n    rerender(<InteractiveMap {...defaultProps} uavs={updatedUAVs} />)\n    \n    // Updated location would be reflected in the marker position\n    expect(screen.getByTestId('uav-marker-1')).toBeInTheDocument()\n  })\n\n  it('supports clustering for many UAVs', () => {\n    const manyUAVs = Array.from({ length: 50 }, (_, i) => \n      createMockUAV({\n        id: i + 1,\n        rfidTag: `UAV-${(i + 1).toString().padStart(3, '0')}`,\n        location: { \n          latitude: 40.7128 + (Math.random() - 0.5) * 0.1, \n          longitude: -74.0060 + (Math.random() - 0.5) * 0.1 \n        },\n      })\n    )\n    \n    render(<InteractiveMap {...defaultProps} uavs={manyUAVs} enableClustering={true} />)\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    // Clustering would be handled by the map library\n  })\n\n  it('handles map interaction events', () => {\n    const onMapClick = jest.fn()\n    render(<InteractiveMap {...defaultProps} onMapClick={onMapClick} />)\n    \n    const mapContainer = screen.getByTestId('map-container')\n    fireEvent.click(mapContainer)\n    \n    // Map click would be handled by the MapContainer component\n    expect(mapContainer).toBeInTheDocument()\n  })\n\n  it('supports custom map controls', () => {\n    render(\n      <InteractiveMap \n        {...defaultProps} \n        showZoomControl={true}\n        showScaleControl={true}\n        showFullscreenControl={true}\n      />\n    )\n    \n    expect(screen.getByTestId('map-container')).toBeInTheDocument()\n    // Custom controls would be rendered as part of the map\n  })\n\n  it('handles responsive design', () => {\n    render(<InteractiveMap {...defaultProps} className=\"h-96 w-full\" />)\n    \n    const mapContainer = screen.getByTestId('map-container')\n    expect(mapContainer.parentElement).toHaveClass('h-96', 'w-full')\n  })\n\n  it('supports accessibility features', () => {\n    render(\n      <InteractiveMap \n        {...defaultProps} \n        aria-label=\"UAV tracking map\"\n        role=\"application\"\n      />\n    )\n    \n    const mapContainer = screen.getByTestId('map-container')\n    expect(mapContainer).toHaveAttribute('aria-label', 'UAV tracking map')\n    expect(mapContainer).toHaveAttribute('role', 'application')\n  })\n})\n"], "names": ["jest", "mock", "motion", "div", "children", "props", "AnimatePresence", "AnimatedUAVMarker", "uav", "onSelect", "data-testid", "id", "onClick", "rfidTag", "AnimatedGeofence", "name", "center", "radius", "color", "type", "AnimatedFlightPath", "path", "AnimatedDockingStation", "position", "status", "capacity", "occupied", "describe", "mockUAVs", "createMockUAV", "operationalStatus", "location", "latitude", "longitude", "mockDockingStations", "createMockDockingStation", "mockRegions", "description", "coordinates", "isActive", "defaultProps", "uavs", "selectedUAV", "zoom", "layers", "geofences", "dockingStations", "flightPaths", "weather", "onUAVSelect", "fn", "beforeEach", "clearAllMocks", "it", "render", "InteractiveMap", "expect", "screen", "getByTestId", "toBeInTheDocument", "getByText", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireEvent", "click", "toHaveBeenCalledWith", "onStationSelect", "stationMarker", "<PERSON><PERSON><PERSON><PERSON>", "uavId", "timestamp", "Date", "toISOString", "showFlightPaths", "filteredProps", "filter", "queryByTestId", "not", "rerender", "newCenter", "regions", "loading", "errorMessage", "error", "getByRole", "map<PERSON>ayer", "updatedUAVs", "waitFor", "map", "manyUAVs", "Array", "from", "length", "_", "i", "toString", "padStart", "Math", "random", "enableClustering", "onMapClick", "mapContainer", "showZoomControl", "showScaleControl", "showFullscreenControl", "className", "parentElement", "toHaveClass", "aria-label", "role", "toHaveAttribute"], "mappings": ";AAMA,kEAAkE;AAClE,qBAAqB;AACrBA,KAAKC,IAAI,CAAC,iBAAiB,IAAO,CAAA;QAChCC,QAAQ;YACNC,KAAK,CAAC,EAAEC,QAAQ,EAAE,GAAGC,OAAY,iBAAK,qBAACF;oBAAK,GAAGE,KAAK;8BAAGD;;QACzD;QACAE,iBAAiB,CAAC,EAAEF,QAAQ,EAAO,GAAKA;IAC1C,CAAA;AAEA,mCAAmC;AACnCJ,KAAKC,IAAI,CAAC,8BAA8B,IAAO,CAAA;QAC7CM,mBAAmB,CAAC,EAAEC,GAAG,EAAEC,QAAQ,EAAO,iBACxC,sBAACN;gBACCO,eAAa,CAAC,WAAW,EAAEF,IAAIG,EAAE,CAAC,CAAC;gBACnCC,SAAS,IAAMH,SAASD;;oBACzB;oBACcA,IAAIK,OAAO;;;QAG5BC,kBAAkB,CAAC,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAO,iBAC3D,sBAAChB;gBAAIO,eAAa,CAAC,SAAS,EAAEK,KAAK,CAAC;;oBAAE;oBACzBA;;;QAGfK,oBAAoB,CAAC,EAAEC,IAAI,EAAO,iBAChC,qBAAClB;gBAAIO,eAAY;0BAAc;;QAIjCY,wBAAwB,CAAC,EAAEP,IAAI,EAAEQ,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAO,iBAC1E,sBAACvB;gBACCO,eAAa,CAAC,gBAAgB,EAAEK,KAAK,CAAC;;oBACvC;oBACmBA;;;IAGxB,CAAA;;;;;8DA1CkB;2BACiC;uEACxB;;;;;;AA0C3BY,SAAS,4BAA4B;IACnC,MAAMC,WAAkB;QACtBC,IAAAA,wBAAa,EAAC;YACZlB,IAAI;YACJE,SAAS;YACTW,QAAQ;YACRM,mBAAmB;YACnBC,UAAU;gBAAEC,UAAU;gBAASC,WAAW,CAAC;YAAQ;QACrD;QACAJ,IAAAA,wBAAa,EAAC;YACZlB,IAAI;YACJE,SAAS;YACTW,QAAQ;YACRM,mBAAmB;YACnBC,UAAU;gBAAEC,UAAU;gBAASC,WAAW,CAAC;YAAQ;QACrD;KACD;IAED,MAAMC,sBAAwC;QAC5CC,IAAAA,mCAAwB,EAAC;YACvBxB,IAAI;YACJI,MAAM;YACNgB,UAAU;gBAAEC,UAAU;gBAASC,WAAW,CAAC;YAAQ;YACnDT,QAAQ;QACV;KACD;IAED,MAAMY,cAAc;QAClB;YACEzB,IAAI;YACJI,MAAM;YACNsB,aAAa;YACbC,aAAa;gBACX;oBAAEN,UAAU;oBAASC,WAAW,CAAC;gBAAQ;gBACzC;oBAAED,UAAU;oBAASC,WAAW,CAAC;gBAAQ;gBACzC;oBAAED,UAAU;oBAASC,WAAW,CAAC;gBAAQ;gBACzC;oBAAED,UAAU;oBAASC,WAAW,CAAC;gBAAQ;aAC1C;YACDM,UAAU;QACZ;KACD;IAED,MAAMC,eAAe;QACnBC,MAAMb;QACNc,aAAa;QACb1B,QAAQ;YAAC;YAAS,CAAC;SAAQ;QAC3B2B,MAAM;QACNC,QAAQ;YACNH,MAAM;YACNI,WAAW;YACXC,iBAAiB;YACjBC,aAAa;YACbC,SAAS;QACX;QACAC,aAAajD,KAAKkD,EAAE;IACtB;IAEAC,WAAW;QACTnD,KAAKoD,aAAa;IACpB;IAEAC,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;;QAEvCgB,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;QAC7DH,OAAOC,iBAAM,CAACC,WAAW,CAAC,eAAeC,iBAAiB;IAC5D;IAEAN,GAAG,uBAAuB;QACxBC,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;;QAEvCgB,OAAOC,iBAAM,CAACC,WAAW,CAAC,iBAAiBC,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACC,WAAW,CAAC,iBAAiBC,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACG,SAAS,CAAC,wBAAwBD,iBAAiB;QACjEH,OAAOC,iBAAM,CAACG,SAAS,CAAC,wBAAwBD,iBAAiB;IACnE;IAEAN,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;;QAEvCgB,OAAOC,iBAAM,CAACC,WAAW,CAAC,kCAAkCC,iBAAiB;QAC7EH,OAAOC,iBAAM,CAACG,SAAS,CAAC,mCAAmCD,iBAAiB;IAC9E;IAEAN,GAAG,iCAAiC;QAClCC,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;;QAEvCgB,OAAOC,iBAAM,CAACC,WAAW,CAAC,+BAA+BC,iBAAiB;QAC1EH,OAAOC,iBAAM,CAACG,SAAS,CAAC,gCAAgCD,iBAAiB;IAC3E;IAEAN,GAAG,yBAAyB;QAC1B,MAAMJ,cAAcjD,KAAKkD,EAAE;QAC3BI,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAES,aAAaA;;QAEtD,MAAMY,YAAYJ,iBAAM,CAACC,WAAW,CAAC;QACrCI,oBAAS,CAACC,KAAK,CAACF;QAEhBL,OAAOP,aAAae,oBAAoB,CAACpC,QAAQ,CAAC,EAAE;IACtD;IAEAyB,GAAG,qCAAqC;QACtC,MAAMY,kBAAkBjE,KAAKkD,EAAE;QAC/BI,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAEyB,iBAAiBA;;QAE1D,MAAMC,gBAAgBT,iBAAM,CAACC,WAAW,CAAC;QACzCI,oBAAS,CAACC,KAAK,CAACG;QAEhB,yFAAyF;QACzFV,OAAOC,iBAAM,CAACG,SAAS,CAAC,mCAAmCD,iBAAiB;IAC9E;IAEAN,GAAG,2BAA2B;QAC5B,MAAMX,cAAcd,QAAQ,CAAC,EAAE;QAC/B0B,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAEE,aAAaA;;QAEtD,MAAMyB,iBAAiBV,iBAAM,CAACC,WAAW,CAAC;QAC1CF,OAAOW,gBAAgBR,iBAAiB;IACxC,wEAAwE;IAC1E;IAEAN,GAAG,mCAAmC;QACpC,MAAMN,cAAc;YAClB;gBACEpC,IAAI;gBACJyD,OAAO;gBACP9B,aAAa;oBACX;wBAAEN,UAAU;wBAASC,WAAW,CAAC;oBAAQ;oBACzC;wBAAED,UAAU;wBAASC,WAAW,CAAC;oBAAQ;iBAC1C;gBACDoC,WAAW,IAAIC,OAAOC,WAAW;YACnC;SACD;QAEDjB,IAAAA,iBAAM,gBACJ,qBAACC,uBAAc;YACZ,GAAGf,YAAY;YAChBO,aAAaA;YACbyB,iBAAiB;;QAIrBhB,OAAOC,iBAAM,CAACC,WAAW,CAAC,gBAAgBC,iBAAiB;IAC7D;IAEAN,GAAG,0BAA0B;QAC3B,MAAMoB,gBAAgB;YACpB,GAAGjC,YAAY;YACfC,MAAMb,SAAS8C,MAAM,CAAClE,CAAAA,MAAOA,IAAIsB,iBAAiB,KAAK;QACzD;QAEAwB,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGkB,aAAa;;QAExCjB,OAAOC,iBAAM,CAACC,WAAW,CAAC,iBAAiBC,iBAAiB;QAC5DH,OAAOC,iBAAM,CAACkB,aAAa,CAAC,iBAAiBC,GAAG,CAACjB,iBAAiB;IACpE;IAEAN,GAAG,wCAAwC;QACzC,MAAM,EAAEwB,QAAQ,EAAE,GAAGvB,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;;QAE5D,MAAMsC,YAAY;YAAE9C,UAAU;YAASC,WAAW,CAAC;QAAQ;QAC3D4C,uBAAS,qBAACtB,uBAAc;YAAE,GAAGf,YAAY;YAAExB,QAAQ8D;;QAEnD,mEAAmE;QACnEtB,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;IAC/D;IAEAN,GAAG,wCAAwC;QACzC,MAAM,EAAEwB,QAAQ,EAAE,GAAGvB,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;;QAE5DqC,uBAAS,qBAACtB,uBAAc;YAAE,GAAGf,YAAY;YAAEG,MAAM;;QAEjD,6DAA6D;QAC7Da,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;IAC/D;IAEAN,GAAG,0BAA0B;QAC3BC,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAEC,MAAM,EAAE;;QAEjDe,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;QAC7DH,OAAOC,iBAAM,CAACkB,aAAa,CAAC,iBAAiBC,GAAG,CAACjB,iBAAiB;QAClEH,OAAOC,iBAAM,CAACkB,aAAa,CAAC,iBAAiBC,GAAG,CAACjB,iBAAiB;IACpE;IAEAN,GAAG,uCAAuC;QACxCC,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAEM,iBAAiB,EAAE;;QAE5DU,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;QAC7DH,OAAOC,iBAAM,CAACkB,aAAa,CAAC,sBAAsBC,GAAG,CAACjB,iBAAiB;IACzE;IAEAN,GAAG,8BAA8B;QAC/BC,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAEuC,SAAS,EAAE;;QAEpDvB,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;QAC7DH,OAAOC,iBAAM,CAACkB,aAAa,CAAC,eAAeC,GAAG,CAACjB,iBAAiB;IAClE;IAEAN,GAAG,uBAAuB;QACxBC,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAEwC,SAAS;;QAElDxB,OAAOC,iBAAM,CAACC,WAAW,CAAC,gBAAgBC,iBAAiB;IAC7D;IAEAN,GAAG,qBAAqB;QACtB,MAAM4B,eAAe;QACrB3B,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAE0C,OAAOD;;QAEhDzB,OAAOC,iBAAM,CAACG,SAAS,CAACqB,eAAetB,iBAAiB;QACxDH,OAAOC,iBAAM,CAAC0B,SAAS,CAAC,UAAUxB,iBAAiB;IACrD;IAEAN,GAAG,iCAAiC;QAClCC,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAE4C,UAAS;;QAElD,yCAAyC;QACzC5B,OAAOC,iBAAM,CAACC,WAAW,CAAC,eAAeC,iBAAiB;IAC5D;IAEAN,GAAG,6BAA6B;QAC9B,MAAM,EAAEwB,QAAQ,EAAE,GAAGvB,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;;QAE5D,MAAM6C,cAAc;eACfzD;YACHC,IAAAA,wBAAa,EAAC;gBACZlB,IAAI;gBACJE,SAAS;gBACTkB,UAAU;oBAAEC,UAAU;oBAASC,WAAW,CAAC;gBAAQ;YACrD;SACD;QAED4C,uBAAS,qBAACtB,uBAAc;YAAE,GAAGf,YAAY;YAAEC,MAAM4C;;QAEjD,MAAMC,IAAAA,kBAAO,EAAC;YACZ9B,OAAOC,iBAAM,CAACC,WAAW,CAAC,iBAAiBC,iBAAiB;QAC9D;IACF;IAEAN,GAAG,gCAAgC;QACjC,MAAMgC,cAAczD,SAAS2D,GAAG,CAAC/E,CAAAA,MAC/BA,IAAIG,EAAE,KAAK,IACP;gBAAE,GAAGH,GAAG;gBAAEuB,UAAU;oBAAEC,UAAU;oBAASC,WAAW,CAAC;gBAAQ;YAAE,IAC/DzB;QAGN,MAAM,EAAEqE,QAAQ,EAAE,GAAGvB,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;;QAC5DqC,uBAAS,qBAACtB,uBAAc;YAAE,GAAGf,YAAY;YAAEC,MAAM4C;;QAEjD,6DAA6D;QAC7D7B,OAAOC,iBAAM,CAACC,WAAW,CAAC,iBAAiBC,iBAAiB;IAC9D;IAEAN,GAAG,qCAAqC;QACtC,MAAMmC,WAAWC,MAAMC,IAAI,CAAC;YAAEC,QAAQ;QAAG,GAAG,CAACC,GAAGC,IAC9ChE,IAAAA,wBAAa,EAAC;gBACZlB,IAAIkF,IAAI;gBACRhF,SAAS,CAAC,IAAI,EAAE,AAACgF,CAAAA,IAAI,CAAA,EAAGC,QAAQ,GAAGC,QAAQ,CAAC,GAAG,KAAK,CAAC;gBACrDhE,UAAU;oBACRC,UAAU,UAAU,AAACgE,CAAAA,KAAKC,MAAM,KAAK,GAAE,IAAK;oBAC5ChE,WAAW,CAAC,UAAU,AAAC+D,CAAAA,KAAKC,MAAM,KAAK,GAAE,IAAK;gBAChD;YACF;QAGF3C,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAEC,MAAM+C;YAAUU,kBAAkB;;QAE3E1C,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;IAC7D,iDAAiD;IACnD;IAEAN,GAAG,kCAAkC;QACnC,MAAM8C,aAAanG,KAAKkD,EAAE;QAC1BI,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAE2D,YAAYA;;QAErD,MAAMC,eAAe3C,iBAAM,CAACC,WAAW,CAAC;QACxCI,oBAAS,CAACC,KAAK,CAACqC;QAEhB,2DAA2D;QAC3D5C,OAAO4C,cAAczC,iBAAiB;IACxC;IAEAN,GAAG,gCAAgC;QACjCC,IAAAA,iBAAM,gBACJ,qBAACC,uBAAc;YACZ,GAAGf,YAAY;YAChB6D,iBAAiB;YACjBC,kBAAkB;YAClBC,uBAAuB;;QAI3B/C,OAAOC,iBAAM,CAACC,WAAW,CAAC,kBAAkBC,iBAAiB;IAC7D,uDAAuD;IACzD;IAEAN,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAACC,uBAAc;YAAE,GAAGf,YAAY;YAAEgE,WAAU;;QAEnD,MAAMJ,eAAe3C,iBAAM,CAACC,WAAW,CAAC;QACxCF,OAAO4C,aAAaK,aAAa,EAAEC,WAAW,CAAC,QAAQ;IACzD;IAEArD,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBACJ,qBAACC,uBAAc;YACZ,GAAGf,YAAY;YAChBmE,cAAW;YACXC,MAAK;;QAIT,MAAMR,eAAe3C,iBAAM,CAACC,WAAW,CAAC;QACxCF,OAAO4C,cAAcS,eAAe,CAAC,cAAc;QACnDrD,OAAO4C,cAAcS,eAAe,CAAC,QAAQ;IAC/C;AACF"}