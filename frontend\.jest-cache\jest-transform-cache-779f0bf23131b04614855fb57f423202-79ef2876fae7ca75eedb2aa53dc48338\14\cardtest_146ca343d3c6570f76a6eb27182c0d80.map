{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\__tests__\\card.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen } from '@/lib/test-utils'\nimport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent } from '../card'\n\ndescribe('Card Components', () => {\n  describe('Card', () => {\n    it('renders correctly', () => {\n      render(\n        <Card data-testid=\"card\">\n          <div>Card content</div>\n        </Card>\n      )\n      \n      const card = screen.getByTestId('card')\n      expect(card).toBeInTheDocument()\n      expect(card).toHaveClass('rounded-lg', 'border', 'bg-card')\n    })\n\n    it('applies custom className', () => {\n      render(\n        <Card className=\"custom-class\" data-testid=\"card\">\n          Content\n        </Card>\n      )\n      \n      const card = screen.getByTestId('card')\n      expect(card).toHaveClass('custom-class')\n    })\n\n    it('forwards ref correctly', () => {\n      const ref = React.createRef<HTMLDivElement>()\n      render(\n        <Card ref={ref}>\n          Content\n        </Card>\n      )\n      \n      expect(ref.current).toBeInstanceOf(HTMLDivElement)\n    })\n\n    it('accepts additional props', () => {\n      render(\n        <Card data-testid=\"card\" role=\"region\" aria-label=\"Test card\">\n          Content\n        </Card>\n      )\n      \n      const card = screen.getByTestId('card')\n      expect(card).toHaveAttribute('role', 'region')\n      expect(card).toHaveAttribute('aria-label', 'Test card')\n    })\n  })\n\n  describe('CardHeader', () => {\n    it('renders correctly', () => {\n      render(\n        <CardHeader data-testid=\"card-header\">\n          <div>Header content</div>\n        </CardHeader>\n      )\n      \n      const header = screen.getByTestId('card-header')\n      expect(header).toBeInTheDocument()\n      expect(header).toHaveClass('flex', 'flex-col', 'space-y-1.5', 'p-6')\n    })\n\n    it('applies custom className', () => {\n      render(\n        <CardHeader className=\"custom-header\" data-testid=\"card-header\">\n          Header\n        </CardHeader>\n      )\n      \n      const header = screen.getByTestId('card-header')\n      expect(header).toHaveClass('custom-header')\n    })\n\n    it('forwards ref correctly', () => {\n      const ref = React.createRef<HTMLDivElement>()\n      render(\n        <CardHeader ref={ref}>\n          Header\n        </CardHeader>\n      )\n      \n      expect(ref.current).toBeInstanceOf(HTMLDivElement)\n    })\n  })\n\n  describe('CardTitle', () => {\n    it('renders correctly', () => {\n      render(\n        <CardTitle data-testid=\"card-title\">\n          Card Title\n        </CardTitle>\n      )\n      \n      const title = screen.getByTestId('card-title')\n      expect(title).toBeInTheDocument()\n      expect(title).toHaveClass('text-2xl', 'font-semibold', 'leading-none', 'tracking-tight')\n      expect(title).toHaveTextContent('Card Title')\n    })\n\n    it('applies custom className', () => {\n      render(\n        <CardTitle className=\"custom-title\" data-testid=\"card-title\">\n          Title\n        </CardTitle>\n      )\n      \n      const title = screen.getByTestId('card-title')\n      expect(title).toHaveClass('custom-title')\n    })\n\n    it('forwards ref correctly', () => {\n      const ref = React.createRef<HTMLParagraphElement>()\n      render(\n        <CardTitle ref={ref}>\n          Title\n        </CardTitle>\n      )\n      \n      expect(ref.current).toBeInstanceOf(HTMLParagraphElement)\n    })\n\n    it('renders as different HTML elements', () => {\n      render(\n        <CardTitle as=\"h1\" data-testid=\"card-title\">\n          Title as H1\n        </CardTitle>\n      )\n      \n      const title = screen.getByTestId('card-title')\n      expect(title.tagName).toBe('H1')\n    })\n  })\n\n  describe('CardDescription', () => {\n    it('renders correctly', () => {\n      render(\n        <CardDescription data-testid=\"card-description\">\n          This is a card description\n        </CardDescription>\n      )\n      \n      const description = screen.getByTestId('card-description')\n      expect(description).toBeInTheDocument()\n      expect(description).toHaveClass('text-sm', 'text-muted-foreground')\n      expect(description).toHaveTextContent('This is a card description')\n    })\n\n    it('applies custom className', () => {\n      render(\n        <CardDescription className=\"custom-description\" data-testid=\"card-description\">\n          Description\n        </CardDescription>\n      )\n      \n      const description = screen.getByTestId('card-description')\n      expect(description).toHaveClass('custom-description')\n    })\n\n    it('forwards ref correctly', () => {\n      const ref = React.createRef<HTMLParagraphElement>()\n      render(\n        <CardDescription ref={ref}>\n          Description\n        </CardDescription>\n      )\n      \n      expect(ref.current).toBeInstanceOf(HTMLParagraphElement)\n    })\n  })\n\n  describe('CardContent', () => {\n    it('renders correctly', () => {\n      render(\n        <CardContent data-testid=\"card-content\">\n          <p>Card content goes here</p>\n        </CardContent>\n      )\n      \n      const content = screen.getByTestId('card-content')\n      expect(content).toBeInTheDocument()\n      expect(content).toHaveClass('p-6', 'pt-0')\n      expect(screen.getByText('Card content goes here')).toBeInTheDocument()\n    })\n\n    it('applies custom className', () => {\n      render(\n        <CardContent className=\"custom-content\" data-testid=\"card-content\">\n          Content\n        </CardContent>\n      )\n      \n      const content = screen.getByTestId('card-content')\n      expect(content).toHaveClass('custom-content')\n    })\n\n    it('forwards ref correctly', () => {\n      const ref = React.createRef<HTMLDivElement>()\n      render(\n        <CardContent ref={ref}>\n          Content\n        </CardContent>\n      )\n      \n      expect(ref.current).toBeInstanceOf(HTMLDivElement)\n    })\n  })\n\n  describe('CardFooter', () => {\n    it('renders correctly', () => {\n      render(\n        <CardFooter data-testid=\"card-footer\">\n          <button>Action</button>\n        </CardFooter>\n      )\n      \n      const footer = screen.getByTestId('card-footer')\n      expect(footer).toBeInTheDocument()\n      expect(footer).toHaveClass('flex', 'items-center', 'p-6', 'pt-0')\n      expect(screen.getByRole('button', { name: 'Action' })).toBeInTheDocument()\n    })\n\n    it('applies custom className', () => {\n      render(\n        <CardFooter className=\"custom-footer\" data-testid=\"card-footer\">\n          Footer\n        </CardFooter>\n      )\n      \n      const footer = screen.getByTestId('card-footer')\n      expect(footer).toHaveClass('custom-footer')\n    })\n\n    it('forwards ref correctly', () => {\n      const ref = React.createRef<HTMLDivElement>()\n      render(\n        <CardFooter ref={ref}>\n          Footer\n        </CardFooter>\n      )\n      \n      expect(ref.current).toBeInstanceOf(HTMLDivElement)\n    })\n  })\n\n  describe('Complete Card', () => {\n    it('renders a complete card with all components', () => {\n      render(\n        <Card data-testid=\"complete-card\">\n          <CardHeader>\n            <CardTitle>UAV Status</CardTitle>\n            <CardDescription>Current status of UAV-001</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <p>Status: Active</p>\n            <p>Battery: 85%</p>\n          </CardContent>\n          <CardFooter>\n            <button>View Details</button>\n            <button>Edit</button>\n          </CardFooter>\n        </Card>\n      )\n      \n      const card = screen.getByTestId('complete-card')\n      expect(card).toBeInTheDocument()\n      \n      expect(screen.getByText('UAV Status')).toBeInTheDocument()\n      expect(screen.getByText('Current status of UAV-001')).toBeInTheDocument()\n      expect(screen.getByText('Status: Active')).toBeInTheDocument()\n      expect(screen.getByText('Battery: 85%')).toBeInTheDocument()\n      expect(screen.getByRole('button', { name: 'View Details' })).toBeInTheDocument()\n      expect(screen.getByRole('button', { name: 'Edit' })).toBeInTheDocument()\n    })\n\n    it('maintains proper semantic structure', () => {\n      render(\n        <Card role=\"article\" aria-labelledby=\"card-title\">\n          <CardHeader>\n            <CardTitle id=\"card-title\">Article Title</CardTitle>\n            <CardDescription>Article description</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <p>Article content</p>\n          </CardContent>\n        </Card>\n      )\n      \n      const card = screen.getByRole('article')\n      expect(card).toHaveAttribute('aria-labelledby', 'card-title')\n      \n      const title = screen.getByText('Article Title')\n      expect(title).toHaveAttribute('id', 'card-title')\n    })\n\n    it('handles interactive cards', () => {\n      const handleClick = jest.fn()\n      \n      render(\n        <Card \n          onClick={handleClick} \n          className=\"cursor-pointer hover:bg-accent\"\n          data-testid=\"interactive-card\"\n        >\n          <CardContent>\n            <p>Click me</p>\n          </CardContent>\n        </Card>\n      )\n      \n      const card = screen.getByTestId('interactive-card')\n      expect(card).toHaveClass('cursor-pointer')\n      \n      card.click()\n      expect(handleClick).toHaveBeenCalledTimes(1)\n    })\n\n    it('supports keyboard navigation for interactive cards', () => {\n      const handleKeyDown = jest.fn()\n      \n      render(\n        <Card \n          tabIndex={0}\n          onKeyDown={handleKeyDown}\n          data-testid=\"keyboard-card\"\n        >\n          <CardContent>\n            <p>Keyboard accessible</p>\n          </CardContent>\n        </Card>\n      )\n      \n      const card = screen.getByTestId('keyboard-card')\n      expect(card).toHaveAttribute('tabIndex', '0')\n      \n      card.focus()\n      expect(card).toHaveFocus()\n    })\n  })\n})\n"], "names": ["describe", "it", "render", "Card", "data-testid", "div", "card", "screen", "getByTestId", "expect", "toBeInTheDocument", "toHaveClass", "className", "ref", "React", "createRef", "current", "toBeInstanceOf", "HTMLDivElement", "role", "aria-label", "toHaveAttribute", "<PERSON><PERSON><PERSON><PERSON>", "header", "CardTitle", "title", "toHaveTextContent", "HTMLParagraphElement", "as", "tagName", "toBe", "CardDescription", "description", "<PERSON><PERSON><PERSON><PERSON>", "p", "content", "getByText", "<PERSON><PERSON><PERSON>er", "button", "footer", "getByRole", "name", "aria-<PERSON>by", "id", "handleClick", "jest", "fn", "onClick", "click", "toHaveBeenCalledTimes", "handleKeyDown", "tabIndex", "onKeyDown", "focus", "toHaveFocus"], "mappings": ";;;;;8DAAkB;2BACa;sBACuD;;;;;;AAEtFA,SAAS,mBAAmB;IAC1BA,SAAS,QAAQ;QACfC,GAAG,qBAAqB;YACtBC,IAAAA,iBAAM,gBACJ,qBAACC,UAAI;gBAACC,eAAY;0BAChB,cAAA,qBAACC;8BAAI;;;YAIT,MAAMC,OAAOC,iBAAM,CAACC,WAAW,CAAC;YAChCC,OAAOH,MAAMI,iBAAiB;YAC9BD,OAAOH,MAAMK,WAAW,CAAC,cAAc,UAAU;QACnD;QAEAV,GAAG,4BAA4B;YAC7BC,IAAAA,iBAAM,gBACJ,qBAACC,UAAI;gBAACS,WAAU;gBAAeR,eAAY;0BAAO;;YAKpD,MAAME,OAAOC,iBAAM,CAACC,WAAW,CAAC;YAChCC,OAAOH,MAAMK,WAAW,CAAC;QAC3B;QAEAV,GAAG,0BAA0B;YAC3B,MAAMY,oBAAMC,cAAK,CAACC,SAAS;YAC3Bb,IAAAA,iBAAM,gBACJ,qBAACC,UAAI;gBAACU,KAAKA;0BAAK;;YAKlBJ,OAAOI,IAAIG,OAAO,EAAEC,cAAc,CAACC;QACrC;QAEAjB,GAAG,4BAA4B;YAC7BC,IAAAA,iBAAM,gBACJ,qBAACC,UAAI;gBAACC,eAAY;gBAAOe,MAAK;gBAASC,cAAW;0BAAY;;YAKhE,MAAMd,OAAOC,iBAAM,CAACC,WAAW,CAAC;YAChCC,OAAOH,MAAMe,eAAe,CAAC,QAAQ;YACrCZ,OAAOH,MAAMe,eAAe,CAAC,cAAc;QAC7C;IACF;IAEArB,SAAS,cAAc;QACrBC,GAAG,qBAAqB;YACtBC,IAAAA,iBAAM,gBACJ,qBAACoB,gBAAU;gBAAClB,eAAY;0BACtB,cAAA,qBAACC;8BAAI;;;YAIT,MAAMkB,SAAShB,iBAAM,CAACC,WAAW,CAAC;YAClCC,OAAOc,QAAQb,iBAAiB;YAChCD,OAAOc,QAAQZ,WAAW,CAAC,QAAQ,YAAY,eAAe;QAChE;QAEAV,GAAG,4BAA4B;YAC7BC,IAAAA,iBAAM,gBACJ,qBAACoB,gBAAU;gBAACV,WAAU;gBAAgBR,eAAY;0BAAc;;YAKlE,MAAMmB,SAAShB,iBAAM,CAACC,WAAW,CAAC;YAClCC,OAAOc,QAAQZ,WAAW,CAAC;QAC7B;QAEAV,GAAG,0BAA0B;YAC3B,MAAMY,oBAAMC,cAAK,CAACC,SAAS;YAC3Bb,IAAAA,iBAAM,gBACJ,qBAACoB,gBAAU;gBAACT,KAAKA;0BAAK;;YAKxBJ,OAAOI,IAAIG,OAAO,EAAEC,cAAc,CAACC;QACrC;IACF;IAEAlB,SAAS,aAAa;QACpBC,GAAG,qBAAqB;YACtBC,IAAAA,iBAAM,gBACJ,qBAACsB,eAAS;gBAACpB,eAAY;0BAAa;;YAKtC,MAAMqB,QAAQlB,iBAAM,CAACC,WAAW,CAAC;YACjCC,OAAOgB,OAAOf,iBAAiB;YAC/BD,OAAOgB,OAAOd,WAAW,CAAC,YAAY,iBAAiB,gBAAgB;YACvEF,OAAOgB,OAAOC,iBAAiB,CAAC;QAClC;QAEAzB,GAAG,4BAA4B;YAC7BC,IAAAA,iBAAM,gBACJ,qBAACsB,eAAS;gBAACZ,WAAU;gBAAeR,eAAY;0BAAa;;YAK/D,MAAMqB,QAAQlB,iBAAM,CAACC,WAAW,CAAC;YACjCC,OAAOgB,OAAOd,WAAW,CAAC;QAC5B;QAEAV,GAAG,0BAA0B;YAC3B,MAAMY,oBAAMC,cAAK,CAACC,SAAS;YAC3Bb,IAAAA,iBAAM,gBACJ,qBAACsB,eAAS;gBAACX,KAAKA;0BAAK;;YAKvBJ,OAAOI,IAAIG,OAAO,EAAEC,cAAc,CAACU;QACrC;QAEA1B,GAAG,sCAAsC;YACvCC,IAAAA,iBAAM,gBACJ,qBAACsB,eAAS;gBAACI,IAAG;gBAAKxB,eAAY;0BAAa;;YAK9C,MAAMqB,QAAQlB,iBAAM,CAACC,WAAW,CAAC;YACjCC,OAAOgB,MAAMI,OAAO,EAAEC,IAAI,CAAC;QAC7B;IACF;IAEA9B,SAAS,mBAAmB;QAC1BC,GAAG,qBAAqB;YACtBC,IAAAA,iBAAM,gBACJ,qBAAC6B,qBAAe;gBAAC3B,eAAY;0BAAmB;;YAKlD,MAAM4B,cAAczB,iBAAM,CAACC,WAAW,CAAC;YACvCC,OAAOuB,aAAatB,iBAAiB;YACrCD,OAAOuB,aAAarB,WAAW,CAAC,WAAW;YAC3CF,OAAOuB,aAAaN,iBAAiB,CAAC;QACxC;QAEAzB,GAAG,4BAA4B;YAC7BC,IAAAA,iBAAM,gBACJ,qBAAC6B,qBAAe;gBAACnB,WAAU;gBAAqBR,eAAY;0BAAmB;;YAKjF,MAAM4B,cAAczB,iBAAM,CAACC,WAAW,CAAC;YACvCC,OAAOuB,aAAarB,WAAW,CAAC;QAClC;QAEAV,GAAG,0BAA0B;YAC3B,MAAMY,oBAAMC,cAAK,CAACC,SAAS;YAC3Bb,IAAAA,iBAAM,gBACJ,qBAAC6B,qBAAe;gBAAClB,KAAKA;0BAAK;;YAK7BJ,OAAOI,IAAIG,OAAO,EAAEC,cAAc,CAACU;QACrC;IACF;IAEA3B,SAAS,eAAe;QACtBC,GAAG,qBAAqB;YACtBC,IAAAA,iBAAM,gBACJ,qBAAC+B,iBAAW;gBAAC7B,eAAY;0BACvB,cAAA,qBAAC8B;8BAAE;;;YAIP,MAAMC,UAAU5B,iBAAM,CAACC,WAAW,CAAC;YACnCC,OAAO0B,SAASzB,iBAAiB;YACjCD,OAAO0B,SAASxB,WAAW,CAAC,OAAO;YACnCF,OAAOF,iBAAM,CAAC6B,SAAS,CAAC,2BAA2B1B,iBAAiB;QACtE;QAEAT,GAAG,4BAA4B;YAC7BC,IAAAA,iBAAM,gBACJ,qBAAC+B,iBAAW;gBAACrB,WAAU;gBAAiBR,eAAY;0BAAe;;YAKrE,MAAM+B,UAAU5B,iBAAM,CAACC,WAAW,CAAC;YACnCC,OAAO0B,SAASxB,WAAW,CAAC;QAC9B;QAEAV,GAAG,0BAA0B;YAC3B,MAAMY,oBAAMC,cAAK,CAACC,SAAS;YAC3Bb,IAAAA,iBAAM,gBACJ,qBAAC+B,iBAAW;gBAACpB,KAAKA;0BAAK;;YAKzBJ,OAAOI,IAAIG,OAAO,EAAEC,cAAc,CAACC;QACrC;IACF;IAEAlB,SAAS,cAAc;QACrBC,GAAG,qBAAqB;YACtBC,IAAAA,iBAAM,gBACJ,qBAACmC,gBAAU;gBAACjC,eAAY;0BACtB,cAAA,qBAACkC;8BAAO;;;YAIZ,MAAMC,SAAShC,iBAAM,CAACC,WAAW,CAAC;YAClCC,OAAO8B,QAAQ7B,iBAAiB;YAChCD,OAAO8B,QAAQ5B,WAAW,CAAC,QAAQ,gBAAgB,OAAO;YAC1DF,OAAOF,iBAAM,CAACiC,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAS,IAAI/B,iBAAiB;QAC1E;QAEAT,GAAG,4BAA4B;YAC7BC,IAAAA,iBAAM,gBACJ,qBAACmC,gBAAU;gBAACzB,WAAU;gBAAgBR,eAAY;0BAAc;;YAKlE,MAAMmC,SAAShC,iBAAM,CAACC,WAAW,CAAC;YAClCC,OAAO8B,QAAQ5B,WAAW,CAAC;QAC7B;QAEAV,GAAG,0BAA0B;YAC3B,MAAMY,oBAAMC,cAAK,CAACC,SAAS;YAC3Bb,IAAAA,iBAAM,gBACJ,qBAACmC,gBAAU;gBAACxB,KAAKA;0BAAK;;YAKxBJ,OAAOI,IAAIG,OAAO,EAAEC,cAAc,CAACC;QACrC;IACF;IAEAlB,SAAS,iBAAiB;QACxBC,GAAG,+CAA+C;YAChDC,IAAAA,iBAAM,gBACJ,sBAACC,UAAI;gBAACC,eAAY;;kCAChB,sBAACkB,gBAAU;;0CACT,qBAACE,eAAS;0CAAC;;0CACX,qBAACO,qBAAe;0CAAC;;;;kCAEnB,sBAACE,iBAAW;;0CACV,qBAACC;0CAAE;;0CACH,qBAACA;0CAAE;;;;kCAEL,sBAACG,gBAAU;;0CACT,qBAACC;0CAAO;;0CACR,qBAACA;0CAAO;;;;;;YAKd,MAAMhC,OAAOC,iBAAM,CAACC,WAAW,CAAC;YAChCC,OAAOH,MAAMI,iBAAiB;YAE9BD,OAAOF,iBAAM,CAAC6B,SAAS,CAAC,eAAe1B,iBAAiB;YACxDD,OAAOF,iBAAM,CAAC6B,SAAS,CAAC,8BAA8B1B,iBAAiB;YACvED,OAAOF,iBAAM,CAAC6B,SAAS,CAAC,mBAAmB1B,iBAAiB;YAC5DD,OAAOF,iBAAM,CAAC6B,SAAS,CAAC,iBAAiB1B,iBAAiB;YAC1DD,OAAOF,iBAAM,CAACiC,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAe,IAAI/B,iBAAiB;YAC9ED,OAAOF,iBAAM,CAACiC,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAO,IAAI/B,iBAAiB;QACxE;QAEAT,GAAG,uCAAuC;YACxCC,IAAAA,iBAAM,gBACJ,sBAACC,UAAI;gBAACgB,MAAK;gBAAUuB,mBAAgB;;kCACnC,sBAACpB,gBAAU;;0CACT,qBAACE,eAAS;gCAACmB,IAAG;0CAAa;;0CAC3B,qBAACZ,qBAAe;0CAAC;;;;kCAEnB,qBAACE,iBAAW;kCACV,cAAA,qBAACC;sCAAE;;;;;YAKT,MAAM5B,OAAOC,iBAAM,CAACiC,SAAS,CAAC;YAC9B/B,OAAOH,MAAMe,eAAe,CAAC,mBAAmB;YAEhD,MAAMI,QAAQlB,iBAAM,CAAC6B,SAAS,CAAC;YAC/B3B,OAAOgB,OAAOJ,eAAe,CAAC,MAAM;QACtC;QAEApB,GAAG,6BAA6B;YAC9B,MAAM2C,cAAcC,KAAKC,EAAE;YAE3B5C,IAAAA,iBAAM,gBACJ,qBAACC,UAAI;gBACH4C,SAASH;gBACThC,WAAU;gBACVR,eAAY;0BAEZ,cAAA,qBAAC6B,iBAAW;8BACV,cAAA,qBAACC;kCAAE;;;;YAKT,MAAM5B,OAAOC,iBAAM,CAACC,WAAW,CAAC;YAChCC,OAAOH,MAAMK,WAAW,CAAC;YAEzBL,KAAK0C,KAAK;YACVvC,OAAOmC,aAAaK,qBAAqB,CAAC;QAC5C;QAEAhD,GAAG,sDAAsD;YACvD,MAAMiD,gBAAgBL,KAAKC,EAAE;YAE7B5C,IAAAA,iBAAM,gBACJ,qBAACC,UAAI;gBACHgD,UAAU;gBACVC,WAAWF;gBACX9C,eAAY;0BAEZ,cAAA,qBAAC6B,iBAAW;8BACV,cAAA,qBAACC;kCAAE;;;;YAKT,MAAM5B,OAAOC,iBAAM,CAACC,WAAW,CAAC;YAChCC,OAAOH,MAAMe,eAAe,CAAC,YAAY;YAEzCf,KAAK+C,KAAK;YACV5C,OAAOH,MAAMgD,WAAW;QAC1B;IACF;AACF"}