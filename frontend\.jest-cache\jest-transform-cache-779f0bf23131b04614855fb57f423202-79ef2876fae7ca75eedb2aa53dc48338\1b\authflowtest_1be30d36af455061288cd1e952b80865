5466ad9eb740e850cfe324f19707c939
"use strict";
// Mock the API
jest.mock("@/api/auth-api");
// Mock Next.js router
jest.mock("next/navigation", ()=>({
        useRouter: jest.fn()
    }));
// Mock react-hot-toast
jest.mock("react-hot-toast", ()=>({
        toast: {
            success: jest.fn(),
            error: jest.fn()
        }
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../lib/test-utils");
const _authstore = require("../../stores/auth-store");
const _authapi = require("../../api/auth-api");
const _navigation = require("next/navigation");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockedAuthApi = _authapi.authApi;
// Integration test component that uses auth flow
const AuthFlowTestComponent = ()=>{
    const { user, login, logout, isLoading, error } = (0, _authstore.useAuthStore)();
    const [formData, setFormData] = _react.default.useState({
        username: "",
        password: ""
    });
    const handleLogin = async (e)=>{
        e.preventDefault();
        await login({
            username: formData.username,
            password: formData.password,
            rememberMe: false
        });
    };
    const handleLogout = async ()=>{
        await logout();
    };
    if (user) {
        return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("h1", {
                    children: [
                        "Welcome, ",
                        user.firstName,
                        "!"
                    ]
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                    children: [
                        "Email: ",
                        user.email
                    ]
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                    children: [
                        "Role: ",
                        user.roles?.[0]?.name
                    ]
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                    onClick: handleLogout,
                    disabled: isLoading,
                    children: isLoading ? "Logging out..." : "Logout"
                })
            ]
        });
    }
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("form", {
        onSubmit: handleLogin,
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                children: "Login"
            }),
            error && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                role: "alert",
                children: error
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                        htmlFor: "username",
                        children: "Username"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                        id: "username",
                        type: "text",
                        value: formData.username,
                        onChange: (e)=>setFormData((prev)=>({
                                    ...prev,
                                    username: e.target.value
                                })),
                        required: true
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                        htmlFor: "password",
                        children: "Password"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                        id: "password",
                        type: "password",
                        value: formData.password,
                        onChange: (e)=>setFormData((prev)=>({
                                    ...prev,
                                    password: e.target.value
                                })),
                        required: true
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                type: "submit",
                disabled: isLoading,
                children: isLoading ? "Logging in..." : "Login"
            })
        ]
    });
};
describe("Authentication Flow Integration Tests", ()=>{
    const mockPush = jest.fn();
    beforeEach(()=>{
        jest.clearAllMocks();
        // Reset auth store
        _authstore.useAuthStore.setState({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
        });
        _navigation.useRouter.mockReturnValue({
            push: mockPush
        });
    });
    it("completes successful login flow", async ()=>{
        const mockUser = (0, _testutils.createMockUser)({
            firstName: "John",
            email: "<EMAIL>",
            roles: [
                {
                    id: 1,
                    name: "ADMIN",
                    description: "Administrator",
                    permissions: []
                }
            ]
        });
        const mockResponse = {
            user: mockUser,
            token: "mock-token",
            refreshToken: "mock-refresh-token",
            expiresIn: 3600
        };
        mockedAuthApi.login.mockResolvedValue(mockResponse);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AuthFlowTestComponent, {}));
        // Initially shows login form
        expect(_testutils.screen.getByText("Login")).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText("Username")).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText("Password")).toBeInTheDocument();
        // Fill in form
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Username"), {
            target: {
                value: "<EMAIL>"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Password"), {
            target: {
                value: "password123"
            }
        });
        // Submit form
        _testutils.fireEvent.click(_testutils.screen.getByRole("button", {
            name: "Login"
        }));
        // Should show loading state
        expect(_testutils.screen.getByText("Logging in...")).toBeInTheDocument();
        // Wait for login to complete
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Welcome, John!")).toBeInTheDocument();
        });
        // Should show user info
        expect(_testutils.screen.getByText("Email: <EMAIL>")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Role: ADMIN")).toBeInTheDocument();
        expect(_testutils.screen.getByRole("button", {
            name: "Logout"
        })).toBeInTheDocument();
        // Verify API was called correctly
        expect(mockedAuthApi.login).toHaveBeenCalledWith({
            username: "<EMAIL>",
            password: "password123",
            rememberMe: false
        });
    });
    it("handles login failure", async ()=>{
        mockedAuthApi.login.mockRejectedValue(new Error("Invalid credentials"));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AuthFlowTestComponent, {}));
        // Fill in form with invalid credentials
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Username"), {
            target: {
                value: "<EMAIL>"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Password"), {
            target: {
                value: "wrongpassword"
            }
        });
        // Submit form
        _testutils.fireEvent.click(_testutils.screen.getByRole("button", {
            name: "Login"
        }));
        // Wait for error to appear
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Invalid credentials")).toBeInTheDocument();
        });
        // Should still show login form
        expect(_testutils.screen.getByText("Login")).toBeInTheDocument();
        expect(_testutils.screen.getByRole("button", {
            name: "Login"
        })).toBeInTheDocument();
    });
    it("completes logout flow", async ()=>{
        const mockUser = (0, _testutils.createMockUser)();
        // Start with authenticated state
        _authstore.useAuthStore.setState({
            user: mockUser,
            token: "mock-token",
            refreshToken: "mock-refresh-token",
            isAuthenticated: true,
            isLoading: false,
            error: null
        });
        mockedAuthApi.logout.mockResolvedValue();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AuthFlowTestComponent, {}));
        // Should show authenticated state
        expect(_testutils.screen.getByText(`Welcome, ${mockUser.firstName}!`)).toBeInTheDocument();
        // Click logout
        _testutils.fireEvent.click(_testutils.screen.getByRole("button", {
            name: "Logout"
        }));
        // Should show loading state
        expect(_testutils.screen.getByText("Logging out...")).toBeInTheDocument();
        // Wait for logout to complete
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Login")).toBeInTheDocument();
        });
        // Should show login form again
        expect(_testutils.screen.getByLabelText("Username")).toBeInTheDocument();
        expect(_testutils.screen.getByLabelText("Password")).toBeInTheDocument();
        // Verify API was called
        expect(mockedAuthApi.logout).toHaveBeenCalled();
    });
    it("handles network errors during login", async ()=>{
        mockedAuthApi.login.mockRejectedValue(new Error("Network error"));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AuthFlowTestComponent, {}));
        // Fill in form
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Username"), {
            target: {
                value: "<EMAIL>"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Password"), {
            target: {
                value: "password123"
            }
        });
        // Submit form
        _testutils.fireEvent.click(_testutils.screen.getByRole("button", {
            name: "Login"
        }));
        // Wait for error
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Network error")).toBeInTheDocument();
        });
        // Should remain on login form
        expect(_testutils.screen.getByText("Login")).toBeInTheDocument();
    });
    it("maintains loading state consistency", async ()=>{
        let resolveLogin;
        const loginPromise = new Promise((resolve)=>{
            resolveLogin = resolve;
        });
        mockedAuthApi.login.mockReturnValue(loginPromise);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AuthFlowTestComponent, {}));
        // Fill in form
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Username"), {
            target: {
                value: "<EMAIL>"
            }
        });
        _testutils.fireEvent.change(_testutils.screen.getByLabelText("Password"), {
            target: {
                value: "password123"
            }
        });
        // Submit form
        _testutils.fireEvent.click(_testutils.screen.getByRole("button", {
            name: "Login"
        }));
        // Should show loading immediately
        expect(_testutils.screen.getByText("Logging in...")).toBeInTheDocument();
        expect(_testutils.screen.getByRole("button")).toBeDisabled();
        // Resolve login
        resolveLogin({
            user: (0, _testutils.createMockUser)(),
            token: "token",
            refreshToken: "refresh-token",
            expiresIn: 3600
        });
        // Wait for completion
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.queryByText("Logging in...")).not.toBeInTheDocument();
        });
    });
    it("handles token refresh flow", async ()=>{
        const mockUser = (0, _testutils.createMockUser)();
        // Start with authenticated state
        _authstore.useAuthStore.setState({
            user: mockUser,
            token: "old-token",
            refreshToken: "refresh-token",
            isAuthenticated: true,
            isLoading: false,
            error: null
        });
        const mockRefreshResponse = {
            token: "new-token",
            refreshToken: "new-refresh-token",
            expiresIn: 3600
        };
        mockedAuthApi.refreshToken.mockResolvedValue(mockRefreshResponse);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AuthFlowTestComponent, {}));
        // Should show authenticated state
        expect(_testutils.screen.getByText(`Welcome, ${mockUser.firstName}!`)).toBeInTheDocument();
        // Trigger token refresh (this would normally happen automatically)
        const { refreshToken } = _authstore.useAuthStore.getState();
        await refreshToken();
        // Should remain authenticated with new token
        await (0, _testutils.waitFor)(()=>{
            const state = _authstore.useAuthStore.getState();
            expect(state.token).toBe("new-token");
            expect(state.refreshToken).toBe("new-refresh-token");
            expect(state.isAuthenticated).toBe(true);
        });
    });
    it("handles refresh token failure and logout", async ()=>{
        const mockUser = (0, _testutils.createMockUser)();
        // Start with authenticated state
        _authstore.useAuthStore.setState({
            user: mockUser,
            token: "old-token",
            refreshToken: "invalid-refresh-token",
            isAuthenticated: true,
            isLoading: false,
            error: null
        });
        mockedAuthApi.refreshToken.mockRejectedValue(new Error("Invalid refresh token"));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AuthFlowTestComponent, {}));
        // Should show authenticated state initially
        expect(_testutils.screen.getByText(`Welcome, ${mockUser.firstName}!`)).toBeInTheDocument();
        // Trigger token refresh
        const { refreshToken } = _authstore.useAuthStore.getState();
        await refreshToken();
        // Should logout user when refresh fails
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Login")).toBeInTheDocument();
        });
        // Should clear auth state
        const state = _authstore.useAuthStore.getState();
        expect(state.user).toBeNull();
        expect(state.token).toBeNull();
        expect(state.isAuthenticated).toBe(false);
    });
});

//# sourceMappingURL=data:application/json;base64,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