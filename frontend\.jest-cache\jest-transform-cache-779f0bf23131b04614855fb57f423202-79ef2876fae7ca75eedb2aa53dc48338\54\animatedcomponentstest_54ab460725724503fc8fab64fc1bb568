51629de8d3038a3b25af2ca227ba5217
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _animatedcomponents = require("../animated-components");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock framer-motion for testing
(0, _testutils.mockFramerMotion)();
describe("Animated Components", ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    describe("AnimatedPage", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedPage, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Page content"
                })
            }));
            expect(_testutils.screen.getByText("Page content")).toBeInTheDocument();
        });
        it("applies custom className", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedPage, {
                className: "custom-page",
                "data-testid": "animated-page",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Content"
                })
            }));
            const page = _testutils.screen.getByTestId("animated-page");
            expect(page).toHaveClass("custom-page");
        });
        it("respects prefers-reduced-motion", ()=>{
            (0, _testutils.mockPrefersReducedMotion)(true);
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedPage, {
                "data-testid": "reduced-motion-page",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Content"
                })
            }));
            const page = _testutils.screen.getByTestId("reduced-motion-page");
            expect(page).toBeInTheDocument();
        });
    });
    describe("AnimatedCard", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedCard, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Card content"
                })
            }));
            expect(_testutils.screen.getByText("Card content")).toBeInTheDocument();
        });
        it("handles hover interactions", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedCard, {
                "data-testid": "animated-card",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Hover me"
                })
            }));
            const card = _testutils.screen.getByTestId("animated-card");
            _testutils.fireEvent.mouseEnter(card);
            _testutils.fireEvent.mouseLeave(card);
            expect(card).toBeInTheDocument();
        });
        it("handles click interactions", ()=>{
            const handleClick = jest.fn();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedCard, {
                onClick: handleClick,
                "data-testid": "clickable-card",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Click me"
                })
            }));
            const card = _testutils.screen.getByTestId("clickable-card");
            _testutils.fireEvent.click(card);
            expect(handleClick).toHaveBeenCalledTimes(1);
        });
    });
    describe("StaggerContainer and StaggerItem", ()=>{
        it("renders stagger container with items", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)(_animatedcomponents.StaggerContainer, {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.StaggerItem, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            children: "Item 1"
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.StaggerItem, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            children: "Item 2"
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.StaggerItem, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            children: "Item 3"
                        })
                    })
                ]
            }));
            expect(_testutils.screen.getByText("Item 1")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Item 2")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Item 3")).toBeInTheDocument();
        });
        it("applies custom stagger delay", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.StaggerContainer, {
                staggerDelay: 0.2,
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.StaggerItem, {
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        children: "Delayed item"
                    })
                })
            }));
            expect(_testutils.screen.getByText("Delayed item")).toBeInTheDocument();
        });
    });
    describe("AnimatedModal", ()=>{
        it("renders when open", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedModal, {
                isOpen: true,
                onClose: ()=>{},
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Modal content"
                })
            }));
            expect(_testutils.screen.getByText("Modal content")).toBeInTheDocument();
        });
        it("does not render when closed", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedModal, {
                isOpen: false,
                onClose: ()=>{},
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Modal content"
                })
            }));
            expect(_testutils.screen.queryByText("Modal content")).not.toBeInTheDocument();
        });
        it("calls onClose when backdrop is clicked", ()=>{
            const handleClose = jest.fn();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedModal, {
                isOpen: true,
                onClose: handleClose,
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Modal content"
                })
            }));
            const backdrop = _testutils.screen.getByTestId("modal-backdrop");
            _testutils.fireEvent.click(backdrop);
            expect(handleClose).toHaveBeenCalledTimes(1);
        });
        it("handles escape key press", ()=>{
            const handleClose = jest.fn();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedModal, {
                isOpen: true,
                onClose: handleClose,
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Modal content"
                })
            }));
            _testutils.fireEvent.keyDown(document, {
                key: "Escape",
                code: "Escape"
            });
            expect(handleClose).toHaveBeenCalledTimes(1);
        });
        it("prevents closing when closeOnBackdropClick is false", ()=>{
            const handleClose = jest.fn();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedModal, {
                isOpen: true,
                onClose: handleClose,
                closeOnBackdropClick: false,
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Modal content"
                })
            }));
            const backdrop = _testutils.screen.getByTestId("modal-backdrop");
            _testutils.fireEvent.click(backdrop);
            expect(handleClose).not.toHaveBeenCalled();
        });
    });
    describe("AnimatedSpinner", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedSpinner, {
                "data-testid": "spinner"
            }));
            const spinner = _testutils.screen.getByTestId("spinner");
            expect(spinner).toBeInTheDocument();
        });
        it("applies different sizes", ()=>{
            const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedSpinner, {
                size: "sm",
                "data-testid": "spinner"
            }));
            let spinner = _testutils.screen.getByTestId("spinner");
            expect(spinner).toHaveClass("h-4", "w-4");
            rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedSpinner, {
                size: "lg",
                "data-testid": "spinner"
            }));
            spinner = _testutils.screen.getByTestId("spinner");
            expect(spinner).toHaveClass("h-8", "w-8");
        });
        it("applies custom color", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedSpinner, {
                color: "red",
                "data-testid": "spinner"
            }));
            const spinner = _testutils.screen.getByTestId("spinner");
            expect(spinner).toHaveClass("text-red-500");
        });
    });
    describe("ScaleOnHover", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.ScaleOnHover, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Hover to scale"
                })
            }));
            expect(_testutils.screen.getByText("Hover to scale")).toBeInTheDocument();
        });
        it("handles hover interactions", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.ScaleOnHover, {
                "data-testid": "scale-element",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Scale me"
                })
            }));
            const element = _testutils.screen.getByTestId("scale-element");
            _testutils.fireEvent.mouseEnter(element);
            _testutils.fireEvent.mouseLeave(element);
            expect(element).toBeInTheDocument();
        });
        it("applies custom scale factor", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.ScaleOnHover, {
                scale: 1.2,
                "data-testid": "custom-scale",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Custom scale"
                })
            }));
            const element = _testutils.screen.getByTestId("custom-scale");
            expect(element).toBeInTheDocument();
        });
    });
    describe("Pulse", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Pulse, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Pulsing element"
                })
            }));
            expect(_testutils.screen.getByText("Pulsing element")).toBeInTheDocument();
        });
        it("applies custom duration", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Pulse, {
                duration: 2,
                "data-testid": "pulse-element",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Custom pulse"
                })
            }));
            const element = _testutils.screen.getByTestId("pulse-element");
            expect(element).toBeInTheDocument();
        });
    });
    describe("Bounce", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Bounce, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Bouncing element"
                })
            }));
            expect(_testutils.screen.getByText("Bouncing element")).toBeInTheDocument();
        });
        it("applies custom height", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Bounce, {
                height: 20,
                "data-testid": "bounce-element",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Custom bounce"
                })
            }));
            const element = _testutils.screen.getByTestId("bounce-element");
            expect(element).toBeInTheDocument();
        });
    });
    describe("Shake", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Shake, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Shaking element"
                })
            }));
            expect(_testutils.screen.getByText("Shaking element")).toBeInTheDocument();
        });
        it("triggers shake animation", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Shake, {
                trigger: true,
                "data-testid": "shake-element",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Shake me"
                })
            }));
            const element = _testutils.screen.getByTestId("shake-element");
            expect(element).toBeInTheDocument();
        });
    });
    describe("Float", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Float, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Floating element"
                })
            }));
            expect(_testutils.screen.getByText("Floating element")).toBeInTheDocument();
        });
        it("applies custom float distance", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Float, {
                distance: 15,
                "data-testid": "float-element",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Custom float"
                })
            }));
            const element = _testutils.screen.getByTestId("float-element");
            expect(element).toBeInTheDocument();
        });
    });
    describe("Glow", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Glow, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Glowing element"
                })
            }));
            expect(_testutils.screen.getByText("Glowing element")).toBeInTheDocument();
        });
        it("applies custom glow color", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Glow, {
                color: "blue",
                "data-testid": "glow-element",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Blue glow"
                })
            }));
            const element = _testutils.screen.getByTestId("glow-element");
            expect(element).toBeInTheDocument();
        });
    });
    describe("Magnetic", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Magnetic, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Magnetic element"
                })
            }));
            expect(_testutils.screen.getByText("Magnetic element")).toBeInTheDocument();
        });
        it("handles mouse movement", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Magnetic, {
                "data-testid": "magnetic-element",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Follow cursor"
                })
            }));
            const element = _testutils.screen.getByTestId("magnetic-element");
            _testutils.fireEvent.mouseMove(element, {
                clientX: 100,
                clientY: 100
            });
            _testutils.fireEvent.mouseLeave(element);
            expect(element).toBeInTheDocument();
        });
    });
    describe("Fade", ()=>{
        it("renders when show is true", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Fade, {
                show: true,
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Fading content"
                })
            }));
            expect(_testutils.screen.getByText("Fading content")).toBeInTheDocument();
        });
        it("does not render when show is false", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Fade, {
                show: false,
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Hidden content"
                })
            }));
            expect(_testutils.screen.queryByText("Hidden content")).not.toBeInTheDocument();
        });
    });
    describe("SlideIn", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.SlideIn, {
                direction: "left",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Sliding content"
                })
            }));
            expect(_testutils.screen.getByText("Sliding content")).toBeInTheDocument();
        });
        it("supports different directions", ()=>{
            const directions = [
                "left",
                "right",
                "up",
                "down"
            ];
            directions.forEach((direction)=>{
                const { unmount } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.SlideIn, {
                    direction: direction,
                    "data-testid": `slide-${direction}`,
                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            "Slide ",
                            direction
                        ]
                    })
                }));
                expect(_testutils.screen.getByText(`Slide ${direction}`)).toBeInTheDocument();
                unmount();
            });
        });
    });
    describe("ZoomIn", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.ZoomIn, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Zooming content"
                })
            }));
            expect(_testutils.screen.getByText("Zooming content")).toBeInTheDocument();
        });
        it("applies custom scale", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.ZoomIn, {
                scale: 0.5,
                "data-testid": "zoom-element",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Custom zoom"
                })
            }));
            const element = _testutils.screen.getByTestId("zoom-element");
            expect(element).toBeInTheDocument();
        });
    });
    describe("Accessibility", ()=>{
        it("respects prefers-reduced-motion for all components", ()=>{
            (0, _testutils.mockPrefersReducedMotion)(true);
            const components = [
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedPage, {
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        children: "Page"
                    })
                }, "page"),
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedCard, {
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        children: "Card"
                    })
                }, "card"),
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Pulse, {
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        children: "Pulse"
                    })
                }, "pulse"),
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Bounce, {
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        children: "Bounce"
                    })
                }, "bounce"),
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.Float, {
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        children: "Float"
                    })
                }, "float")
            ];
            components.forEach((component)=>{
                const { unmount } = (0, _testutils.render)(component);
                expect(_testutils.screen.getByText(component.props.children.props.children)).toBeInTheDocument();
                unmount();
            });
        });
        it("maintains focus management in modals", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.AnimatedModal, {
                isOpen: true,
                onClose: ()=>{},
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            children: "First button"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            children: "Second button"
                        })
                    ]
                })
            }));
            const firstButton = _testutils.screen.getByText("First button");
            const secondButton = _testutils.screen.getByText("Second button");
            expect(firstButton).toBeInTheDocument();
            expect(secondButton).toBeInTheDocument();
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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