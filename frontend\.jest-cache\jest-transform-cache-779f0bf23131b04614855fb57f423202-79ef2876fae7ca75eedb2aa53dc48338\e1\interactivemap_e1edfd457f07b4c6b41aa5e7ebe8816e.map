{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\features\\map\\interactive-map.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useRef } from 'react'\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, useMap } from 'react-leaflet'\nimport L from 'leaflet'\nimport 'leaflet/dist/leaflet.css'\nimport { UAV } from '@/types/uav'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport {\n  AnimatedUAVMarker,\n  AnimatedGeofence,\n  AnimatedFlightPath,\n  AnimatedDockingStation\n} from './animated-map-components'\nimport { \n  Plane, \n  Battery, \n  MapPin, \n  Clock, \n  User,\n  Eye,\n  Navigation,\n} from 'lucide-react'\nimport { formatRelativeTime, getStatusVariant } from '@/lib/utils'\n\n// Fix for default markers in react-leaflet\ndelete (L.Icon.Default.prototype as any)._getIconUrl\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n})\n\n// Custom UAV icons\nconst createUAVIcon = (status: string, operationalStatus: string) => {\n  const getColor = () => {\n    if (operationalStatus === 'EMERGENCY') return '#dc2626'\n    if (status === 'UNAUTHORIZED') return '#dc2626'\n    if (operationalStatus === 'IN_FLIGHT') return '#2563eb'\n    if (operationalStatus === 'HIBERNATING') return '#7c3aed'\n    if (operationalStatus === 'CHARGING') return '#ea580c'\n    if (operationalStatus === 'MAINTENANCE') return '#d97706'\n    return '#16a34a' // AUTHORIZED and READY\n  }\n\n  const color = getColor()\n  \n  return L.divIcon({\n    html: `\n      <div style=\"\n        background-color: ${color};\n        width: 24px;\n        height: 24px;\n        border-radius: 50%;\n        border: 2px solid white;\n        box-shadow: 0 2px 4px rgba(0,0,0,0.3);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      \">\n        <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"white\">\n          <path d=\"M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z\"/>\n        </svg>\n      </div>\n    `,\n    className: 'custom-uav-icon',\n    iconSize: [24, 24],\n    iconAnchor: [12, 12],\n    popupAnchor: [0, -12],\n  })\n}\n\n// Geofence data (mock)\nconst mockGeofences = [\n  {\n    id: 1,\n    name: 'Restricted Zone A',\n    center: [39.9042, 116.4074] as [number, number],\n    radius: 1000,\n    type: 'restricted',\n    color: '#dc2626',\n  },\n  {\n    id: 2,\n    name: 'Safe Zone B',\n    center: [39.9142, 116.4174] as [number, number],\n    radius: 1500,\n    type: 'safe',\n    color: '#16a34a',\n  },\n  {\n    id: 3,\n    name: 'Warning Zone C',\n    center: [39.8942, 116.3974] as [number, number],\n    radius: 800,\n    type: 'warning',\n    color: '#ea580c',\n  },\n]\n\n// Docking stations data (mock)\nconst mockDockingStations = [\n  {\n    id: 1,\n    name: 'Station Alpha',\n    position: [39.9000, 116.4000] as [number, number],\n    capacity: 5,\n    occupied: 2,\n    status: 'operational',\n  },\n  {\n    id: 2,\n    name: 'Station Beta',\n    position: [39.9100, 116.4100] as [number, number],\n    capacity: 3,\n    occupied: 1,\n    status: 'operational',\n  },\n  {\n    id: 3,\n    name: 'Station Gamma',\n    position: [39.8950, 116.4150] as [number, number],\n    capacity: 4,\n    occupied: 0,\n    status: 'maintenance',\n  },\n]\n\ninterface MapControllerProps {\n  center: [number, number]\n  zoom: number\n}\n\nfunction MapController({ center, zoom }: MapControllerProps) {\n  const map = useMap()\n  \n  useEffect(() => {\n    map.setView(center, zoom)\n  }, [map, center, zoom])\n  \n  return null\n}\n\ninterface InteractiveMapProps {\n  uavs: UAV[]\n  selectedUAV: UAV | null\n  onUAVSelect: (uav: UAV) => void\n  center: [number, number]\n  zoom: number\n  layers: {\n    uavs: boolean\n    geofences: boolean\n    dockingStations: boolean\n    flightPaths: boolean\n    weather: boolean\n  }\n  className?: string\n}\n\nexport default function InteractiveMap({\n  uavs,\n  selectedUAV,\n  onUAVSelect,\n  center,\n  zoom,\n  layers,\n  className,\n}: InteractiveMapProps) {\n  const mapRef = useRef<L.Map>(null)\n\n  // Add mock location data to UAVs that don't have it\n  const uavsWithMockLocations = uavs.map((uav, index) => {\n    if (!uav.currentLatitude || !uav.currentLongitude) {\n      return {\n        ...uav,\n        currentLatitude: 39.9042 + (Math.random() - 0.5) * 0.1,\n        currentLongitude: 116.4074 + (Math.random() - 0.5) * 0.1,\n        currentAltitudeMeters: Math.floor(Math.random() * 500) + 50,\n      }\n    }\n    return uav\n  })\n\n  return (\n    <div className={className}>\n      <MapContainer\n        ref={mapRef}\n        center={center}\n        zoom={zoom}\n        style={{ height: '100%', width: '100%' }}\n        className=\"rounded-lg\"\n      >\n        <MapController center={center} zoom={zoom} />\n        \n        <TileLayer\n          attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n          url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n        />\n\n        {/* UAV Markers */}\n        {layers.uavs && uavsWithMockLocations.map((uav) => (\n          <AnimatedUAVMarker\n            key={uav.id}\n            uav={uav}\n            isSelected={selectedUAV?.id === uav.id}\n            onSelect={onUAVSelect}\n            icon={createUAVIcon(uav.status, uav.operationalStatus)}\n          />\n        ))}\n\n        {/* Legacy UAV Markers for complex popups */}\n        {layers.uavs && uavsWithMockLocations.filter(uav => selectedUAV?.id === uav.id).map((uav) => (\n          <Marker\n            key={`detailed-${uav.id}`}\n            position={[uav.currentLatitude!, uav.currentLongitude!]}\n            icon={L.divIcon({ html: '', iconSize: [0, 0] })}\n          >\n            <Popup>\n              <div className=\"p-2 min-w-64\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Plane className=\"h-4 w-4 text-primary\" />\n                    <span className=\"font-semibold\">{uav.rfidTag}</span>\n                  </div>\n                  <div className=\"flex space-x-1\">\n                    <Badge variant={getStatusVariant(uav.status)}>\n                      {uav.status}\n                    </Badge>\n                    <Badge variant={getStatusVariant(uav.operationalStatus)}>\n                      {uav.operationalStatus.replace('_', ' ')}\n                    </Badge>\n                  </div>\n                </div>\n\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex items-center space-x-2\">\n                    <User className=\"h-3 w-3 text-muted-foreground\" />\n                    <span>{uav.ownerName}</span>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-2\">\n                    <Plane className=\"h-3 w-3 text-muted-foreground\" />\n                    <span>{uav.model}</span>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <MapPin className=\"h-3 w-3 text-muted-foreground\" />\n                    <span className=\"font-mono text-xs\">\n                      {uav.currentLatitude!.toFixed(6)}, {uav.currentLongitude!.toFixed(6)}\n                    </span>\n                  </div>\n\n                  {uav.currentAltitudeMeters && (\n                    <div className=\"flex items-center space-x-2\">\n                      <Navigation className=\"h-3 w-3 text-muted-foreground\" />\n                      <span>{uav.currentAltitudeMeters}m altitude</span>\n                    </div>\n                  )}\n\n                  {uav.batteryStatus && (\n                    <div className=\"flex items-center space-x-2\">\n                      <Battery className=\"h-3 w-3 text-muted-foreground\" />\n                      <span>{uav.batteryStatus.currentChargePercentage}% battery</span>\n                    </div>\n                  )}\n\n                  <div className=\"flex items-center space-x-2\">\n                    <Clock className=\"h-3 w-3 text-muted-foreground\" />\n                    <span>{formatRelativeTime(uav.updatedAt)}</span>\n                  </div>\n                </div>\n\n                <div className=\"mt-3 pt-2 border-t\">\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    className=\"w-full\"\n                    onClick={() => onUAVSelect(uav)}\n                  >\n                    <Eye className=\"h-3 w-3 mr-1\" />\n                    View Details\n                  </Button>\n                </div>\n              </div>\n            </Popup>\n          </Marker>\n        ))}\n\n        {/* Geofences */}\n        {layers.geofences && mockGeofences.map((geofence) => (\n          <AnimatedGeofence\n            key={geofence.id}\n            center={geofence.center}\n            radius={geofence.radius}\n            color={geofence.color}\n            name={geofence.name}\n            type={geofence.type}\n            isVisible={layers.geofences}\n          />\n        ))}\n\n        {/* Docking Stations */}\n        {layers.dockingStations && mockDockingStations.map((station) => (\n          <AnimatedDockingStation\n            key={station.id}\n            position={station.position}\n            status={station.status}\n            name={station.name}\n            capacity={station.capacity}\n            occupied={station.occupied}\n            isVisible={layers.dockingStations}\n          />\n        ))}\n      </MapContainer>\n    </div>\n  )\n}\n"], "names": ["InteractiveMap", "L", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "createUAVIcon", "status", "operationalStatus", "getColor", "color", "divIcon", "html", "className", "iconSize", "iconAnchor", "popupAnchor", "mockGeofences", "id", "name", "center", "radius", "type", "mockDockingStations", "position", "capacity", "occupied", "MapController", "zoom", "map", "useMap", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "uavs", "selectedUAV", "onUAVSelect", "layers", "mapRef", "useRef", "uavsWithMockLocations", "uav", "index", "currentLatitude", "currentLongitude", "Math", "random", "currentAltitudeMeters", "floor", "div", "MapContainer", "ref", "style", "height", "width", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribution", "url", "AnimatedUAVMarker", "isSelected", "onSelect", "icon", "filter", "<PERSON><PERSON>", "Popup", "Plane", "span", "rfidTag", "Badge", "variant", "getStatusVariant", "replace", "User", "ownerName", "model", "MapPin", "toFixed", "Navigation", "batteryStatus", "Battery", "currentChargePercentage", "Clock", "formatRelativeTime", "updatedAt", "<PERSON><PERSON>", "size", "onClick", "Eye", "geofences", "geofence", "AnimatedGeofence", "isVisible", "dockingStations", "station", "AnimatedDockingStation"], "mappings": "AAAA;;;;;+BAgKA;;;eAAwBA;;;;+DA9JiB;8BAC8B;gEACzD;QACP;uBAEe;wBACC;uCAMhB;6BASA;uBAC8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErD,2CAA2C;AAC3C,OAAO,AAACC,gBAAC,CAACC,IAAI,CAACC,OAAO,CAACC,SAAS,CAASC,WAAW;AACpDJ,gBAAC,CAACC,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;IAC1BC,eAAe;IACfC,SAAS;IACTC,WAAW;AACb;AAEA,mBAAmB;AACnB,MAAMC,gBAAgB,CAACC,QAAgBC;IACrC,MAAMC,WAAW;QACf,IAAID,sBAAsB,aAAa,OAAO;QAC9C,IAAID,WAAW,gBAAgB,OAAO;QACtC,IAAIC,sBAAsB,aAAa,OAAO;QAC9C,IAAIA,sBAAsB,eAAe,OAAO;QAChD,IAAIA,sBAAsB,YAAY,OAAO;QAC7C,IAAIA,sBAAsB,eAAe,OAAO;QAChD,OAAO,UAAU,uBAAuB;;IAC1C;IAEA,MAAME,QAAQD;IAEd,OAAOZ,gBAAC,CAACc,OAAO,CAAC;QACfC,MAAM,CAAC;;0BAEe,EAAEF,MAAM;;;;;;;;;;;;;;IAc9B,CAAC;QACDG,WAAW;QACXC,UAAU;YAAC;YAAI;SAAG;QAClBC,YAAY;YAAC;YAAI;SAAG;QACpBC,aAAa;YAAC;YAAG,CAAC;SAAG;IACvB;AACF;AAEA,uBAAuB;AACvB,MAAMC,gBAAgB;IACpB;QACEC,IAAI;QACJC,MAAM;QACNC,QAAQ;YAAC;YAAS;SAAS;QAC3BC,QAAQ;QACRC,MAAM;QACNZ,OAAO;IACT;IACA;QACEQ,IAAI;QACJC,MAAM;QACNC,QAAQ;YAAC;YAAS;SAAS;QAC3BC,QAAQ;QACRC,MAAM;QACNZ,OAAO;IACT;IACA;QACEQ,IAAI;QACJC,MAAM;QACNC,QAAQ;YAAC;YAAS;SAAS;QAC3BC,QAAQ;QACRC,MAAM;QACNZ,OAAO;IACT;CACD;AAED,+BAA+B;AAC/B,MAAMa,sBAAsB;IAC1B;QACEL,IAAI;QACJC,MAAM;QACNK,UAAU;YAAC;YAAS;SAAS;QAC7BC,UAAU;QACVC,UAAU;QACVnB,QAAQ;IACV;IACA;QACEW,IAAI;QACJC,MAAM;QACNK,UAAU;YAAC;YAAS;SAAS;QAC7BC,UAAU;QACVC,UAAU;QACVnB,QAAQ;IACV;IACA;QACEW,IAAI;QACJC,MAAM;QACNK,UAAU;YAAC;YAAS;SAAS;QAC7BC,UAAU;QACVC,UAAU;QACVnB,QAAQ;IACV;CACD;AAOD,SAASoB,cAAc,EAAEP,MAAM,EAAEQ,IAAI,EAAsB;IACzD,MAAMC,MAAMC,IAAAA,oBAAM;IAElBC,IAAAA,gBAAS,EAAC;QACRF,IAAIG,OAAO,CAACZ,QAAQQ;IACtB,GAAG;QAACC;QAAKT;QAAQQ;KAAK;IAEtB,OAAO;AACT;AAkBe,SAAShC,eAAe,EACrCqC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXf,MAAM,EACNQ,IAAI,EACJQ,MAAM,EACNvB,SAAS,EACW;IACpB,MAAMwB,SAASC,IAAAA,aAAM,EAAQ;IAE7B,oDAAoD;IACpD,MAAMC,wBAAwBN,KAAKJ,GAAG,CAAC,CAACW,KAAKC;QAC3C,IAAI,CAACD,IAAIE,eAAe,IAAI,CAACF,IAAIG,gBAAgB,EAAE;YACjD,OAAO;gBACL,GAAGH,GAAG;gBACNE,iBAAiB,UAAU,AAACE,CAAAA,KAAKC,MAAM,KAAK,GAAE,IAAK;gBACnDF,kBAAkB,WAAW,AAACC,CAAAA,KAAKC,MAAM,KAAK,GAAE,IAAK;gBACrDC,uBAAuBF,KAAKG,KAAK,CAACH,KAAKC,MAAM,KAAK,OAAO;YAC3D;QACF;QACA,OAAOL;IACT;IAEA,qBACE,qBAACQ;QAAInC,WAAWA;kBACd,cAAA,sBAACoC,0BAAY;YACXC,KAAKb;YACLjB,QAAQA;YACRQ,MAAMA;YACNuB,OAAO;gBAAEC,QAAQ;gBAAQC,OAAO;YAAO;YACvCxC,WAAU;;8BAEV,qBAACc;oBAAcP,QAAQA;oBAAQQ,MAAMA;;8BAErC,qBAAC0B,uBAAS;oBACRC,aAAY;oBACZC,KAAI;;gBAILpB,OAAOH,IAAI,IAAIM,sBAAsBV,GAAG,CAAC,CAACW,oBACzC,qBAACiB,wCAAiB;wBAEhBjB,KAAKA;wBACLkB,YAAYxB,aAAahB,OAAOsB,IAAItB,EAAE;wBACtCyC,UAAUxB;wBACVyB,MAAMtD,cAAckC,IAAIjC,MAAM,EAAEiC,IAAIhC,iBAAiB;uBAJhDgC,IAAItB,EAAE;gBASdkB,OAAOH,IAAI,IAAIM,sBAAsBsB,MAAM,CAACrB,CAAAA,MAAON,aAAahB,OAAOsB,IAAItB,EAAE,EAAEW,GAAG,CAAC,CAACW,oBACnF,qBAACsB,oBAAM;wBAELtC,UAAU;4BAACgB,IAAIE,eAAe;4BAAGF,IAAIG,gBAAgB;yBAAE;wBACvDiB,MAAM/D,gBAAC,CAACc,OAAO,CAAC;4BAAEC,MAAM;4BAAIE,UAAU;gCAAC;gCAAG;6BAAE;wBAAC;kCAE7C,cAAA,qBAACiD,mBAAK;sCACJ,cAAA,sBAACf;gCAAInC,WAAU;;kDACb,sBAACmC;wCAAInC,WAAU;;0DACb,sBAACmC;gDAAInC,WAAU;;kEACb,qBAACmD,kBAAK;wDAACnD,WAAU;;kEACjB,qBAACoD;wDAAKpD,WAAU;kEAAiB2B,IAAI0B,OAAO;;;;0DAE9C,sBAAClB;gDAAInC,WAAU;;kEACb,qBAACsD,YAAK;wDAACC,SAASC,IAAAA,uBAAgB,EAAC7B,IAAIjC,MAAM;kEACxCiC,IAAIjC,MAAM;;kEAEb,qBAAC4D,YAAK;wDAACC,SAASC,IAAAA,uBAAgB,EAAC7B,IAAIhC,iBAAiB;kEACnDgC,IAAIhC,iBAAiB,CAAC8D,OAAO,CAAC,KAAK;;;;;;kDAK1C,sBAACtB;wCAAInC,WAAU;;0DACb,sBAACmC;gDAAInC,WAAU;;kEACb,qBAAC0D,iBAAI;wDAAC1D,WAAU;;kEAChB,qBAACoD;kEAAMzB,IAAIgC,SAAS;;;;0DAGtB,sBAACxB;gDAAInC,WAAU;;kEACb,qBAACmD,kBAAK;wDAACnD,WAAU;;kEACjB,qBAACoD;kEAAMzB,IAAIiC,KAAK;;;;0DAGlB,sBAACzB;gDAAInC,WAAU;;kEACb,qBAAC6D,mBAAM;wDAAC7D,WAAU;;kEAClB,sBAACoD;wDAAKpD,WAAU;;4DACb2B,IAAIE,eAAe,CAAEiC,OAAO,CAAC;4DAAG;4DAAGnC,IAAIG,gBAAgB,CAAEgC,OAAO,CAAC;;;;;4CAIrEnC,IAAIM,qBAAqB,kBACxB,sBAACE;gDAAInC,WAAU;;kEACb,qBAAC+D,uBAAU;wDAAC/D,WAAU;;kEACtB,sBAACoD;;4DAAMzB,IAAIM,qBAAqB;4DAAC;;;;;4CAIpCN,IAAIqC,aAAa,kBAChB,sBAAC7B;gDAAInC,WAAU;;kEACb,qBAACiE,oBAAO;wDAACjE,WAAU;;kEACnB,sBAACoD;;4DAAMzB,IAAIqC,aAAa,CAACE,uBAAuB;4DAAC;;;;;0DAIrD,sBAAC/B;gDAAInC,WAAU;;kEACb,qBAACmE,kBAAK;wDAACnE,WAAU;;kEACjB,qBAACoD;kEAAMgB,IAAAA,yBAAkB,EAACzC,IAAI0C,SAAS;;;;;;kDAI3C,qBAAClC;wCAAInC,WAAU;kDACb,cAAA,sBAACsE,cAAM;4CACLC,MAAK;4CACLhB,SAAQ;4CACRvD,WAAU;4CACVwE,SAAS,IAAMlD,YAAYK;;8DAE3B,qBAAC8C,gBAAG;oDAACzE,WAAU;;gDAAiB;;;;;;;uBAlEnC,CAAC,SAAS,EAAE2B,IAAItB,EAAE,CAAC,CAAC;gBA4E5BkB,OAAOmD,SAAS,IAAItE,cAAcY,GAAG,CAAC,CAAC2D,yBACtC,qBAACC,uCAAgB;wBAEfrE,QAAQoE,SAASpE,MAAM;wBACvBC,QAAQmE,SAASnE,MAAM;wBACvBX,OAAO8E,SAAS9E,KAAK;wBACrBS,MAAMqE,SAASrE,IAAI;wBACnBG,MAAMkE,SAASlE,IAAI;wBACnBoE,WAAWtD,OAAOmD,SAAS;uBANtBC,SAAStE,EAAE;gBAWnBkB,OAAOuD,eAAe,IAAIpE,oBAAoBM,GAAG,CAAC,CAAC+D,wBAClD,qBAACC,6CAAsB;wBAErBrE,UAAUoE,QAAQpE,QAAQ;wBAC1BjB,QAAQqF,QAAQrF,MAAM;wBACtBY,MAAMyE,QAAQzE,IAAI;wBAClBM,UAAUmE,QAAQnE,QAAQ;wBAC1BC,UAAUkE,QAAQlE,QAAQ;wBAC1BgE,WAAWtD,OAAOuD,eAAe;uBAN5BC,QAAQ1E,EAAE;;;;AAY3B"}