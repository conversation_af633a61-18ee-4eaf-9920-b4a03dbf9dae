8d9780eebbf64172232757f194fafac1
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    checkAriaAttributes: function() {
        return checkAriaAttributes;
    },
    cleanup: function() {
        return cleanup;
    },
    clickElement: function() {
        return clickElement;
    },
    createMockAlert: function() {
        return createMockAlert;
    },
    createMockDockingStation: function() {
        return createMockDockingStation;
    },
    createMockPermission: function() {
        return createMockPermission;
    },
    createMockRole: function() {
        return createMockRole;
    },
    createMockUAV: function() {
        return createMockUAV;
    },
    createMockUser: function() {
        return createMockUser;
    },
    createTestQueryClient: function() {
        return createTestQueryClient;
    },
    createUser: function() {
        return createUser;
    },
    mockApiError: function() {
        return mockApiError;
    },
    mockApiResponse: function() {
        return mockApiResponse;
    },
    mockFetch: function() {
        return mockFetch;
    },
    mockFramerMotion: function() {
        return mockFramerMotion;
    },
    mockPrefersReducedMotion: function() {
        return mockPrefersReducedMotion;
    },
    render: function() {
        return customRender;
    },
    runAxeTest: function() {
        return runAxeTest;
    },
    setupAuthStore: function() {
        return setupAuthStore;
    },
    setupDashboardStore: function() {
        return setupDashboardStore;
    },
    setupUAVStore: function() {
        return setupUAVStore;
    },
    typeIntoInput: function() {
        return typeIntoInput;
    },
    userEvent: function() {
        return _userevent.default;
    },
    waitForElementToBeRemoved: function() {
        return waitForElementToBeRemoved;
    },
    waitForLoadingToComplete: function() {
        return waitForLoadingToComplete;
    },
    waitForLoadingToFinish: function() {
        return waitForLoadingToFinish;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _react1 = _export_star(require("@testing-library/react"), exports);
const _userevent = /*#__PURE__*/ _interop_require_default(require("@testing-library/user-event"));
const _reactquery = require("@tanstack/react-query");
const _uavstore = require("../stores/uav-store");
const _dashboardstore = require("../stores/dashboard-store");
const _authstore = require("../stores/auth-store");
const _jestaxe = require("jest-axe");
function _export_star(from, to) {
    Object.keys(from).forEach(function(k) {
        if (k !== "default" && !Object.prototype.hasOwnProperty.call(to, k)) {
            Object.defineProperty(to, k, {
                enumerable: true,
                get: function() {
                    return from[k];
                }
            });
        }
    });
    return from;
}
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Extend Jest matchers
expect.extend(_jestaxe.toHaveNoViolations);
// Create a test query client
const createTestQueryClient = ()=>new _reactquery.QueryClient({
        defaultOptions: {
            queries: {
                retry: false,
                cacheTime: 0
            }
        }
    });
function TestProviders({ children, queryClient }) {
    const client = queryClient || createTestQueryClient();
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_reactquery.QueryClientProvider, {
        client: client,
        children: children
    });
}
// Custom render function
const customRender = (ui, options)=>{
    const { queryClient, ...renderOptions } = options || {};
    return (0, _react1.render)(ui, {
        wrapper: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)(TestProviders, {
                queryClient: queryClient,
                children: children
            }),
        ...renderOptions
    });
};
const createMockUser = (overrides)=>({
        id: 1,
        username: "testuser",
        email: "<EMAIL>",
        firstName: "Test",
        lastName: "User",
        roles: [
            createMockRole()
        ],
        permissions: [
            createMockPermission()
        ],
        isActive: true,
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        ...overrides
    });
const createMockRole = (overrides)=>({
        id: 1,
        name: "ADMIN",
        description: "Administrator role",
        permissions: [
            createMockPermission()
        ],
        ...overrides
    });
const createMockPermission = (overrides)=>({
        id: 1,
        name: "UAV_READ",
        resource: "UAV",
        action: "READ",
        description: "Read UAV data",
        ...overrides
    });
const createMockUAV = (overrides)=>({
        id: 1,
        rfidTag: "UAV-001",
        ownerName: "Test Owner",
        model: "Test Model",
        status: "AUTHORIZED",
        operationalStatus: "READY",
        inHibernatePod: false,
        totalFlightHours: 10.5,
        totalFlightCycles: 25,
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        regions: [],
        ...overrides
    });
const createMockAlert = (overrides)=>({
        id: 1,
        type: "WARNING",
        title: "Test Alert",
        message: "This is a test alert",
        timestamp: new Date().toISOString(),
        acknowledged: false,
        source: "SYSTEM",
        severity: "MEDIUM",
        ...overrides
    });
const createMockDockingStation = (overrides)=>({
        id: 1,
        name: "Test Station",
        location: {
            latitude: 40.7128,
            longitude: -74.0060
        },
        status: "AVAILABLE",
        capacity: 4,
        currentOccupancy: 1,
        isActive: true,
        ...overrides
    });
const setupAuthStore = (user)=>{
    const store = _authstore.useAuthStore.getState();
    if (user) {
        store.user = user;
        store.isAuthenticated = true;
        store.token = "mock-token";
    } else {
        store.user = null;
        store.isAuthenticated = false;
        store.token = null;
    }
    store.isLoading = false;
    store.error = null;
};
const setupUAVStore = (uavs = [])=>{
    const store = _uavstore.useUAVStore.getState();
    store.uavs = uavs;
    store.loading = false;
    store.error = null;
    store.selectedUAV = null;
};
const setupDashboardStore = ()=>{
    const store = _dashboardstore.useDashboardStore.getState();
    store.metrics = {
        totalUAVs: 10,
        authorizedUAVs: 8,
        unauthorizedUAVs: 2,
        activeFlights: 3,
        hibernatingUAVs: 2,
        lowBatteryCount: 1,
        chargingCount: 2,
        maintenanceCount: 1,
        emergencyCount: 0
    };
    store.isConnected = true;
    store.lastUpdate = new Date().toISOString();
    store.alerts = [];
};
const waitForLoadingToFinish = ()=>{
    return new Promise((resolve)=>setTimeout(resolve, 0));
};
const mockApiResponse = (data, success = true)=>({
        success,
        message: success ? "Success" : "Error",
        data: success ? data : undefined
    });
const mockApiError = (message = "API Error")=>{
    throw new Error(message);
};
const mockFetch = (response, ok = true)=>{
    global.fetch = jest.fn(()=>Promise.resolve({
            ok,
            json: ()=>Promise.resolve(response),
            text: ()=>Promise.resolve(JSON.stringify(response)),
            status: ok ? 200 : 400,
            statusText: ok ? "OK" : "Bad Request"
        }));
};
const cleanup = ()=>{
    // Reset all stores
    _authstore.useAuthStore.getState().logout();
    _uavstore.useUAVStore.setState({
        uavs: [],
        selectedUAV: null,
        loading: false,
        error: null,
        filter: {},
        searchQuery: ""
    });
    _dashboardstore.useDashboardStore.setState({
        metrics: null,
        flightActivity: null,
        batteryStats: null,
        hibernatePodMetrics: null,
        chartData: null,
        alerts: [],
        recentLocationUpdates: [],
        isConnected: false,
        lastUpdate: null,
        connectionError: null
    });
    // Clear mocks
    jest.clearAllMocks();
    // Clear localStorage
    localStorage.clear();
    sessionStorage.clear();
};
const mockFramerMotion = ()=>{
    jest.mock("framer-motion", ()=>({
            motion: {
                div: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        ...props,
                        children: children
                    }),
                button: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        ...props,
                        children: children
                    }),
                span: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                        ...props,
                        children: children
                    }),
                section: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("section", {
                        ...props,
                        children: children
                    })
            },
            AnimatePresence: ({ children })=>children,
            useAnimation: ()=>({
                    start: jest.fn(),
                    stop: jest.fn(),
                    set: jest.fn()
                }),
            useMotionValue: ()=>({
                    get: jest.fn(),
                    set: jest.fn()
                })
        }));
};
const mockPrefersReducedMotion = (value)=>{
    Object.defineProperty(window, "matchMedia", {
        writable: true,
        value: jest.fn().mockImplementation((query)=>({
                matches: query === "(prefers-reduced-motion: reduce)" ? value : false,
                media: query,
                onchange: null,
                addListener: jest.fn(),
                removeListener: jest.fn(),
                addEventListener: jest.fn(),
                removeEventListener: jest.fn(),
                dispatchEvent: jest.fn()
            }))
    });
};
const runAxeTest = async (container)=>{
    const results = await (0, _jestaxe.axe)(container);
    expect(results).toHaveNoViolations();
};
const checkAriaAttributes = (element, expectedAttributes)=>{
    Object.entries(expectedAttributes).forEach(([attr, value])=>{
        expect(element).toHaveAttribute(attr, value);
    });
};
const createUser = ()=>_userevent.default.setup();
const typeIntoInput = async (input, text)=>{
    const user = createUser();
    await user.clear(input);
    await user.type(input, text);
};
const clickElement = async (element)=>{
    const user = createUser();
    await user.click(element);
};
const waitForElementToBeRemoved = async (element)=>{
    await (0, _react1.waitFor)(()=>{
        expect(element).not.toBeInTheDocument();
    });
};
const waitForLoadingToComplete = async ()=>{
    await (0, _react1.waitFor)(()=>{
        expect(_react1.screen.queryByTestId("loading")).not.toBeInTheDocument();
    }, {
        timeout: 5000
    });
};
// Custom matchers
expect.extend({
    toBeInTheDocument (received) {
        const pass = received !== null && received !== undefined;
        return {
            message: ()=>`expected element ${pass ? "not " : ""}to be in the document`,
            pass
        };
    }
});

//# sourceMappingURL=data:application/json;base64,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