162483c0459c17038c17fd5730b9d57b
"use strict";
// Mock react-leaflet components (already mocked in jest.setup.js)
// Mock framer-motion
jest.mock("framer-motion", ()=>({
        motion: {
            div: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    ...props,
                    children: children
                })
        },
        AnimatePresence: ({ children })=>children
    }));
// Mock the animated map components
jest.mock("../animated-map-components", ()=>({
        AnimatedUAVMarker: ({ uav, onSelect })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `uav-marker-${uav.id}`,
                onClick: ()=>onSelect(uav),
                children: [
                    "UAV Marker: ",
                    uav.rfidTag
                ]
            }),
        AnimatedGeofence: ({ name, center, radius, color, type })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `geofence-${name}`,
                children: [
                    "Geofence: ",
                    name
                ]
            }),
        AnimatedFlightPath: ({ path })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "flight-path",
                children: "Flight Path"
            }),
        AnimatedDockingStation: ({ name, position, status, capacity, occupied })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `docking-station-${name}`,
                children: [
                    "Docking Station: ",
                    name
                ]
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../../lib/test-utils");
const _interactivemap = /*#__PURE__*/ _interop_require_default(require("../interactive-map"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
describe("InteractiveMap Component", ()=>{
    const mockUAVs = [
        (0, _testutils.createMockUAV)({
            id: 1,
            rfidTag: "UAV-001",
            status: "AUTHORIZED",
            operationalStatus: "ACTIVE",
            location: {
                latitude: 40.7128,
                longitude: -74.0060
            }
        }),
        (0, _testutils.createMockUAV)({
            id: 2,
            rfidTag: "UAV-002",
            status: "AUTHORIZED",
            operationalStatus: "READY",
            location: {
                latitude: 40.7589,
                longitude: -73.9851
            }
        })
    ];
    const mockDockingStations = [
        (0, _testutils.createMockDockingStation)({
            id: 1,
            name: "Station Alpha",
            location: {
                latitude: 40.7505,
                longitude: -73.9934
            },
            status: "AVAILABLE"
        })
    ];
    const mockRegions = [
        {
            id: 1,
            name: "Zone A",
            description: "Authorized zone A",
            coordinates: [
                {
                    latitude: 40.7000,
                    longitude: -74.0200
                },
                {
                    latitude: 40.7200,
                    longitude: -74.0200
                },
                {
                    latitude: 40.7200,
                    longitude: -73.9800
                },
                {
                    latitude: 40.7000,
                    longitude: -73.9800
                }
            ],
            isActive: true
        }
    ];
    const defaultProps = {
        uavs: mockUAVs,
        selectedUAV: null,
        center: [
            40.7128,
            -74.0060
        ],
        zoom: 12,
        layers: {
            uavs: true,
            geofences: true,
            dockingStations: true,
            flightPaths: true,
            weather: false
        },
        onUAVSelect: jest.fn()
    };
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("tile-layer")).toBeInTheDocument();
    });
    it("renders UAV markers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("uav-marker-2")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Marker: UAV-001")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Marker: UAV-002")).toBeInTheDocument();
    });
    it("renders docking station markers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("docking-station-Station Alpha")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Docking Station: Station Alpha")).toBeInTheDocument();
    });
    it("renders geofences for regions", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("geofence-Restricted Zone A")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Geofence: Restricted Zone A")).toBeInTheDocument();
    });
    it("handles UAV selection", ()=>{
        const onUAVSelect = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onUAVSelect: onUAVSelect
        }));
        const uavMarker = _testutils.screen.getByTestId("uav-marker-1");
        _testutils.fireEvent.click(uavMarker);
        expect(onUAVSelect).toHaveBeenCalledWith(mockUAVs[0]);
    });
    it("handles docking station selection", ()=>{
        const onStationSelect = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onStationSelect: onStationSelect
        }));
        const stationMarker = _testutils.screen.getByTestId("docking-station-Station Alpha");
        _testutils.fireEvent.click(stationMarker);
        // Note: The mock component doesn't have click handler, so this test needs to be adjusted
        expect(_testutils.screen.getByText("Docking Station: Station Alpha")).toBeInTheDocument();
    });
    it("highlights selected UAV", ()=>{
        const selectedUAV = mockUAVs[0];
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            selectedUAV: selectedUAV
        }));
        const selectedMarker = _testutils.screen.getByTestId("uav-marker-1");
        expect(selectedMarker).toBeInTheDocument();
    // The selected state would be passed to the AnimatedUAVMarker component
    });
    it("shows flight paths when enabled", ()=>{
        const flightPaths = [
            {
                id: 1,
                uavId: 1,
                coordinates: [
                    {
                        latitude: 40.7128,
                        longitude: -74.0060
                    },
                    {
                        latitude: 40.7589,
                        longitude: -73.9851
                    }
                ],
                timestamp: new Date().toISOString()
            }
        ];
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            flightPaths: flightPaths,
            showFlightPaths: true
        }));
        expect(_testutils.screen.getByTestId("flight-path")).toBeInTheDocument();
    });
    it("filters UAVs by status", ()=>{
        const filteredProps = {
            ...defaultProps,
            uavs: mockUAVs.filter((uav)=>uav.operationalStatus === "ACTIVE")
        };
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...filteredProps
        }));
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-2")).not.toBeInTheDocument();
    });
    it("updates map center when prop changes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        const newCenter = {
            latitude: 41.8781,
            longitude: -87.6298
        };
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            center: newCenter
        }));
        // Map center update would be handled by the MapContainer component
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    });
    it("updates zoom level when prop changes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            zoom: 15
        }));
        // Zoom update would be handled by the MapContainer component
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    });
    it("handles empty UAV list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-1")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-2")).not.toBeInTheDocument();
    });
    it("handles empty docking stations list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            dockingStations: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("docking-station-1")).not.toBeInTheDocument();
    });
    it("handles empty regions list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            regions: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("geofence-1")).not.toBeInTheDocument();
    });
    it("shows loading state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            loading: true
        }));
        expect(_testutils.screen.getByTestId("map-loading")).toBeInTheDocument();
    });
    it("shows error state", ()=>{
        const errorMessage = "Failed to load map data";
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            error: errorMessage
        }));
        expect(_testutils.screen.getByText(errorMessage)).toBeInTheDocument();
        expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
    });
    it("supports different map layers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            mapLayer: "satellite"
        }));
        // Different tile layer would be rendered
        expect(_testutils.screen.getByTestId("tile-layer")).toBeInTheDocument();
    });
    it("handles real-time updates", async ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        const updatedUAVs = [
            ...mockUAVs,
            (0, _testutils.createMockUAV)({
                id: 3,
                rfidTag: "UAV-003",
                location: {
                    latitude: 40.7300,
                    longitude: -74.0000
                }
            })
        ];
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: updatedUAVs
        }));
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByTestId("uav-marker-3")).toBeInTheDocument();
        });
    });
    it("handles UAV location updates", ()=>{
        const updatedUAVs = mockUAVs.map((uav)=>uav.id === 1 ? {
                ...uav,
                location: {
                    latitude: 40.7200,
                    longitude: -74.0100
                }
            } : uav);
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: updatedUAVs
        }));
        // Updated location would be reflected in the marker position
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
    });
    it("supports clustering for many UAVs", ()=>{
        const manyUAVs = Array.from({
            length: 50
        }, (_, i)=>(0, _testutils.createMockUAV)({
                id: i + 1,
                rfidTag: `UAV-${(i + 1).toString().padStart(3, "0")}`,
                location: {
                    latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
                    longitude: -74.0060 + (Math.random() - 0.5) * 0.1
                }
            }));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: manyUAVs,
            enableClustering: true
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    // Clustering would be handled by the map library
    });
    it("handles map interaction events", ()=>{
        const onMapClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onMapClick: onMapClick
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        _testutils.fireEvent.click(mapContainer);
        // Map click would be handled by the MapContainer component
        expect(mapContainer).toBeInTheDocument();
    });
    it("supports custom map controls", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            showZoomControl: true,
            showScaleControl: true,
            showFullscreenControl: true
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    // Custom controls would be rendered as part of the map
    });
    it("handles responsive design", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            className: "h-96 w-full"
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        expect(mapContainer.parentElement).toHaveClass("h-96", "w-full");
    });
    it("supports accessibility features", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            "aria-label": "UAV tracking map",
            role: "application"
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        expect(mapContainer).toHaveAttribute("aria-label", "UAV tracking map");
        expect(mapContainer).toHaveAttribute("role", "application");
    });
});

//# sourceMappingURL=data:application/json;base64,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