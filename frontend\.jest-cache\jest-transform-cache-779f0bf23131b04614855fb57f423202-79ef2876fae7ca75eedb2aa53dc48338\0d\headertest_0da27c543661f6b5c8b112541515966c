47696d79815b07bee4221008396c5e6d
"use strict";
// Mock Next.js navigation
jest.mock("next/navigation", ()=>({
        usePathname: jest.fn()
    }));
// Mock the dashboard store
jest.mock("@/stores/dashboard-store", ()=>({
        useDashboardStore: jest.fn(),
        useConnectionStatus: jest.fn(),
        useUnacknowledgedAlerts: jest.fn()
    }));
// Mock the main nav component
jest.mock("../main-nav", ()=>({
        MainNav: ({ onNavigate })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "main-nav",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                    onClick: onNavigate,
                    "data-testid": "nav-item",
                    children: "Dashboard"
                })
            })
    }));
// Mock UI components
jest.mock("@/components/ui/sheet", ()=>({
        Sheet: ({ children, open, onOpenChange })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "sheet",
                "data-open": open,
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    onClick: ()=>onOpenChange?.(false),
                    children: children
                })
            }),
        SheetContent: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "sheet-content",
                children: children
            }),
        SheetTrigger: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "sheet-trigger",
                children: children
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _header = require("../header");
const _dashboardstore = require("../../../stores/dashboard-store");
const _navigation = require("next/navigation");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock framer-motion
(0, _testutils.mockFramerMotion)();
describe("Header Component", ()=>{
    const mockOnMenuClick = jest.fn();
    beforeEach(()=>{
        jest.clearAllMocks();
        _navigation.usePathname.mockReturnValue("/dashboard");
        _dashboardstore.useDashboardStore.mockReturnValue({
            alerts: [],
            isConnected: true
        });
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {
            onMenuClick: mockOnMenuClick
        }));
        expect(_testutils.screen.getByRole("banner")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Control")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Management System")).toBeInTheDocument();
    });
    it("displays logo and branding", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        expect(_testutils.screen.getByText("UAV Control")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Management System")).toBeInTheDocument();
        // Check for Shield icon (logo)
        const logoLink = _testutils.screen.getByRole("link", {
            name: /uav control/i
        });
        expect(logoLink).toHaveAttribute("href", "/dashboard");
    });
    it("shows mobile menu trigger", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        expect(menuButton).toBeInTheDocument();
        expect(menuButton).toHaveClass("md:hidden");
    });
    it("handles mobile menu interaction", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        const sheet = _testutils.screen.getByTestId("sheet");
        // Initially closed
        expect(sheet).toHaveAttribute("data-open", "false");
        // Open mobile menu
        _testutils.fireEvent.click(menuButton);
        expect(sheet).toHaveAttribute("data-open", "true");
    });
    it("displays search functionality", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search/i);
        expect(searchInput).toBeInTheDocument();
    });
    it("handles search form submission", ()=>{
        const consoleSpy = jest.spyOn(console, "log").mockImplementation();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search/i);
        const searchForm = searchInput.closest("form");
        _testutils.fireEvent.change(searchInput, {
            target: {
                value: "UAV-001"
            }
        });
        _testutils.fireEvent.submit(searchForm);
        expect(consoleSpy).toHaveBeenCalledWith("Searching for:", "UAV-001");
        consoleSpy.mockRestore();
    });
    it("does not search with empty query", ()=>{
        const consoleSpy = jest.spyOn(console, "log").mockImplementation();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search/i);
        const searchForm = searchInput.closest("form");
        _testutils.fireEvent.submit(searchForm);
        expect(consoleSpy).not.toHaveBeenCalled();
        consoleSpy.mockRestore();
    });
    it("displays connection status", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        // Should show connected status by default
        const connectionIndicator = _testutils.screen.getByTestId("connection-status");
        expect(connectionIndicator).toBeInTheDocument();
    });
    it("shows disconnected state", ()=>{
        _dashboardstore.useDashboardStore.mockReturnValue({
            alerts: [],
            isConnected: false
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const connectionIndicator = _testutils.screen.getByTestId("connection-status");
        expect(connectionIndicator).toHaveClass("text-destructive");
    });
    it("displays notification badge with alert count", ()=>{
        _dashboardstore.useDashboardStore.mockReturnValue({
            alerts: [
                {
                    id: "1",
                    type: "ERROR",
                    acknowledged: false
                },
                {
                    id: "2",
                    type: "WARNING",
                    acknowledged: false
                }
            ],
            isConnected: true
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const notificationButton = _testutils.screen.getByRole("button", {
            name: /notifications/i
        });
        expect(notificationButton).toBeInTheDocument();
        const badge = _testutils.screen.getByText("2");
        expect(badge).toBeInTheDocument();
    });
    it("handles dark mode toggle", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const darkModeButton = _testutils.screen.getByRole("button", {
            name: /toggle theme/i
        });
        expect(darkModeButton).toBeInTheDocument();
        _testutils.fireEvent.click(darkModeButton);
    // Dark mode toggle functionality would be tested here
    });
    it("displays user menu", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const userMenuButton = _testutils.screen.getByRole("button", {
            name: /user menu/i
        });
        expect(userMenuButton).toBeInTheDocument();
    });
    it("shows page title based on pathname", ()=>{
        _navigation.usePathname.mockReturnValue("/uavs");
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        expect(_testutils.screen.getByText("UAV Management")).toBeInTheDocument();
    });
    it("handles different pathnames correctly", ()=>{
        const pathTitleMap = [
            [
                "/dashboard",
                "Dashboard"
            ],
            [
                "/uavs",
                "UAV Management"
            ],
            [
                "/map",
                "Map View"
            ],
            [
                "/hibernate-pod",
                "Hibernate Pod"
            ],
            [
                "/battery",
                "Battery Monitor"
            ]
        ];
        pathTitleMap.forEach(([path, title])=>{
            _navigation.usePathname.mockReturnValue(path);
            const { unmount } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
            expect(_testutils.screen.getByText(title)).toBeInTheDocument();
            unmount();
        });
    });
    it("calls onMenuClick when provided", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {
            onMenuClick: mockOnMenuClick
        }));
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        _testutils.fireEvent.click(menuButton);
        expect(mockOnMenuClick).toHaveBeenCalledTimes(1);
    });
    it("maintains accessibility standards", async ()=>{
        const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        // Check for proper header role
        expect(_testutils.screen.getByRole("banner")).toBeInTheDocument();
        // Check for proper button labels
        expect(_testutils.screen.getByRole("button", {
            name: /toggle menu/i
        })).toBeInTheDocument();
        expect(_testutils.screen.getByRole("button", {
            name: /toggle theme/i
        })).toBeInTheDocument();
        // Run accessibility tests
        await (0, _testutils.runAxeTest)(container);
    });
    it("handles keyboard navigation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search/i);
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        // Test tab navigation
        searchInput.focus();
        expect(searchInput).toHaveFocus();
        menuButton.focus();
        expect(menuButton).toHaveFocus();
    });
    it("supports responsive design", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        // Check responsive classes
        const header = _testutils.screen.getByRole("banner");
        expect(header).toHaveClass("sticky", "top-0", "z-50", "w-full");
        // Mobile menu should be hidden on desktop
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        expect(menuButton).toHaveClass("md:hidden");
        // Logo text should be hidden on small screens
        const logoText = _testutils.screen.getByText("UAV Control").parentElement;
        expect(logoText).toHaveClass("hidden", "sm:block");
    });
    it("closes mobile menu when navigation occurs", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const menuButton = _testutils.screen.getByRole("button", {
            name: /toggle menu/i
        });
        const sheet = _testutils.screen.getByTestId("sheet");
        // Open mobile menu
        _testutils.fireEvent.click(menuButton);
        expect(sheet).toHaveAttribute("data-open", "true");
        // Navigate (simulate clicking nav item)
        const navItem = _testutils.screen.getByTestId("nav-item");
        _testutils.fireEvent.click(navItem);
        // Menu should close
        expect(sheet).toHaveAttribute("data-open", "false");
    });
    it("handles search input changes", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search/i);
        _testutils.fireEvent.change(searchInput, {
            target: {
                value: "test query"
            }
        });
        expect(searchInput).toHaveValue("test query");
    });
    it("displays proper backdrop blur effect", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_header.Header, {}));
        const header = _testutils.screen.getByRole("banner");
        expect(header).toHaveClass("bg-background/95", "backdrop-blur", "supports-[backdrop-filter]:bg-background/60");
    });
});

//# sourceMappingURL=data:application/json;base64,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