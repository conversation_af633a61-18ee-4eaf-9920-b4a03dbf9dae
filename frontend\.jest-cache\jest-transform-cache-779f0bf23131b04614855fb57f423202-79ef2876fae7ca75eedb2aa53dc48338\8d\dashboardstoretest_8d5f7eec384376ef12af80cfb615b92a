1353b08b303b927f662eb3398afe42ed
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _react = require("@testing-library/react");
const _dashboardstore = require("../dashboard-store");
const _testutils = require("../../lib/test-utils");
describe("Dashboard Store", ()=>{
    beforeEach(()=>{
        // Reset store state
        _dashboardstore.useDashboardStore.setState({
            metrics: null,
            flightActivity: null,
            batteryStats: null,
            hibernatePodMetrics: null,
            chartData: null,
            alerts: [],
            recentLocationUpdates: [],
            isConnected: false,
            lastUpdate: null,
            connectionError: null,
            selectedTimeRange: "24h",
            autoRefresh: true,
            refreshInterval: 30,
            showAlerts: true
        });
        // Clear all mocks
        jest.clearAllMocks();
    });
    describe("metrics management", ()=>{
        it("should update metrics", ()=>{
            const mockMetrics = {
                totalUAVs: 15,
                authorizedUAVs: 12,
                unauthorizedUAVs: 3,
                activeFlights: 5,
                hibernatingUAVs: 4,
                lowBatteryCount: 2,
                chargingCount: 3,
                maintenanceCount: 1,
                emergencyCount: 0
            };
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.updateMetrics(mockMetrics);
            });
            expect(result.current.metrics).toEqual(mockMetrics);
            expect(result.current.lastUpdate).toBeTruthy();
        });
        it("should update flight activity", ()=>{
            const mockFlightActivity = {
                activeFlights: 3,
                todayFlights: 8,
                completedFlights: 5,
                flights: [
                    {
                        id: 1,
                        uavRfid: "UAV-001",
                        missionName: "Test Mission",
                        startTime: "2024-01-01T10:00:00Z",
                        status: "ACTIVE"
                    }
                ]
            };
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.updateFlightActivity(mockFlightActivity);
            });
            expect(result.current.flightActivity).toEqual(mockFlightActivity);
        });
        it("should update battery statistics", ()=>{
            const mockBatteryStats = {
                lowBattery: 2,
                criticalBattery: 1,
                overheating: 0,
                charging: 3,
                healthy: 8
            };
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.updateBatteryStats(mockBatteryStats);
            });
            expect(result.current.batteryStats).toEqual(mockBatteryStats);
        });
    });
    describe("alerts management", ()=>{
        it("should add alert", ()=>{
            const mockAlert = (0, _testutils.createMockAlert)({
                id: 1,
                type: "WARNING",
                title: "Low Battery",
                message: "UAV-001 has low battery"
            });
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.addAlert(mockAlert);
            });
            expect(result.current.alerts).toHaveLength(1);
            expect(result.current.alerts[0]).toEqual(mockAlert);
        });
        it("should remove alert", ()=>{
            const mockAlert1 = (0, _testutils.createMockAlert)({
                id: "1",
                title: "Alert 1"
            });
            const mockAlert2 = (0, _testutils.createMockAlert)({
                id: "2",
                title: "Alert 2"
            });
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add alerts
            (0, _react.act)(()=>{
                result.current.addAlert(mockAlert1);
                result.current.addAlert(mockAlert2);
            });
            expect(result.current.alerts).toHaveLength(2);
            // Remove one alert
            (0, _react.act)(()=>{
                result.current.removeAlert("1");
            });
            expect(result.current.alerts).toHaveLength(1);
            expect(result.current.alerts[0].id).toBe("2");
        });
        it("should acknowledge alert", ()=>{
            const mockAlert = (0, _testutils.createMockAlert)({
                id: "1",
                acknowledged: false
            });
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add alert
            (0, _react.act)(()=>{
                result.current.addAlert(mockAlert);
            });
            expect(result.current.alerts[0].acknowledged).toBe(false);
            // Acknowledge alert
            (0, _react.act)(()=>{
                result.current.acknowledgeAlert("1");
            });
            expect(result.current.alerts[0].acknowledged).toBe(true);
        });
        it("should clear all alerts", ()=>{
            const mockAlert1 = (0, _testutils.createMockAlert)({
                id: 1
            });
            const mockAlert2 = (0, _testutils.createMockAlert)({
                id: 2
            });
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add alerts
            (0, _react.act)(()=>{
                result.current.addAlert(mockAlert1);
                result.current.addAlert(mockAlert2);
            });
            expect(result.current.alerts).toHaveLength(2);
            // Clear all alerts
            (0, _react.act)(()=>{
                result.current.clearAlerts();
            });
            expect(result.current.alerts).toHaveLength(0);
        });
        it("should limit alerts to maximum count", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add more than 50 alerts (assuming max is 50)
            (0, _react.act)(()=>{
                for(let i = 1; i <= 55; i++){
                    result.current.addAlert((0, _testutils.createMockAlert)({
                        id: i.toString(),
                        title: `Alert ${i}`
                    }));
                }
            });
            // Should only keep the latest 50 alerts
            expect(result.current.alerts).toHaveLength(50);
            expect(result.current.alerts[0].id).toBe("55") // First alert should be #55 (newest first)
            ;
            expect(result.current.alerts[49].id).toBe("6") // Last alert should be #6
            ;
        });
    });
    describe("location updates", ()=>{
        it("should add location update", ()=>{
            const mockLocationUpdate = {
                uavId: 1,
                rfidTag: "UAV-001",
                latitude: 40.7128,
                longitude: -74.0060,
                altitude: 100,
                timestamp: new Date().toISOString(),
                speed: 25,
                heading: 180
            };
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.addLocationUpdate(mockLocationUpdate);
            });
            expect(result.current.recentLocationUpdates).toHaveLength(1);
            expect(result.current.recentLocationUpdates[0]).toEqual(mockLocationUpdate);
        });
        it("should limit location updates to maximum count", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add more than 100 location updates (assuming max is 100)
            (0, _react.act)(()=>{
                for(let i = 1; i <= 105; i++){
                    result.current.addLocationUpdate({
                        uavId: i,
                        rfidTag: `UAV-${i.toString().padStart(3, "0")}`,
                        latitude: 40.7128 + i * 0.001,
                        longitude: -74.0060 + i * 0.001,
                        altitude: 100,
                        timestamp: new Date().toISOString(),
                        speed: 25,
                        heading: 180
                    });
                }
            });
            // Should only keep the latest 100 updates
            expect(result.current.recentLocationUpdates).toHaveLength(100);
            expect(result.current.recentLocationUpdates[0].uavId).toBe(105) // First should be #105 (newest first)
            ;
            expect(result.current.recentLocationUpdates[99].uavId).toBe(6) // Last should be #6
            ;
        });
    });
    describe("connection management", ()=>{
        it("should set connection status", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.setConnectionStatus(true);
            });
            expect(result.current.isConnected).toBe(true);
            expect(result.current.connectionError).toBeNull();
            (0, _react.act)(()=>{
                result.current.setConnectionStatus(false, "Connection lost");
            });
            expect(result.current.isConnected).toBe(false);
            expect(result.current.connectionError).toBe("Connection lost");
        });
        it("should clear connection error when reconnecting", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Set error first
            (0, _react.act)(()=>{
                result.current.setConnectionStatus(false, "Connection error");
            });
            expect(result.current.connectionError).toBe("Connection error");
            // Clear error by reconnecting
            (0, _react.act)(()=>{
                result.current.setConnectionStatus(true);
            });
            expect(result.current.connectionError).toBeNull();
        });
    });
    describe("UI settings", ()=>{
        it("should set time range", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.setTimeRange("1h");
            });
            expect(result.current.selectedTimeRange).toBe("1h");
            (0, _react.act)(()=>{
                result.current.setTimeRange("7d");
            });
            expect(result.current.selectedTimeRange).toBe("7d");
        });
        it("should set auto refresh", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            expect(result.current.autoRefresh).toBe(true);
            (0, _react.act)(()=>{
                result.current.setAutoRefresh(false);
            });
            expect(result.current.autoRefresh).toBe(false);
            (0, _react.act)(()=>{
                result.current.setAutoRefresh(true);
            });
            expect(result.current.autoRefresh).toBe(true);
        });
        it("should set refresh interval", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.setRefreshInterval(60);
            });
            expect(result.current.refreshInterval).toBe(60);
        });
        it("should set alerts visibility", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            expect(result.current.showAlerts).toBe(true);
            (0, _react.act)(()=>{
                result.current.setShowAlerts(false);
            });
            expect(result.current.showAlerts).toBe(false);
            (0, _react.act)(()=>{
                result.current.setShowAlerts(true);
            });
            expect(result.current.showAlerts).toBe(true);
        });
    });
    describe("data management", ()=>{
        it("should update last update timestamp", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            expect(result.current.lastUpdate).toBeNull();
            (0, _react.act)(()=>{
                result.current.updateLastUpdate();
            });
            expect(result.current.lastUpdate).toBeTruthy();
            expect(typeof result.current.lastUpdate).toBe("string");
        });
        it("should fetch dashboard data", async ()=>{
            // Mock fetch
            global.fetch = jest.fn().mockResolvedValue({
                ok: true,
                json: jest.fn().mockResolvedValue({
                    metrics: {
                        totalUAVs: 10,
                        authorizedUAVs: 8,
                        unauthorizedUAVs: 2,
                        activeFlights: 3,
                        hibernatingUAVs: 2,
                        lowBatteryCount: 1,
                        chargingCount: 2,
                        maintenanceCount: 1,
                        emergencyCount: 0
                    }
                })
            });
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            await (0, _react.act)(async ()=>{
                await result.current.fetchDashboardData();
            });
            expect(result.current.metrics).toBeTruthy();
            expect(result.current.lastUpdate).toBeTruthy();
            expect(result.current.connectionError).toBeNull();
        });
    });
    describe("computed selectors", ()=>{
        it("should get unacknowledged alerts using selector", ()=>{
            const { result: storeResult } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            const { result: selectorResult } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)((state)=>state.alerts.filter((alert)=>!alert.acknowledged)));
            // Add alerts with different acknowledgment status
            (0, _react.act)(()=>{
                storeResult.current.addAlert((0, _testutils.createMockAlert)({
                    id: "1",
                    acknowledged: false
                }));
                storeResult.current.addAlert((0, _testutils.createMockAlert)({
                    id: "2",
                    acknowledged: true
                }));
                storeResult.current.addAlert((0, _testutils.createMockAlert)({
                    id: "3",
                    acknowledged: false
                }));
            });
            expect(selectorResult.current).toHaveLength(2);
            expect(selectorResult.current.map((a)=>a.id)).toEqual([
                "3",
                "1"
            ]) // Newest first
            ;
        });
        it("should get critical alerts using selector", ()=>{
            const { result: storeResult } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            const { result: selectorResult } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)((state)=>state.alerts.filter((alert)=>alert.type === "CRITICAL" || alert.type === "ERROR")));
            // Add different types of alerts
            (0, _react.act)(()=>{
                storeResult.current.addAlert((0, _testutils.createMockAlert)({
                    id: "1",
                    type: "ERROR"
                }));
                storeResult.current.addAlert((0, _testutils.createMockAlert)({
                    id: "2",
                    type: "WARNING"
                }));
                storeResult.current.addAlert((0, _testutils.createMockAlert)({
                    id: "3",
                    type: "CRITICAL"
                }));
            });
            expect(selectorResult.current).toHaveLength(2);
            expect(selectorResult.current.map((a)=>a.type)).toEqual([
                "CRITICAL",
                "ERROR"
            ]);
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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