5a730f181b49cede13ee6d8e26b2f5f6
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    camelToTitle: function() {
        return camelToTitle;
    },
    capitalizeFirst: function() {
        return capitalizeFirst;
    },
    chartColors: function() {
        return chartColors;
    },
    cn: function() {
        return cn;
    },
    createSearchParams: function() {
        return createSearchParams;
    },
    debounce: function() {
        return debounce;
    },
    formatBytes: function() {
        return formatBytes;
    },
    formatDate: function() {
        return formatDate;
    },
    formatDateTime: function() {
        return formatDateTime;
    },
    formatNumber: function() {
        return formatNumber;
    },
    formatPercentage: function() {
        return formatPercentage;
    },
    formatRelativeTime: function() {
        return formatRelativeTime;
    },
    generateChartColors: function() {
        return generateChartColors;
    },
    getBatteryColor: function() {
        return getBatteryColor;
    },
    getBatteryIcon: function() {
        return getBatteryIcon;
    },
    getErrorMessage: function() {
        return getErrorMessage;
    },
    getFromStorage: function() {
        return getFromStorage;
    },
    getStatusColor: function() {
        return getStatusColor;
    },
    getStatusVariant: function() {
        return getStatusVariant;
    },
    groupBy: function() {
        return groupBy;
    },
    isValidEmail: function() {
        return isValidEmail;
    },
    isValidRFID: function() {
        return isValidRFID;
    },
    removeFromStorage: function() {
        return removeFromStorage;
    },
    setToStorage: function() {
        return setToStorage;
    },
    sortBy: function() {
        return sortBy;
    },
    truncateString: function() {
        return truncateString;
    }
});
const _clsx = require("clsx");
const _tailwindmerge = require("tailwind-merge");
function cn(...inputs) {
    return (0, _tailwindmerge.twMerge)((0, _clsx.clsx)(inputs));
}
function formatDate(date, format = "short") {
    const d = new Date(date);
    if (isNaN(d.getTime())) {
        return "Invalid Date";
    }
    switch(format){
        case "short":
            return d.toLocaleDateString();
        case "long":
            return d.toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric"
            });
        case "time":
            return d.toLocaleTimeString("en-US", {
                hour: "2-digit",
                minute: "2-digit"
            });
        default:
            return d.toLocaleDateString();
    }
}
function formatDateTime(date) {
    const d = new Date(date);
    if (isNaN(d.getTime())) {
        return "Invalid Date";
    }
    return d.toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit"
    });
}
function formatRelativeTime(date) {
    const d = new Date(date);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);
    if (diffInSeconds < 60) {
        return "Just now";
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? "s" : ""} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? "s" : ""} ago`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? "s" : ""} ago`;
    }
}
function formatNumber(num, decimals = 0) {
    return new Intl.NumberFormat("en-US", {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(num);
}
function formatPercentage(num, decimals = 1) {
    return new Intl.NumberFormat("en-US", {
        style: "percent",
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(num / 100);
}
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = [
        "Bytes",
        "KB",
        "MB",
        "GB",
        "TB"
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
}
function truncateString(str, length) {
    if (str.length <= length) return str;
    return str.slice(0, length) + "...";
}
function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}
function camelToTitle(str) {
    return str.replace(/([A-Z])/g, " $1").replace(/^./, (str)=>str.toUpperCase()).trim();
}
function getStatusColor(status) {
    const statusColors = {
        AUTHORIZED: "text-green-600 bg-green-100 border-green-200",
        UNAUTHORIZED: "text-red-600 bg-red-100 border-red-200",
        HIBERNATING: "text-purple-600 bg-purple-100 border-purple-200",
        CHARGING: "text-yellow-600 bg-yellow-100 border-yellow-200",
        MAINTENANCE: "text-orange-600 bg-orange-100 border-orange-200",
        EMERGENCY: "text-red-800 bg-red-200 border-red-300",
        READY: "text-blue-600 bg-blue-100 border-blue-200",
        IN_FLIGHT: "text-indigo-600 bg-indigo-100 border-indigo-200",
        OUT_OF_SERVICE: "text-gray-600 bg-gray-100 border-gray-200"
    };
    return statusColors[status] || "text-gray-600 bg-gray-100 border-gray-200";
}
function getStatusVariant(status) {
    const statusVariants = {
        AUTHORIZED: "success",
        UNAUTHORIZED: "destructive",
        HIBERNATING: "secondary",
        CHARGING: "warning",
        MAINTENANCE: "warning",
        EMERGENCY: "destructive",
        READY: "info",
        IN_FLIGHT: "info",
        OUT_OF_SERVICE: "secondary"
    };
    return statusVariants[status] || "secondary";
}
function getBatteryColor(percentage) {
    if (percentage > 50) return "text-green-600";
    if (percentage > 20) return "text-yellow-600";
    return "text-red-600";
}
function getBatteryIcon(percentage) {
    if (percentage > 75) return "battery-full";
    if (percentage > 50) return "battery-three-quarters";
    if (percentage > 25) return "battery-half";
    if (percentage > 10) return "battery-quarter";
    return "battery-empty";
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidRFID(rfid) {
    // RFID should be alphanumeric with hyphens and underscores, 3-50 characters
    const rfidRegex = /^[A-Za-z0-9-_]{3,50}$/;
    return rfidRegex.test(rfid);
}
function groupBy(array, key) {
    return array.reduce((groups, item)=>{
        const group = String(item[key]);
        groups[group] = groups[group] || [];
        groups[group].push(item);
        return groups;
    }, {});
}
function sortBy(array, key, direction = "asc") {
    return [
        ...array
    ].sort((a, b)=>{
        const aVal = a[key];
        const bVal = b[key];
        if (aVal < bVal) return direction === "asc" ? -1 : 1;
        if (aVal > bVal) return direction === "asc" ? 1 : -1;
        return 0;
    });
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function getFromStorage(key, defaultValue) {
    if (typeof window === "undefined") return defaultValue;
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.error(`Error reading from localStorage key "${key}":`, error);
        return defaultValue;
    }
}
function setToStorage(key, value) {
    if (typeof window === "undefined") return;
    try {
        localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
        console.error(`Error writing to localStorage key "${key}":`, error);
    }
}
function removeFromStorage(key) {
    if (typeof window === "undefined") return;
    try {
        localStorage.removeItem(key);
    } catch (error) {
        console.error(`Error removing from localStorage key "${key}":`, error);
    }
}
function createSearchParams(params) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value])=>{
        if (value !== undefined && value !== null && value !== "") {
            if (Array.isArray(value)) {
                value.forEach((item)=>searchParams.append(key, String(item)));
            } else {
                searchParams.append(key, String(value));
            }
        }
    });
    return searchParams;
}
function getErrorMessage(error) {
    if (error instanceof Error) return error.message;
    if (typeof error === "string") return error;
    return "An unknown error occurred";
}
const chartColors = {
    primary: "#3498db",
    secondary: "#2c3e50",
    success: "#2ecc71",
    warning: "#f39c12",
    danger: "#e74c3c",
    info: "#17a2b8",
    light: "#f8f9fa",
    dark: "#343a40"
};
function generateChartColors(count) {
    const baseColors = Object.values(chartColors);
    const colors = [];
    for(let i = 0; i < count; i++){
        colors.push(baseColors[i % baseColors.length]);
    }
    return colors;
}

//# sourceMappingURL=data:application/json;base64,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