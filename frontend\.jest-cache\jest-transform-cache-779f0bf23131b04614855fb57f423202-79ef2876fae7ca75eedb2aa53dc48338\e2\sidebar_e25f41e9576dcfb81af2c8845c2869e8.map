{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\layout\\sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Shield, ChevronLeft, ChevronRight } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { MainNav } from './main-nav'\nimport { cn } from '@/lib/utils'\nimport { sidebarVariants, getAnimationVariants } from '@/lib/animations'\n\ninterface SidebarProps {\n  collapsed?: boolean\n  onToggle?: () => void\n  className?: string\n}\n\nexport function Sidebar({ collapsed = false, onToggle, className }: SidebarProps) {\n  return (\n    <motion.div\n      variants={getAnimationVariants(sidebarVariants)}\n      animate={collapsed ? 'collapsed' : 'expanded'}\n      className={cn(\n        'flex flex-col h-full bg-card border-r',\n        className\n      )}\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b\">\n        <Link href=\"/dashboard\" className=\"flex items-center space-x-2\">\n          <Shield className=\"h-8 w-8 text-primary flex-shrink-0\" data-testid=\"shield-icon\" />\n          <AnimatePresence>\n            {!collapsed && (\n              <motion.div\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: -20 }}\n                transition={{ duration: 0.2 }}\n              >\n                <h1 className=\"font-orbitron font-bold text-lg text-primary\">\n                  UAV Control\n                </h1>\n                <p className=\"text-xs text-muted-foreground\">\n                  Management System\n                </p>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </Link>\n        \n        {onToggle && (\n          <motion.div\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n          >\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onToggle}\n              className=\"h-8 w-8\"\n              aria-label=\"Toggle sidebar\"\n            >\n              <motion.div\n                animate={{ rotate: collapsed ? 0 : 180 }}\n                transition={{ duration: 0.2 }}\n              >\n                {collapsed ? (\n                  <ChevronRight className=\"h-4 w-4\" />\n                ) : (\n                  <ChevronLeft className=\"h-4 w-4\" />\n                )}\n              </motion.div>\n              <span className=\"sr-only\">Toggle sidebar</span>\n            </Button>\n          </motion.div>\n        )}\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex-1 overflow-y-auto py-4\">\n        <div className={cn('px-3', collapsed && 'px-2')}>\n          <MainNav />\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t\">\n        <AnimatePresence>\n          {!collapsed && (\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: 10 }}\n              transition={{ duration: 0.2, delay: 0.1 }}\n              className=\"text-xs text-muted-foreground\"\n            >\n              <p>Version 1.0.0</p>\n              <p>© 2024 UAV Systems</p>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.div>\n  )\n}\n"], "names": ["Sidebar", "collapsed", "onToggle", "className", "motion", "div", "variants", "getAnimationVariants", "sidebarVariants", "animate", "cn", "Link", "href", "Shield", "data-testid", "AnimatePresence", "initial", "opacity", "x", "exit", "transition", "duration", "h1", "p", "whileHover", "scale", "whileTap", "<PERSON><PERSON>", "variant", "size", "onClick", "aria-label", "rotate", "ChevronRight", "ChevronLeft", "span", "MainNav", "y", "delay"], "mappings": "AAAA;;;;;+BAiBgBA;;;eAAAA;;;;8DAfE;6DACD;8BACuB;6BACU;wBAC3B;yBACC;uBACL;4BACmC;;;;;;AAQ/C,SAASA,QAAQ,EAAEC,YAAY,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAgB;IAC9E,qBACE,sBAACC,oBAAM,CAACC,GAAG;QACTC,UAAUC,IAAAA,gCAAoB,EAACC,2BAAe;QAC9CC,SAASR,YAAY,cAAc;QACnCE,WAAWO,IAAAA,SAAE,EACX,yCACAP;;0BAIF,sBAACE;gBAAIF,WAAU;;kCACb,sBAACQ,aAAI;wBAACC,MAAK;wBAAaT,WAAU;;0CAChC,qBAACU,mBAAM;gCAACV,WAAU;gCAAqCW,eAAY;;0CACnE,qBAACC,6BAAe;0CACb,CAACd,2BACA,sBAACG,oBAAM,CAACC,GAAG;oCACTW,SAAS;wCAAEC,SAAS;wCAAGC,GAAG,CAAC;oCAAG;oCAC9BT,SAAS;wCAAEQ,SAAS;wCAAGC,GAAG;oCAAE;oCAC5BC,MAAM;wCAAEF,SAAS;wCAAGC,GAAG,CAAC;oCAAG;oCAC3BE,YAAY;wCAAEC,UAAU;oCAAI;;sDAE5B,qBAACC;4CAAGnB,WAAU;sDAA+C;;sDAG7D,qBAACoB;4CAAEpB,WAAU;sDAAgC;;;;;;;oBAQpDD,0BACC,qBAACE,oBAAM,CAACC,GAAG;wBACTmB,YAAY;4BAAEC,OAAO;wBAAI;wBACzBC,UAAU;4BAAED,OAAO;wBAAI;kCAEvB,cAAA,sBAACE,cAAM;4BACLC,SAAQ;4BACRC,MAAK;4BACLC,SAAS5B;4BACTC,WAAU;4BACV4B,cAAW;;8CAEX,qBAAC3B,oBAAM,CAACC,GAAG;oCACTI,SAAS;wCAAEuB,QAAQ/B,YAAY,IAAI;oCAAI;oCACvCmB,YAAY;wCAAEC,UAAU;oCAAI;8CAE3BpB,0BACC,qBAACgC,yBAAY;wCAAC9B,WAAU;uDAExB,qBAAC+B,wBAAW;wCAAC/B,WAAU;;;8CAG3B,qBAACgC;oCAAKhC,WAAU;8CAAU;;;;;;;0BAOlC,qBAACE;gBAAIF,WAAU;0BACb,cAAA,qBAACE;oBAAIF,WAAWO,IAAAA,SAAE,EAAC,QAAQT,aAAa;8BACtC,cAAA,qBAACmC,gBAAO;;;0BAKZ,qBAAC/B;gBAAIF,WAAU;0BACb,cAAA,qBAACY,6BAAe;8BACb,CAACd,2BACA,sBAACG,oBAAM,CAACC,GAAG;wBACTW,SAAS;4BAAEC,SAAS;4BAAGoB,GAAG;wBAAG;wBAC7B5B,SAAS;4BAAEQ,SAAS;4BAAGoB,GAAG;wBAAE;wBAC5BlB,MAAM;4BAAEF,SAAS;4BAAGoB,GAAG;wBAAG;wBAC1BjB,YAAY;4BAAEC,UAAU;4BAAKiB,OAAO;wBAAI;wBACxCnC,WAAU;;0CAEV,qBAACoB;0CAAE;;0CACH,qBAACA;0CAAE;;;;;;;;AAOjB"}