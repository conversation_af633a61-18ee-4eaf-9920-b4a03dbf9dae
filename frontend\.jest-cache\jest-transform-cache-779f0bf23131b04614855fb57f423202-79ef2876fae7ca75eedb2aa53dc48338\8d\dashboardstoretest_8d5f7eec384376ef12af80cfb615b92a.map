{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\stores\\__tests__\\dashboard-store.test.ts"], "sourcesContent": ["import { renderHook, act } from '@testing-library/react'\nimport { useDashboardStore } from '../dashboard-store'\nimport { createMockAlert, createMockUAV } from '@/lib/test-utils'\nimport { DashboardMetrics, FlightActivity, BatteryStatistics } from '../dashboard-store'\nimport { SystemAlert, UAVLocationUpdate } from '@/types/uav'\n\ndescribe('Dashboard Store', () => {\n  beforeEach(() => {\n    // Reset store state\n    useDashboardStore.setState({\n      metrics: null,\n      flightActivity: null,\n      batteryStats: null,\n      hibernatePodMetrics: null,\n      chartData: null,\n      alerts: [],\n      recentLocationUpdates: [],\n      isConnected: false,\n      lastUpdate: null,\n      connectionError: null,\n      selectedTimeRange: '24h',\n      autoRefresh: true,\n      refreshInterval: 30,\n      showAlerts: true,\n    })\n    \n    // Clear all mocks\n    jest.clearAllMocks()\n  })\n\n  describe('metrics management', () => {\n    it('should update metrics', () => {\n      const mockMetrics: DashboardMetrics = {\n        totalUAVs: 15,\n        authorizedUAVs: 12,\n        unauthorizedUAVs: 3,\n        activeFlights: 5,\n        hibernatingUAVs: 4,\n        lowBatteryCount: 2,\n        chargingCount: 3,\n        maintenanceCount: 1,\n        emergencyCount: 0,\n      }\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.updateMetrics(mockMetrics)\n      })\n\n      expect(result.current.metrics).toEqual(mockMetrics)\n      expect(result.current.lastUpdate).toBeTruthy()\n    })\n\n    it('should update flight activity', () => {\n      const mockFlightActivity: FlightActivity = {\n        activeFlights: 3,\n        todayFlights: 8,\n        completedFlights: 5,\n        flights: [\n          {\n            id: 1,\n            uavRfid: 'UAV-001',\n            missionName: 'Test Mission',\n            startTime: '2024-01-01T10:00:00Z',\n            status: 'ACTIVE',\n          },\n        ],\n      }\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.updateFlightActivity(mockFlightActivity)\n      })\n\n      expect(result.current.flightActivity).toEqual(mockFlightActivity)\n    })\n\n    it('should update battery statistics', () => {\n      const mockBatteryStats: BatteryStatistics = {\n        lowBattery: 2,\n        criticalBattery: 1,\n        overheating: 0,\n        charging: 3,\n        healthy: 8,\n      }\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.updateBatteryStats(mockBatteryStats)\n      })\n\n      expect(result.current.batteryStats).toEqual(mockBatteryStats)\n    })\n  })\n\n  describe('alerts management', () => {\n    it('should add alert', () => {\n      const mockAlert = createMockAlert({\n        id: 1,\n        type: 'WARNING',\n        title: 'Low Battery',\n        message: 'UAV-001 has low battery',\n      })\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.addAlert(mockAlert)\n      })\n\n      expect(result.current.alerts).toHaveLength(1)\n      expect(result.current.alerts[0]).toEqual(mockAlert)\n    })\n\n    it('should remove alert', () => {\n      const mockAlert1 = createMockAlert({ id: '1', title: 'Alert 1' })\n      const mockAlert2 = createMockAlert({ id: '2', title: 'Alert 2' })\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add alerts\n      act(() => {\n        result.current.addAlert(mockAlert1)\n        result.current.addAlert(mockAlert2)\n      })\n\n      expect(result.current.alerts).toHaveLength(2)\n\n      // Remove one alert\n      act(() => {\n        result.current.removeAlert('1')\n      })\n\n      expect(result.current.alerts).toHaveLength(1)\n      expect(result.current.alerts[0].id).toBe('2')\n    })\n\n    it('should acknowledge alert', () => {\n      const mockAlert = createMockAlert({\n        id: '1',\n        acknowledged: false,\n      })\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add alert\n      act(() => {\n        result.current.addAlert(mockAlert)\n      })\n\n      expect(result.current.alerts[0].acknowledged).toBe(false)\n\n      // Acknowledge alert\n      act(() => {\n        result.current.acknowledgeAlert('1')\n      })\n\n      expect(result.current.alerts[0].acknowledged).toBe(true)\n    })\n\n    it('should clear all alerts', () => {\n      const mockAlert1 = createMockAlert({ id: 1 })\n      const mockAlert2 = createMockAlert({ id: 2 })\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add alerts\n      act(() => {\n        result.current.addAlert(mockAlert1)\n        result.current.addAlert(mockAlert2)\n      })\n\n      expect(result.current.alerts).toHaveLength(2)\n\n      // Clear all alerts\n      act(() => {\n        result.current.clearAlerts()\n      })\n\n      expect(result.current.alerts).toHaveLength(0)\n    })\n\n    it('should limit alerts to maximum count', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add more than 50 alerts (assuming max is 50)\n      act(() => {\n        for (let i = 1; i <= 55; i++) {\n          result.current.addAlert(createMockAlert({ id: i.toString(), title: `Alert ${i}` }))\n        }\n      })\n\n      // Should only keep the latest 50 alerts\n      expect(result.current.alerts).toHaveLength(50)\n      expect(result.current.alerts[0].id).toBe('55') // First alert should be #55 (newest first)\n      expect(result.current.alerts[49].id).toBe('6') // Last alert should be #6\n    })\n  })\n\n  describe('location updates', () => {\n    it('should add location update', () => {\n      const mockLocationUpdate: UAVLocationUpdate = {\n        uavId: 1,\n        rfidTag: 'UAV-001',\n        latitude: 40.7128,\n        longitude: -74.0060,\n        altitude: 100,\n        timestamp: new Date().toISOString(),\n        speed: 25,\n        heading: 180,\n      }\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.addLocationUpdate(mockLocationUpdate)\n      })\n\n      expect(result.current.recentLocationUpdates).toHaveLength(1)\n      expect(result.current.recentLocationUpdates[0]).toEqual(mockLocationUpdate)\n    })\n\n    it('should limit location updates to maximum count', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add more than 100 location updates (assuming max is 100)\n      act(() => {\n        for (let i = 1; i <= 105; i++) {\n          result.current.addLocationUpdate({\n            uavId: i,\n            rfidTag: `UAV-${i.toString().padStart(3, '0')}`,\n            latitude: 40.7128 + i * 0.001,\n            longitude: -74.0060 + i * 0.001,\n            altitude: 100,\n            timestamp: new Date().toISOString(),\n            speed: 25,\n            heading: 180,\n          })\n        }\n      })\n\n      // Should only keep the latest 100 updates\n      expect(result.current.recentLocationUpdates).toHaveLength(100)\n      expect(result.current.recentLocationUpdates[0].uavId).toBe(105) // First should be #105 (newest first)\n      expect(result.current.recentLocationUpdates[99].uavId).toBe(6) // Last should be #6\n    })\n  })\n\n  describe('connection management', () => {\n    it('should set connection status', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.setConnectionStatus(true)\n      })\n\n      expect(result.current.isConnected).toBe(true)\n      expect(result.current.connectionError).toBeNull()\n\n      act(() => {\n        result.current.setConnectionStatus(false, 'Connection lost')\n      })\n\n      expect(result.current.isConnected).toBe(false)\n      expect(result.current.connectionError).toBe('Connection lost')\n    })\n\n    it('should clear connection error when reconnecting', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Set error first\n      act(() => {\n        result.current.setConnectionStatus(false, 'Connection error')\n      })\n\n      expect(result.current.connectionError).toBe('Connection error')\n\n      // Clear error by reconnecting\n      act(() => {\n        result.current.setConnectionStatus(true)\n      })\n\n      expect(result.current.connectionError).toBeNull()\n    })\n  })\n\n  describe('UI settings', () => {\n    it('should set time range', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.setTimeRange('1h')\n      })\n\n      expect(result.current.selectedTimeRange).toBe('1h')\n\n      act(() => {\n        result.current.setTimeRange('7d')\n      })\n\n      expect(result.current.selectedTimeRange).toBe('7d')\n    })\n\n    it('should set auto refresh', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      expect(result.current.autoRefresh).toBe(true)\n\n      act(() => {\n        result.current.setAutoRefresh(false)\n      })\n\n      expect(result.current.autoRefresh).toBe(false)\n\n      act(() => {\n        result.current.setAutoRefresh(true)\n      })\n\n      expect(result.current.autoRefresh).toBe(true)\n    })\n\n    it('should set refresh interval', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.setRefreshInterval(60)\n      })\n\n      expect(result.current.refreshInterval).toBe(60)\n    })\n\n    it('should set alerts visibility', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      expect(result.current.showAlerts).toBe(true)\n\n      act(() => {\n        result.current.setShowAlerts(false)\n      })\n\n      expect(result.current.showAlerts).toBe(false)\n\n      act(() => {\n        result.current.setShowAlerts(true)\n      })\n\n      expect(result.current.showAlerts).toBe(true)\n    })\n  })\n\n  describe('data management', () => {\n    it('should update last update timestamp', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      expect(result.current.lastUpdate).toBeNull()\n\n      act(() => {\n        result.current.updateLastUpdate()\n      })\n\n      expect(result.current.lastUpdate).toBeTruthy()\n      expect(typeof result.current.lastUpdate).toBe('string')\n    })\n\n    it('should fetch dashboard data', async () => {\n      // Mock fetch\n      global.fetch = jest.fn().mockResolvedValue({\n        ok: true,\n        json: jest.fn().mockResolvedValue({\n          metrics: {\n            totalUAVs: 10,\n            authorizedUAVs: 8,\n            unauthorizedUAVs: 2,\n            activeFlights: 3,\n            hibernatingUAVs: 2,\n            lowBatteryCount: 1,\n            chargingCount: 2,\n            maintenanceCount: 1,\n            emergencyCount: 0,\n          },\n        }),\n      })\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      await act(async () => {\n        await result.current.fetchDashboardData()\n      })\n\n      expect(result.current.metrics).toBeTruthy()\n      expect(result.current.lastUpdate).toBeTruthy()\n      expect(result.current.connectionError).toBeNull()\n    })\n  })\n\n  describe('computed selectors', () => {\n    it('should get unacknowledged alerts using selector', () => {\n      const { result: storeResult } = renderHook(() => useDashboardStore())\n      const { result: selectorResult } = renderHook(() => useDashboardStore((state) =>\n        state.alerts.filter(alert => !alert.acknowledged)\n      ))\n\n      // Add alerts with different acknowledgment status\n      act(() => {\n        storeResult.current.addAlert(createMockAlert({ id: '1', acknowledged: false }))\n        storeResult.current.addAlert(createMockAlert({ id: '2', acknowledged: true }))\n        storeResult.current.addAlert(createMockAlert({ id: '3', acknowledged: false }))\n      })\n\n      expect(selectorResult.current).toHaveLength(2)\n      expect(selectorResult.current.map(a => a.id)).toEqual(['3', '1']) // Newest first\n    })\n\n    it('should get critical alerts using selector', () => {\n      const { result: storeResult } = renderHook(() => useDashboardStore())\n      const { result: selectorResult } = renderHook(() => useDashboardStore((state) =>\n        state.alerts.filter(alert => alert.type === 'CRITICAL' || alert.type === 'ERROR')\n      ))\n\n      // Add different types of alerts\n      act(() => {\n        storeResult.current.addAlert(createMockAlert({ id: '1', type: 'ERROR' }))\n        storeResult.current.addAlert(createMockAlert({ id: '2', type: 'WARNING' }))\n        storeResult.current.addAlert(createMockAlert({ id: '3', type: 'CRITICAL' }))\n      })\n\n      expect(selectorResult.current).toHaveLength(2)\n      expect(selectorResult.current.map(a => a.type)).toEqual(['CRITICAL', 'ERROR'])\n    })\n  })\n})\n"], "names": ["describe", "beforeEach", "useDashboardStore", "setState", "metrics", "flightActivity", "batteryStats", "hibernatePodMetrics", "chartData", "alerts", "recentLocationUpdates", "isConnected", "lastUpdate", "connectionError", "selectedTimeRange", "autoRefresh", "refreshInterval", "show<PERSON><PERSON><PERSON>", "jest", "clearAllMocks", "it", "mockMetrics", "totalUAVs", "authorizedUAVs", "unauthorizedUAVs", "activeFlights", "hibernatingUAVs", "lowBatteryCount", "chargingCount", "maintenanceCount", "emergencyCount", "result", "renderHook", "act", "current", "updateMetrics", "expect", "toEqual", "toBeTruthy", "mockFlightActivity", "todayFlights", "completedFlights", "flights", "id", "uavRfid", "missionName", "startTime", "status", "updateFlightActivity", "mockBatteryStats", "lowBattery", "criticalBattery", "overheating", "charging", "healthy", "updateBatteryStats", "<PERSON><PERSON><PERSON><PERSON>", "createMockAlert", "type", "title", "message", "add<PERSON><PERSON><PERSON>", "toHave<PERSON>ength", "mockAlert1", "mockAlert2", "<PERSON><PERSON><PERSON><PERSON>", "toBe", "acknowledged", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "i", "toString", "mockLocationUpdate", "uavId", "rfidTag", "latitude", "longitude", "altitude", "timestamp", "Date", "toISOString", "speed", "heading", "addLocationUpdate", "padStart", "setConnectionStatus", "toBeNull", "setTimeRange", "setAutoRefresh", "setRefreshInterval", "setShow<PERSON>ler<PERSON>", "updateLastUpdate", "global", "fetch", "fn", "mockResolvedValue", "ok", "json", "fetchDashboardData", "storeResult", "selector<PERSON><PERSON><PERSON>", "state", "filter", "alert", "map", "a"], "mappings": ";;;;uBAAgC;gCACE;2BACa;AAI/CA,SAAS,mBAAmB;IAC1BC,WAAW;QACT,oBAAoB;QACpBC,iCAAiB,CAACC,QAAQ,CAAC;YACzBC,SAAS;YACTC,gBAAgB;YAChBC,cAAc;YACdC,qBAAqB;YACrBC,WAAW;YACXC,QAAQ,EAAE;YACVC,uBAAuB,EAAE;YACzBC,aAAa;YACbC,YAAY;YACZC,iBAAiB;YACjBC,mBAAmB;YACnBC,aAAa;YACbC,iBAAiB;YACjBC,YAAY;QACd;QAEA,kBAAkB;QAClBC,KAAKC,aAAa;IACpB;IAEAnB,SAAS,sBAAsB;QAC7BoB,GAAG,yBAAyB;YAC1B,MAAMC,cAAgC;gBACpCC,WAAW;gBACXC,gBAAgB;gBAChBC,kBAAkB;gBAClBC,eAAe;gBACfC,iBAAiB;gBACjBC,iBAAiB;gBACjBC,eAAe;gBACfC,kBAAkB;gBAClBC,gBAAgB;YAClB;YAEA,MAAM,EAAEC,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACC,aAAa,CAACd;YAC/B;YAEAe,OAAOL,OAAOG,OAAO,CAAC9B,OAAO,EAAEiC,OAAO,CAAChB;YACvCe,OAAOL,OAAOG,OAAO,CAACtB,UAAU,EAAE0B,UAAU;QAC9C;QAEAlB,GAAG,iCAAiC;YAClC,MAAMmB,qBAAqC;gBACzCd,eAAe;gBACfe,cAAc;gBACdC,kBAAkB;gBAClBC,SAAS;oBACP;wBACEC,IAAI;wBACJC,SAAS;wBACTC,aAAa;wBACbC,WAAW;wBACXC,QAAQ;oBACV;iBACD;YACH;YAEA,MAAM,EAAEhB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACc,oBAAoB,CAACT;YACtC;YAEAH,OAAOL,OAAOG,OAAO,CAAC7B,cAAc,EAAEgC,OAAO,CAACE;QAChD;QAEAnB,GAAG,oCAAoC;YACrC,MAAM6B,mBAAsC;gBAC1CC,YAAY;gBACZC,iBAAiB;gBACjBC,aAAa;gBACbC,UAAU;gBACVC,SAAS;YACX;YAEA,MAAM,EAAEvB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACqB,kBAAkB,CAACN;YACpC;YAEAb,OAAOL,OAAOG,OAAO,CAAC5B,YAAY,EAAE+B,OAAO,CAACY;QAC9C;IACF;IAEAjD,SAAS,qBAAqB;QAC5BoB,GAAG,oBAAoB;YACrB,MAAMoC,YAAYC,IAAAA,0BAAe,EAAC;gBAChCd,IAAI;gBACJe,MAAM;gBACNC,OAAO;gBACPC,SAAS;YACX;YAEA,MAAM,EAAE7B,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAAC2B,QAAQ,CAACL;YAC1B;YAEApB,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEqD,YAAY,CAAC;YAC3C1B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,EAAE,EAAE4B,OAAO,CAACmB;QAC3C;QAEApC,GAAG,uBAAuB;YACxB,MAAM2C,aAAaN,IAAAA,0BAAe,EAAC;gBAAEd,IAAI;gBAAKgB,OAAO;YAAU;YAC/D,MAAMK,aAAaP,IAAAA,0BAAe,EAAC;gBAAEd,IAAI;gBAAKgB,OAAO;YAAU;YAE/D,MAAM,EAAE5B,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,aAAa;YACb+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAAC2B,QAAQ,CAACE;gBACxBhC,OAAOG,OAAO,CAAC2B,QAAQ,CAACG;YAC1B;YAEA5B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEqD,YAAY,CAAC;YAE3C,mBAAmB;YACnB7B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAAC+B,WAAW,CAAC;YAC7B;YAEA7B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEqD,YAAY,CAAC;YAC3C1B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,EAAE,CAACkC,EAAE,EAAEuB,IAAI,CAAC;QAC3C;QAEA9C,GAAG,4BAA4B;YAC7B,MAAMoC,YAAYC,IAAAA,0BAAe,EAAC;gBAChCd,IAAI;gBACJwB,cAAc;YAChB;YAEA,MAAM,EAAEpC,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,YAAY;YACZ+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAAC2B,QAAQ,CAACL;YAC1B;YAEApB,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,EAAE,CAAC0D,YAAY,EAAED,IAAI,CAAC;YAEnD,oBAAoB;YACpBjC,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACkC,gBAAgB,CAAC;YAClC;YAEAhC,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,EAAE,CAAC0D,YAAY,EAAED,IAAI,CAAC;QACrD;QAEA9C,GAAG,2BAA2B;YAC5B,MAAM2C,aAAaN,IAAAA,0BAAe,EAAC;gBAAEd,IAAI;YAAE;YAC3C,MAAMqB,aAAaP,IAAAA,0BAAe,EAAC;gBAAEd,IAAI;YAAE;YAE3C,MAAM,EAAEZ,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,aAAa;YACb+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAAC2B,QAAQ,CAACE;gBACxBhC,OAAOG,OAAO,CAAC2B,QAAQ,CAACG;YAC1B;YAEA5B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEqD,YAAY,CAAC;YAE3C,mBAAmB;YACnB7B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACmC,WAAW;YAC5B;YAEAjC,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEqD,YAAY,CAAC;QAC7C;QAEA1C,GAAG,wCAAwC;YACzC,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,+CAA+C;YAC/C+B,IAAAA,UAAG,EAAC;gBACF,IAAK,IAAIqC,IAAI,GAAGA,KAAK,IAAIA,IAAK;oBAC5BvC,OAAOG,OAAO,CAAC2B,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;wBAAEd,IAAI2B,EAAEC,QAAQ;wBAAIZ,OAAO,CAAC,MAAM,EAAEW,EAAE,CAAC;oBAAC;gBAClF;YACF;YAEA,wCAAwC;YACxClC,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEqD,YAAY,CAAC;YAC3C1B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,EAAE,CAACkC,EAAE,EAAEuB,IAAI,CAAC,MAAM,2CAA2C;;YAC1F9B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,GAAG,CAACkC,EAAE,EAAEuB,IAAI,CAAC,KAAK,0BAA0B;;QAC3E;IACF;IAEAlE,SAAS,oBAAoB;QAC3BoB,GAAG,8BAA8B;YAC/B,MAAMoD,qBAAwC;gBAC5CC,OAAO;gBACPC,SAAS;gBACTC,UAAU;gBACVC,WAAW,CAAC;gBACZC,UAAU;gBACVC,WAAW,IAAIC,OAAOC,WAAW;gBACjCC,OAAO;gBACPC,SAAS;YACX;YAEA,MAAM,EAAEnD,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACiD,iBAAiB,CAACX;YACnC;YAEApC,OAAOL,OAAOG,OAAO,CAACxB,qBAAqB,EAAEoD,YAAY,CAAC;YAC1D1B,OAAOL,OAAOG,OAAO,CAACxB,qBAAqB,CAAC,EAAE,EAAE2B,OAAO,CAACmC;QAC1D;QAEApD,GAAG,kDAAkD;YACnD,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,2DAA2D;YAC3D+B,IAAAA,UAAG,EAAC;gBACF,IAAK,IAAIqC,IAAI,GAAGA,KAAK,KAAKA,IAAK;oBAC7BvC,OAAOG,OAAO,CAACiD,iBAAiB,CAAC;wBAC/BV,OAAOH;wBACPI,SAAS,CAAC,IAAI,EAAEJ,EAAEC,QAAQ,GAAGa,QAAQ,CAAC,GAAG,KAAK,CAAC;wBAC/CT,UAAU,UAAUL,IAAI;wBACxBM,WAAW,CAAC,UAAUN,IAAI;wBAC1BO,UAAU;wBACVC,WAAW,IAAIC,OAAOC,WAAW;wBACjCC,OAAO;wBACPC,SAAS;oBACX;gBACF;YACF;YAEA,0CAA0C;YAC1C9C,OAAOL,OAAOG,OAAO,CAACxB,qBAAqB,EAAEoD,YAAY,CAAC;YAC1D1B,OAAOL,OAAOG,OAAO,CAACxB,qBAAqB,CAAC,EAAE,CAAC+D,KAAK,EAAEP,IAAI,CAAC,KAAK,sCAAsC;;YACtG9B,OAAOL,OAAOG,OAAO,CAACxB,qBAAqB,CAAC,GAAG,CAAC+D,KAAK,EAAEP,IAAI,CAAC,GAAG,oBAAoB;;QACrF;IACF;IAEAlE,SAAS,yBAAyB;QAChCoB,GAAG,gCAAgC;YACjC,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACmD,mBAAmB,CAAC;YACrC;YAEAjD,OAAOL,OAAOG,OAAO,CAACvB,WAAW,EAAEuD,IAAI,CAAC;YACxC9B,OAAOL,OAAOG,OAAO,CAACrB,eAAe,EAAEyE,QAAQ;YAE/CrD,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACmD,mBAAmB,CAAC,OAAO;YAC5C;YAEAjD,OAAOL,OAAOG,OAAO,CAACvB,WAAW,EAAEuD,IAAI,CAAC;YACxC9B,OAAOL,OAAOG,OAAO,CAACrB,eAAe,EAAEqD,IAAI,CAAC;QAC9C;QAEA9C,GAAG,mDAAmD;YACpD,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,kBAAkB;YAClB+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACmD,mBAAmB,CAAC,OAAO;YAC5C;YAEAjD,OAAOL,OAAOG,OAAO,CAACrB,eAAe,EAAEqD,IAAI,CAAC;YAE5C,8BAA8B;YAC9BjC,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACmD,mBAAmB,CAAC;YACrC;YAEAjD,OAAOL,OAAOG,OAAO,CAACrB,eAAe,EAAEyE,QAAQ;QACjD;IACF;IAEAtF,SAAS,eAAe;QACtBoB,GAAG,yBAAyB;YAC1B,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACqD,YAAY,CAAC;YAC9B;YAEAnD,OAAOL,OAAOG,OAAO,CAACpB,iBAAiB,EAAEoD,IAAI,CAAC;YAE9CjC,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACqD,YAAY,CAAC;YAC9B;YAEAnD,OAAOL,OAAOG,OAAO,CAACpB,iBAAiB,EAAEoD,IAAI,CAAC;QAChD;QAEA9C,GAAG,2BAA2B;YAC5B,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErDkC,OAAOL,OAAOG,OAAO,CAACnB,WAAW,EAAEmD,IAAI,CAAC;YAExCjC,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACsD,cAAc,CAAC;YAChC;YAEApD,OAAOL,OAAOG,OAAO,CAACnB,WAAW,EAAEmD,IAAI,CAAC;YAExCjC,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACsD,cAAc,CAAC;YAChC;YAEApD,OAAOL,OAAOG,OAAO,CAACnB,WAAW,EAAEmD,IAAI,CAAC;QAC1C;QAEA9C,GAAG,+BAA+B;YAChC,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACuD,kBAAkB,CAAC;YACpC;YAEArD,OAAOL,OAAOG,OAAO,CAAClB,eAAe,EAAEkD,IAAI,CAAC;QAC9C;QAEA9C,GAAG,gCAAgC;YACjC,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErDkC,OAAOL,OAAOG,OAAO,CAACjB,UAAU,EAAEiD,IAAI,CAAC;YAEvCjC,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACwD,aAAa,CAAC;YAC/B;YAEAtD,OAAOL,OAAOG,OAAO,CAACjB,UAAU,EAAEiD,IAAI,CAAC;YAEvCjC,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACwD,aAAa,CAAC;YAC/B;YAEAtD,OAAOL,OAAOG,OAAO,CAACjB,UAAU,EAAEiD,IAAI,CAAC;QACzC;IACF;IAEAlE,SAAS,mBAAmB;QAC1BoB,GAAG,uCAAuC;YACxC,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErDkC,OAAOL,OAAOG,OAAO,CAACtB,UAAU,EAAE0E,QAAQ;YAE1CrD,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACyD,gBAAgB;YACjC;YAEAvD,OAAOL,OAAOG,OAAO,CAACtB,UAAU,EAAE0B,UAAU;YAC5CF,OAAO,OAAOL,OAAOG,OAAO,CAACtB,UAAU,EAAEsD,IAAI,CAAC;QAChD;QAEA9C,GAAG,+BAA+B;YAChC,aAAa;YACbwE,OAAOC,KAAK,GAAG3E,KAAK4E,EAAE,GAAGC,iBAAiB,CAAC;gBACzCC,IAAI;gBACJC,MAAM/E,KAAK4E,EAAE,GAAGC,iBAAiB,CAAC;oBAChC3F,SAAS;wBACPkB,WAAW;wBACXC,gBAAgB;wBAChBC,kBAAkB;wBAClBC,eAAe;wBACfC,iBAAiB;wBACjBC,iBAAiB;wBACjBC,eAAe;wBACfC,kBAAkB;wBAClBC,gBAAgB;oBAClB;gBACF;YACF;YAEA,MAAM,EAAEC,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,MAAM+B,IAAAA,UAAG,EAAC;gBACR,MAAMF,OAAOG,OAAO,CAACgE,kBAAkB;YACzC;YAEA9D,OAAOL,OAAOG,OAAO,CAAC9B,OAAO,EAAEkC,UAAU;YACzCF,OAAOL,OAAOG,OAAO,CAACtB,UAAU,EAAE0B,UAAU;YAC5CF,OAAOL,OAAOG,OAAO,CAACrB,eAAe,EAAEyE,QAAQ;QACjD;IACF;IAEAtF,SAAS,sBAAsB;QAC7BoB,GAAG,mDAAmD;YACpD,MAAM,EAAEW,QAAQoE,WAAW,EAAE,GAAGnE,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAClE,MAAM,EAAE6B,QAAQqE,cAAc,EAAE,GAAGpE,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB,EAAC,CAACmG,QACrEA,MAAM5F,MAAM,CAAC6F,MAAM,CAACC,CAAAA,QAAS,CAACA,MAAMpC,YAAY;YAGlD,kDAAkD;YAClDlC,IAAAA,UAAG,EAAC;gBACFkE,YAAYjE,OAAO,CAAC2B,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEd,IAAI;oBAAKwB,cAAc;gBAAM;gBAC5EgC,YAAYjE,OAAO,CAAC2B,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEd,IAAI;oBAAKwB,cAAc;gBAAK;gBAC3EgC,YAAYjE,OAAO,CAAC2B,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEd,IAAI;oBAAKwB,cAAc;gBAAM;YAC9E;YAEA/B,OAAOgE,eAAelE,OAAO,EAAE4B,YAAY,CAAC;YAC5C1B,OAAOgE,eAAelE,OAAO,CAACsE,GAAG,CAACC,CAAAA,IAAKA,EAAE9D,EAAE,GAAGN,OAAO,CAAC;gBAAC;gBAAK;aAAI,EAAE,eAAe;;QACnF;QAEAjB,GAAG,6CAA6C;YAC9C,MAAM,EAAEW,QAAQoE,WAAW,EAAE,GAAGnE,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAClE,MAAM,EAAE6B,QAAQqE,cAAc,EAAE,GAAGpE,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB,EAAC,CAACmG,QACrEA,MAAM5F,MAAM,CAAC6F,MAAM,CAACC,CAAAA,QAASA,MAAM7C,IAAI,KAAK,cAAc6C,MAAM7C,IAAI,KAAK;YAG3E,gCAAgC;YAChCzB,IAAAA,UAAG,EAAC;gBACFkE,YAAYjE,OAAO,CAAC2B,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEd,IAAI;oBAAKe,MAAM;gBAAQ;gBACtEyC,YAAYjE,OAAO,CAAC2B,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEd,IAAI;oBAAKe,MAAM;gBAAU;gBACxEyC,YAAYjE,OAAO,CAAC2B,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEd,IAAI;oBAAKe,MAAM;gBAAW;YAC3E;YAEAtB,OAAOgE,eAAelE,OAAO,EAAE4B,YAAY,CAAC;YAC5C1B,OAAOgE,eAAelE,OAAO,CAACsE,GAAG,CAACC,CAAAA,IAAKA,EAAE/C,IAAI,GAAGrB,OAAO,CAAC;gBAAC;gBAAY;aAAQ;QAC/E;IACF;AACF"}