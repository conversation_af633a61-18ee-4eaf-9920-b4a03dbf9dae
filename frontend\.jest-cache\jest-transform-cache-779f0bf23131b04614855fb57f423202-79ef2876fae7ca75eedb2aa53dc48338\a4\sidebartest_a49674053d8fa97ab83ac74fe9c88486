c3e689707e29d8a2412f0a5e3a35c3c7
"use strict";
// Mock the main nav component
jest.mock("../main-nav", ()=>({
        MainNav: ()=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": "main-nav",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("a", {
                        href: "/dashboard",
                        children: "Dashboard"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("a", {
                        href: "/uavs",
                        children: "UAVs"
                    })
                ]
            })
    }));
// Mock animations
jest.mock("@/lib/animations", ()=>({
        sidebarVariants: {
            expanded: {
                width: 280
            },
            collapsed: {
                width: 80
            }
        },
        getAnimationVariants: jest.fn((variants)=>variants)
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _sidebar = require("../sidebar");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock framer-motion
(0, _testutils.mockFramerMotion)();
describe("Sidebar Component", ()=>{
    const mockOnToggle = jest.fn();
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {}));
        expect(_testutils.screen.getByTestId("main-nav")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Control")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Management System")).toBeInTheDocument();
    });
    it("displays logo and branding when expanded", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: false
        }));
        expect(_testutils.screen.getByText("UAV Control")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Management System")).toBeInTheDocument();
    });
    it("hides text when collapsed", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: true
        }));
        // Text should be hidden when collapsed
        const brandingText = _testutils.screen.queryByText("UAV Control");
        expect(brandingText).not.toBeInTheDocument();
    });
    it("shows toggle button", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            onToggle: mockOnToggle
        }));
        const toggleButton = _testutils.screen.getByRole("button", {
            name: /toggle sidebar/i
        });
        expect(toggleButton).toBeInTheDocument();
    });
    it("handles toggle button click", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            onToggle: mockOnToggle
        }));
        const toggleButton = _testutils.screen.getByRole("button", {
            name: /toggle sidebar/i
        });
        _testutils.fireEvent.click(toggleButton);
        expect(mockOnToggle).toHaveBeenCalledTimes(1);
    });
    it("shows correct toggle icon when expanded", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: false,
            onToggle: mockOnToggle
        }));
        const toggleButton = _testutils.screen.getByRole("button", {
            name: /toggle sidebar/i
        });
        // Should show ChevronLeft icon when expanded
        expect(toggleButton).toBeInTheDocument();
    });
    it("shows correct toggle icon when collapsed", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: true,
            onToggle: mockOnToggle
        }));
        const toggleButton = _testutils.screen.getByRole("button", {
            name: /toggle sidebar/i
        });
        // Should show ChevronRight icon when collapsed
        expect(toggleButton).toBeInTheDocument();
    });
    it("applies custom className", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            className: "custom-sidebar"
        }));
        const sidebar = _testutils.screen.getByTestId("main-nav").closest("div");
        expect(sidebar).toHaveClass("custom-sidebar");
    });
    it("renders navigation component", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {}));
        expect(_testutils.screen.getByTestId("main-nav")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Dashboard")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAVs")).toBeInTheDocument();
    });
    it("displays version information when expanded", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: false
        }));
        expect(_testutils.screen.getByText("Version 1.0.0")).toBeInTheDocument();
        expect(_testutils.screen.getByText("\xa9 2024 UAV Systems")).toBeInTheDocument();
    });
    it("hides version information when collapsed", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: true
        }));
        expect(_testutils.screen.queryByText("Version 1.0.0")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByText("\xa9 2024 UAV Systems")).not.toBeInTheDocument();
    });
    it("has proper layout structure", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {}));
        const sidebar = _testutils.screen.getByTestId("main-nav").closest("div");
        expect(sidebar).toHaveClass("flex", "flex-col", "h-full", "bg-card", "border-r");
    });
    it("handles animation states", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: false
        }));
        // Should be in expanded state
        let sidebar = _testutils.screen.getByTestId("main-nav").closest("div");
        expect(sidebar).toBeInTheDocument();
        // Change to collapsed
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: true
        }));
        sidebar = _testutils.screen.getByTestId("main-nav").closest("div");
        expect(sidebar).toBeInTheDocument();
    });
    it("maintains accessibility standards", async ()=>{
        const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            onToggle: mockOnToggle
        }));
        // Check for proper button accessibility
        const toggleButton = _testutils.screen.getByRole("button", {
            name: /toggle sidebar/i
        });
        expect(toggleButton).toBeInTheDocument();
        // Check for navigation accessibility
        expect(_testutils.screen.getByTestId("main-nav")).toBeInTheDocument();
        // Run accessibility tests
        await (0, _testutils.runAxeTest)(container);
    });
    it("supports keyboard navigation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            onToggle: mockOnToggle
        }));
        const toggleButton = _testutils.screen.getByRole("button", {
            name: /toggle sidebar/i
        });
        toggleButton.focus();
        expect(toggleButton).toHaveFocus();
        // Test Enter key
        _testutils.fireEvent.keyDown(toggleButton, {
            key: "Enter"
        });
        expect(mockOnToggle).toHaveBeenCalledTimes(1);
        // Test Space key
        _testutils.fireEvent.keyDown(toggleButton, {
            key: " "
        });
        expect(mockOnToggle).toHaveBeenCalledTimes(2);
    });
    it("handles missing onToggle prop gracefully", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {}));
        const toggleButton = _testutils.screen.getByRole("button", {
            name: /toggle sidebar/i
        });
        // Should not throw error when clicked without onToggle
        expect(()=>{
            _testutils.fireEvent.click(toggleButton);
        }).not.toThrow();
    });
    it("adjusts padding based on collapsed state", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: false
        }));
        let navContainer = _testutils.screen.getByTestId("main-nav").parentElement;
        expect(navContainer).toHaveClass("px-3");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: true
        }));
        navContainer = _testutils.screen.getByTestId("main-nav").parentElement;
        expect(navContainer).toHaveClass("px-2");
    });
    it("shows logo icon consistently", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: false
        }));
        // Logo should be present when expanded
        expect(_testutils.screen.getByTestId("shield-icon")).toBeInTheDocument();
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: true
        }));
        // Logo should still be present when collapsed
        expect(_testutils.screen.getByTestId("shield-icon")).toBeInTheDocument();
    });
    it("handles responsive behavior", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {}));
        const sidebar = _testutils.screen.getByTestId("main-nav").closest("div");
        // Should have proper responsive classes
        expect(sidebar).toHaveClass("flex", "flex-col", "h-full");
    });
    it("maintains proper z-index and positioning", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {}));
        const sidebar = _testutils.screen.getByTestId("main-nav").closest("div");
        // Should have proper background and border
        expect(sidebar).toHaveClass("bg-card", "border-r");
    });
    it("handles animation variants correctly", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: false
        }));
        // Should use expanded variant
        let sidebar = _testutils.screen.getByTestId("main-nav").closest("div");
        expect(sidebar).toBeInTheDocument();
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {
            collapsed: true
        }));
        // Should use collapsed variant
        sidebar = _testutils.screen.getByTestId("main-nav").closest("div");
        expect(sidebar).toBeInTheDocument();
    });
    it("provides proper semantic structure", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_sidebar.Sidebar, {}));
        // Should have proper navigation structure
        const nav = _testutils.screen.getByTestId("main-nav");
        expect(nav).toBeInTheDocument();
        // Should have header section with logo
        expect(_testutils.screen.getByText("UAV Control")).toBeInTheDocument();
        // Should have footer section with version info
        expect(_testutils.screen.getByText("Version 1.0.0")).toBeInTheDocument();
    });
});

//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFxEYUNodWFuZ0JhY2tlbmRcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcX190ZXN0c19fXFxzaWRlYmFyLnRlc3QudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHJlbmRlciwgc2NyZWVuLCBmaXJlRXZlbnQgfSBmcm9tICdAL2xpYi90ZXN0LXV0aWxzJ1xuaW1wb3J0IHsgU2lkZWJhciB9IGZyb20gJy4uL3NpZGViYXInXG5pbXBvcnQgeyBtb2NrRnJhbWVyTW90aW9uLCBydW5BeGVUZXN0IH0gZnJvbSAnQC9saWIvdGVzdC11dGlscydcblxuLy8gTW9jayB0aGUgbWFpbiBuYXYgY29tcG9uZW50XG5qZXN0Lm1vY2soJy4uL21haW4tbmF2JywgKCkgPT4gKHtcbiAgTWFpbk5hdjogKCkgPT4gKFxuICAgIDxkaXYgZGF0YS10ZXN0aWQ9XCJtYWluLW5hdlwiPlxuICAgICAgPGEgaHJlZj1cIi9kYXNoYm9hcmRcIj5EYXNoYm9hcmQ8L2E+XG4gICAgICA8YSBocmVmPVwiL3VhdnNcIj5VQVZzPC9hPlxuICAgIDwvZGl2PlxuICApLFxufSkpXG5cbi8vIE1vY2sgYW5pbWF0aW9uc1xuamVzdC5tb2NrKCdAL2xpYi9hbmltYXRpb25zJywgKCkgPT4gKHtcbiAgc2lkZWJhclZhcmlhbnRzOiB7XG4gICAgZXhwYW5kZWQ6IHsgd2lkdGg6IDI4MCB9LFxuICAgIGNvbGxhcHNlZDogeyB3aWR0aDogODAgfSxcbiAgfSxcbiAgZ2V0QW5pbWF0aW9uVmFyaWFudHM6IGplc3QuZm4oKHZhcmlhbnRzKSA9PiB2YXJpYW50cyksXG59KSlcblxuLy8gTW9jayBmcmFtZXItbW90aW9uXG5tb2NrRnJhbWVyTW90aW9uKClcblxuZGVzY3JpYmUoJ1NpZGViYXIgQ29tcG9uZW50JywgKCkgPT4ge1xuICBjb25zdCBtb2NrT25Ub2dnbGUgPSBqZXN0LmZuKClcblxuICBiZWZvcmVFYWNoKCgpID0+IHtcbiAgICBqZXN0LmNsZWFyQWxsTW9ja3MoKVxuICB9KVxuXG4gIGl0KCdyZW5kZXJzIGNvcnJlY3RseScsICgpID0+IHtcbiAgICByZW5kZXIoPFNpZGViYXIgLz4pXG5cbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdtYWluLW5hdicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ1VBViBDb250cm9sJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnTWFuYWdlbWVudCBTeXN0ZW0nKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdkaXNwbGF5cyBsb2dvIGFuZCBicmFuZGluZyB3aGVuIGV4cGFuZGVkJywgKCkgPT4ge1xuICAgIHJlbmRlcig8U2lkZWJhciBjb2xsYXBzZWQ9e2ZhbHNlfSAvPilcblxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdVQVYgQ29udHJvbCcpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ01hbmFnZW1lbnQgU3lzdGVtJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcblxuICBpdCgnaGlkZXMgdGV4dCB3aGVuIGNvbGxhcHNlZCcsICgpID0+IHtcbiAgICByZW5kZXIoPFNpZGViYXIgY29sbGFwc2VkPXt0cnVlfSAvPilcblxuICAgIC8vIFRleHQgc2hvdWxkIGJlIGhpZGRlbiB3aGVuIGNvbGxhcHNlZFxuICAgIGNvbnN0IGJyYW5kaW5nVGV4dCA9IHNjcmVlbi5xdWVyeUJ5VGV4dCgnVUFWIENvbnRyb2wnKVxuICAgIGV4cGVjdChicmFuZGluZ1RleHQpLm5vdC50b0JlSW5UaGVEb2N1bWVudCgpXG4gIH0pXG5cbiAgaXQoJ3Nob3dzIHRvZ2dsZSBidXR0b24nLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxTaWRlYmFyIG9uVG9nZ2xlPXttb2NrT25Ub2dnbGV9IC8+KVxuXG4gICAgY29uc3QgdG9nZ2xlQnV0dG9uID0gc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJywgeyBuYW1lOiAvdG9nZ2xlIHNpZGViYXIvaSB9KVxuICAgIGV4cGVjdCh0b2dnbGVCdXR0b24pLnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcblxuICBpdCgnaGFuZGxlcyB0b2dnbGUgYnV0dG9uIGNsaWNrJywgKCkgPT4ge1xuICAgIHJlbmRlcig8U2lkZWJhciBvblRvZ2dsZT17bW9ja09uVG9nZ2xlfSAvPilcblxuICAgIGNvbnN0IHRvZ2dsZUJ1dHRvbiA9IHNjcmVlbi5nZXRCeVJvbGUoJ2J1dHRvbicsIHsgbmFtZTogL3RvZ2dsZSBzaWRlYmFyL2kgfSlcbiAgICBmaXJlRXZlbnQuY2xpY2sodG9nZ2xlQnV0dG9uKVxuXG4gICAgZXhwZWN0KG1vY2tPblRvZ2dsZSkudG9IYXZlQmVlbkNhbGxlZFRpbWVzKDEpXG4gIH0pXG5cbiAgaXQoJ3Nob3dzIGNvcnJlY3QgdG9nZ2xlIGljb24gd2hlbiBleHBhbmRlZCcsICgpID0+IHtcbiAgICByZW5kZXIoPFNpZGViYXIgY29sbGFwc2VkPXtmYWxzZX0gb25Ub2dnbGU9e21vY2tPblRvZ2dsZX0gLz4pXG5cbiAgICBjb25zdCB0b2dnbGVCdXR0b24gPSBzY3JlZW4uZ2V0QnlSb2xlKCdidXR0b24nLCB7IG5hbWU6IC90b2dnbGUgc2lkZWJhci9pIH0pXG4gICAgLy8gU2hvdWxkIHNob3cgQ2hldnJvbkxlZnQgaWNvbiB3aGVuIGV4cGFuZGVkXG4gICAgZXhwZWN0KHRvZ2dsZUJ1dHRvbikudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdzaG93cyBjb3JyZWN0IHRvZ2dsZSBpY29uIHdoZW4gY29sbGFwc2VkJywgKCkgPT4ge1xuICAgIHJlbmRlcig8U2lkZWJhciBjb2xsYXBzZWQ9e3RydWV9IG9uVG9nZ2xlPXttb2NrT25Ub2dnbGV9IC8+KVxuXG4gICAgY29uc3QgdG9nZ2xlQnV0dG9uID0gc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJywgeyBuYW1lOiAvdG9nZ2xlIHNpZGViYXIvaSB9KVxuICAgIC8vIFNob3VsZCBzaG93IENoZXZyb25SaWdodCBpY29uIHdoZW4gY29sbGFwc2VkXG4gICAgZXhwZWN0KHRvZ2dsZUJ1dHRvbikudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdhcHBsaWVzIGN1c3RvbSBjbGFzc05hbWUnLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxTaWRlYmFyIGNsYXNzTmFtZT1cImN1c3RvbS1zaWRlYmFyXCIgLz4pXG5cbiAgICBjb25zdCBzaWRlYmFyID0gc2NyZWVuLmdldEJ5VGVzdElkKCdtYWluLW5hdicpLmNsb3Nlc3QoJ2RpdicpXG4gICAgZXhwZWN0KHNpZGViYXIpLnRvSGF2ZUNsYXNzKCdjdXN0b20tc2lkZWJhcicpXG4gIH0pXG5cbiAgaXQoJ3JlbmRlcnMgbmF2aWdhdGlvbiBjb21wb25lbnQnLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxTaWRlYmFyIC8+KVxuXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFpbi1uYXYnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdEYXNoYm9hcmQnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdVQVZzJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcblxuICBpdCgnZGlzcGxheXMgdmVyc2lvbiBpbmZvcm1hdGlvbiB3aGVuIGV4cGFuZGVkJywgKCkgPT4ge1xuICAgIHJlbmRlcig8U2lkZWJhciBjb2xsYXBzZWQ9e2ZhbHNlfSAvPilcblxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdWZXJzaW9uIDEuMC4wJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnwqkgMjAyNCBVQVYgU3lzdGVtcycpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gIH0pXG5cbiAgaXQoJ2hpZGVzIHZlcnNpb24gaW5mb3JtYXRpb24gd2hlbiBjb2xsYXBzZWQnLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxTaWRlYmFyIGNvbGxhcHNlZD17dHJ1ZX0gLz4pXG5cbiAgICBleHBlY3Qoc2NyZWVuLnF1ZXJ5QnlUZXh0KCdWZXJzaW9uIDEuMC4wJykpLm5vdC50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgZXhwZWN0KHNjcmVlbi5xdWVyeUJ5VGV4dCgnwqkgMjAyNCBVQVYgU3lzdGVtcycpKS5ub3QudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdoYXMgcHJvcGVyIGxheW91dCBzdHJ1Y3R1cmUnLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxTaWRlYmFyIC8+KVxuXG4gICAgY29uc3Qgc2lkZWJhciA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFpbi1uYXYnKS5jbG9zZXN0KCdkaXYnKVxuICAgIGV4cGVjdChzaWRlYmFyKS50b0hhdmVDbGFzcygnZmxleCcsICdmbGV4LWNvbCcsICdoLWZ1bGwnLCAnYmctY2FyZCcsICdib3JkZXItcicpXG4gIH0pXG5cbiAgaXQoJ2hhbmRsZXMgYW5pbWF0aW9uIHN0YXRlcycsICgpID0+IHtcbiAgICBjb25zdCB7IHJlcmVuZGVyIH0gPSByZW5kZXIoPFNpZGViYXIgY29sbGFwc2VkPXtmYWxzZX0gLz4pXG5cbiAgICAvLyBTaG91bGQgYmUgaW4gZXhwYW5kZWQgc3RhdGVcbiAgICBsZXQgc2lkZWJhciA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFpbi1uYXYnKS5jbG9zZXN0KCdkaXYnKVxuICAgIGV4cGVjdChzaWRlYmFyKS50b0JlSW5UaGVEb2N1bWVudCgpXG5cbiAgICAvLyBDaGFuZ2UgdG8gY29sbGFwc2VkXG4gICAgcmVyZW5kZXIoPFNpZGViYXIgY29sbGFwc2VkPXt0cnVlfSAvPilcbiAgICBzaWRlYmFyID0gc2NyZWVuLmdldEJ5VGVzdElkKCdtYWluLW5hdicpLmNsb3Nlc3QoJ2RpdicpXG4gICAgZXhwZWN0KHNpZGViYXIpLnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcblxuICBpdCgnbWFpbnRhaW5zIGFjY2Vzc2liaWxpdHkgc3RhbmRhcmRzJywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHsgY29udGFpbmVyIH0gPSByZW5kZXIoPFNpZGViYXIgb25Ub2dnbGU9e21vY2tPblRvZ2dsZX0gLz4pXG5cbiAgICAvLyBDaGVjayBmb3IgcHJvcGVyIGJ1dHRvbiBhY2Nlc3NpYmlsaXR5XG4gICAgY29uc3QgdG9nZ2xlQnV0dG9uID0gc2NyZWVuLmdldEJ5Um9sZSgnYnV0dG9uJywgeyBuYW1lOiAvdG9nZ2xlIHNpZGViYXIvaSB9KVxuICAgIGV4cGVjdCh0b2dnbGVCdXR0b24pLnRvQmVJblRoZURvY3VtZW50KClcblxuICAgIC8vIENoZWNrIGZvciBuYXZpZ2F0aW9uIGFjY2Vzc2liaWxpdHlcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdtYWluLW5hdicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG5cbiAgICAvLyBSdW4gYWNjZXNzaWJpbGl0eSB0ZXN0c1xuICAgIGF3YWl0IHJ1bkF4ZVRlc3QoY29udGFpbmVyKVxuICB9KVxuXG4gIGl0KCdzdXBwb3J0cyBrZXlib2FyZCBuYXZpZ2F0aW9uJywgKCkgPT4ge1xuICAgIHJlbmRlcig8U2lkZWJhciBvblRvZ2dsZT17bW9ja09uVG9nZ2xlfSAvPilcblxuICAgIGNvbnN0IHRvZ2dsZUJ1dHRvbiA9IHNjcmVlbi5nZXRCeVJvbGUoJ2J1dHRvbicsIHsgbmFtZTogL3RvZ2dsZSBzaWRlYmFyL2kgfSlcbiAgICBcbiAgICB0b2dnbGVCdXR0b24uZm9jdXMoKVxuICAgIGV4cGVjdCh0b2dnbGVCdXR0b24pLnRvSGF2ZUZvY3VzKClcblxuICAgIC8vIFRlc3QgRW50ZXIga2V5XG4gICAgZmlyZUV2ZW50LmtleURvd24odG9nZ2xlQnV0dG9uLCB7IGtleTogJ0VudGVyJyB9KVxuICAgIGV4cGVjdChtb2NrT25Ub2dnbGUpLnRvSGF2ZUJlZW5DYWxsZWRUaW1lcygxKVxuXG4gICAgLy8gVGVzdCBTcGFjZSBrZXlcbiAgICBmaXJlRXZlbnQua2V5RG93bih0b2dnbGVCdXR0b24sIHsga2V5OiAnICcgfSlcbiAgICBleHBlY3QobW9ja09uVG9nZ2xlKS50b0hhdmVCZWVuQ2FsbGVkVGltZXMoMilcbiAgfSlcblxuICBpdCgnaGFuZGxlcyBtaXNzaW5nIG9uVG9nZ2xlIHByb3AgZ3JhY2VmdWxseScsICgpID0+IHtcbiAgICByZW5kZXIoPFNpZGViYXIgLz4pXG5cbiAgICBjb25zdCB0b2dnbGVCdXR0b24gPSBzY3JlZW4uZ2V0QnlSb2xlKCdidXR0b24nLCB7IG5hbWU6IC90b2dnbGUgc2lkZWJhci9pIH0pXG4gICAgXG4gICAgLy8gU2hvdWxkIG5vdCB0aHJvdyBlcnJvciB3aGVuIGNsaWNrZWQgd2l0aG91dCBvblRvZ2dsZVxuICAgIGV4cGVjdCgoKSA9PiB7XG4gICAgICBmaXJlRXZlbnQuY2xpY2sodG9nZ2xlQnV0dG9uKVxuICAgIH0pLm5vdC50b1Rocm93KClcbiAgfSlcblxuICBpdCgnYWRqdXN0cyBwYWRkaW5nIGJhc2VkIG9uIGNvbGxhcHNlZCBzdGF0ZScsICgpID0+IHtcbiAgICBjb25zdCB7IHJlcmVuZGVyIH0gPSByZW5kZXIoPFNpZGViYXIgY29sbGFwc2VkPXtmYWxzZX0gLz4pXG5cbiAgICBsZXQgbmF2Q29udGFpbmVyID0gc2NyZWVuLmdldEJ5VGVzdElkKCdtYWluLW5hdicpLnBhcmVudEVsZW1lbnRcbiAgICBleHBlY3QobmF2Q29udGFpbmVyKS50b0hhdmVDbGFzcygncHgtMycpXG5cbiAgICByZXJlbmRlcig8U2lkZWJhciBjb2xsYXBzZWQ9e3RydWV9IC8+KVxuICAgIG5hdkNvbnRhaW5lciA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFpbi1uYXYnKS5wYXJlbnRFbGVtZW50XG4gICAgZXhwZWN0KG5hdkNvbnRhaW5lcikudG9IYXZlQ2xhc3MoJ3B4LTInKVxuICB9KVxuXG4gIGl0KCdzaG93cyBsb2dvIGljb24gY29uc2lzdGVudGx5JywgKCkgPT4ge1xuICAgIGNvbnN0IHsgcmVyZW5kZXIgfSA9IHJlbmRlcig8U2lkZWJhciBjb2xsYXBzZWQ9e2ZhbHNlfSAvPilcblxuICAgIC8vIExvZ28gc2hvdWxkIGJlIHByZXNlbnQgd2hlbiBleHBhbmRlZFxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXN0SWQoJ3NoaWVsZC1pY29uJykpLnRvQmVJblRoZURvY3VtZW50KClcblxuICAgIHJlcmVuZGVyKDxTaWRlYmFyIGNvbGxhcHNlZD17dHJ1ZX0gLz4pXG5cbiAgICAvLyBMb2dvIHNob3VsZCBzdGlsbCBiZSBwcmVzZW50IHdoZW4gY29sbGFwc2VkXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnc2hpZWxkLWljb24nKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdoYW5kbGVzIHJlc3BvbnNpdmUgYmVoYXZpb3InLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxTaWRlYmFyIC8+KVxuXG4gICAgY29uc3Qgc2lkZWJhciA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFpbi1uYXYnKS5jbG9zZXN0KCdkaXYnKVxuICAgIFxuICAgIC8vIFNob3VsZCBoYXZlIHByb3BlciByZXNwb25zaXZlIGNsYXNzZXNcbiAgICBleHBlY3Qoc2lkZWJhcikudG9IYXZlQ2xhc3MoJ2ZsZXgnLCAnZmxleC1jb2wnLCAnaC1mdWxsJylcbiAgfSlcblxuICBpdCgnbWFpbnRhaW5zIHByb3BlciB6LWluZGV4IGFuZCBwb3NpdGlvbmluZycsICgpID0+IHtcbiAgICByZW5kZXIoPFNpZGViYXIgLz4pXG5cbiAgICBjb25zdCBzaWRlYmFyID0gc2NyZWVuLmdldEJ5VGVzdElkKCdtYWluLW5hdicpLmNsb3Nlc3QoJ2RpdicpXG4gICAgXG4gICAgLy8gU2hvdWxkIGhhdmUgcHJvcGVyIGJhY2tncm91bmQgYW5kIGJvcmRlclxuICAgIGV4cGVjdChzaWRlYmFyKS50b0hhdmVDbGFzcygnYmctY2FyZCcsICdib3JkZXItcicpXG4gIH0pXG5cbiAgaXQoJ2hhbmRsZXMgYW5pbWF0aW9uIHZhcmlhbnRzIGNvcnJlY3RseScsICgpID0+IHtcbiAgICBjb25zdCB7IHJlcmVuZGVyIH0gPSByZW5kZXIoPFNpZGViYXIgY29sbGFwc2VkPXtmYWxzZX0gLz4pXG5cbiAgICAvLyBTaG91bGQgdXNlIGV4cGFuZGVkIHZhcmlhbnRcbiAgICBsZXQgc2lkZWJhciA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFpbi1uYXYnKS5jbG9zZXN0KCdkaXYnKVxuICAgIGV4cGVjdChzaWRlYmFyKS50b0JlSW5UaGVEb2N1bWVudCgpXG5cbiAgICByZXJlbmRlcig8U2lkZWJhciBjb2xsYXBzZWQ9e3RydWV9IC8+KVxuXG4gICAgLy8gU2hvdWxkIHVzZSBjb2xsYXBzZWQgdmFyaWFudFxuICAgIHNpZGViYXIgPSBzY3JlZW4uZ2V0QnlUZXN0SWQoJ21haW4tbmF2JykuY2xvc2VzdCgnZGl2JylcbiAgICBleHBlY3Qoc2lkZWJhcikudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdwcm92aWRlcyBwcm9wZXIgc2VtYW50aWMgc3RydWN0dXJlJywgKCkgPT4ge1xuICAgIHJlbmRlcig8U2lkZWJhciAvPilcblxuICAgIC8vIFNob3VsZCBoYXZlIHByb3BlciBuYXZpZ2F0aW9uIHN0cnVjdHVyZVxuICAgIGNvbnN0IG5hdiA9IHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFpbi1uYXYnKVxuICAgIGV4cGVjdChuYXYpLnRvQmVJblRoZURvY3VtZW50KClcblxuICAgIC8vIFNob3VsZCBoYXZlIGhlYWRlciBzZWN0aW9uIHdpdGggbG9nb1xuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdVQVYgQ29udHJvbCcpKS50b0JlSW5UaGVEb2N1bWVudCgpXG5cbiAgICAvLyBTaG91bGQgaGF2ZSBmb290ZXIgc2VjdGlvbiB3aXRoIHZlcnNpb24gaW5mb1xuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdWZXJzaW9uIDEuMC4wJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcbn0pXG4iXSwibmFtZXMiOlsiamVzdCIsIm1vY2siLCJNYWluTmF2IiwiZGl2IiwiZGF0YS10ZXN0aWQiLCJhIiwiaHJlZiIsInNpZGViYXJWYXJpYW50cyIsImV4cGFuZGVkIiwid2lkdGgiLCJjb2xsYXBzZWQiLCJnZXRBbmltYXRpb25WYXJpYW50cyIsImZuIiwidmFyaWFudHMiLCJtb2NrRnJhbWVyTW90aW9uIiwiZGVzY3JpYmUiLCJtb2NrT25Ub2dnbGUiLCJiZWZvcmVFYWNoIiwiY2xlYXJBbGxNb2NrcyIsIml0IiwicmVuZGVyIiwiU2lkZWJhciIsImV4cGVjdCIsInNjcmVlbiIsImdldEJ5VGVzdElkIiwidG9CZUluVGhlRG9jdW1lbnQiLCJnZXRCeVRleHQiLCJicmFuZGluZ1RleHQiLCJxdWVyeUJ5VGV4dCIsIm5vdCIsIm9uVG9nZ2xlIiwidG9nZ2xlQnV0dG9uIiwiZ2V0QnlSb2xlIiwibmFtZSIsImZpcmVFdmVudCIsImNsaWNrIiwidG9IYXZlQmVlbkNhbGxlZFRpbWVzIiwiY2xhc3NOYW1lIiwic2lkZWJhciIsImNsb3Nlc3QiLCJ0b0hhdmVDbGFzcyIsInJlcmVuZGVyIiwiY29udGFpbmVyIiwicnVuQXhlVGVzdCIsImZvY3VzIiwidG9IYXZlRm9jdXMiLCJrZXlEb3duIiwia2V5IiwidG9UaHJvdyIsIm5hdkNvbnRhaW5lciIsInBhcmVudEVsZW1lbnQiLCJuYXYiXSwibWFwcGluZ3MiOiI7QUFLQSw4QkFBOEI7QUFDOUJBLEtBQUtDLElBQUksQ0FBQyxlQUFlLElBQU8sQ0FBQTtRQUM5QkMsU0FBUyxrQkFDUCxzQkFBQ0M7Z0JBQUlDLGVBQVk7O2tDQUNmLHFCQUFDQzt3QkFBRUMsTUFBSztrQ0FBYTs7a0NBQ3JCLHFCQUFDRDt3QkFBRUMsTUFBSztrQ0FBUTs7OztJQUd0QixDQUFBO0FBRUEsa0JBQWtCO0FBQ2xCTixLQUFLQyxJQUFJLENBQUMsb0JBQW9CLElBQU8sQ0FBQTtRQUNuQ00saUJBQWlCO1lBQ2ZDLFVBQVU7Z0JBQUVDLE9BQU87WUFBSTtZQUN2QkMsV0FBVztnQkFBRUQsT0FBTztZQUFHO1FBQ3pCO1FBQ0FFLHNCQUFzQlgsS0FBS1ksRUFBRSxDQUFDLENBQUNDLFdBQWFBO0lBQzlDLENBQUE7Ozs7OzhEQXRCa0I7MkJBQ3dCO3lCQUNsQjs7Ozs7O0FBc0J4QixxQkFBcUI7QUFDckJDLElBQUFBLDJCQUFnQjtBQUVoQkMsU0FBUyxxQkFBcUI7SUFDNUIsTUFBTUMsZUFBZWhCLEtBQUtZLEVBQUU7SUFFNUJLLFdBQVc7UUFDVGpCLEtBQUtrQixhQUFhO0lBQ3BCO0lBRUFDLEdBQUcscUJBQXFCO1FBQ3RCQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsZ0JBQU87UUFFZkMsT0FBT0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLGFBQWFDLGlCQUFpQjtRQUN4REgsT0FBT0MsaUJBQU0sQ0FBQ0csU0FBUyxDQUFDLGdCQUFnQkQsaUJBQWlCO1FBQ3pESCxPQUFPQyxpQkFBTSxDQUFDRyxTQUFTLENBQUMsc0JBQXNCRCxpQkFBaUI7SUFDakU7SUFFQU4sR0FBRyw0Q0FBNEM7UUFDN0NDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyxnQkFBTztZQUFDWCxXQUFXOztRQUUzQlksT0FBT0MsaUJBQU0sQ0FBQ0csU0FBUyxDQUFDLGdCQUFnQkQsaUJBQWlCO1FBQ3pESCxPQUFPQyxpQkFBTSxDQUFDRyxTQUFTLENBQUMsc0JBQXNCRCxpQkFBaUI7SUFDakU7SUFFQU4sR0FBRyw2QkFBNkI7UUFDOUJDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyxnQkFBTztZQUFDWCxXQUFXOztRQUUzQix1Q0FBdUM7UUFDdkMsTUFBTWlCLGVBQWVKLGlCQUFNLENBQUNLLFdBQVcsQ0FBQztRQUN4Q04sT0FBT0ssY0FBY0UsR0FBRyxDQUFDSixpQkFBaUI7SUFDNUM7SUFFQU4sR0FBRyx1QkFBdUI7UUFDeEJDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyxnQkFBTztZQUFDUyxVQUFVZDs7UUFFMUIsTUFBTWUsZUFBZVIsaUJBQU0sQ0FBQ1MsU0FBUyxDQUFDLFVBQVU7WUFBRUMsTUFBTTtRQUFrQjtRQUMxRVgsT0FBT1MsY0FBY04saUJBQWlCO0lBQ3hDO0lBRUFOLEdBQUcsK0JBQStCO1FBQ2hDQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsZ0JBQU87WUFBQ1MsVUFBVWQ7O1FBRTFCLE1BQU1lLGVBQWVSLGlCQUFNLENBQUNTLFNBQVMsQ0FBQyxVQUFVO1lBQUVDLE1BQU07UUFBa0I7UUFDMUVDLG9CQUFTLENBQUNDLEtBQUssQ0FBQ0o7UUFFaEJULE9BQU9OLGNBQWNvQixxQkFBcUIsQ0FBQztJQUM3QztJQUVBakIsR0FBRywyQ0FBMkM7UUFDNUNDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyxnQkFBTztZQUFDWCxXQUFXO1lBQU9vQixVQUFVZDs7UUFFNUMsTUFBTWUsZUFBZVIsaUJBQU0sQ0FBQ1MsU0FBUyxDQUFDLFVBQVU7WUFBRUMsTUFBTTtRQUFrQjtRQUMxRSw2Q0FBNkM7UUFDN0NYLE9BQU9TLGNBQWNOLGlCQUFpQjtJQUN4QztJQUVBTixHQUFHLDRDQUE0QztRQUM3Q0MsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLGdCQUFPO1lBQUNYLFdBQVc7WUFBTW9CLFVBQVVkOztRQUUzQyxNQUFNZSxlQUFlUixpQkFBTSxDQUFDUyxTQUFTLENBQUMsVUFBVTtZQUFFQyxNQUFNO1FBQWtCO1FBQzFFLCtDQUErQztRQUMvQ1gsT0FBT1MsY0FBY04saUJBQWlCO0lBQ3hDO0lBRUFOLEdBQUcsNEJBQTRCO1FBQzdCQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsZ0JBQU87WUFBQ2dCLFdBQVU7O1FBRTFCLE1BQU1DLFVBQVVmLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxZQUFZZSxPQUFPLENBQUM7UUFDdkRqQixPQUFPZ0IsU0FBU0UsV0FBVyxDQUFDO0lBQzlCO0lBRUFyQixHQUFHLGdDQUFnQztRQUNqQ0MsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLGdCQUFPO1FBRWZDLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxhQUFhQyxpQkFBaUI7UUFDeERILE9BQU9DLGlCQUFNLENBQUNHLFNBQVMsQ0FBQyxjQUFjRCxpQkFBaUI7UUFDdkRILE9BQU9DLGlCQUFNLENBQUNHLFNBQVMsQ0FBQyxTQUFTRCxpQkFBaUI7SUFDcEQ7SUFFQU4sR0FBRyw4Q0FBOEM7UUFDL0NDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyxnQkFBTztZQUFDWCxXQUFXOztRQUUzQlksT0FBT0MsaUJBQU0sQ0FBQ0csU0FBUyxDQUFDLGtCQUFrQkQsaUJBQWlCO1FBQzNESCxPQUFPQyxpQkFBTSxDQUFDRyxTQUFTLENBQUMsMEJBQXVCRCxpQkFBaUI7SUFDbEU7SUFFQU4sR0FBRyw0Q0FBNEM7UUFDN0NDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyxnQkFBTztZQUFDWCxXQUFXOztRQUUzQlksT0FBT0MsaUJBQU0sQ0FBQ0ssV0FBVyxDQUFDLGtCQUFrQkMsR0FBRyxDQUFDSixpQkFBaUI7UUFDakVILE9BQU9DLGlCQUFNLENBQUNLLFdBQVcsQ0FBQywwQkFBdUJDLEdBQUcsQ0FBQ0osaUJBQWlCO0lBQ3hFO0lBRUFOLEdBQUcsK0JBQStCO1FBQ2hDQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsZ0JBQU87UUFFZixNQUFNaUIsVUFBVWYsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLFlBQVllLE9BQU8sQ0FBQztRQUN2RGpCLE9BQU9nQixTQUFTRSxXQUFXLENBQUMsUUFBUSxZQUFZLFVBQVUsV0FBVztJQUN2RTtJQUVBckIsR0FBRyw0QkFBNEI7UUFDN0IsTUFBTSxFQUFFc0IsUUFBUSxFQUFFLEdBQUdyQixJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsZ0JBQU87WUFBQ1gsV0FBVzs7UUFFaEQsOEJBQThCO1FBQzlCLElBQUk0QixVQUFVZixpQkFBTSxDQUFDQyxXQUFXLENBQUMsWUFBWWUsT0FBTyxDQUFDO1FBQ3JEakIsT0FBT2dCLFNBQVNiLGlCQUFpQjtRQUVqQyxzQkFBc0I7UUFDdEJnQix1QkFBUyxxQkFBQ3BCLGdCQUFPO1lBQUNYLFdBQVc7O1FBQzdCNEIsVUFBVWYsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLFlBQVllLE9BQU8sQ0FBQztRQUNqRGpCLE9BQU9nQixTQUFTYixpQkFBaUI7SUFDbkM7SUFFQU4sR0FBRyxxQ0FBcUM7UUFDdEMsTUFBTSxFQUFFdUIsU0FBUyxFQUFFLEdBQUd0QixJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsZ0JBQU87WUFBQ1MsVUFBVWQ7O1FBRWhELHdDQUF3QztRQUN4QyxNQUFNZSxlQUFlUixpQkFBTSxDQUFDUyxTQUFTLENBQUMsVUFBVTtZQUFFQyxNQUFNO1FBQWtCO1FBQzFFWCxPQUFPUyxjQUFjTixpQkFBaUI7UUFFdEMscUNBQXFDO1FBQ3JDSCxPQUFPQyxpQkFBTSxDQUFDQyxXQUFXLENBQUMsYUFBYUMsaUJBQWlCO1FBRXhELDBCQUEwQjtRQUMxQixNQUFNa0IsSUFBQUEscUJBQVUsRUFBQ0Q7SUFDbkI7SUFFQXZCLEdBQUcsZ0NBQWdDO1FBQ2pDQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsZ0JBQU87WUFBQ1MsVUFBVWQ7O1FBRTFCLE1BQU1lLGVBQWVSLGlCQUFNLENBQUNTLFNBQVMsQ0FBQyxVQUFVO1lBQUVDLE1BQU07UUFBa0I7UUFFMUVGLGFBQWFhLEtBQUs7UUFDbEJ0QixPQUFPUyxjQUFjYyxXQUFXO1FBRWhDLGlCQUFpQjtRQUNqQlgsb0JBQVMsQ0FBQ1ksT0FBTyxDQUFDZixjQUFjO1lBQUVnQixLQUFLO1FBQVE7UUFDL0N6QixPQUFPTixjQUFjb0IscUJBQXFCLENBQUM7UUFFM0MsaUJBQWlCO1FBQ2pCRixvQkFBUyxDQUFDWSxPQUFPLENBQUNmLGNBQWM7WUFBRWdCLEtBQUs7UUFBSTtRQUMzQ3pCLE9BQU9OLGNBQWNvQixxQkFBcUIsQ0FBQztJQUM3QztJQUVBakIsR0FBRyw0Q0FBNEM7UUFDN0NDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyxnQkFBTztRQUVmLE1BQU1VLGVBQWVSLGlCQUFNLENBQUNTLFNBQVMsQ0FBQyxVQUFVO1lBQUVDLE1BQU07UUFBa0I7UUFFMUUsdURBQXVEO1FBQ3ZEWCxPQUFPO1lBQ0xZLG9CQUFTLENBQUNDLEtBQUssQ0FBQ0o7UUFDbEIsR0FBR0YsR0FBRyxDQUFDbUIsT0FBTztJQUNoQjtJQUVBN0IsR0FBRyw0Q0FBNEM7UUFDN0MsTUFBTSxFQUFFc0IsUUFBUSxFQUFFLEdBQUdyQixJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsZ0JBQU87WUFBQ1gsV0FBVzs7UUFFaEQsSUFBSXVDLGVBQWUxQixpQkFBTSxDQUFDQyxXQUFXLENBQUMsWUFBWTBCLGFBQWE7UUFDL0Q1QixPQUFPMkIsY0FBY1QsV0FBVyxDQUFDO1FBRWpDQyx1QkFBUyxxQkFBQ3BCLGdCQUFPO1lBQUNYLFdBQVc7O1FBQzdCdUMsZUFBZTFCLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxZQUFZMEIsYUFBYTtRQUMzRDVCLE9BQU8yQixjQUFjVCxXQUFXLENBQUM7SUFDbkM7SUFFQXJCLEdBQUcsZ0NBQWdDO1FBQ2pDLE1BQU0sRUFBRXNCLFFBQVEsRUFBRSxHQUFHckIsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLGdCQUFPO1lBQUNYLFdBQVc7O1FBRWhELHVDQUF1QztRQUN2Q1ksT0FBT0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLGdCQUFnQkMsaUJBQWlCO1FBRTNEZ0IsdUJBQVMscUJBQUNwQixnQkFBTztZQUFDWCxXQUFXOztRQUU3Qiw4Q0FBOEM7UUFDOUNZLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxnQkFBZ0JDLGlCQUFpQjtJQUM3RDtJQUVBTixHQUFHLCtCQUErQjtRQUNoQ0MsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLGdCQUFPO1FBRWYsTUFBTWlCLFVBQVVmLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxZQUFZZSxPQUFPLENBQUM7UUFFdkQsd0NBQXdDO1FBQ3hDakIsT0FBT2dCLFNBQVNFLFdBQVcsQ0FBQyxRQUFRLFlBQVk7SUFDbEQ7SUFFQXJCLEdBQUcsNENBQTRDO1FBQzdDQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsZ0JBQU87UUFFZixNQUFNaUIsVUFBVWYsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLFlBQVllLE9BQU8sQ0FBQztRQUV2RCwyQ0FBMkM7UUFDM0NqQixPQUFPZ0IsU0FBU0UsV0FBVyxDQUFDLFdBQVc7SUFDekM7SUFFQXJCLEdBQUcsd0NBQXdDO1FBQ3pDLE1BQU0sRUFBRXNCLFFBQVEsRUFBRSxHQUFHckIsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLGdCQUFPO1lBQUNYLFdBQVc7O1FBRWhELDhCQUE4QjtRQUM5QixJQUFJNEIsVUFBVWYsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLFlBQVllLE9BQU8sQ0FBQztRQUNyRGpCLE9BQU9nQixTQUFTYixpQkFBaUI7UUFFakNnQix1QkFBUyxxQkFBQ3BCLGdCQUFPO1lBQUNYLFdBQVc7O1FBRTdCLCtCQUErQjtRQUMvQjRCLFVBQVVmLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxZQUFZZSxPQUFPLENBQUM7UUFDakRqQixPQUFPZ0IsU0FBU2IsaUJBQWlCO0lBQ25DO0lBRUFOLEdBQUcsc0NBQXNDO1FBQ3ZDQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsZ0JBQU87UUFFZiwwQ0FBMEM7UUFDMUMsTUFBTThCLE1BQU01QixpQkFBTSxDQUFDQyxXQUFXLENBQUM7UUFDL0JGLE9BQU82QixLQUFLMUIsaUJBQWlCO1FBRTdCLHVDQUF1QztRQUN2Q0gsT0FBT0MsaUJBQU0sQ0FBQ0csU0FBUyxDQUFDLGdCQUFnQkQsaUJBQWlCO1FBRXpELCtDQUErQztRQUMvQ0gsT0FBT0MsaUJBQU0sQ0FBQ0csU0FBUyxDQUFDLGtCQUFrQkQsaUJBQWlCO0lBQzdEO0FBQ0YifQ==