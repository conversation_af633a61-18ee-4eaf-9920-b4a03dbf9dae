54aa4adb55e612ee5b35cb9f0df5ad22
"use client";
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "Sidebar", {
    enumerable: true,
    get: function() {
        return Sidebar;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _link = /*#__PURE__*/ _interop_require_default(require("next/link"));
const _framermotion = require("framer-motion");
const _lucidereact = require("lucide-react");
const _button = require("../ui/button");
const _mainnav = require("./main-nav");
const _utils = require("../../lib/utils");
const _animations = require("../../lib/animations");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function Sidebar({ collapsed = false, onToggle, className }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_framermotion.motion.div, {
        variants: (0, _animations.getAnimationVariants)(_animations.sidebarVariants),
        animate: collapsed ? "collapsed" : "expanded",
        className: (0, _utils.cn)("flex flex-col h-full bg-card border-r", className),
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "flex items-center justify-between p-4 border-b",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_link.default, {
                        href: "/dashboard",
                        className: "flex items-center space-x-2",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Shield, {
                                className: "h-8 w-8 text-primary flex-shrink-0",
                                "data-testid": "shield-icon"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.AnimatePresence, {
                                children: !collapsed && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_framermotion.motion.div, {
                                    initial: {
                                        opacity: 0,
                                        x: -20
                                    },
                                    animate: {
                                        opacity: 1,
                                        x: 0
                                    },
                                    exit: {
                                        opacity: 0,
                                        x: -20
                                    },
                                    transition: {
                                        duration: 0.2
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                                            className: "font-orbitron font-bold text-lg text-primary",
                                            children: "UAV Control"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-xs text-muted-foreground",
                                            children: "Management System"
                                        })
                                    ]
                                })
                            })
                        ]
                    }),
                    onToggle && /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                        whileHover: {
                            scale: 1.1
                        },
                        whileTap: {
                            scale: 0.9
                        },
                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_button.Button, {
                            variant: "ghost",
                            size: "icon",
                            onClick: onToggle,
                            className: "h-8 w-8",
                            "aria-label": "Toggle sidebar",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                                    animate: {
                                        rotate: collapsed ? 0 : 180
                                    },
                                    transition: {
                                        duration: 0.2
                                    },
                                    children: collapsed ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.ChevronRight, {
                                        className: "h-4 w-4"
                                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.ChevronLeft, {
                                        className: "h-4 w-4"
                                    })
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                    className: "sr-only",
                                    children: "Toggle sidebar"
                                })
                            ]
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "flex-1 overflow-y-auto py-4",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: (0, _utils.cn)("px-3", collapsed && "px-2"),
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {})
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "p-4 border-t",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.AnimatePresence, {
                    children: !collapsed && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_framermotion.motion.div, {
                        initial: {
                            opacity: 0,
                            y: 10
                        },
                        animate: {
                            opacity: 1,
                            y: 0
                        },
                        exit: {
                            opacity: 0,
                            y: 10
                        },
                        transition: {
                            duration: 0.2,
                            delay: 0.1
                        },
                        className: "text-xs text-muted-foreground",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                children: "Version 1.0.0"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                children: "\xa9 2024 UAV Systems"
                            })
                        ]
                    })
                })
            })
        ]
    });
}

//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFxEYUNodWFuZ0JhY2tlbmRcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcc2lkZWJhci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IG1vdGlvbiwgQW5pbWF0ZVByZXNlbmNlIH0gZnJvbSAnZnJhbWVyLW1vdGlvbidcbmltcG9ydCB7IFNoaWVsZCwgQ2hldnJvbkxlZnQsIENoZXZyb25SaWdodCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBNYWluTmF2IH0gZnJvbSAnLi9tYWluLW5hdidcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnXG5pbXBvcnQgeyBzaWRlYmFyVmFyaWFudHMsIGdldEFuaW1hdGlvblZhcmlhbnRzIH0gZnJvbSAnQC9saWIvYW5pbWF0aW9ucydcblxuaW50ZXJmYWNlIFNpZGViYXJQcm9wcyB7XG4gIGNvbGxhcHNlZD86IGJvb2xlYW5cbiAgb25Ub2dnbGU/OiAoKSA9PiB2b2lkXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gU2lkZWJhcih7IGNvbGxhcHNlZCA9IGZhbHNlLCBvblRvZ2dsZSwgY2xhc3NOYW1lIH06IFNpZGViYXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxtb3Rpb24uZGl2XG4gICAgICB2YXJpYW50cz17Z2V0QW5pbWF0aW9uVmFyaWFudHMoc2lkZWJhclZhcmlhbnRzKX1cbiAgICAgIGFuaW1hdGU9e2NvbGxhcHNlZCA/ICdjb2xsYXBzZWQnIDogJ2V4cGFuZGVkJ31cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICdmbGV4IGZsZXgtY29sIGgtZnVsbCBiZy1jYXJkIGJvcmRlci1yJyxcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgID5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgYm9yZGVyLWJcIj5cbiAgICAgICAgPExpbmsgaHJlZj1cIi9kYXNoYm9hcmRcIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICA8U2hpZWxkIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1wcmltYXJ5IGZsZXgtc2hyaW5rLTBcIiBkYXRhLXRlc3RpZD1cInNoaWVsZC1pY29uXCIgLz5cbiAgICAgICAgICA8QW5pbWF0ZVByZXNlbmNlPlxuICAgICAgICAgICAgeyFjb2xsYXBzZWQgJiYgKFxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCB4OiAtMjAgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjIgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJmb250LW9yYml0cm9uIGZvbnQtYm9sZCB0ZXh0LWxnIHRleHQtcHJpbWFyeVwiPlxuICAgICAgICAgICAgICAgICAgVUFWIENvbnRyb2xcbiAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICBNYW5hZ2VtZW50IFN5c3RlbVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAgPC9MaW5rPlxuICAgICAgICBcbiAgICAgICAge29uVG9nZ2xlICYmIChcbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4xIH19XG4gICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45IH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uVG9nZ2xlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTggdy04XCJcbiAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIlRvZ2dsZSBzaWRlYmFyXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBhbmltYXRlPXt7IHJvdGF0ZTogY29sbGFwc2VkID8gMCA6IDE4MCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMiB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2NvbGxhcHNlZCA/IChcbiAgICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxDaGV2cm9uTGVmdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHlcIj5Ub2dnbGUgc2lkZWJhcjwvc3Bhbj5cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTmF2aWdhdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBweS00XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbigncHgtMycsIGNvbGxhcHNlZCAmJiAncHgtMicpfT5cbiAgICAgICAgICA8TWFpbk5hdiAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRm9vdGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyLXRcIj5cbiAgICAgICAgPEFuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAgICB7IWNvbGxhcHNlZCAmJiAoXG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDEwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHk6IDEwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMiwgZGVsYXk6IDAuMSB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxwPlZlcnNpb24gMS4wLjA8L3A+XG4gICAgICAgICAgICAgIDxwPsKpIDIwMjQgVUFWIFN5c3RlbXM8L3A+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XG4gICAgICA8L2Rpdj5cbiAgICA8L21vdGlvbi5kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJTaWRlYmFyIiwiY29sbGFwc2VkIiwib25Ub2dnbGUiLCJjbGFzc05hbWUiLCJtb3Rpb24iLCJkaXYiLCJ2YXJpYW50cyIsImdldEFuaW1hdGlvblZhcmlhbnRzIiwic2lkZWJhclZhcmlhbnRzIiwiYW5pbWF0ZSIsImNuIiwiTGluayIsImhyZWYiLCJTaGllbGQiLCJkYXRhLXRlc3RpZCIsIkFuaW1hdGVQcmVzZW5jZSIsImluaXRpYWwiLCJvcGFjaXR5IiwieCIsImV4aXQiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJoMSIsInAiLCJ3aGlsZUhvdmVyIiwic2NhbGUiLCJ3aGlsZVRhcCIsIkJ1dHRvbiIsInZhcmlhbnQiLCJzaXplIiwib25DbGljayIsImFyaWEtbGFiZWwiLCJyb3RhdGUiLCJDaGV2cm9uUmlnaHQiLCJDaGV2cm9uTGVmdCIsInNwYW4iLCJNYWluTmF2IiwieSIsImRlbGF5Il0sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7K0JBaUJnQkE7OztlQUFBQTs7Ozs4REFmRTs2REFDRDs4QkFDdUI7NkJBQ1U7d0JBQzNCO3lCQUNDO3VCQUNMOzRCQUNtQzs7Ozs7O0FBUS9DLFNBQVNBLFFBQVEsRUFBRUMsWUFBWSxLQUFLLEVBQUVDLFFBQVEsRUFBRUMsU0FBUyxFQUFnQjtJQUM5RSxxQkFDRSxzQkFBQ0Msb0JBQU0sQ0FBQ0MsR0FBRztRQUNUQyxVQUFVQyxJQUFBQSxnQ0FBb0IsRUFBQ0MsMkJBQWU7UUFDOUNDLFNBQVNSLFlBQVksY0FBYztRQUNuQ0UsV0FBV08sSUFBQUEsU0FBRSxFQUNYLHlDQUNBUDs7MEJBSUYsc0JBQUNFO2dCQUFJRixXQUFVOztrQ0FDYixzQkFBQ1EsYUFBSTt3QkFBQ0MsTUFBSzt3QkFBYVQsV0FBVTs7MENBQ2hDLHFCQUFDVSxtQkFBTTtnQ0FBQ1YsV0FBVTtnQ0FBcUNXLGVBQVk7OzBDQUNuRSxxQkFBQ0MsNkJBQWU7MENBQ2IsQ0FBQ2QsMkJBQ0Esc0JBQUNHLG9CQUFNLENBQUNDLEdBQUc7b0NBQ1RXLFNBQVM7d0NBQUVDLFNBQVM7d0NBQUdDLEdBQUcsQ0FBQztvQ0FBRztvQ0FDOUJULFNBQVM7d0NBQUVRLFNBQVM7d0NBQUdDLEdBQUc7b0NBQUU7b0NBQzVCQyxNQUFNO3dDQUFFRixTQUFTO3dDQUFHQyxHQUFHLENBQUM7b0NBQUc7b0NBQzNCRSxZQUFZO3dDQUFFQyxVQUFVO29DQUFJOztzREFFNUIscUJBQUNDOzRDQUFHbkIsV0FBVTtzREFBK0M7O3NEQUc3RCxxQkFBQ29COzRDQUFFcEIsV0FBVTtzREFBZ0M7Ozs7Ozs7b0JBUXBERCwwQkFDQyxxQkFBQ0Usb0JBQU0sQ0FBQ0MsR0FBRzt3QkFDVG1CLFlBQVk7NEJBQUVDLE9BQU87d0JBQUk7d0JBQ3pCQyxVQUFVOzRCQUFFRCxPQUFPO3dCQUFJO2tDQUV2QixjQUFBLHNCQUFDRSxjQUFNOzRCQUNMQyxTQUFROzRCQUNSQyxNQUFLOzRCQUNMQyxTQUFTNUI7NEJBQ1RDLFdBQVU7NEJBQ1Y0QixjQUFXOzs4Q0FFWCxxQkFBQzNCLG9CQUFNLENBQUNDLEdBQUc7b0NBQ1RJLFNBQVM7d0NBQUV1QixRQUFRL0IsWUFBWSxJQUFJO29DQUFJO29DQUN2Q21CLFlBQVk7d0NBQUVDLFVBQVU7b0NBQUk7OENBRTNCcEIsMEJBQ0MscUJBQUNnQyx5QkFBWTt3Q0FBQzlCLFdBQVU7dURBRXhCLHFCQUFDK0Isd0JBQVc7d0NBQUMvQixXQUFVOzs7OENBRzNCLHFCQUFDZ0M7b0NBQUtoQyxXQUFVOzhDQUFVOzs7Ozs7OzBCQU9sQyxxQkFBQ0U7Z0JBQUlGLFdBQVU7MEJBQ2IsY0FBQSxxQkFBQ0U7b0JBQUlGLFdBQVdPLElBQUFBLFNBQUUsRUFBQyxRQUFRVCxhQUFhOzhCQUN0QyxjQUFBLHFCQUFDbUMsZ0JBQU87OzswQkFLWixxQkFBQy9CO2dCQUFJRixXQUFVOzBCQUNiLGNBQUEscUJBQUNZLDZCQUFlOzhCQUNiLENBQUNkLDJCQUNBLHNCQUFDRyxvQkFBTSxDQUFDQyxHQUFHO3dCQUNUVyxTQUFTOzRCQUFFQyxTQUFTOzRCQUFHb0IsR0FBRzt3QkFBRzt3QkFDN0I1QixTQUFTOzRCQUFFUSxTQUFTOzRCQUFHb0IsR0FBRzt3QkFBRTt3QkFDNUJsQixNQUFNOzRCQUFFRixTQUFTOzRCQUFHb0IsR0FBRzt3QkFBRzt3QkFDMUJqQixZQUFZOzRCQUFFQyxVQUFVOzRCQUFLaUIsT0FBTzt3QkFBSTt3QkFDeENuQyxXQUFVOzswQ0FFVixxQkFBQ29COzBDQUFFOzswQ0FDSCxxQkFBQ0E7MENBQUU7Ozs7Ozs7O0FBT2pCIn0=