df3f33e3395a018b234b63fc32ce1b94
"use client";
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "RealtimeAlerts", {
    enumerable: true,
    get: function() {
        return RealtimeAlerts;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _framermotion = require("framer-motion");
const _card = require("../../ui/card");
const _badge = require("../../ui/badge");
const _button = require("../../ui/button");
const _scrollarea = require("../../ui/scroll-area");
const _animatedcomponents = require("../../ui/animated-components");
const _lucidereact = require("lucide-react");
const _dashboardstore = require("../../../stores/dashboard-store");
const _utils = require("../../../lib/utils");
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
// Mock alerts for demonstration
const mockAlerts = [
    {
        id: "1",
        type: "CRITICAL",
        title: "UAV Battery Critical",
        message: "UAV-005 battery level is critically low (8%)",
        uavId: 5,
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        acknowledged: false
    },
    {
        id: "2",
        type: "WARNING",
        title: "Hibernate Pod Near Capacity",
        message: "Hibernate pod is at 80% capacity (8/10 slots occupied)",
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        acknowledged: false
    },
    {
        id: "3",
        type: "INFO",
        title: "Flight Completed",
        message: 'UAV-003 has successfully completed mission "Perimeter Patrol"',
        uavId: 3,
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        acknowledged: true
    },
    {
        id: "4",
        type: "ERROR",
        title: "Communication Lost",
        message: "Lost communication with UAV-007 during flight",
        uavId: 7,
        timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
        acknowledged: false
    },
    {
        id: "5",
        type: "INFO",
        title: "Maintenance Scheduled",
        message: "Routine maintenance scheduled for UAV-001 tomorrow at 09:00",
        uavId: 1,
        timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
        acknowledged: true
    }
];
function RealtimeAlerts() {
    const alerts = (0, _dashboardstore.useAlerts)();
    const { acknowledgeAlert, removeAlert, addAlert } = (0, _dashboardstore.useDashboardStore)();
    // Initialize with mock data for demonstration
    (0, _react.useEffect)(()=>{
        if (alerts.length === 0) {
            mockAlerts.forEach((alert)=>addAlert(alert));
        }
    }, [
        alerts.length,
        addAlert
    ]);
    const getAlertIcon = (type)=>{
        switch(type){
            case "CRITICAL":
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.XCircle, {
                    className: "h-4 w-4 text-red-600"
                });
            case "ERROR":
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertTriangle, {
                    className: "h-4 w-4 text-red-500"
                });
            case "WARNING":
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertTriangle, {
                    className: "h-4 w-4 text-yellow-500"
                });
            case "INFO":
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Info, {
                    className: "h-4 w-4 text-blue-500"
                });
            default:
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Bell, {
                    className: "h-4 w-4 text-gray-500"
                });
        }
    };
    const getAlertVariant = (type)=>{
        switch(type){
            case "CRITICAL":
            case "ERROR":
                return "destructive";
            case "WARNING":
                return "warning";
            case "INFO":
                return "info";
            default:
                return "secondary";
        }
    };
    const handleAcknowledge = (alertId)=>{
        acknowledgeAlert(alertId);
    };
    const handleDismiss = (alertId)=>{
        removeAlert(alertId);
    };
    const unacknowledgedAlerts = alerts.filter((alert)=>!alert.acknowledged);
    const acknowledgedAlerts = alerts.filter((alert)=>alert.acknowledged);
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.Card, {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.CardHeader, {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardTitle, {
                                className: "flex items-center justify-between",
                                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Bell, {
                                            className: "h-5 w-5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            children: "Active Alerts"
                                        }),
                                        unacknowledgedAlerts.length > 0 && /*#__PURE__*/ (0, _jsxruntime.jsx)(_badge.Badge, {
                                            variant: "destructive",
                                            className: "ml-2",
                                            children: unacknowledgedAlerts.length
                                        })
                                    ]
                                })
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardDescription, {
                                children: "Unacknowledged system alerts requiring attention"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardContent, {
                        children: unacknowledgedAlerts.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "text-center py-8 text-muted-foreground",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                    className: "h-12 w-12 mx-auto mb-4 text-green-500"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-lg font-medium",
                                    children: "All Clear!"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-sm",
                                    children: "No active alerts at this time"
                                })
                            ]
                        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_scrollarea.ScrollArea, {
                            className: "h-80",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.StaggerContainer, {
                                className: "space-y-3",
                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.AnimatePresence, {
                                    children: unacknowledgedAlerts.map((alert, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.StaggerItem, {
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                                                layout: true,
                                                initial: {
                                                    opacity: 0,
                                                    x: -20,
                                                    scale: 0.95
                                                },
                                                animate: {
                                                    opacity: 1,
                                                    x: 0,
                                                    scale: 1
                                                },
                                                exit: {
                                                    opacity: 0,
                                                    x: 20,
                                                    scale: 0.95
                                                },
                                                transition: {
                                                    duration: 0.3,
                                                    delay: index * 0.1
                                                },
                                                className: (0, _utils.cn)("p-4 rounded-lg border transition-colors", alert.type === "CRITICAL" && "border-red-200 bg-red-50", alert.type === "ERROR" && "border-red-200 bg-red-50", alert.type === "WARNING" && "border-yellow-200 bg-yellow-50", alert.type === "INFO" && "border-blue-200 bg-blue-50"),
                                                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex items-start justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                            className: "flex items-start space-x-3 flex-1",
                                                            children: [
                                                                getAlertIcon(alert.type),
                                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                                    className: "flex-1 min-w-0",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                                            className: "flex items-center space-x-2 mb-1",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_badge.Badge, {
                                                                                    variant: getAlertVariant(alert.type),
                                                                                    children: alert.type
                                                                                }),
                                                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                                                    className: "flex items-center space-x-1 text-xs text-muted-foreground",
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                                                                            className: "h-3 w-3"
                                                                                        }),
                                                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                                                            children: (0, _utils.formatRelativeTime)(alert.timestamp)
                                                                                        })
                                                                                    ]
                                                                                })
                                                                            ]
                                                                        }),
                                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                                            className: "font-medium text-sm mb-1",
                                                                            children: alert.title
                                                                        }),
                                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                                            className: "text-sm text-muted-foreground",
                                                                            children: alert.message
                                                                        }),
                                                                        alert.uavId && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                                            className: "text-xs text-muted-foreground mt-1",
                                                                            children: [
                                                                                "Related UAV ID: ",
                                                                                alert.uavId
                                                                            ]
                                                                        })
                                                                    ]
                                                                })
                                                            ]
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                            className: "flex items-center space-x-2 ml-4",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                                                                    whileHover: {
                                                                        scale: 1.05
                                                                    },
                                                                    whileTap: {
                                                                        scale: 0.95
                                                                    },
                                                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
                                                                        size: "sm",
                                                                        variant: "outline",
                                                                        onClick: ()=>handleAcknowledge(alert.id),
                                                                        children: "Acknowledge"
                                                                    })
                                                                }),
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                                                                    whileHover: {
                                                                        scale: 1.1,
                                                                        rotate: 90
                                                                    },
                                                                    whileTap: {
                                                                        scale: 0.9
                                                                    },
                                                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
                                                                        size: "sm",
                                                                        variant: "ghost",
                                                                        onClick: ()=>handleDismiss(alert.id),
                                                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.X, {
                                                                            className: "h-4 w-4"
                                                                        })
                                                                    })
                                                                })
                                                            ]
                                                        })
                                                    ]
                                                })
                                            })
                                        }, alert.id))
                                })
                            })
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.Card, {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.CardHeader, {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.CardTitle, {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                        className: "h-5 w-5"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                        children: "Recent Activity"
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardDescription, {
                                children: "Recently acknowledged alerts and system events"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardContent, {
                        children: acknowledgedAlerts.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "text-center py-4 text-muted-foreground",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                className: "text-sm",
                                children: "No recent activity"
                            })
                        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_scrollarea.ScrollArea, {
                            className: "h-60",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.StaggerContainer, {
                                className: "space-y-2",
                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.AnimatePresence, {
                                    children: acknowledgedAlerts.slice(0, 10).map((alert, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedcomponents.StaggerItem, {
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_framermotion.motion.div, {
                                                layout: true,
                                                initial: {
                                                    opacity: 0,
                                                    y: 10
                                                },
                                                animate: {
                                                    opacity: 1,
                                                    y: 0
                                                },
                                                exit: {
                                                    opacity: 0,
                                                    y: -10
                                                },
                                                transition: {
                                                    duration: 0.2,
                                                    delay: index * 0.05
                                                },
                                                className: "flex items-center justify-between p-3 rounded-lg bg-muted/50",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                        className: "flex items-center space-x-3",
                                                        children: [
                                                            getAlertIcon(alert.type),
                                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                                        className: "text-sm font-medium",
                                                                        children: alert.title
                                                                    }),
                                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                                        className: "text-xs text-muted-foreground",
                                                                        children: (0, _utils.formatRelativeTime)(alert.timestamp)
                                                                    })
                                                                ]
                                                            })
                                                        ]
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                        className: "flex items-center space-x-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_badge.Badge, {
                                                                variant: "outline",
                                                                className: "text-xs",
                                                                children: "Acknowledged"
                                                            }),
                                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                                                                whileHover: {
                                                                    scale: 1.1,
                                                                    rotate: 90
                                                                },
                                                                whileTap: {
                                                                    scale: 0.9
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_button.Button, {
                                                                    size: "sm",
                                                                    variant: "ghost",
                                                                    onClick: ()=>handleDismiss(alert.id),
                                                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.X, {
                                                                        className: "h-3 w-3"
                                                                    })
                                                                })
                                                            })
                                                        ]
                                                    })
                                                ]
                                            })
                                        }, alert.id))
                                })
                            })
                        })
                    })
                ]
            })
        ]
    });
}

//# sourceMappingURL=data:application/json;base64,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