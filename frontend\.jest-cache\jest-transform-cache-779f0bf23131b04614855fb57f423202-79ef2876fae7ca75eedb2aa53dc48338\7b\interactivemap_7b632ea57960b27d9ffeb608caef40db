acdbaa1d47780d6dee64f77ffdda478c
"use client";
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return InteractiveMap;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _reactleaflet = require("react-leaflet");
const _leaflet = /*#__PURE__*/ _interop_require_default(require("leaflet"));
require("leaflet/dist/leaflet.css");
const _badge = require("../../ui/badge");
const _button = require("../../ui/button");
const _animatedmapcomponents = require("./animated-map-components");
const _lucidereact = require("lucide-react");
const _utils = require("../../../lib/utils");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
// Fix for default markers in react-leaflet
delete _leaflet.default.Icon.Default.prototype._getIconUrl;
_leaflet.default.Icon.Default.mergeOptions({
    iconRetinaUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
    iconUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
    shadowUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"
});
// Custom UAV icons
const createUAVIcon = (status, operationalStatus)=>{
    const getColor = ()=>{
        if (operationalStatus === "EMERGENCY") return "#dc2626";
        if (status === "UNAUTHORIZED") return "#dc2626";
        if (operationalStatus === "IN_FLIGHT") return "#2563eb";
        if (operationalStatus === "HIBERNATING") return "#7c3aed";
        if (operationalStatus === "CHARGING") return "#ea580c";
        if (operationalStatus === "MAINTENANCE") return "#d97706";
        return "#16a34a" // AUTHORIZED and READY
        ;
    };
    const color = getColor();
    return _leaflet.default.divIcon({
        html: `
      <div style="
        background-color: ${color};
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
      ">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="white">
          <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
        </svg>
      </div>
    `,
        className: "custom-uav-icon",
        iconSize: [
            24,
            24
        ],
        iconAnchor: [
            12,
            12
        ],
        popupAnchor: [
            0,
            -12
        ]
    });
};
// Geofence data (mock)
const mockGeofences = [
    {
        id: 1,
        name: "Restricted Zone A",
        center: [
            39.9042,
            116.4074
        ],
        radius: 1000,
        type: "restricted",
        color: "#dc2626"
    },
    {
        id: 2,
        name: "Safe Zone B",
        center: [
            39.9142,
            116.4174
        ],
        radius: 1500,
        type: "safe",
        color: "#16a34a"
    },
    {
        id: 3,
        name: "Warning Zone C",
        center: [
            39.8942,
            116.3974
        ],
        radius: 800,
        type: "warning",
        color: "#ea580c"
    }
];
// Docking stations data (mock)
const mockDockingStations = [
    {
        id: 1,
        name: "Station Alpha",
        position: [
            39.9000,
            116.4000
        ],
        capacity: 5,
        occupied: 2,
        status: "operational"
    },
    {
        id: 2,
        name: "Station Beta",
        position: [
            39.9100,
            116.4100
        ],
        capacity: 3,
        occupied: 1,
        status: "operational"
    },
    {
        id: 3,
        name: "Station Gamma",
        position: [
            39.8950,
            116.4150
        ],
        capacity: 4,
        occupied: 0,
        status: "maintenance"
    }
];
function MapController({ center, zoom }) {
    const map = (0, _reactleaflet.useMap)();
    (0, _react.useEffect)(()=>{
        map.setView(center, zoom);
    }, [
        map,
        center,
        zoom
    ]);
    return null;
}
function InteractiveMap({ uavs, selectedUAV, onUAVSelect, center, zoom, layers, className }) {
    const mapRef = (0, _react.useRef)(null);
    // Add mock location data to UAVs that don't have it
    const uavsWithMockLocations = uavs.map((uav, index)=>{
        if (!uav.currentLatitude || !uav.currentLongitude) {
            return {
                ...uav,
                currentLatitude: 39.9042 + (Math.random() - 0.5) * 0.1,
                currentLongitude: 116.4074 + (Math.random() - 0.5) * 0.1,
                currentAltitudeMeters: Math.floor(Math.random() * 500) + 50
            };
        }
        return uav;
    });
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
        className: className,
        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_reactleaflet.MapContainer, {
            ref: mapRef,
            center: center,
            zoom: zoom,
            style: {
                height: "100%",
                width: "100%"
            },
            className: "rounded-lg",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)(MapController, {
                    center: center,
                    zoom: zoom
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_reactleaflet.TileLayer, {
                    attribution: '\xa9 <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                    url: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                }),
                layers.uavs && uavsWithMockLocations.map((uav)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedmapcomponents.AnimatedUAVMarker, {
                        uav: uav,
                        isSelected: selectedUAV?.id === uav.id,
                        onSelect: onUAVSelect,
                        icon: createUAVIcon(uav.status, uav.operationalStatus)
                    }, uav.id)),
                layers.uavs && uavsWithMockLocations.filter((uav)=>selectedUAV?.id === uav.id).map((uav)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_reactleaflet.Marker, {
                        position: [
                            uav.currentLatitude,
                            uav.currentLongitude
                        ],
                        icon: _leaflet.default.divIcon({
                            html: "",
                            iconSize: [
                                0,
                                0
                            ]
                        }),
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_reactleaflet.Popup, {
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "p-2 min-w-64",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "flex items-center justify-between mb-3",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-center space-x-2",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plane, {
                                                        className: "h-4 w-4 text-primary"
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                        className: "font-semibold",
                                                        children: uav.rfidTag
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex space-x-1",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_badge.Badge, {
                                                        variant: (0, _utils.getStatusVariant)(uav.status),
                                                        children: uav.status
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_badge.Badge, {
                                                        variant: (0, _utils.getStatusVariant)(uav.operationalStatus),
                                                        children: uav.operationalStatus.replace("_", " ")
                                                    })
                                                ]
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "space-y-2 text-sm",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-center space-x-2",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.User, {
                                                        className: "h-3 w-3 text-muted-foreground"
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                        children: uav.ownerName
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-center space-x-2",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plane, {
                                                        className: "h-3 w-3 text-muted-foreground"
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                        children: uav.model
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-center space-x-2",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.MapPin, {
                                                        className: "h-3 w-3 text-muted-foreground"
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                                                        className: "font-mono text-xs",
                                                        children: [
                                                            uav.currentLatitude.toFixed(6),
                                                            ", ",
                                                            uav.currentLongitude.toFixed(6)
                                                        ]
                                                    })
                                                ]
                                            }),
                                            uav.currentAltitudeMeters && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-center space-x-2",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Navigation, {
                                                        className: "h-3 w-3 text-muted-foreground"
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                                                        children: [
                                                            uav.currentAltitudeMeters,
                                                            "m altitude"
                                                        ]
                                                    })
                                                ]
                                            }),
                                            uav.batteryStatus && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-center space-x-2",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Battery, {
                                                        className: "h-3 w-3 text-muted-foreground"
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                                                        children: [
                                                            uav.batteryStatus.currentChargePercentage,
                                                            "% battery"
                                                        ]
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-center space-x-2",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                                        className: "h-3 w-3 text-muted-foreground"
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                        children: (0, _utils.formatRelativeTime)(uav.updatedAt)
                                                    })
                                                ]
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "mt-3 pt-2 border-t",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_button.Button, {
                                            size: "sm",
                                            variant: "outline",
                                            className: "w-full",
                                            onClick: ()=>onUAVSelect(uav),
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Eye, {
                                                    className: "h-3 w-3 mr-1"
                                                }),
                                                "View Details"
                                            ]
                                        })
                                    })
                                ]
                            })
                        })
                    }, `detailed-${uav.id}`)),
                layers.geofences && mockGeofences.map((geofence)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedmapcomponents.AnimatedGeofence, {
                        center: geofence.center,
                        radius: geofence.radius,
                        color: geofence.color,
                        name: geofence.name,
                        type: geofence.type,
                        isVisible: layers.geofences
                    }, geofence.id)),
                layers.dockingStations && mockDockingStations.map((station)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedmapcomponents.AnimatedDockingStation, {
                        position: station.position,
                        status: station.status,
                        name: station.name,
                        capacity: station.capacity,
                        occupied: station.occupied,
                        isVisible: layers.dockingStations
                    }, station.id))
            ]
        })
    });
}

//# sourceMappingURL=data:application/json;base64,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