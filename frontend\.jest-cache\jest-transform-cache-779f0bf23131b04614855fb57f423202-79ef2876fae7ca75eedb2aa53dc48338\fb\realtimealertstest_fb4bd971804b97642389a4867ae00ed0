f27d867d87d4ca7f5adf8430960def9d
"use strict";
// Mock the dashboard store
jest.mock("@/stores/dashboard-store", ()=>({
        useDashboardStore: jest.fn(),
        useAlerts: jest.fn()
    }));
// Mock framer-motion
jest.mock("framer-motion", ()=>({
        motion: {
            div: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    ...props,
                    children: children
                }),
            li: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("li", {
                    ...props,
                    children: children
                })
        },
        AnimatePresence: ({ children })=>children
    }));
// Mock animated components
jest.mock("@/components/ui/animated-alert", ()=>({
        AnimatedAlert: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "animated-alert",
                ...props,
                children: children
            }),
        RealtimeAlerts: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "realtime-alerts-container",
                children: children
            })
    }));
jest.mock("@/components/ui/animated-components", ()=>({
        StaggerContainer: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "stagger-container",
                children: children
            }),
        StaggerItem: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "stagger-item",
                children: children
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../../lib/test-utils");
const _realtimealerts = require("../realtime-alerts");
const _dashboardstore = require("../../../../stores/dashboard-store");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockUseDashboardStore = _dashboardstore.useDashboardStore;
const { useAlerts } = require("@/stores/dashboard-store");
const mockUseAlerts = useAlerts;
describe("RealtimeAlerts Component", ()=>{
    const mockAlerts = [
        (0, _testutils.createMockAlert)({
            id: 1,
            type: "ERROR",
            title: "Critical Battery",
            message: "UAV-001 battery level is critically low (5%)",
            severity: "HIGH",
            acknowledged: false,
            timestamp: "2024-01-01T10:00:00Z"
        }),
        (0, _testutils.createMockAlert)({
            id: 2,
            type: "WARNING",
            title: "Maintenance Required",
            message: "UAV-002 requires scheduled maintenance",
            severity: "MEDIUM",
            acknowledged: false,
            timestamp: "2024-01-01T09:30:00Z"
        }),
        (0, _testutils.createMockAlert)({
            id: 3,
            type: "INFO",
            title: "Flight Completed",
            message: "UAV-003 has successfully completed mission Alpha",
            severity: "LOW",
            acknowledged: true,
            timestamp: "2024-01-01T09:00:00Z"
        })
    ];
    const mockAcknowledgeAlert = jest.fn();
    const mockRemoveAlert = jest.fn();
    const mockClearAlerts = jest.fn();
    const mockToggleAlerts = jest.fn();
    beforeEach(()=>{
        jest.clearAllMocks();
        // Mock useAlerts hook
        mockUseAlerts.mockReturnValue(mockAlerts);
        mockUseDashboardStore.mockReturnValue({
            alerts: mockAlerts,
            showAlerts: true,
            acknowledgeAlert: mockAcknowledgeAlert,
            removeAlert: mockRemoveAlert,
            clearAlerts: mockClearAlerts,
            toggleAlerts: mockToggleAlerts,
            addAlert: jest.fn(),
            getAlertCounts: jest.fn(()=>({
                    total: 3,
                    error: 1,
                    warning: 1,
                    info: 1,
                    high: 1,
                    medium: 1,
                    low: 1
                })),
            getUnacknowledgedAlerts: jest.fn(()=>mockAlerts.filter((a)=>!a.acknowledged)),
            // Other store properties
            metrics: null,
            flightActivity: null,
            batteryStats: null,
            hibernatePodMetrics: null,
            chartData: null,
            recentLocationUpdates: [],
            isConnected: true,
            lastUpdate: null,
            connectionError: null,
            selectedTimeRange: "24h",
            autoRefresh: true,
            refreshInterval: 30,
            updateMetrics: jest.fn(),
            updateFlightActivity: jest.fn(),
            updateBatteryStats: jest.fn(),
            updateHibernatePodMetrics: jest.fn(),
            updateChartData: jest.fn(),
            addAlert: jest.fn(),
            addLocationUpdate: jest.fn(),
            setConnectionStatus: jest.fn(),
            clearConnectionError: jest.fn(),
            setTimeRange: jest.fn(),
            toggleAutoRefresh: jest.fn(),
            setRefreshInterval: jest.fn(),
            resetData: jest.fn()
        });
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText("Real-time Alerts")).toBeInTheDocument();
        expect(_testutils.screen.getByText("System notifications and warnings")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("stagger-container")).toBeInTheDocument();
    });
    it("displays all alerts", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText("Critical Battery")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Maintenance Required")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Flight Completed")).toBeInTheDocument();
    });
    it("shows alert count in header", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText("3")).toBeInTheDocument() // Total alert count
        ;
    });
    it("displays different alert types with correct styling", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const errorAlert = _testutils.screen.getByText("Critical Battery").closest('[data-testid="stagger-item"]');
        const warningAlert = _testutils.screen.getByText("Maintenance Required").closest('[data-testid="stagger-item"]');
        const infoAlert = _testutils.screen.getByText("Flight Completed").closest('[data-testid="stagger-item"]');
        expect(errorAlert).toBeInTheDocument();
        expect(warningAlert).toBeInTheDocument();
        expect(infoAlert).toBeInTheDocument();
    });
    it("handles alert acknowledgment", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const acknowledgeButton = _testutils.screen.getAllByRole("button", {
            name: /acknowledge/i
        })[0];
        _testutils.fireEvent.click(acknowledgeButton);
        expect(mockAcknowledgeAlert).toHaveBeenCalledWith(1);
    });
    it("handles alert removal", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const removeButton = _testutils.screen.getAllByRole("button", {
            name: /remove|dismiss/i
        })[0];
        _testutils.fireEvent.click(removeButton);
        expect(mockRemoveAlert).toHaveBeenCalledWith(1);
    });
    it("handles clear all alerts", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const clearAllButton = _testutils.screen.getByRole("button", {
            name: /clear all/i
        });
        _testutils.fireEvent.click(clearAllButton);
        expect(mockClearAlerts).toHaveBeenCalled();
    });
    it("toggles alerts visibility", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const toggleButton = _testutils.screen.getByRole("button", {
            name: /toggle alerts/i
        });
        _testutils.fireEvent.click(toggleButton);
        expect(mockToggleAlerts).toHaveBeenCalled();
    });
    it("hides alerts when showAlerts is false", ()=>{
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            showAlerts: false
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.queryByText("Critical Battery")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByText("Maintenance Required")).not.toBeInTheDocument();
    });
    it("displays empty state when no alerts", ()=>{
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            alerts: []
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText(/no alerts/i)).toBeInTheDocument();
        expect(_testutils.screen.getByText(/all systems are running normally/i)).toBeInTheDocument();
    });
    it("filters alerts by type", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const errorFilter = _testutils.screen.getByRole("button", {
            name: /error/i
        });
        _testutils.fireEvent.click(errorFilter);
        expect(_testutils.screen.getByText("Critical Battery")).toBeInTheDocument();
        expect(_testutils.screen.queryByText("Maintenance Required")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByText("Flight Completed")).not.toBeInTheDocument();
    });
    it("filters alerts by severity", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const highSeverityFilter = _testutils.screen.getByRole("button", {
            name: /high/i
        });
        _testutils.fireEvent.click(highSeverityFilter);
        expect(_testutils.screen.getByText("Critical Battery")).toBeInTheDocument();
        expect(_testutils.screen.queryByText("Maintenance Required")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByText("Flight Completed")).not.toBeInTheDocument();
    });
    it("shows only unacknowledged alerts when filter is applied", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const unacknowledgedFilter = _testutils.screen.getByRole("button", {
            name: /unacknowledged/i
        });
        _testutils.fireEvent.click(unacknowledgedFilter);
        expect(_testutils.screen.getByText("Critical Battery")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Maintenance Required")).toBeInTheDocument();
        expect(_testutils.screen.queryByText("Flight Completed")).not.toBeInTheDocument();
    });
    it("displays alert timestamps", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        // Timestamps would be formatted and displayed
        expect(_testutils.screen.getByText(/10:00/)).toBeInTheDocument();
        expect(_testutils.screen.getByText(/09:30/)).toBeInTheDocument();
        expect(_testutils.screen.getByText(/09:00/)).toBeInTheDocument();
    });
    it("shows alert severity badges", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText("HIGH")).toBeInTheDocument();
        expect(_testutils.screen.getByText("MEDIUM")).toBeInTheDocument();
        expect(_testutils.screen.getByText("LOW")).toBeInTheDocument();
    });
    it("handles real-time alert updates", async ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const newAlert = (0, _testutils.createMockAlert)({
            id: 4,
            type: "ERROR",
            title: "Connection Lost",
            message: "Lost connection to UAV-004",
            severity: "HIGH",
            acknowledged: false,
            timestamp: new Date().toISOString()
        });
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            alerts: [
                ...mockAlerts,
                newAlert
            ]
        });
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Connection Lost")).toBeInTheDocument();
        });
    });
    it("sorts alerts by timestamp (newest first)", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const alertTitles = _testutils.screen.getAllByRole("heading", {
            level: 4
        });
        expect(alertTitles[0]).toHaveTextContent("Critical Battery") // 10:00
        ;
        expect(alertTitles[1]).toHaveTextContent("Maintenance Required") // 09:30
        ;
        expect(alertTitles[2]).toHaveTextContent("Flight Completed") // 09:00
        ;
    });
    it("limits displayed alerts to maximum count", ()=>{
        const manyAlerts = Array.from({
            length: 25
        }, (_, i)=>(0, _testutils.createMockAlert)({
                id: i + 1,
                title: `Alert ${i + 1}`,
                timestamp: new Date(Date.now() - i * 60000).toISOString()
            }));
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            alerts: manyAlerts
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {
            maxAlerts: 20
        }));
        const alertItems = _testutils.screen.getAllByTestId("stagger-item");
        expect(alertItems).toHaveLength(20);
    });
    it("shows load more button when there are more alerts", ()=>{
        const manyAlerts = Array.from({
            length: 25
        }, (_, i)=>(0, _testutils.createMockAlert)({
                id: i + 1,
                title: `Alert ${i + 1}`
            }));
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            alerts: manyAlerts
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {
            maxAlerts: 20
        }));
        expect(_testutils.screen.getByRole("button", {
            name: /load more/i
        })).toBeInTheDocument();
    });
    it("handles alert search", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const searchInput = _testutils.screen.getByPlaceholderText(/search alerts/i);
        _testutils.fireEvent.change(searchInput, {
            target: {
                value: "battery"
            }
        });
        expect(_testutils.screen.getByText("Critical Battery")).toBeInTheDocument();
        expect(_testutils.screen.queryByText("Maintenance Required")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByText("Flight Completed")).not.toBeInTheDocument();
    });
    it("supports keyboard navigation", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        const firstAlert = _testutils.screen.getByText("Critical Battery").closest('[data-testid="stagger-item"]');
        const acknowledgeButton = _testutils.screen.getAllByRole("button", {
            name: /acknowledge/i
        })[0];
        acknowledgeButton.focus();
        expect(acknowledgeButton).toHaveFocus();
        _testutils.fireEvent.keyDown(acknowledgeButton, {
            key: "Enter"
        });
        expect(mockAcknowledgeAlert).toHaveBeenCalledWith(1);
    });
    it("shows connection status indicator", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByTestId("connection-indicator")).toBeInTheDocument();
        expect(_testutils.screen.getByText(/connected/i)).toBeInTheDocument();
    });
    it("handles disconnected state", ()=>{
        mockUseDashboardStore.mockReturnValue({
            ...mockUseDashboardStore(),
            isConnected: false,
            connectionError: "Connection lost"
        });
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {}));
        expect(_testutils.screen.getByText(/disconnected/i)).toBeInTheDocument();
        expect(_testutils.screen.getByText("Connection lost")).toBeInTheDocument();
    });
    it("auto-refreshes alerts when connected", ()=>{
        jest.useFakeTimers();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_realtimealerts.RealtimeAlerts, {
            autoRefresh: true,
            refreshInterval: 5000
        }));
        // Fast-forward time
        jest.advanceTimersByTime(5000);
        // Auto-refresh would trigger store updates
        expect(_testutils.screen.getByTestId("stagger-container")).toBeInTheDocument();
        jest.useRealTimers();
    });
});

//# sourceMappingURL=data:application/json;base64,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