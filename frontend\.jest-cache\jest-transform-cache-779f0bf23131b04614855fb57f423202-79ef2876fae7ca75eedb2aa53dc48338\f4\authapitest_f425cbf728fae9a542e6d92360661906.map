{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\api\\__tests__\\auth-api.test.ts"], "sourcesContent": ["import { AuthApi } from '../auth-api'\nimport apiClient from '@/lib/api-client'\nimport { createMockUser } from '@/lib/test-utils'\nimport {\n  LoginRequest,\n  RegisterRequest,\n  ChangePasswordRequest,\n  ForgotPasswordRequest,\n  ResetPasswordRequest,\n} from '@/types/auth'\n\n// Mock the API client\njest.mock('@/lib/api-client')\nconst mockedApiClient = apiClient as jest.Mocked<typeof apiClient>\n\ndescribe('AuthApi', () => {\n  let authApi: AuthApi\n\n  beforeEach(() => {\n    authApi = new AuthApi()\n    jest.clearAllMocks()\n  })\n\n  describe('login', () => {\n    it('should login successfully', async () => {\n      const loginData: LoginRequest = {\n        username: 'testuser',\n        password: 'password123',\n        rememberMe: false,\n      }\n\n      const mockResponse = {\n        user: createMockUser(),\n        token: 'mock-token',\n        refreshToken: 'mock-refresh-token',\n        expiresIn: 3600,\n      }\n\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await authApi.login(loginData)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/auth/login', loginData)\n    })\n\n    it('should handle login error', async () => {\n      const loginData: LoginRequest = {\n        username: 'testuser',\n        password: 'wrongpassword',\n        rememberMe: false,\n      }\n\n      const error = new Error('Invalid credentials')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(authApi.login(loginData)).rejects.toThrow('Invalid credentials')\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/auth/login', loginData)\n    })\n  })\n\n  describe('logout', () => {\n    it('should logout successfully', async () => {\n      mockedApiClient.post.mockResolvedValue(undefined)\n\n      await authApi.logout()\n\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/auth/logout')\n    })\n\n    it('should handle logout error', async () => {\n      const error = new Error('Logout failed')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(authApi.logout()).rejects.toThrow('Logout failed')\n    })\n  })\n\n  describe('register', () => {\n    it('should register successfully', async () => {\n      const registerData: RegisterRequest = {\n        username: 'newuser',\n        email: '<EMAIL>',\n        password: 'password123',\n        firstName: 'New',\n        lastName: 'User',\n      }\n\n      const mockResponse = {\n        user: createMockUser({\n          username: 'newuser',\n          email: '<EMAIL>',\n          firstName: 'New',\n          lastName: 'User',\n        }),\n        token: 'mock-token',\n        refreshToken: 'mock-refresh-token',\n        expiresIn: 3600,\n      }\n\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await authApi.register(registerData)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/auth/register', registerData)\n    })\n\n    it('should handle registration error', async () => {\n      const registerData: RegisterRequest = {\n        username: 'existinguser',\n        email: '<EMAIL>',\n        password: 'password123',\n        firstName: 'Existing',\n        lastName: 'User',\n      }\n\n      const error = new Error('Username already exists')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(authApi.register(registerData)).rejects.toThrow('Username already exists')\n    })\n  })\n\n  describe('refreshToken', () => {\n    it('should refresh token successfully', async () => {\n      const refreshRequest = { refreshToken: 'refresh-token' }\n      const mockResponse = {\n        token: 'new-token',\n        refreshToken: 'new-refresh-token',\n        expiresIn: 3600,\n      }\n\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await authApi.refreshToken(refreshRequest)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/auth/refresh', refreshRequest)\n    })\n\n    it('should handle refresh token error', async () => {\n      const refreshRequest = { refreshToken: 'invalid-token' }\n      const error = new Error('Invalid refresh token')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(authApi.refreshToken(refreshRequest)).rejects.toThrow('Invalid refresh token')\n    })\n  })\n\n  describe('changePassword', () => {\n    it('should change password successfully', async () => {\n      const changePasswordData: ChangePasswordRequest = {\n        currentPassword: 'oldpassword',\n        newPassword: 'newpassword',\n        confirmPassword: 'newpassword',\n      }\n\n      const mockResponse = { success: true, message: 'Password changed successfully' }\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await authApi.changePassword(changePasswordData)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/auth/change-password', changePasswordData)\n    })\n\n    it('should handle change password error', async () => {\n      const changePasswordData: ChangePasswordRequest = {\n        currentPassword: 'wrongpassword',\n        newPassword: 'newpassword',\n        confirmPassword: 'newpassword',\n      }\n\n      const error = new Error('Current password is incorrect')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(authApi.changePassword(changePasswordData)).rejects.toThrow('Current password is incorrect')\n    })\n  })\n\n  describe('forgotPassword', () => {\n    it('should send forgot password email successfully', async () => {\n      const forgotPasswordData: ForgotPasswordRequest = {\n        email: '<EMAIL>',\n      }\n\n      const mockResponse = { success: true, message: 'Reset email sent' }\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await authApi.forgotPassword(forgotPasswordData)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/auth/forgot-password', forgotPasswordData)\n    })\n\n    it('should handle forgot password error', async () => {\n      const forgotPasswordData: ForgotPasswordRequest = {\n        email: '<EMAIL>',\n      }\n\n      const error = new Error('Email not found')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(authApi.forgotPassword(forgotPasswordData)).rejects.toThrow('Email not found')\n    })\n  })\n\n  describe('resetPassword', () => {\n    it('should reset password successfully', async () => {\n      const resetPasswordData: ResetPasswordRequest = {\n        token: 'reset-token',\n        newPassword: 'newpassword',\n        confirmPassword: 'newpassword',\n      }\n\n      const mockResponse = { success: true, message: 'Password reset successfully' }\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await authApi.resetPassword(resetPasswordData)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/auth/reset-password', resetPasswordData)\n    })\n\n    it('should handle reset password error', async () => {\n      const resetPasswordData: ResetPasswordRequest = {\n        token: 'invalid-token',\n        newPassword: 'newpassword',\n        confirmPassword: 'newpassword',\n      }\n\n      const error = new Error('Invalid or expired token')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(authApi.resetPassword(resetPasswordData)).rejects.toThrow('Invalid or expired token')\n    })\n  })\n\n  describe('getUserProfile', () => {\n    it('should get user profile successfully', async () => {\n      const mockUser = createMockUser()\n      mockedApiClient.get.mockResolvedValue(mockUser)\n\n      const result = await authApi.getUserProfile()\n\n      expect(result).toEqual(mockUser)\n      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/auth/profile')\n    })\n\n    it('should handle get profile error', async () => {\n      const error = new Error('Unauthorized')\n      mockedApiClient.get.mockRejectedValue(error)\n\n      await expect(authApi.getUserProfile()).rejects.toThrow('Unauthorized')\n    })\n  })\n\n  describe('updateProfile', () => {\n    it('should update profile successfully', async () => {\n      const updateData = {\n        firstName: 'Updated',\n        lastName: 'Name',\n        email: '<EMAIL>',\n      }\n\n      const mockUpdatedUser = createMockUser(updateData)\n      mockedApiClient.put.mockResolvedValue(mockUpdatedUser)\n\n      const result = await authApi.updateProfile(updateData)\n\n      expect(result).toEqual(mockUpdatedUser)\n      expect(mockedApiClient.put).toHaveBeenCalledWith('/api/auth/profile', updateData)\n    })\n\n    it('should handle update profile error', async () => {\n      const updateData = {\n        email: 'invalid-email',\n      }\n\n      const error = new Error('Invalid email format')\n      mockedApiClient.put.mockRejectedValue(error)\n\n      await expect(authApi.updateProfile(updateData)).rejects.toThrow('Invalid email format')\n    })\n  })\n\n  describe('verifyEmail', () => {\n    it('should verify email successfully', async () => {\n      const token = 'verification-token'\n      const mockResponse = { success: true, message: 'Email verified' }\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await authApi.verifyEmail(token)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/auth/verify-email', { token })\n    })\n\n    it('should handle email verification error', async () => {\n      const token = 'invalid-token'\n      const error = new Error('Invalid verification token')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(authApi.verifyEmail(token)).rejects.toThrow('Invalid verification token')\n    })\n  })\n\n  describe('resendVerificationEmail', () => {\n    it('should resend verification email successfully', async () => {\n      const email = '<EMAIL>'\n      const mockResponse = { success: true, message: 'Verification email sent' }\n      mockedApiClient.post.mockResolvedValue(mockResponse)\n\n      const result = await authApi.resendVerificationEmail(email)\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/auth/resend-verification', { email })\n    })\n\n    it('should handle resend verification error', async () => {\n      const email = '<EMAIL>'\n      const error = new Error('Email not found')\n      mockedApiClient.post.mockRejectedValue(error)\n\n      await expect(authApi.resendVerificationEmail(email)).rejects.toThrow('Email not found')\n    })\n  })\n\n  describe('checkSession', () => {\n    it('should check session successfully', async () => {\n      const mockResponse = { valid: true, user: createMockUser() }\n      mockedApiClient.get.mockResolvedValue(mockResponse)\n\n      const result = await authApi.checkSession()\n\n      expect(result).toEqual(mockResponse)\n      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/auth/session')\n    })\n\n    it('should handle invalid session', async () => {\n      const error = new Error('Session expired')\n      mockedApiClient.get.mockRejectedValue(error)\n\n      await expect(authApi.checkSession()).rejects.toThrow('Session expired')\n    })\n  })\n})\n"], "names": ["jest", "mock", "mockedApiClient", "apiClient", "describe", "authApi", "beforeEach", "AuthA<PERSON>", "clearAllMocks", "it", "loginData", "username", "password", "rememberMe", "mockResponse", "user", "createMockUser", "token", "refreshToken", "expiresIn", "post", "mockResolvedValue", "result", "login", "expect", "toEqual", "toHaveBeenCalledWith", "error", "Error", "mockRejectedValue", "rejects", "toThrow", "undefined", "logout", "registerData", "email", "firstName", "lastName", "register", "refreshRequest", "changePasswordData", "currentPassword", "newPassword", "confirmPassword", "success", "message", "changePassword", "forgotPasswordData", "forgotPassword", "resetPasswordData", "resetPassword", "mockUser", "get", "getUserProfile", "updateData", "mockUpdatedUser", "put", "updateProfile", "verifyEmail", "resendVerificationEmail", "valid", "checkSession"], "mappings": ";AAWA,sBAAsB;AACtBA,KAAKC,IAAI,CAAC;;;;yBAZc;kEACF;2BACS;;;;;;AAW/B,MAAMC,kBAAkBC,kBAAS;AAEjCC,SAAS,WAAW;IAClB,IAAIC;IAEJC,WAAW;QACTD,UAAU,IAAIE,gBAAO;QACrBP,KAAKQ,aAAa;IACpB;IAEAJ,SAAS,SAAS;QAChBK,GAAG,6BAA6B;YAC9B,MAAMC,YAA0B;gBAC9BC,UAAU;gBACVC,UAAU;gBACVC,YAAY;YACd;YAEA,MAAMC,eAAe;gBACnBC,MAAMC,IAAAA,yBAAc;gBACpBC,OAAO;gBACPC,cAAc;gBACdC,WAAW;YACb;YAEAjB,gBAAgBkB,IAAI,CAACC,iBAAiB,CAACP;YAEvC,MAAMQ,SAAS,MAAMjB,QAAQkB,KAAK,CAACb;YAEnCc,OAAOF,QAAQG,OAAO,CAACX;YACvBU,OAAOtB,gBAAgBkB,IAAI,EAAEM,oBAAoB,CAAC,mBAAmBhB;QACvE;QAEAD,GAAG,6BAA6B;YAC9B,MAAMC,YAA0B;gBAC9BC,UAAU;gBACVC,UAAU;gBACVC,YAAY;YACd;YAEA,MAAMc,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBkB,IAAI,CAACS,iBAAiB,CAACF;YAEvC,MAAMH,OAAOnB,QAAQkB,KAAK,CAACb,YAAYoB,OAAO,CAACC,OAAO,CAAC;YACvDP,OAAOtB,gBAAgBkB,IAAI,EAAEM,oBAAoB,CAAC,mBAAmBhB;QACvE;IACF;IAEAN,SAAS,UAAU;QACjBK,GAAG,8BAA8B;YAC/BP,gBAAgBkB,IAAI,CAACC,iBAAiB,CAACW;YAEvC,MAAM3B,QAAQ4B,MAAM;YAEpBT,OAAOtB,gBAAgBkB,IAAI,EAAEM,oBAAoB,CAAC;QACpD;QAEAjB,GAAG,8BAA8B;YAC/B,MAAMkB,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBkB,IAAI,CAACS,iBAAiB,CAACF;YAEvC,MAAMH,OAAOnB,QAAQ4B,MAAM,IAAIH,OAAO,CAACC,OAAO,CAAC;QACjD;IACF;IAEA3B,SAAS,YAAY;QACnBK,GAAG,gCAAgC;YACjC,MAAMyB,eAAgC;gBACpCvB,UAAU;gBACVwB,OAAO;gBACPvB,UAAU;gBACVwB,WAAW;gBACXC,UAAU;YACZ;YAEA,MAAMvB,eAAe;gBACnBC,MAAMC,IAAAA,yBAAc,EAAC;oBACnBL,UAAU;oBACVwB,OAAO;oBACPC,WAAW;oBACXC,UAAU;gBACZ;gBACApB,OAAO;gBACPC,cAAc;gBACdC,WAAW;YACb;YAEAjB,gBAAgBkB,IAAI,CAACC,iBAAiB,CAACP;YAEvC,MAAMQ,SAAS,MAAMjB,QAAQiC,QAAQ,CAACJ;YAEtCV,OAAOF,QAAQG,OAAO,CAACX;YACvBU,OAAOtB,gBAAgBkB,IAAI,EAAEM,oBAAoB,CAAC,sBAAsBQ;QAC1E;QAEAzB,GAAG,oCAAoC;YACrC,MAAMyB,eAAgC;gBACpCvB,UAAU;gBACVwB,OAAO;gBACPvB,UAAU;gBACVwB,WAAW;gBACXC,UAAU;YACZ;YAEA,MAAMV,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBkB,IAAI,CAACS,iBAAiB,CAACF;YAEvC,MAAMH,OAAOnB,QAAQiC,QAAQ,CAACJ,eAAeJ,OAAO,CAACC,OAAO,CAAC;QAC/D;IACF;IAEA3B,SAAS,gBAAgB;QACvBK,GAAG,qCAAqC;YACtC,MAAM8B,iBAAiB;gBAAErB,cAAc;YAAgB;YACvD,MAAMJ,eAAe;gBACnBG,OAAO;gBACPC,cAAc;gBACdC,WAAW;YACb;YAEAjB,gBAAgBkB,IAAI,CAACC,iBAAiB,CAACP;YAEvC,MAAMQ,SAAS,MAAMjB,QAAQa,YAAY,CAACqB;YAE1Cf,OAAOF,QAAQG,OAAO,CAACX;YACvBU,OAAOtB,gBAAgBkB,IAAI,EAAEM,oBAAoB,CAAC,qBAAqBa;QACzE;QAEA9B,GAAG,qCAAqC;YACtC,MAAM8B,iBAAiB;gBAAErB,cAAc;YAAgB;YACvD,MAAMS,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBkB,IAAI,CAACS,iBAAiB,CAACF;YAEvC,MAAMH,OAAOnB,QAAQa,YAAY,CAACqB,iBAAiBT,OAAO,CAACC,OAAO,CAAC;QACrE;IACF;IAEA3B,SAAS,kBAAkB;QACzBK,GAAG,uCAAuC;YACxC,MAAM+B,qBAA4C;gBAChDC,iBAAiB;gBACjBC,aAAa;gBACbC,iBAAiB;YACnB;YAEA,MAAM7B,eAAe;gBAAE8B,SAAS;gBAAMC,SAAS;YAAgC;YAC/E3C,gBAAgBkB,IAAI,CAACC,iBAAiB,CAACP;YAEvC,MAAMQ,SAAS,MAAMjB,QAAQyC,cAAc,CAACN;YAE5ChB,OAAOF,QAAQG,OAAO,CAACX;YACvBU,OAAOtB,gBAAgBkB,IAAI,EAAEM,oBAAoB,CAAC,6BAA6Bc;QACjF;QAEA/B,GAAG,uCAAuC;YACxC,MAAM+B,qBAA4C;gBAChDC,iBAAiB;gBACjBC,aAAa;gBACbC,iBAAiB;YACnB;YAEA,MAAMhB,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBkB,IAAI,CAACS,iBAAiB,CAACF;YAEvC,MAAMH,OAAOnB,QAAQyC,cAAc,CAACN,qBAAqBV,OAAO,CAACC,OAAO,CAAC;QAC3E;IACF;IAEA3B,SAAS,kBAAkB;QACzBK,GAAG,kDAAkD;YACnD,MAAMsC,qBAA4C;gBAChDZ,OAAO;YACT;YAEA,MAAMrB,eAAe;gBAAE8B,SAAS;gBAAMC,SAAS;YAAmB;YAClE3C,gBAAgBkB,IAAI,CAACC,iBAAiB,CAACP;YAEvC,MAAMQ,SAAS,MAAMjB,QAAQ2C,cAAc,CAACD;YAE5CvB,OAAOF,QAAQG,OAAO,CAACX;YACvBU,OAAOtB,gBAAgBkB,IAAI,EAAEM,oBAAoB,CAAC,6BAA6BqB;QACjF;QAEAtC,GAAG,uCAAuC;YACxC,MAAMsC,qBAA4C;gBAChDZ,OAAO;YACT;YAEA,MAAMR,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBkB,IAAI,CAACS,iBAAiB,CAACF;YAEvC,MAAMH,OAAOnB,QAAQ2C,cAAc,CAACD,qBAAqBjB,OAAO,CAACC,OAAO,CAAC;QAC3E;IACF;IAEA3B,SAAS,iBAAiB;QACxBK,GAAG,sCAAsC;YACvC,MAAMwC,oBAA0C;gBAC9ChC,OAAO;gBACPyB,aAAa;gBACbC,iBAAiB;YACnB;YAEA,MAAM7B,eAAe;gBAAE8B,SAAS;gBAAMC,SAAS;YAA8B;YAC7E3C,gBAAgBkB,IAAI,CAACC,iBAAiB,CAACP;YAEvC,MAAMQ,SAAS,MAAMjB,QAAQ6C,aAAa,CAACD;YAE3CzB,OAAOF,QAAQG,OAAO,CAACX;YACvBU,OAAOtB,gBAAgBkB,IAAI,EAAEM,oBAAoB,CAAC,4BAA4BuB;QAChF;QAEAxC,GAAG,sCAAsC;YACvC,MAAMwC,oBAA0C;gBAC9ChC,OAAO;gBACPyB,aAAa;gBACbC,iBAAiB;YACnB;YAEA,MAAMhB,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBkB,IAAI,CAACS,iBAAiB,CAACF;YAEvC,MAAMH,OAAOnB,QAAQ6C,aAAa,CAACD,oBAAoBnB,OAAO,CAACC,OAAO,CAAC;QACzE;IACF;IAEA3B,SAAS,kBAAkB;QACzBK,GAAG,wCAAwC;YACzC,MAAM0C,WAAWnC,IAAAA,yBAAc;YAC/Bd,gBAAgBkD,GAAG,CAAC/B,iBAAiB,CAAC8B;YAEtC,MAAM7B,SAAS,MAAMjB,QAAQgD,cAAc;YAE3C7B,OAAOF,QAAQG,OAAO,CAAC0B;YACvB3B,OAAOtB,gBAAgBkD,GAAG,EAAE1B,oBAAoB,CAAC;QACnD;QAEAjB,GAAG,mCAAmC;YACpC,MAAMkB,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBkD,GAAG,CAACvB,iBAAiB,CAACF;YAEtC,MAAMH,OAAOnB,QAAQgD,cAAc,IAAIvB,OAAO,CAACC,OAAO,CAAC;QACzD;IACF;IAEA3B,SAAS,iBAAiB;QACxBK,GAAG,sCAAsC;YACvC,MAAM6C,aAAa;gBACjBlB,WAAW;gBACXC,UAAU;gBACVF,OAAO;YACT;YAEA,MAAMoB,kBAAkBvC,IAAAA,yBAAc,EAACsC;YACvCpD,gBAAgBsD,GAAG,CAACnC,iBAAiB,CAACkC;YAEtC,MAAMjC,SAAS,MAAMjB,QAAQoD,aAAa,CAACH;YAE3C9B,OAAOF,QAAQG,OAAO,CAAC8B;YACvB/B,OAAOtB,gBAAgBsD,GAAG,EAAE9B,oBAAoB,CAAC,qBAAqB4B;QACxE;QAEA7C,GAAG,sCAAsC;YACvC,MAAM6C,aAAa;gBACjBnB,OAAO;YACT;YAEA,MAAMR,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBsD,GAAG,CAAC3B,iBAAiB,CAACF;YAEtC,MAAMH,OAAOnB,QAAQoD,aAAa,CAACH,aAAaxB,OAAO,CAACC,OAAO,CAAC;QAClE;IACF;IAEA3B,SAAS,eAAe;QACtBK,GAAG,oCAAoC;YACrC,MAAMQ,QAAQ;YACd,MAAMH,eAAe;gBAAE8B,SAAS;gBAAMC,SAAS;YAAiB;YAChE3C,gBAAgBkB,IAAI,CAACC,iBAAiB,CAACP;YAEvC,MAAMQ,SAAS,MAAMjB,QAAQqD,WAAW,CAACzC;YAEzCO,OAAOF,QAAQG,OAAO,CAACX;YACvBU,OAAOtB,gBAAgBkB,IAAI,EAAEM,oBAAoB,CAAC,0BAA0B;gBAAET;YAAM;QACtF;QAEAR,GAAG,0CAA0C;YAC3C,MAAMQ,QAAQ;YACd,MAAMU,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBkB,IAAI,CAACS,iBAAiB,CAACF;YAEvC,MAAMH,OAAOnB,QAAQqD,WAAW,CAACzC,QAAQa,OAAO,CAACC,OAAO,CAAC;QAC3D;IACF;IAEA3B,SAAS,2BAA2B;QAClCK,GAAG,iDAAiD;YAClD,MAAM0B,QAAQ;YACd,MAAMrB,eAAe;gBAAE8B,SAAS;gBAAMC,SAAS;YAA0B;YACzE3C,gBAAgBkB,IAAI,CAACC,iBAAiB,CAACP;YAEvC,MAAMQ,SAAS,MAAMjB,QAAQsD,uBAAuB,CAACxB;YAErDX,OAAOF,QAAQG,OAAO,CAACX;YACvBU,OAAOtB,gBAAgBkB,IAAI,EAAEM,oBAAoB,CAAC,iCAAiC;gBAAES;YAAM;QAC7F;QAEA1B,GAAG,2CAA2C;YAC5C,MAAM0B,QAAQ;YACd,MAAMR,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBkB,IAAI,CAACS,iBAAiB,CAACF;YAEvC,MAAMH,OAAOnB,QAAQsD,uBAAuB,CAACxB,QAAQL,OAAO,CAACC,OAAO,CAAC;QACvE;IACF;IAEA3B,SAAS,gBAAgB;QACvBK,GAAG,qCAAqC;YACtC,MAAMK,eAAe;gBAAE8C,OAAO;gBAAM7C,MAAMC,IAAAA,yBAAc;YAAG;YAC3Dd,gBAAgBkD,GAAG,CAAC/B,iBAAiB,CAACP;YAEtC,MAAMQ,SAAS,MAAMjB,QAAQwD,YAAY;YAEzCrC,OAAOF,QAAQG,OAAO,CAACX;YACvBU,OAAOtB,gBAAgBkD,GAAG,EAAE1B,oBAAoB,CAAC;QACnD;QAEAjB,GAAG,iCAAiC;YAClC,MAAMkB,QAAQ,IAAIC,MAAM;YACxB1B,gBAAgBkD,GAAG,CAACvB,iBAAiB,CAACF;YAEtC,MAAMH,OAAOnB,QAAQwD,YAAY,IAAI/B,OAAO,CAACC,OAAO,CAAC;QACvD;IACF;AACF"}