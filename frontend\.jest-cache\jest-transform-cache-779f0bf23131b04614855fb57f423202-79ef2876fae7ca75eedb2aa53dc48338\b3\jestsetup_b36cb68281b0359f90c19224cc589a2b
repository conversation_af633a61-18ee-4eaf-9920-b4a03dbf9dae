a434add6ae630d0780297339ddf570c5
"use strict";
// Mock Next.js router
jest.mock("next/navigation", ()=>({
        useRouter () {
            return {
                push: jest.fn(),
                replace: jest.fn(),
                prefetch: jest.fn(),
                back: jest.fn(),
                forward: jest.fn(),
                refresh: jest.fn()
            };
        },
        usePathname () {
            return "/dashboard";
        },
        useSearchParams () {
            return new URLSearchParams();
        }
    }));
// Mock Next.js dynamic imports
jest.mock("next/dynamic", ()=>(func)=>{
        const DynamicComponent = (props)=>{
            const Component = func();
            return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {
                ...props
            });
        };
        DynamicComponent.displayName = "DynamicComponent";
        return DynamicComponent;
    });
// Mock Leaflet
jest.mock("leaflet", ()=>({
        map: jest.fn(()=>({
                setView: jest.fn(),
                on: jest.fn(),
                off: jest.fn(),
                remove: jest.fn()
            })),
        tileLayer: jest.fn(()=>({
                addTo: jest.fn()
            })),
        marker: jest.fn(()=>({
                addTo: jest.fn(),
                bindPopup: jest.fn(),
                setLatLng: jest.fn()
            })),
        icon: jest.fn(),
        divIcon: jest.fn(),
        Icon: {
            Default: {
                prototype: {},
                mergeOptions: jest.fn()
            }
        }
    }));
// Mock react-leaflet
jest.mock("react-leaflet", ()=>({
        MapContainer: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "map-container",
                children: children
            }),
        TileLayer: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "tile-layer"
            }),
        Marker: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "marker",
                children: children
            }),
        Popup: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "popup",
                children: children
            }),
        Circle: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "circle",
                children: children
            }),
        useMap: ()=>({
                setView: jest.fn(),
                on: jest.fn(),
                off: jest.fn()
            })
    }));
// Mock Recharts
jest.mock("recharts", ()=>({
        ResponsiveContainer: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "responsive-container",
                children: children
            }),
        LineChart: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "line-chart",
                children: children
            }),
        AreaChart: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "area-chart",
                children: children
            }),
        BarChart: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "bar-chart",
                children: children
            }),
        PieChart: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "pie-chart",
                children: children
            }),
        Line: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "line"
            }),
        Area: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "area"
            }),
        Bar: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "bar"
            }),
        Pie: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "pie"
            }),
        Cell: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "cell"
            }),
        XAxis: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "x-axis"
            }),
        YAxis: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "y-axis"
            }),
        CartesianGrid: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "cartesian-grid"
            }),
        Tooltip: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "tooltip"
            }),
        Legend: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "legend"
            })
    }));
// Mock Socket.IO
jest.mock("socket.io-client", ()=>({
        io: jest.fn(()=>({
                on: jest.fn(),
                off: jest.fn(),
                emit: jest.fn(),
                connect: jest.fn(),
                disconnect: jest.fn(),
                connected: true
            }))
    }));
// Mock react-hot-toast
jest.mock("react-hot-toast", ()=>({
        toast: {
            success: jest.fn(),
            error: jest.fn(),
            loading: jest.fn(),
            dismiss: jest.fn()
        },
        Toaster: ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "toaster"
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
require("@testing-library/jest-dom");
const _util = require("util");
// Polyfill for TextEncoder/TextDecoder
global.TextEncoder = _util.TextEncoder;
global.TextDecoder = _util.TextDecoder;
// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
    constructor(){}
    disconnect() {}
    observe() {}
    unobserve() {}
};
// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
    constructor(){}
    disconnect() {}
    observe() {}
    unobserve() {}
};
// Mock matchMedia
Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: jest.fn().mockImplementation((query)=>({
            matches: false,
            media: query,
            onchange: null,
            addListener: jest.fn(),
            removeListener: jest.fn(),
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
            dispatchEvent: jest.fn()
        }))
});
// Mock localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
global.localStorage = localStorageMock;
// Mock sessionStorage
const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
global.sessionStorage = sessionStorageMock;
// Suppress console errors during tests
const originalError = console.error;
beforeAll(()=>{
    console.error = (...args)=>{
        if (typeof args[0] === "string" && args[0].includes("Warning: ReactDOM.render is no longer supported")) {
            return;
        }
        originalError.call(console, ...args);
    };
});
afterAll(()=>{
    console.error = originalError;
});

//# sourceMappingURL=data:application/json;base64,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