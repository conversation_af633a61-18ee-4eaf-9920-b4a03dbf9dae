b86aa1cedc5b5873af610672e285f267
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    useAuth: function() {
        return useAuth;
    },
    useAuthStore: function() {
        return useAuthStore;
    },
    usePermissions: function() {
        return usePermissions;
    }
});
const _zustand = require("zustand");
const _middleware = require("zustand/middleware");
const _immer = require("zustand/middleware/immer");
const _authapi = require("../api/auth-api");
const _reacthottoast = require("react-hot-toast");
const useAuthStore = (0, _zustand.create)()((0, _middleware.devtools)((0, _middleware.persist)((0, _middleware.subscribeWithSelector)((0, _immer.immer)((set, get)=>({
        // Initial state
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        // Login
        login: async (credentials)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.login(credentials);
                if (response.success && response.data) {
                    set((state)=>{
                        state.user = response.data.user;
                        state.token = response.data.token;
                        state.refreshToken = response.data.refreshToken;
                        state.isAuthenticated = true;
                        state.isLoading = false;
                        state.error = null;
                    });
                    // Set token in API client
                    _authapi.authApi.setAuthToken(response.data.token);
                    _reacthottoast.toast.success("Login successful");
                    return true;
                } else {
                    throw new Error(response.message || "Login failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Login failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                    state.isAuthenticated = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Logout
        logout: async ()=>{
            try {
                await _authapi.authApi.logout();
            } catch (error) {
                console.error("Logout error:", error);
            } finally{
                set((state)=>{
                    state.user = null;
                    state.token = null;
                    state.refreshToken = null;
                    state.isAuthenticated = false;
                    state.error = null;
                });
                // Clear token from API client
                _authapi.authApi.clearAuthToken();
                _reacthottoast.toast.success("Logged out successfully");
            }
        },
        // Register
        register: async (userData)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.register(userData);
                if (response.success && response.data) {
                    set((state)=>{
                        state.user = response.data.user;
                        state.token = response.data.token;
                        state.refreshToken = response.data.refreshToken;
                        state.isAuthenticated = true;
                        state.isLoading = false;
                    });
                    // Store tokens in localStorage
                    localStorage.setItem("token", response.data.token);
                    localStorage.setItem("refreshToken", response.data.refreshToken);
                    _reacthottoast.toast.success("Registration successful. Welcome!");
                    return true;
                } else {
                    throw new Error(response.message || "Registration failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Registration failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Refresh token
        refreshAuthToken: async ()=>{
            const { refreshToken } = get();
            if (!refreshToken) return false;
            try {
                const response = await _authapi.authApi.refreshToken({
                    refreshToken
                });
                if (response.success && response.data) {
                    set((state)=>{
                        state.token = response.data.token;
                        state.refreshToken = response.data.refreshToken;
                        state.error = null;
                    });
                    // Update token in API client
                    _authapi.authApi.setAuthToken(response.data.token);
                    return true;
                } else {
                    // Refresh failed, logout user
                    get().logout();
                    return false;
                }
            } catch (error) {
                console.error("Token refresh failed:", error);
                get().logout();
                return false;
            }
        },
        // Alias for refreshAuthToken (for compatibility with tests)
        refreshToken: async ()=>{
            return get().refreshAuthToken();
        },
        // Change password
        changePassword: async (passwords)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.changePassword(passwords);
                if (response.success) {
                    set((state)=>{
                        state.isLoading = false;
                    });
                    _reacthottoast.toast.success("Password changed successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Password change failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Password change failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Update profile
        updateProfile: async (userData)=>{
            set((state)=>{
                state.isLoading = true;
                state.error = null;
            });
            try {
                const response = await _authapi.authApi.updateProfile(userData);
                if (response.success && response.data) {
                    set((state)=>{
                        state.user = response.data;
                        state.isLoading = false;
                    });
                    _reacthottoast.toast.success("Profile updated successfully");
                    return true;
                } else {
                    throw new Error(response.message || "Profile update failed");
                }
            } catch (error) {
                const message = error instanceof Error ? error.message : "Profile update failed";
                set((state)=>{
                    state.error = message;
                    state.isLoading = false;
                });
                _reacthottoast.toast.error(message);
                return false;
            }
        },
        // Permission checks
        hasPermission: (check)=>{
            const { user } = get();
            if (!user || !user.permissions) return false;
            return user.permissions.some((permission)=>permission.resource === check.resource && permission.action === check.action);
        },
        hasRole: (roleName)=>{
            const { user } = get();
            if (!user || !user.roles) return false;
            return user.roles.some((role)=>role.name === roleName);
        },
        canAccess: (resource, action)=>{
            return get().hasPermission({
                resource,
                action
            });
        },
        // Session management
        checkSession: async ()=>{
            const { token, refreshToken } = get();
            if (!token) {
                set((state)=>{
                    state.isAuthenticated = false;
                });
                return false;
            }
            try {
                // Check if token is still valid
                const isValid = await _authapi.authApi.validateToken();
                if (isValid) {
                    set((state)=>{
                        state.isAuthenticated = true;
                    });
                    return true;
                } else if (refreshToken) {
                    // Try to refresh token
                    return await get().refreshAuthToken();
                } else {
                    // No valid token or refresh token
                    get().logout();
                    return false;
                }
            } catch (error) {
                console.error("Session check failed:", error);
                get().logout();
                return false;
            }
        },
        // Fetch user profile
        fetchUserProfile: async ()=>{
            try {
                const user = await _authapi.authApi.getUserProfile();
                set((state)=>{
                    state.user = user;
                });
            } catch (error) {
                console.error("Failed to fetch user profile:", error);
            }
        },
        // Update last activity
        updateLastActivity: ()=>{
            const { user } = get();
            if (user) {
                set((state)=>{
                    if (state.user) {
                        state.user.lastLogin = new Date().toISOString();
                    }
                });
            }
        },
        // Clear error
        clearError: ()=>{
            set((state)=>{
                state.error = null;
            });
        },
        // Set loading
        setLoading: (loading)=>{
            set((state)=>{
                state.isLoading = loading;
            });
        }
    }))), {
    name: "auth-store",
    partialize: (state)=>({
            user: state.user,
            token: state.token,
            refreshToken: state.refreshToken,
            isAuthenticated: state.isAuthenticated
        })
}), {
    name: "auth-store"
}));
const useAuth = ()=>useAuthStore((state)=>({
            user: state.user,
            isAuthenticated: state.isAuthenticated,
            isLoading: state.isLoading,
            error: state.error
        }));
const usePermissions = ()=>useAuthStore((state)=>({
            hasPermission: state.hasPermission,
            hasRole: state.hasRole,
            canAccess: state.canAccess
        }));
// Auto-refresh token before expiration
let refreshInterval = null;
useAuthStore.subscribe((state)=>state.token, (token)=>{
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
    if (token) {
        // Refresh token every 50 minutes (assuming 60-minute expiration)
        refreshInterval = setInterval(()=>{
            useAuthStore.getState().refreshAuthToken();
        }, 50 * 60 * 1000);
    }
});

//# sourceMappingURL=data:application/json;base64,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