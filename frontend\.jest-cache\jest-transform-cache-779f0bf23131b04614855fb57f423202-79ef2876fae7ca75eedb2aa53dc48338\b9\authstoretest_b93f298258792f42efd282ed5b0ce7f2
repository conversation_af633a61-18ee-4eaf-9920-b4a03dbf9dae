a968ae0a983ac35e0351c904936be430
"use strict";
// Mock the API
jest.mock("@/api/auth-api");
// Mock react-hot-toast
jest.mock("react-hot-toast", ()=>({
        toast: {
            success: jest.fn(),
            error: jest.fn(),
            loading: jest.fn(),
            dismiss: jest.fn()
        }
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _react = require("@testing-library/react");
const _authstore = require("../auth-store");
const _authapi = require("../../api/auth-api");
const _testutils = require("../../lib/test-utils");
const mockedAuthApi = _authapi.authApi;
describe("Auth Store", ()=>{
    beforeEach(()=>{
        // Reset store state
        _authstore.useAuthStore.setState({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
        });
        // Clear all mocks
        jest.clearAllMocks();
        // Clear localStorage
        localStorage.clear();
    });
    describe("login", ()=>{
        it("should login successfully", async ()=>{
            const mockUser = (0, _testutils.createMockUser)();
            const loginData = {
                username: "testuser",
                password: "password123",
                rememberMe: false
            };
            const mockResponse = {
                user: mockUser,
                token: "mock-token",
                refreshToken: "mock-refresh-token",
                expiresIn: 3600
            };
            mockedAuthApi.login.mockResolvedValue(mockResponse);
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            await (0, _react.act)(async ()=>{
                const success = await result.current.login(loginData);
                expect(success).toBe(true);
            });
            expect(result.current.user).toEqual(mockUser);
            expect(result.current.token).toBe("mock-token");
            expect(result.current.refreshToken).toBe("mock-refresh-token");
            expect(result.current.isAuthenticated).toBe(true);
            expect(result.current.isLoading).toBe(false);
            expect(result.current.error).toBeNull();
            expect(mockedAuthApi.login).toHaveBeenCalledWith(loginData);
        });
        it("should handle login failure", async ()=>{
            const loginData = {
                username: "testuser",
                password: "wrongpassword",
                rememberMe: false
            };
            mockedAuthApi.login.mockRejectedValue(new Error("Invalid credentials"));
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            await (0, _react.act)(async ()=>{
                const success = await result.current.login(loginData);
                expect(success).toBe(false);
            });
            expect(result.current.user).toBeNull();
            expect(result.current.token).toBeNull();
            expect(result.current.isAuthenticated).toBe(false);
            expect(result.current.isLoading).toBe(false);
            expect(result.current.error).toBe("Invalid credentials");
        });
        it("should set loading state during login", async ()=>{
            const loginData = {
                username: "testuser",
                password: "password123",
                rememberMe: false
            };
            let resolveLogin;
            const loginPromise = new Promise((resolve)=>{
                resolveLogin = resolve;
            });
            mockedAuthApi.login.mockReturnValue(loginPromise);
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            // Start login
            (0, _react.act)(()=>{
                result.current.login(loginData);
            });
            // Check loading state
            expect(result.current.isLoading).toBe(true);
            // Resolve login
            await (0, _react.act)(async ()=>{
                resolveLogin({
                    user: (0, _testutils.createMockUser)(),
                    token: "mock-token",
                    refreshToken: "mock-refresh-token",
                    expiresIn: 3600
                });
                await loginPromise;
            });
            expect(result.current.isLoading).toBe(false);
        });
    });
    describe("logout", ()=>{
        it("should logout successfully", async ()=>{
            // Set initial authenticated state
            const mockUser = (0, _testutils.createMockUser)();
            _authstore.useAuthStore.setState({
                user: mockUser,
                token: "mock-token",
                refreshToken: "mock-refresh-token",
                isAuthenticated: true
            });
            mockedAuthApi.logout.mockResolvedValue();
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            await (0, _react.act)(async ()=>{
                await result.current.logout();
            });
            expect(result.current.user).toBeNull();
            expect(result.current.token).toBeNull();
            expect(result.current.refreshToken).toBeNull();
            expect(result.current.isAuthenticated).toBe(false);
            expect(result.current.error).toBeNull();
            expect(mockedAuthApi.logout).toHaveBeenCalled();
        });
        it("should handle logout failure gracefully", async ()=>{
            // Set initial authenticated state
            const mockUser = (0, _testutils.createMockUser)();
            _authstore.useAuthStore.setState({
                user: mockUser,
                token: "mock-token",
                refreshToken: "mock-refresh-token",
                isAuthenticated: true
            });
            mockedAuthApi.logout.mockRejectedValue(new Error("Logout failed"));
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            await (0, _react.act)(async ()=>{
                await result.current.logout();
            });
            // Should still clear local state even if API call fails
            expect(result.current.user).toBeNull();
            expect(result.current.token).toBeNull();
            expect(result.current.refreshToken).toBeNull();
            expect(result.current.isAuthenticated).toBe(false);
        });
    });
    describe("register", ()=>{
        it("should register successfully", async ()=>{
            const registerData = {
                username: "newuser",
                email: "<EMAIL>",
                password: "password123",
                firstName: "New",
                lastName: "User"
            };
            const mockUser = (0, _testutils.createMockUser)({
                username: "newuser",
                email: "<EMAIL>",
                firstName: "New",
                lastName: "User"
            });
            const mockResponse = {
                user: mockUser,
                token: "mock-token",
                refreshToken: "mock-refresh-token",
                expiresIn: 3600
            };
            mockedAuthApi.register.mockResolvedValue(mockResponse);
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            await (0, _react.act)(async ()=>{
                const success = await result.current.register(registerData);
                expect(success).toBe(true);
            });
            expect(result.current.user).toEqual(mockUser);
            expect(result.current.token).toBe("mock-token");
            expect(result.current.isAuthenticated).toBe(true);
            expect(mockedAuthApi.register).toHaveBeenCalledWith(registerData);
        });
        it("should handle registration failure", async ()=>{
            const registerData = {
                username: "existinguser",
                email: "<EMAIL>",
                password: "password123",
                firstName: "Existing",
                lastName: "User"
            };
            mockedAuthApi.register.mockRejectedValue(new Error("Username already exists"));
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            await (0, _react.act)(async ()=>{
                const success = await result.current.register(registerData);
                expect(success).toBe(false);
            });
            expect(result.current.user).toBeNull();
            expect(result.current.isAuthenticated).toBe(false);
            expect(result.current.error).toBe("Username already exists");
        });
    });
    describe("refreshToken", ()=>{
        it("should refresh token successfully", async ()=>{
            const mockUser = (0, _testutils.createMockUser)();
            _authstore.useAuthStore.setState({
                user: mockUser,
                token: "old-token",
                refreshToken: "refresh-token",
                isAuthenticated: true
            });
            const mockResponse = {
                token: "new-token",
                refreshToken: "new-refresh-token",
                expiresIn: 3600
            };
            mockedAuthApi.refreshToken.mockResolvedValue(mockResponse);
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            await (0, _react.act)(async ()=>{
                const success = await result.current.refreshToken();
                expect(success).toBe(true);
            });
            expect(result.current.token).toBe("new-token");
            expect(result.current.refreshToken).toBe("new-refresh-token");
            expect(result.current.isAuthenticated).toBe(true);
            expect(mockedAuthApi.refreshToken).toHaveBeenCalledWith({
                refreshToken: "refresh-token"
            });
        });
        it("should handle refresh token failure", async ()=>{
            _authstore.useAuthStore.setState({
                user: (0, _testutils.createMockUser)(),
                token: "old-token",
                refreshToken: "invalid-refresh-token",
                isAuthenticated: true
            });
            mockedAuthApi.refreshToken.mockRejectedValue(new Error("Invalid refresh token"));
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            await (0, _react.act)(async ()=>{
                const success = await result.current.refreshToken();
                expect(success).toBe(false);
            });
            // Should logout user when refresh fails
            expect(result.current.user).toBeNull();
            expect(result.current.token).toBeNull();
            expect(result.current.refreshToken).toBeNull();
            expect(result.current.isAuthenticated).toBe(false);
        });
    });
    describe("permission checks", ()=>{
        it("should check permissions correctly", ()=>{
            const mockUser = (0, _testutils.createMockUser)({
                permissions: [
                    {
                        id: 1,
                        name: "UAV_READ",
                        resource: "UAV",
                        action: "READ",
                        description: "Read UAV data"
                    },
                    {
                        id: 2,
                        name: "UAV_WRITE",
                        resource: "UAV",
                        action: "WRITE",
                        description: "Write UAV data"
                    }
                ]
            });
            _authstore.useAuthStore.setState({
                user: mockUser,
                isAuthenticated: true
            });
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            expect(result.current.hasPermission({
                permission: "UAV_READ"
            })).toBe(true);
            expect(result.current.hasPermission({
                permission: "UAV_DELETE"
            })).toBe(false);
            expect(result.current.canAccess("UAV", "READ")).toBe(true);
            expect(result.current.canAccess("UAV", "DELETE")).toBe(false);
        });
        it("should check roles correctly", ()=>{
            const mockUser = (0, _testutils.createMockUser)({
                roles: [
                    {
                        id: 1,
                        name: "ADMIN",
                        description: "Administrator",
                        permissions: []
                    },
                    {
                        id: 2,
                        name: "USER",
                        description: "Regular user",
                        permissions: []
                    }
                ]
            });
            _authstore.useAuthStore.setState({
                user: mockUser,
                isAuthenticated: true
            });
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            expect(result.current.hasRole("ADMIN")).toBe(true);
            expect(result.current.hasRole("USER")).toBe(true);
            expect(result.current.hasRole("SUPER_ADMIN")).toBe(false);
        });
        it("should return false for unauthenticated users", ()=>{
            _authstore.useAuthStore.setState({
                user: null,
                isAuthenticated: false
            });
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            expect(result.current.hasPermission({
                permission: "UAV_READ"
            })).toBe(false);
            expect(result.current.hasRole("ADMIN")).toBe(false);
            expect(result.current.canAccess("UAV", "READ")).toBe(false);
        });
    });
    describe("utility functions", ()=>{
        it("should clear error", ()=>{
            _authstore.useAuthStore.setState({
                error: "Some error"
            });
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            (0, _react.act)(()=>{
                result.current.clearError();
            });
            expect(result.current.error).toBeNull();
        });
        it("should set loading state", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            (0, _react.act)(()=>{
                result.current.setLoading(true);
            });
            expect(result.current.isLoading).toBe(true);
            (0, _react.act)(()=>{
                result.current.setLoading(false);
            });
            expect(result.current.isLoading).toBe(false);
        });
        it("should update last activity", ()=>{
            const mockUser = (0, _testutils.createMockUser)();
            _authstore.useAuthStore.setState({
                user: mockUser,
                isAuthenticated: true
            });
            const { result } = (0, _react.renderHook)(()=>(0, _authstore.useAuthStore)());
            const beforeUpdate = result.current.user?.lastActivity;
            (0, _react.act)(()=>{
                result.current.updateLastActivity();
            });
            const afterUpdate = result.current.user?.lastActivity;
            expect(afterUpdate).not.toBe(beforeUpdate);
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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