998ebe255ddd19f29a7593ff16e7084e
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AuthApi: function() {
        return AuthApi;
    },
    authApi: function() {
        return authApi;
    },
    default: function() {
        return _default;
    }
});
const _apiclient = /*#__PURE__*/ _interop_require_default(require("../lib/api-client"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
class AuthApi {
    // Authentication endpoints
    async login(credentials) {
        return _apiclient.default.post(`${this.basePath}/login`, credentials);
    }
    async logout() {
        return _apiclient.default.post(`${this.basePath}/logout`);
    }
    async register(userData) {
        return _apiclient.default.post(`${this.basePath}/register`, userData);
    }
    async refreshToken(request) {
        return _apiclient.default.post(`${this.basePath}/refresh`, request);
    }
    async validateToken() {
        try {
            await _apiclient.default.get(`${this.basePath}/validate`);
            return true;
        } catch (error) {
            return false;
        }
    }
    // Password management
    async changePassword(passwords) {
        return _apiclient.default.post(`${this.basePath}/change-password`, passwords);
    }
    async forgotPassword(request) {
        return _apiclient.default.post(`${this.basePath}/forgot-password`, request);
    }
    async resetPassword(request) {
        return _apiclient.default.post(`${this.basePath}/reset-password`, request);
    }
    // User profile
    async getUserProfile() {
        return _apiclient.default.get(`${this.basePath}/profile`);
    }
    async updateProfile(userData) {
        return _apiclient.default.put(`${this.basePath}/profile`, userData);
    }
    // Session management
    async getSessions() {
        return _apiclient.default.get(`${this.basePath}/sessions`);
    }
    async terminateSession(sessionId) {
        return _apiclient.default.delete(`${this.basePath}/sessions/${sessionId}`);
    }
    async terminateAllSessions() {
        return _apiclient.default.delete(`${this.basePath}/sessions`);
    }
    // Two-factor authentication
    async setupTwoFactor() {
        return _apiclient.default.post(`${this.basePath}/2fa/setup`);
    }
    async verifyTwoFactor(code) {
        return _apiclient.default.post(`${this.basePath}/2fa/verify`, {
            code
        });
    }
    async disableTwoFactor(code) {
        return _apiclient.default.post(`${this.basePath}/2fa/disable`, {
            code
        });
    }
    // OAuth
    async oauthLogin(provider, code, state) {
        return _apiclient.default.post(`${this.basePath}/oauth/${provider}`, {
            code,
            state
        });
    }
    // Security
    async getSecurityEvents() {
        return _apiclient.default.get(`${this.basePath}/security/events`);
    }
    async reportSecurityIncident(incident) {
        return _apiclient.default.post(`${this.basePath}/security/incident`, incident);
    }
    // Token management
    setAuthToken(token) {
        _apiclient.default.setAuthToken(token);
    }
    clearAuthToken() {
        _apiclient.default.clearAuthToken();
    }
    constructor(){
        this.basePath = "/api/auth";
    }
}
const authApi = new AuthApi();
const _default = authApi;

//# sourceMappingURL=data:application/json;base64,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