{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": ["Input", "React", "forwardRef", "className", "type", "props", "ref", "input", "cn", "displayName"], "mappings": ";;;;+BAwBSA;;;eAAAA;;;;+DAxBc;uBAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKnB,MAAMA,sBAAQC,OAAMC,UAAU,CAC5B,CAAC,EAAEC,SAAS,EAAEC,IAAI,EAAE,GAAGC,OAAO,EAAEC;IAC9B,qBACE,qBAACC;QACCH,MAAMA;QACND,WAAWK,IAAAA,SAAE,EACX,gWACAL;QAEFG,KAAKA;QACJ,GAAGD,KAAK;;AAGf;AAEFL,MAAMS,WAAW,GAAG"}