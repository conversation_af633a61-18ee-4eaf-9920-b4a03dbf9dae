efe8f15e1ac0a28970943eaee52d1224
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _card = require("../card");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
describe("Card Components", ()=>{
    describe("Card", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.Card, {
                "data-testid": "card",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Card content"
                })
            }));
            const card = _testutils.screen.getByTestId("card");
            expect(card).toBeInTheDocument();
            expect(card).toHaveClass("rounded-lg", "border", "bg-card");
        });
        it("applies custom className", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.Card, {
                className: "custom-class",
                "data-testid": "card",
                children: "Content"
            }));
            const card = _testutils.screen.getByTestId("card");
            expect(card).toHaveClass("custom-class");
        });
        it("forwards ref correctly", ()=>{
            const ref = /*#__PURE__*/ _react.default.createRef();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.Card, {
                ref: ref,
                children: "Content"
            }));
            expect(ref.current).toBeInstanceOf(HTMLDivElement);
        });
        it("accepts additional props", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.Card, {
                "data-testid": "card",
                role: "region",
                "aria-label": "Test card",
                children: "Content"
            }));
            const card = _testutils.screen.getByTestId("card");
            expect(card).toHaveAttribute("role", "region");
            expect(card).toHaveAttribute("aria-label", "Test card");
        });
    });
    describe("CardHeader", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardHeader, {
                "data-testid": "card-header",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Header content"
                })
            }));
            const header = _testutils.screen.getByTestId("card-header");
            expect(header).toBeInTheDocument();
            expect(header).toHaveClass("flex", "flex-col", "space-y-1.5", "p-6");
        });
        it("applies custom className", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardHeader, {
                className: "custom-header",
                "data-testid": "card-header",
                children: "Header"
            }));
            const header = _testutils.screen.getByTestId("card-header");
            expect(header).toHaveClass("custom-header");
        });
        it("forwards ref correctly", ()=>{
            const ref = /*#__PURE__*/ _react.default.createRef();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardHeader, {
                ref: ref,
                children: "Header"
            }));
            expect(ref.current).toBeInstanceOf(HTMLDivElement);
        });
    });
    describe("CardTitle", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardTitle, {
                "data-testid": "card-title",
                children: "Card Title"
            }));
            const title = _testutils.screen.getByTestId("card-title");
            expect(title).toBeInTheDocument();
            expect(title).toHaveClass("text-2xl", "font-semibold", "leading-none", "tracking-tight");
            expect(title).toHaveTextContent("Card Title");
        });
        it("applies custom className", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardTitle, {
                className: "custom-title",
                "data-testid": "card-title",
                children: "Title"
            }));
            const title = _testutils.screen.getByTestId("card-title");
            expect(title).toHaveClass("custom-title");
        });
        it("forwards ref correctly", ()=>{
            const ref = /*#__PURE__*/ _react.default.createRef();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardTitle, {
                ref: ref,
                children: "Title"
            }));
            expect(ref.current).toBeInstanceOf(HTMLParagraphElement);
        });
        it("renders as different HTML elements", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardTitle, {
                as: "h1",
                "data-testid": "card-title",
                children: "Title as H1"
            }));
            const title = _testutils.screen.getByTestId("card-title");
            expect(title.tagName).toBe("H1");
        });
    });
    describe("CardDescription", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardDescription, {
                "data-testid": "card-description",
                children: "This is a card description"
            }));
            const description = _testutils.screen.getByTestId("card-description");
            expect(description).toBeInTheDocument();
            expect(description).toHaveClass("text-sm", "text-muted-foreground");
            expect(description).toHaveTextContent("This is a card description");
        });
        it("applies custom className", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardDescription, {
                className: "custom-description",
                "data-testid": "card-description",
                children: "Description"
            }));
            const description = _testutils.screen.getByTestId("card-description");
            expect(description).toHaveClass("custom-description");
        });
        it("forwards ref correctly", ()=>{
            const ref = /*#__PURE__*/ _react.default.createRef();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardDescription, {
                ref: ref,
                children: "Description"
            }));
            expect(ref.current).toBeInstanceOf(HTMLParagraphElement);
        });
    });
    describe("CardContent", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardContent, {
                "data-testid": "card-content",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                    children: "Card content goes here"
                })
            }));
            const content = _testutils.screen.getByTestId("card-content");
            expect(content).toBeInTheDocument();
            expect(content).toHaveClass("p-6", "pt-0");
            expect(_testutils.screen.getByText("Card content goes here")).toBeInTheDocument();
        });
        it("applies custom className", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardContent, {
                className: "custom-content",
                "data-testid": "card-content",
                children: "Content"
            }));
            const content = _testutils.screen.getByTestId("card-content");
            expect(content).toHaveClass("custom-content");
        });
        it("forwards ref correctly", ()=>{
            const ref = /*#__PURE__*/ _react.default.createRef();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardContent, {
                ref: ref,
                children: "Content"
            }));
            expect(ref.current).toBeInstanceOf(HTMLDivElement);
        });
    });
    describe("CardFooter", ()=>{
        it("renders correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardFooter, {
                "data-testid": "card-footer",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                    children: "Action"
                })
            }));
            const footer = _testutils.screen.getByTestId("card-footer");
            expect(footer).toBeInTheDocument();
            expect(footer).toHaveClass("flex", "items-center", "p-6", "pt-0");
            expect(_testutils.screen.getByRole("button", {
                name: "Action"
            })).toBeInTheDocument();
        });
        it("applies custom className", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardFooter, {
                className: "custom-footer",
                "data-testid": "card-footer",
                children: "Footer"
            }));
            const footer = _testutils.screen.getByTestId("card-footer");
            expect(footer).toHaveClass("custom-footer");
        });
        it("forwards ref correctly", ()=>{
            const ref = /*#__PURE__*/ _react.default.createRef();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardFooter, {
                ref: ref,
                children: "Footer"
            }));
            expect(ref.current).toBeInstanceOf(HTMLDivElement);
        });
    });
    describe("Complete Card", ()=>{
        it("renders a complete card with all components", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.Card, {
                "data-testid": "complete-card",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.CardHeader, {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardTitle, {
                                children: "UAV Status"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardDescription, {
                                children: "Current status of UAV-001"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.CardContent, {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                children: "Status: Active"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                children: "Battery: 85%"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.CardFooter, {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                children: "View Details"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                children: "Edit"
                            })
                        ]
                    })
                ]
            }));
            const card = _testutils.screen.getByTestId("complete-card");
            expect(card).toBeInTheDocument();
            expect(_testutils.screen.getByText("UAV Status")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Current status of UAV-001")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Status: Active")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Battery: 85%")).toBeInTheDocument();
            expect(_testutils.screen.getByRole("button", {
                name: "View Details"
            })).toBeInTheDocument();
            expect(_testutils.screen.getByRole("button", {
                name: "Edit"
            })).toBeInTheDocument();
        });
        it("maintains proper semantic structure", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.Card, {
                role: "article",
                "aria-labelledby": "card-title",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_card.CardHeader, {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardTitle, {
                                id: "card-title",
                                children: "Article Title"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardDescription, {
                                children: "Article description"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardContent, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                            children: "Article content"
                        })
                    })
                ]
            }));
            const card = _testutils.screen.getByRole("article");
            expect(card).toHaveAttribute("aria-labelledby", "card-title");
            const title = _testutils.screen.getByText("Article Title");
            expect(title).toHaveAttribute("id", "card-title");
        });
        it("handles interactive cards", ()=>{
            const handleClick = jest.fn();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.Card, {
                onClick: handleClick,
                className: "cursor-pointer hover:bg-accent",
                "data-testid": "interactive-card",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardContent, {
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                        children: "Click me"
                    })
                })
            }));
            const card = _testutils.screen.getByTestId("interactive-card");
            expect(card).toHaveClass("cursor-pointer");
            card.click();
            expect(handleClick).toHaveBeenCalledTimes(1);
        });
        it("supports keyboard navigation for interactive cards", ()=>{
            const handleKeyDown = jest.fn();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_card.Card, {
                tabIndex: 0,
                onKeyDown: handleKeyDown,
                "data-testid": "keyboard-card",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_card.CardContent, {
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                        children: "Keyboard accessible"
                    })
                })
            }));
            const card = _testutils.screen.getByTestId("keyboard-card");
            expect(card).toHaveAttribute("tabIndex", "0");
            card.focus();
            expect(card).toHaveFocus();
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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