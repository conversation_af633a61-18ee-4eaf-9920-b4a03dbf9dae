29a8d5e990774112f233f63975d03f0a
"use strict";
// Mock the API client
jest.mock("@/lib/api-client");
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _uavapi = require("../uav-api");
const _apiclient = /*#__PURE__*/ _interop_require_default(require("../../lib/api-client"));
const _testutils = require("../../lib/test-utils");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockedApiClient = _apiclient.default;
describe("UAVApi", ()=>{
    let uavApi;
    beforeEach(()=>{
        uavApi = new _uavapi.UAVApi();
        jest.clearAllMocks();
    });
    describe("getUAVs", ()=>{
        it("should get UAVs without filters", async ()=>{
            const mockUAVs = [
                (0, _testutils.createMockUAV)(),
                (0, _testutils.createMockUAV)({
                    id: 2,
                    rfidTag: "UAV-002"
                })
            ];
            mockedApiClient.get.mockResolvedValue(mockUAVs);
            const result = await uavApi.getUAVs();
            expect(result).toEqual(mockUAVs);
            expect(mockedApiClient.get).toHaveBeenCalledWith("/api/uav/all");
        });
        it("should get UAVs with filters and pagination", async ()=>{
            const filter = {
                status: "AUTHORIZED",
                operationalStatus: "READY"
            };
            const pagination = {
                page: 1,
                limit: 10,
                sortBy: "id",
                sortOrder: "desc"
            };
            const mockUAVs = [
                (0, _testutils.createMockUAV)()
            ];
            mockedApiClient.get.mockResolvedValue(mockUAVs);
            const result = await uavApi.getUAVs(filter, pagination);
            expect(result).toEqual(mockUAVs);
            expect(mockedApiClient.get).toHaveBeenCalledWith("/api/uav/all?status=AUTHORIZED&operationalStatus=READY&page=1&limit=10&sortBy=id&sortOrder=desc");
        });
        it("should handle get UAVs error", async ()=>{
            const error = new Error("Failed to fetch UAVs");
            mockedApiClient.get.mockRejectedValue(error);
            await expect(uavApi.getUAVs()).rejects.toThrow("Failed to fetch UAVs");
        });
    });
    describe("getUAVById", ()=>{
        it("should get UAV by ID successfully", async ()=>{
            const mockUAV = (0, _testutils.createMockUAV)();
            mockedApiClient.get.mockResolvedValue(mockUAV);
            const result = await uavApi.getUAVById(1);
            expect(result).toEqual(mockUAV);
            expect(mockedApiClient.get).toHaveBeenCalledWith("/api/uav/1");
        });
        it("should handle UAV not found", async ()=>{
            const error = new Error("UAV not found");
            mockedApiClient.get.mockRejectedValue(error);
            await expect(uavApi.getUAVById(999)).rejects.toThrow("UAV not found");
        });
    });
    describe("createUAV", ()=>{
        it("should create UAV successfully", async ()=>{
            const createData = {
                rfidTag: "UAV-003",
                ownerName: "New Owner",
                model: "New Model"
            };
            const mockResponse = (0, _testutils.mockApiResponse)((0, _testutils.createMockUAV)({
                ...createData,
                id: 3
            }));
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await uavApi.createUAV(createData);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/uav", createData);
        });
        it("should handle create UAV error", async ()=>{
            const createData = {
                rfidTag: "DUPLICATE-TAG",
                ownerName: "Owner",
                model: "Model"
            };
            const error = new Error("RFID tag already exists");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(uavApi.createUAV(createData)).rejects.toThrow("RFID tag already exists");
        });
    });
    describe("updateUAV", ()=>{
        it("should update UAV successfully", async ()=>{
            const updateData = {
                ownerName: "Updated Owner",
                model: "Updated Model"
            };
            const mockResponse = (0, _testutils.mockApiResponse)((0, _testutils.createMockUAV)({
                id: 1,
                ...updateData
            }));
            mockedApiClient.put.mockResolvedValue(mockResponse);
            const result = await uavApi.updateUAV(1, updateData);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.put).toHaveBeenCalledWith("/api/uav/1", updateData);
        });
        it("should handle update UAV error", async ()=>{
            const updateData = {
                ownerName: "Updated Owner"
            };
            const error = new Error("UAV not found");
            mockedApiClient.put.mockRejectedValue(error);
            await expect(uavApi.updateUAV(999, updateData)).rejects.toThrow("UAV not found");
        });
    });
    describe("deleteUAV", ()=>{
        it("should delete UAV successfully", async ()=>{
            const mockResponse = (0, _testutils.mockApiResponse)(null);
            mockedApiClient.delete.mockResolvedValue(mockResponse);
            const result = await uavApi.deleteUAV(1);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.delete).toHaveBeenCalledWith("/api/uav/1");
        });
        it("should handle delete UAV error", async ()=>{
            const error = new Error("UAV not found");
            mockedApiClient.delete.mockRejectedValue(error);
            await expect(uavApi.deleteUAV(999)).rejects.toThrow("UAV not found");
        });
    });
    describe("updateUAVStatus", ()=>{
        it("should update UAV status successfully", async ()=>{
            const mockResponse = (0, _testutils.mockApiResponse)({
                message: "Status updated"
            });
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await uavApi.updateUAVStatus(1);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/uav/1/status");
        });
        it("should handle update status error", async ()=>{
            const error = new Error("UAV not found");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(uavApi.updateUAVStatus(999)).rejects.toThrow("UAV not found");
        });
    });
    describe("getSystemStatistics", ()=>{
        it("should get system statistics successfully", async ()=>{
            const mockStats = {
                totalUAVs: 10,
                authorizedUAVs: 8,
                unauthorizedUAVs: 2,
                activeFlights: 3,
                hibernatingUAVs: 2,
                averageBatteryLevel: 75,
                totalFlightHours: 1000,
                totalFlightCycles: 500
            };
            mockedApiClient.get.mockResolvedValue(mockStats);
            const result = await uavApi.getSystemStatistics();
            expect(result).toEqual(mockStats);
            expect(mockedApiClient.get).toHaveBeenCalledWith("/api/uav/statistics");
        });
        it("should handle get statistics error", async ()=>{
            const error = new Error("Failed to fetch statistics");
            mockedApiClient.get.mockRejectedValue(error);
            await expect(uavApi.getSystemStatistics()).rejects.toThrow("Failed to fetch statistics");
        });
    });
    describe("bulkUpdateStatus", ()=>{
        it("should bulk update status successfully", async ()=>{
            const uavIds = [
                1,
                2,
                3
            ];
            const status = "AUTHORIZED";
            const mockResponse = (0, _testutils.mockApiResponse)({
                updated: 3
            });
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await uavApi.bulkUpdateStatus(uavIds, status);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/uav/bulk/status", {
                uavIds,
                status
            });
        });
        it("should handle bulk update error", async ()=>{
            const uavIds = [
                999,
                998
            ];
            const status = "AUTHORIZED";
            const error = new Error("Some UAVs not found");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(uavApi.bulkUpdateStatus(uavIds, status)).rejects.toThrow("Some UAVs not found");
        });
    });
    describe("bulkDelete", ()=>{
        it("should bulk delete successfully", async ()=>{
            const uavIds = [
                1,
                2,
                3
            ];
            const mockResponse = (0, _testutils.mockApiResponse)({
                deleted: 3
            });
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await uavApi.bulkDelete(uavIds);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/uav/bulk/delete", {
                uavIds
            });
        });
        it("should handle bulk delete error", async ()=>{
            const uavIds = [
                999,
                998
            ];
            const error = new Error("Some UAVs not found");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(uavApi.bulkDelete(uavIds)).rejects.toThrow("Some UAVs not found");
        });
    });
});
describe("HibernatePodApi", ()=>{
    let hibernatePodApi;
    beforeEach(()=>{
        hibernatePodApi = new _uavapi.HibernatePodApi();
        jest.clearAllMocks();
    });
    describe("getStatus", ()=>{
        it("should get hibernate pod status successfully", async ()=>{
            const mockStatus = {
                isActive: true,
                capacity: 10,
                currentOccupancy: 5,
                availableSlots: 5,
                temperature: 20,
                humidity: 45,
                powerConsumption: 150,
                lastMaintenance: "2024-01-01T00:00:00Z"
            };
            mockedApiClient.get.mockResolvedValue(mockStatus);
            const result = await hibernatePodApi.getStatus();
            expect(result).toEqual(mockStatus);
            expect(mockedApiClient.get).toHaveBeenCalledWith("/api/hibernate-pod/status");
        });
        it("should handle get status error", async ()=>{
            const error = new Error("Failed to fetch status");
            mockedApiClient.get.mockRejectedValue(error);
            await expect(hibernatePodApi.getStatus()).rejects.toThrow("Failed to fetch status");
        });
    });
    describe("addUAV", ()=>{
        it("should add UAV to hibernate pod successfully", async ()=>{
            const mockResponse = (0, _testutils.mockApiResponse)({
                message: "UAV added to hibernate pod"
            });
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await hibernatePodApi.addUAV(1);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/hibernate-pod/add", {
                uavId: 1
            });
        });
        it("should handle add UAV error", async ()=>{
            const error = new Error("Hibernate pod is full");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(hibernatePodApi.addUAV(1)).rejects.toThrow("Hibernate pod is full");
        });
    });
    describe("removeUAV", ()=>{
        it("should remove UAV from hibernate pod successfully", async ()=>{
            const mockResponse = (0, _testutils.mockApiResponse)({
                message: "UAV removed from hibernate pod"
            });
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await hibernatePodApi.removeUAV(1);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/hibernate-pod/remove", {
                uavId: 1
            });
        });
        it("should handle remove UAV error", async ()=>{
            const error = new Error("UAV not in hibernate pod");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(hibernatePodApi.removeUAV(1)).rejects.toThrow("UAV not in hibernate pod");
        });
    });
    describe("getUAVsInPod", ()=>{
        it("should get UAVs in hibernate pod successfully", async ()=>{
            const mockUAVs = [
                (0, _testutils.createMockUAV)({
                    id: 1,
                    inHibernatePod: true
                }),
                (0, _testutils.createMockUAV)({
                    id: 2,
                    inHibernatePod: true
                })
            ];
            mockedApiClient.get.mockResolvedValue(mockUAVs);
            const result = await hibernatePodApi.getUAVsInPod();
            expect(result).toEqual(mockUAVs);
            expect(mockedApiClient.get).toHaveBeenCalledWith("/api/hibernate-pod/uavs");
        });
        it("should handle get UAVs error", async ()=>{
            const error = new Error("Failed to fetch UAVs");
            mockedApiClient.get.mockRejectedValue(error);
            await expect(hibernatePodApi.getUAVsInPod()).rejects.toThrow("Failed to fetch UAVs");
        });
    });
});
describe("RegionApi", ()=>{
    let regionApi;
    beforeEach(()=>{
        regionApi = new _uavapi.RegionApi();
        jest.clearAllMocks();
    });
    describe("getRegions", ()=>{
        it("should get regions successfully", async ()=>{
            const mockRegions = [
                {
                    id: 1,
                    name: "Region 1",
                    description: "Test region 1",
                    coordinates: [
                        {
                            latitude: 40.7128,
                            longitude: -74.0060
                        },
                        {
                            latitude: 40.7589,
                            longitude: -73.9851
                        }
                    ],
                    isActive: true
                }
            ];
            mockedApiClient.get.mockResolvedValue(mockRegions);
            const result = await regionApi.getRegions();
            expect(result).toEqual(mockRegions);
            expect(mockedApiClient.get).toHaveBeenCalledWith("/api/regions");
        });
        it("should handle get regions error", async ()=>{
            const error = new Error("Failed to fetch regions");
            mockedApiClient.get.mockRejectedValue(error);
            await expect(regionApi.getRegions()).rejects.toThrow("Failed to fetch regions");
        });
    });
    describe("createRegion", ()=>{
        it("should create region successfully", async ()=>{
            const regionData = {
                name: "New Region",
                description: "A new test region",
                coordinates: [
                    {
                        latitude: 40.7128,
                        longitude: -74.0060
                    },
                    {
                        latitude: 40.7589,
                        longitude: -73.9851
                    }
                ]
            };
            const mockResponse = (0, _testutils.mockApiResponse)({
                id: 1,
                ...regionData,
                isActive: true
            });
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await regionApi.createRegion(regionData);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/regions", regionData);
        });
        it("should handle create region error", async ()=>{
            const regionData = {
                name: "Duplicate Region",
                description: "A duplicate region",
                coordinates: []
            };
            const error = new Error("Region name already exists");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(regionApi.createRegion(regionData)).rejects.toThrow("Region name already exists");
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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