277fb18e7a5a7a28928e773de61d251f
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _react = require("@testing-library/react");
const _dashboardstore = require("../dashboard-store");
const _testutils = require("../../lib/test-utils");
describe("Dashboard Store", ()=>{
    beforeEach(()=>{
        // Reset store state
        _dashboardstore.useDashboardStore.setState({
            metrics: null,
            flightActivity: null,
            batteryStats: null,
            hibernatePodMetrics: null,
            chartData: null,
            alerts: [],
            recentLocationUpdates: [],
            isConnected: false,
            lastUpdate: null,
            connectionError: null,
            selectedTimeRange: "24h",
            autoRefresh: true,
            refreshInterval: 30,
            showAlerts: true
        });
        // Clear all mocks
        jest.clearAllMocks();
    });
    describe("metrics management", ()=>{
        it("should update metrics", ()=>{
            const mockMetrics = {
                totalUAVs: 15,
                authorizedUAVs: 12,
                unauthorizedUAVs: 3,
                activeFlights: 5,
                hibernatingUAVs: 4,
                lowBatteryCount: 2,
                chargingCount: 3,
                maintenanceCount: 1,
                emergencyCount: 0
            };
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.updateMetrics(mockMetrics);
            });
            expect(result.current.metrics).toEqual(mockMetrics);
            expect(result.current.lastUpdate).toBeTruthy();
        });
        it("should update flight activity", ()=>{
            const mockFlightActivity = {
                activeFlights: 3,
                todayFlights: 8,
                completedFlights: 5,
                flights: [
                    {
                        id: 1,
                        uavRfid: "UAV-001",
                        missionName: "Test Mission",
                        startTime: "2024-01-01T10:00:00Z",
                        status: "ACTIVE"
                    }
                ]
            };
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.updateFlightActivity(mockFlightActivity);
            });
            expect(result.current.flightActivity).toEqual(mockFlightActivity);
        });
        it("should update battery statistics", ()=>{
            const mockBatteryStats = {
                averageBatteryLevel: 75,
                lowBatteryCount: 2,
                chargingCount: 3,
                criticalBatteryCount: 1,
                batteryDistribution: {
                    "0-25": 1,
                    "26-50": 2,
                    "51-75": 4,
                    "76-100": 8
                }
            };
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.updateBatteryStats(mockBatteryStats);
            });
            expect(result.current.batteryStats).toEqual(mockBatteryStats);
        });
    });
    describe("alerts management", ()=>{
        it("should add alert", ()=>{
            const mockAlert = (0, _testutils.createMockAlert)({
                id: 1,
                type: "WARNING",
                title: "Low Battery",
                message: "UAV-001 has low battery"
            });
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.addAlert(mockAlert);
            });
            expect(result.current.alerts).toHaveLength(1);
            expect(result.current.alerts[0]).toEqual(mockAlert);
        });
        it("should remove alert", ()=>{
            const mockAlert1 = (0, _testutils.createMockAlert)({
                id: 1,
                title: "Alert 1"
            });
            const mockAlert2 = (0, _testutils.createMockAlert)({
                id: 2,
                title: "Alert 2"
            });
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add alerts
            (0, _react.act)(()=>{
                result.current.addAlert(mockAlert1);
                result.current.addAlert(mockAlert2);
            });
            expect(result.current.alerts).toHaveLength(2);
            // Remove one alert
            (0, _react.act)(()=>{
                result.current.removeAlert(1);
            });
            expect(result.current.alerts).toHaveLength(1);
            expect(result.current.alerts[0].id).toBe(2);
        });
        it("should acknowledge alert", ()=>{
            const mockAlert = (0, _testutils.createMockAlert)({
                id: 1,
                acknowledged: false
            });
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add alert
            (0, _react.act)(()=>{
                result.current.addAlert(mockAlert);
            });
            expect(result.current.alerts[0].acknowledged).toBe(false);
            // Acknowledge alert
            (0, _react.act)(()=>{
                result.current.acknowledgeAlert(1);
            });
            expect(result.current.alerts[0].acknowledged).toBe(true);
        });
        it("should clear all alerts", ()=>{
            const mockAlert1 = (0, _testutils.createMockAlert)({
                id: 1
            });
            const mockAlert2 = (0, _testutils.createMockAlert)({
                id: 2
            });
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add alerts
            (0, _react.act)(()=>{
                result.current.addAlert(mockAlert1);
                result.current.addAlert(mockAlert2);
            });
            expect(result.current.alerts).toHaveLength(2);
            // Clear all alerts
            (0, _react.act)(()=>{
                result.current.clearAlerts();
            });
            expect(result.current.alerts).toHaveLength(0);
        });
        it("should limit alerts to maximum count", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add more than 50 alerts (assuming max is 50)
            (0, _react.act)(()=>{
                for(let i = 1; i <= 55; i++){
                    result.current.addAlert((0, _testutils.createMockAlert)({
                        id: i,
                        title: `Alert ${i}`
                    }));
                }
            });
            // Should only keep the latest 50 alerts
            expect(result.current.alerts).toHaveLength(50);
            expect(result.current.alerts[0].id).toBe(6) // First alert should be #6
            ;
            expect(result.current.alerts[49].id).toBe(55) // Last alert should be #55
            ;
        });
    });
    describe("location updates", ()=>{
        it("should add location update", ()=>{
            const mockLocationUpdate = {
                uavId: 1,
                rfidTag: "UAV-001",
                latitude: 40.7128,
                longitude: -74.0060,
                altitude: 100,
                timestamp: new Date().toISOString(),
                speed: 25,
                heading: 180
            };
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.addLocationUpdate(mockLocationUpdate);
            });
            expect(result.current.recentLocationUpdates).toHaveLength(1);
            expect(result.current.recentLocationUpdates[0]).toEqual(mockLocationUpdate);
        });
        it("should limit location updates to maximum count", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add more than 100 location updates (assuming max is 100)
            (0, _react.act)(()=>{
                for(let i = 1; i <= 105; i++){
                    result.current.addLocationUpdate({
                        uavId: i,
                        rfidTag: `UAV-${i.toString().padStart(3, "0")}`,
                        latitude: 40.7128 + i * 0.001,
                        longitude: -74.0060 + i * 0.001,
                        altitude: 100,
                        timestamp: new Date().toISOString(),
                        speed: 25,
                        heading: 180
                    });
                }
            });
            // Should only keep the latest 100 updates
            expect(result.current.recentLocationUpdates).toHaveLength(100);
            expect(result.current.recentLocationUpdates[0].uavId).toBe(6) // First should be #6
            ;
            expect(result.current.recentLocationUpdates[99].uavId).toBe(105) // Last should be #105
            ;
        });
    });
    describe("connection management", ()=>{
        it("should set connection status", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.setConnectionStatus(true);
            });
            expect(result.current.isConnected).toBe(true);
            expect(result.current.connectionError).toBeNull();
            (0, _react.act)(()=>{
                result.current.setConnectionStatus(false, "Connection lost");
            });
            expect(result.current.isConnected).toBe(false);
            expect(result.current.connectionError).toBe("Connection lost");
        });
        it("should clear connection error", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Set error first
            (0, _react.act)(()=>{
                result.current.setConnectionStatus(false, "Connection error");
            });
            expect(result.current.connectionError).toBe("Connection error");
            // Clear error
            (0, _react.act)(()=>{
                result.current.clearConnectionError();
            });
            expect(result.current.connectionError).toBeNull();
        });
    });
    describe("UI settings", ()=>{
        it("should set time range", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.setTimeRange("1h");
            });
            expect(result.current.selectedTimeRange).toBe("1h");
            (0, _react.act)(()=>{
                result.current.setTimeRange("7d");
            });
            expect(result.current.selectedTimeRange).toBe("7d");
        });
        it("should toggle auto refresh", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            expect(result.current.autoRefresh).toBe(true);
            (0, _react.act)(()=>{
                result.current.toggleAutoRefresh();
            });
            expect(result.current.autoRefresh).toBe(false);
            (0, _react.act)(()=>{
                result.current.toggleAutoRefresh();
            });
            expect(result.current.autoRefresh).toBe(true);
        });
        it("should set refresh interval", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            (0, _react.act)(()=>{
                result.current.setRefreshInterval(60);
            });
            expect(result.current.refreshInterval).toBe(60);
        });
        it("should toggle alerts visibility", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            expect(result.current.showAlerts).toBe(true);
            (0, _react.act)(()=>{
                result.current.toggleAlerts();
            });
            expect(result.current.showAlerts).toBe(false);
            (0, _react.act)(()=>{
                result.current.toggleAlerts();
            });
            expect(result.current.showAlerts).toBe(true);
        });
    });
    describe("data reset", ()=>{
        it("should reset all data", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Set some data first
            (0, _react.act)(()=>{
                result.current.updateMetrics({
                    totalUAVs: 10,
                    authorizedUAVs: 8,
                    unauthorizedUAVs: 2,
                    activeFlights: 3,
                    hibernatingUAVs: 2,
                    lowBatteryCount: 1,
                    chargingCount: 2,
                    maintenanceCount: 1,
                    emergencyCount: 0
                });
                result.current.addAlert((0, _testutils.createMockAlert)());
                result.current.setConnectionStatus(true);
            });
            expect(result.current.metrics).toBeTruthy();
            expect(result.current.alerts).toHaveLength(1);
            expect(result.current.isConnected).toBe(true);
            // Reset all data
            (0, _react.act)(()=>{
                result.current.resetData();
            });
            expect(result.current.metrics).toBeNull();
            expect(result.current.flightActivity).toBeNull();
            expect(result.current.batteryStats).toBeNull();
            expect(result.current.hibernatePodMetrics).toBeNull();
            expect(result.current.chartData).toBeNull();
            expect(result.current.alerts).toHaveLength(0);
            expect(result.current.recentLocationUpdates).toHaveLength(0);
            expect(result.current.isConnected).toBe(false);
            expect(result.current.lastUpdate).toBeNull();
            expect(result.current.connectionError).toBeNull();
        });
    });
    describe("computed values", ()=>{
        it("should calculate alert counts correctly", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add different types of alerts
            (0, _react.act)(()=>{
                result.current.addAlert((0, _testutils.createMockAlert)({
                    id: 1,
                    type: "ERROR",
                    severity: "HIGH"
                }));
                result.current.addAlert((0, _testutils.createMockAlert)({
                    id: 2,
                    type: "WARNING",
                    severity: "MEDIUM"
                }));
                result.current.addAlert((0, _testutils.createMockAlert)({
                    id: 3,
                    type: "INFO",
                    severity: "LOW"
                }));
                result.current.addAlert((0, _testutils.createMockAlert)({
                    id: 4,
                    type: "ERROR",
                    severity: "HIGH"
                }));
            });
            const alertCounts = result.current.getAlertCounts();
            expect(alertCounts.total).toBe(4);
            expect(alertCounts.error).toBe(2);
            expect(alertCounts.warning).toBe(1);
            expect(alertCounts.info).toBe(1);
            expect(alertCounts.high).toBe(2);
            expect(alertCounts.medium).toBe(1);
            expect(alertCounts.low).toBe(1);
        });
        it("should get unacknowledged alerts", ()=>{
            const { result } = (0, _react.renderHook)(()=>(0, _dashboardstore.useDashboardStore)());
            // Add alerts with different acknowledgment status
            (0, _react.act)(()=>{
                result.current.addAlert((0, _testutils.createMockAlert)({
                    id: 1,
                    acknowledged: false
                }));
                result.current.addAlert((0, _testutils.createMockAlert)({
                    id: 2,
                    acknowledged: true
                }));
                result.current.addAlert((0, _testutils.createMockAlert)({
                    id: 3,
                    acknowledged: false
                }));
            });
            const unacknowledgedAlerts = result.current.getUnacknowledgedAlerts();
            expect(unacknowledgedAlerts).toHaveLength(2);
            expect(unacknowledgedAlerts.map((a)=>a.id)).toEqual([
                1,
                3
            ]);
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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