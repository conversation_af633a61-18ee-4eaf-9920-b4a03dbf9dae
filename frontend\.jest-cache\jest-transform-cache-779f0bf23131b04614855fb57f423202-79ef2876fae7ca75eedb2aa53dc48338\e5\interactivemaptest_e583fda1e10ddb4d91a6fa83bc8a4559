f95654c5a098056ce6f011900478a65a
"use strict";
// Mock react-leaflet components (already mocked in jest.setup.js)
// Mock framer-motion
jest.mock("framer-motion", ()=>({
        motion: {
            div: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    ...props,
                    children: children
                })
        },
        AnimatePresence: ({ children })=>children
    }));
// Mock the animated map components
jest.mock("../animated-map-components", ()=>({
        AnimatedUAVMarker: ({ uav, onSelect })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `uav-marker-${uav.id}`,
                onClick: ()=>onSelect(uav),
                children: [
                    "UAV Marker: ",
                    uav.rfidTag
                ]
            }),
        AnimatedGeofence: ({ region })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `geofence-${region.id}`,
                children: [
                    "Geofence: ",
                    region.name
                ]
            }),
        AnimatedFlightPath: ({ path })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "flight-path",
                children: "Flight Path"
            }),
        AnimatedDockingStation: ({ station, onSelect })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `docking-station-${station.id}`,
                onClick: ()=>onSelect(station),
                children: [
                    "Docking Station: ",
                    station.name
                ]
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../../lib/test-utils");
const _interactivemap = /*#__PURE__*/ _interop_require_default(require("../interactive-map"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
describe("InteractiveMap Component", ()=>{
    const mockUAVs = [
        (0, _testutils.createMockUAV)({
            id: 1,
            rfidTag: "UAV-001",
            status: "AUTHORIZED",
            operationalStatus: "ACTIVE",
            location: {
                latitude: 40.7128,
                longitude: -74.0060
            }
        }),
        (0, _testutils.createMockUAV)({
            id: 2,
            rfidTag: "UAV-002",
            status: "AUTHORIZED",
            operationalStatus: "READY",
            location: {
                latitude: 40.7589,
                longitude: -73.9851
            }
        })
    ];
    const mockDockingStations = [
        (0, _testutils.createMockDockingStation)({
            id: 1,
            name: "Station Alpha",
            location: {
                latitude: 40.7505,
                longitude: -73.9934
            },
            status: "AVAILABLE"
        })
    ];
    const mockRegions = [
        {
            id: 1,
            name: "Zone A",
            description: "Authorized zone A",
            coordinates: [
                {
                    latitude: 40.7000,
                    longitude: -74.0200
                },
                {
                    latitude: 40.7200,
                    longitude: -74.0200
                },
                {
                    latitude: 40.7200,
                    longitude: -73.9800
                },
                {
                    latitude: 40.7000,
                    longitude: -73.9800
                }
            ],
            isActive: true
        }
    ];
    const defaultProps = {
        uavs: mockUAVs,
        selectedUAV: null,
        center: [
            40.7128,
            -74.0060
        ],
        zoom: 12,
        layers: {
            uavs: true,
            geofences: true,
            dockingStations: true,
            flightPaths: true,
            weather: false
        },
        onUAVSelect: jest.fn()
    };
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("tile-layer")).toBeInTheDocument();
    });
    it("renders UAV markers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("uav-marker-2")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Marker: UAV-001")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Marker: UAV-002")).toBeInTheDocument();
    });
    it("renders docking station markers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("docking-station-1")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Docking Station: Station Alpha")).toBeInTheDocument();
    });
    it("renders geofences for regions", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("geofence-1")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Geofence: Zone A")).toBeInTheDocument();
    });
    it("handles UAV selection", ()=>{
        const onUAVSelect = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onUAVSelect: onUAVSelect
        }));
        const uavMarker = _testutils.screen.getByTestId("uav-marker-1");
        _testutils.fireEvent.click(uavMarker);
        expect(onUAVSelect).toHaveBeenCalledWith(mockUAVs[0]);
    });
    it("handles docking station selection", ()=>{
        const onStationSelect = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onStationSelect: onStationSelect
        }));
        const stationMarker = _testutils.screen.getByTestId("docking-station-1");
        _testutils.fireEvent.click(stationMarker);
        expect(onStationSelect).toHaveBeenCalledWith(mockDockingStations[0]);
    });
    it("highlights selected UAV", ()=>{
        const selectedUAV = mockUAVs[0];
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            selectedUAV: selectedUAV
        }));
        const selectedMarker = _testutils.screen.getByTestId("uav-marker-1");
        expect(selectedMarker).toBeInTheDocument();
    // The selected state would be passed to the AnimatedUAVMarker component
    });
    it("shows flight paths when enabled", ()=>{
        const flightPaths = [
            {
                id: 1,
                uavId: 1,
                coordinates: [
                    {
                        latitude: 40.7128,
                        longitude: -74.0060
                    },
                    {
                        latitude: 40.7589,
                        longitude: -73.9851
                    }
                ],
                timestamp: new Date().toISOString()
            }
        ];
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            flightPaths: flightPaths,
            showFlightPaths: true
        }));
        expect(_testutils.screen.getByTestId("flight-path")).toBeInTheDocument();
    });
    it("filters UAVs by status", ()=>{
        const filteredProps = {
            ...defaultProps,
            uavs: mockUAVs.filter((uav)=>uav.operationalStatus === "ACTIVE")
        };
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...filteredProps
        }));
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-2")).not.toBeInTheDocument();
    });
    it("updates map center when prop changes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        const newCenter = {
            latitude: 41.8781,
            longitude: -87.6298
        };
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            center: newCenter
        }));
        // Map center update would be handled by the MapContainer component
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    });
    it("updates zoom level when prop changes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            zoom: 15
        }));
        // Zoom update would be handled by the MapContainer component
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    });
    it("handles empty UAV list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-1")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-2")).not.toBeInTheDocument();
    });
    it("handles empty docking stations list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            dockingStations: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("docking-station-1")).not.toBeInTheDocument();
    });
    it("handles empty regions list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            regions: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("geofence-1")).not.toBeInTheDocument();
    });
    it("shows loading state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            loading: true
        }));
        expect(_testutils.screen.getByTestId("map-loading")).toBeInTheDocument();
    });
    it("shows error state", ()=>{
        const errorMessage = "Failed to load map data";
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            error: errorMessage
        }));
        expect(_testutils.screen.getByText(errorMessage)).toBeInTheDocument();
        expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
    });
    it("supports different map layers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            mapLayer: "satellite"
        }));
        // Different tile layer would be rendered
        expect(_testutils.screen.getByTestId("tile-layer")).toBeInTheDocument();
    });
    it("handles real-time updates", async ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        const updatedUAVs = [
            ...mockUAVs,
            (0, _testutils.createMockUAV)({
                id: 3,
                rfidTag: "UAV-003",
                location: {
                    latitude: 40.7300,
                    longitude: -74.0000
                }
            })
        ];
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: updatedUAVs
        }));
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByTestId("uav-marker-3")).toBeInTheDocument();
        });
    });
    it("handles UAV location updates", ()=>{
        const updatedUAVs = mockUAVs.map((uav)=>uav.id === 1 ? {
                ...uav,
                location: {
                    latitude: 40.7200,
                    longitude: -74.0100
                }
            } : uav);
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: updatedUAVs
        }));
        // Updated location would be reflected in the marker position
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
    });
    it("supports clustering for many UAVs", ()=>{
        const manyUAVs = Array.from({
            length: 50
        }, (_, i)=>(0, _testutils.createMockUAV)({
                id: i + 1,
                rfidTag: `UAV-${(i + 1).toString().padStart(3, "0")}`,
                location: {
                    latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
                    longitude: -74.0060 + (Math.random() - 0.5) * 0.1
                }
            }));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: manyUAVs,
            enableClustering: true
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    // Clustering would be handled by the map library
    });
    it("handles map interaction events", ()=>{
        const onMapClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onMapClick: onMapClick
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        _testutils.fireEvent.click(mapContainer);
        // Map click would be handled by the MapContainer component
        expect(mapContainer).toBeInTheDocument();
    });
    it("supports custom map controls", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            showZoomControl: true,
            showScaleControl: true,
            showFullscreenControl: true
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    // Custom controls would be rendered as part of the map
    });
    it("handles responsive design", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            className: "h-96 w-full"
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        expect(mapContainer.parentElement).toHaveClass("h-96", "w-full");
    });
    it("supports accessibility features", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            "aria-label": "UAV tracking map",
            role: "application"
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        expect(mapContainer).toHaveAttribute("aria-label", "UAV tracking map");
        expect(mapContainer).toHaveAttribute("role", "application");
    });
});

//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFxEYUNodWFuZ0JhY2tlbmRcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGZlYXR1cmVzXFxtYXBcXF9fdGVzdHNfX1xcaW50ZXJhY3RpdmUtbWFwLnRlc3QudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHJlbmRlciwgc2NyZWVuLCBmaXJlRXZlbnQsIHdhaXRGb3IgfSBmcm9tICdAL2xpYi90ZXN0LXV0aWxzJ1xuaW1wb3J0IEludGVyYWN0aXZlTWFwIGZyb20gJy4uL2ludGVyYWN0aXZlLW1hcCdcbmltcG9ydCB7IGNyZWF0ZU1vY2tVQVYsIGNyZWF0ZU1vY2tEb2NraW5nU3RhdGlvbiB9IGZyb20gJ0AvbGliL3Rlc3QtdXRpbHMnXG5pbXBvcnQgeyBVQVYsIERvY2tpbmdTdGF0aW9uIH0gZnJvbSAnQC90eXBlcy91YXYnXG5cbi8vIE1vY2sgcmVhY3QtbGVhZmxldCBjb21wb25lbnRzIChhbHJlYWR5IG1vY2tlZCBpbiBqZXN0LnNldHVwLmpzKVxuLy8gTW9jayBmcmFtZXItbW90aW9uXG5qZXN0Lm1vY2soJ2ZyYW1lci1tb3Rpb24nLCAoKSA9PiAoe1xuICBtb3Rpb246IHtcbiAgICBkaXY6ICh7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBhbnkpID0+IDxkaXYgey4uLnByb3BzfT57Y2hpbGRyZW59PC9kaXY+LFxuICB9LFxuICBBbmltYXRlUHJlc2VuY2U6ICh7IGNoaWxkcmVuIH06IGFueSkgPT4gY2hpbGRyZW4sXG59KSlcblxuLy8gTW9jayB0aGUgYW5pbWF0ZWQgbWFwIGNvbXBvbmVudHNcbmplc3QubW9jaygnLi4vYW5pbWF0ZWQtbWFwLWNvbXBvbmVudHMnLCAoKSA9PiAoe1xuICBBbmltYXRlZFVBVk1hcmtlcjogKHsgdWF2LCBvblNlbGVjdCB9OiBhbnkpID0+IChcbiAgICA8ZGl2IFxuICAgICAgZGF0YS10ZXN0aWQ9e2B1YXYtbWFya2VyLSR7dWF2LmlkfWB9XG4gICAgICBvbkNsaWNrPXsoKSA9PiBvblNlbGVjdCh1YXYpfVxuICAgID5cbiAgICAgIFVBViBNYXJrZXI6IHt1YXYucmZpZFRhZ31cbiAgICA8L2Rpdj5cbiAgKSxcbiAgQW5pbWF0ZWRHZW9mZW5jZTogKHsgcmVnaW9uIH06IGFueSkgPT4gKFxuICAgIDxkaXYgZGF0YS10ZXN0aWQ9e2BnZW9mZW5jZS0ke3JlZ2lvbi5pZH1gfT5cbiAgICAgIEdlb2ZlbmNlOiB7cmVnaW9uLm5hbWV9XG4gICAgPC9kaXY+XG4gICksXG4gIEFuaW1hdGVkRmxpZ2h0UGF0aDogKHsgcGF0aCB9OiBhbnkpID0+IChcbiAgICA8ZGl2IGRhdGEtdGVzdGlkPVwiZmxpZ2h0LXBhdGhcIj5cbiAgICAgIEZsaWdodCBQYXRoXG4gICAgPC9kaXY+XG4gICksXG4gIEFuaW1hdGVkRG9ja2luZ1N0YXRpb246ICh7IHN0YXRpb24sIG9uU2VsZWN0IH06IGFueSkgPT4gKFxuICAgIDxkaXYgXG4gICAgICBkYXRhLXRlc3RpZD17YGRvY2tpbmctc3RhdGlvbi0ke3N0YXRpb24uaWR9YH1cbiAgICAgIG9uQ2xpY2s9eygpID0+IG9uU2VsZWN0KHN0YXRpb24pfVxuICAgID5cbiAgICAgIERvY2tpbmcgU3RhdGlvbjoge3N0YXRpb24ubmFtZX1cbiAgICA8L2Rpdj5cbiAgKSxcbn0pKVxuXG5kZXNjcmliZSgnSW50ZXJhY3RpdmVNYXAgQ29tcG9uZW50JywgKCkgPT4ge1xuICBjb25zdCBtb2NrVUFWczogVUFWW10gPSBbXG4gICAgY3JlYXRlTW9ja1VBVih7XG4gICAgICBpZDogMSxcbiAgICAgIHJmaWRUYWc6ICdVQVYtMDAxJyxcbiAgICAgIHN0YXR1czogJ0FVVEhPUklaRUQnLFxuICAgICAgb3BlcmF0aW9uYWxTdGF0dXM6ICdBQ1RJVkUnLFxuICAgICAgbG9jYXRpb246IHsgbGF0aXR1ZGU6IDQwLjcxMjgsIGxvbmdpdHVkZTogLTc0LjAwNjAgfSxcbiAgICB9KSxcbiAgICBjcmVhdGVNb2NrVUFWKHtcbiAgICAgIGlkOiAyLFxuICAgICAgcmZpZFRhZzogJ1VBVi0wMDInLFxuICAgICAgc3RhdHVzOiAnQVVUSE9SSVpFRCcsXG4gICAgICBvcGVyYXRpb25hbFN0YXR1czogJ1JFQURZJyxcbiAgICAgIGxvY2F0aW9uOiB7IGxhdGl0dWRlOiA0MC43NTg5LCBsb25naXR1ZGU6IC03My45ODUxIH0sXG4gICAgfSksXG4gIF1cblxuICBjb25zdCBtb2NrRG9ja2luZ1N0YXRpb25zOiBEb2NraW5nU3RhdGlvbltdID0gW1xuICAgIGNyZWF0ZU1vY2tEb2NraW5nU3RhdGlvbih7XG4gICAgICBpZDogMSxcbiAgICAgIG5hbWU6ICdTdGF0aW9uIEFscGhhJyxcbiAgICAgIGxvY2F0aW9uOiB7IGxhdGl0dWRlOiA0MC43NTA1LCBsb25naXR1ZGU6IC03My45OTM0IH0sXG4gICAgICBzdGF0dXM6ICdBVkFJTEFCTEUnLFxuICAgIH0pLFxuICBdXG5cbiAgY29uc3QgbW9ja1JlZ2lvbnMgPSBbXG4gICAge1xuICAgICAgaWQ6IDEsXG4gICAgICBuYW1lOiAnWm9uZSBBJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQXV0aG9yaXplZCB6b25lIEEnLFxuICAgICAgY29vcmRpbmF0ZXM6IFtcbiAgICAgICAgeyBsYXRpdHVkZTogNDAuNzAwMCwgbG9uZ2l0dWRlOiAtNzQuMDIwMCB9LFxuICAgICAgICB7IGxhdGl0dWRlOiA0MC43MjAwLCBsb25naXR1ZGU6IC03NC4wMjAwIH0sXG4gICAgICAgIHsgbGF0aXR1ZGU6IDQwLjcyMDAsIGxvbmdpdHVkZTogLTczLjk4MDAgfSxcbiAgICAgICAgeyBsYXRpdHVkZTogNDAuNzAwMCwgbG9uZ2l0dWRlOiAtNzMuOTgwMCB9LFxuICAgICAgXSxcbiAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgIH0sXG4gIF1cblxuICBjb25zdCBkZWZhdWx0UHJvcHMgPSB7XG4gICAgdWF2czogbW9ja1VBVnMsXG4gICAgc2VsZWN0ZWRVQVY6IG51bGwsXG4gICAgY2VudGVyOiBbNDAuNzEyOCwgLTc0LjAwNjBdIGFzIFtudW1iZXIsIG51bWJlcl0sXG4gICAgem9vbTogMTIsXG4gICAgbGF5ZXJzOiB7XG4gICAgICB1YXZzOiB0cnVlLFxuICAgICAgZ2VvZmVuY2VzOiB0cnVlLFxuICAgICAgZG9ja2luZ1N0YXRpb25zOiB0cnVlLFxuICAgICAgZmxpZ2h0UGF0aHM6IHRydWUsXG4gICAgICB3ZWF0aGVyOiBmYWxzZSxcbiAgICB9LFxuICAgIG9uVUFWU2VsZWN0OiBqZXN0LmZuKCksXG4gIH1cblxuICBiZWZvcmVFYWNoKCgpID0+IHtcbiAgICBqZXN0LmNsZWFyQWxsTW9ja3MoKVxuICB9KVxuXG4gIGl0KCdyZW5kZXJzIGNvcnJlY3RseScsICgpID0+IHtcbiAgICByZW5kZXIoPEludGVyYWN0aXZlTWFwIHsuLi5kZWZhdWx0UHJvcHN9IC8+KVxuICAgIFxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXN0SWQoJ21hcC1jb250YWluZXInKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXN0SWQoJ3RpbGUtbGF5ZXInKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdyZW5kZXJzIFVBViBtYXJrZXJzJywgKCkgPT4ge1xuICAgIHJlbmRlcig8SW50ZXJhY3RpdmVNYXAgey4uLmRlZmF1bHRQcm9wc30gLz4pXG4gICAgXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgndWF2LW1hcmtlci0xJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCd1YXYtbWFya2VyLTInKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdVQVYgTWFya2VyOiBVQVYtMDAxJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnVUFWIE1hcmtlcjogVUFWLTAwMicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gIH0pXG5cbiAgaXQoJ3JlbmRlcnMgZG9ja2luZyBzdGF0aW9uIG1hcmtlcnMnLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxJbnRlcmFjdGl2ZU1hcCB7Li4uZGVmYXVsdFByb3BzfSAvPilcbiAgICBcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdkb2NraW5nLXN0YXRpb24tMScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0RvY2tpbmcgU3RhdGlvbjogU3RhdGlvbiBBbHBoYScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gIH0pXG5cbiAgaXQoJ3JlbmRlcnMgZ2VvZmVuY2VzIGZvciByZWdpb25zJywgKCkgPT4ge1xuICAgIHJlbmRlcig8SW50ZXJhY3RpdmVNYXAgey4uLmRlZmF1bHRQcm9wc30gLz4pXG4gICAgXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnZ2VvZmVuY2UtMScpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0dlb2ZlbmNlOiBab25lIEEnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdoYW5kbGVzIFVBViBzZWxlY3Rpb24nLCAoKSA9PiB7XG4gICAgY29uc3Qgb25VQVZTZWxlY3QgPSBqZXN0LmZuKClcbiAgICByZW5kZXIoPEludGVyYWN0aXZlTWFwIHsuLi5kZWZhdWx0UHJvcHN9IG9uVUFWU2VsZWN0PXtvblVBVlNlbGVjdH0gLz4pXG4gICAgXG4gICAgY29uc3QgdWF2TWFya2VyID0gc2NyZWVuLmdldEJ5VGVzdElkKCd1YXYtbWFya2VyLTEnKVxuICAgIGZpcmVFdmVudC5jbGljayh1YXZNYXJrZXIpXG4gICAgXG4gICAgZXhwZWN0KG9uVUFWU2VsZWN0KS50b0hhdmVCZWVuQ2FsbGVkV2l0aChtb2NrVUFWc1swXSlcbiAgfSlcblxuICBpdCgnaGFuZGxlcyBkb2NraW5nIHN0YXRpb24gc2VsZWN0aW9uJywgKCkgPT4ge1xuICAgIGNvbnN0IG9uU3RhdGlvblNlbGVjdCA9IGplc3QuZm4oKVxuICAgIHJlbmRlcig8SW50ZXJhY3RpdmVNYXAgey4uLmRlZmF1bHRQcm9wc30gb25TdGF0aW9uU2VsZWN0PXtvblN0YXRpb25TZWxlY3R9IC8+KVxuICAgIFxuICAgIGNvbnN0IHN0YXRpb25NYXJrZXIgPSBzY3JlZW4uZ2V0QnlUZXN0SWQoJ2RvY2tpbmctc3RhdGlvbi0xJylcbiAgICBmaXJlRXZlbnQuY2xpY2soc3RhdGlvbk1hcmtlcilcbiAgICBcbiAgICBleHBlY3Qob25TdGF0aW9uU2VsZWN0KS50b0hhdmVCZWVuQ2FsbGVkV2l0aChtb2NrRG9ja2luZ1N0YXRpb25zWzBdKVxuICB9KVxuXG4gIGl0KCdoaWdobGlnaHRzIHNlbGVjdGVkIFVBVicsICgpID0+IHtcbiAgICBjb25zdCBzZWxlY3RlZFVBViA9IG1vY2tVQVZzWzBdXG4gICAgcmVuZGVyKDxJbnRlcmFjdGl2ZU1hcCB7Li4uZGVmYXVsdFByb3BzfSBzZWxlY3RlZFVBVj17c2VsZWN0ZWRVQVZ9IC8+KVxuICAgIFxuICAgIGNvbnN0IHNlbGVjdGVkTWFya2VyID0gc2NyZWVuLmdldEJ5VGVzdElkKCd1YXYtbWFya2VyLTEnKVxuICAgIGV4cGVjdChzZWxlY3RlZE1hcmtlcikudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIC8vIFRoZSBzZWxlY3RlZCBzdGF0ZSB3b3VsZCBiZSBwYXNzZWQgdG8gdGhlIEFuaW1hdGVkVUFWTWFya2VyIGNvbXBvbmVudFxuICB9KVxuXG4gIGl0KCdzaG93cyBmbGlnaHQgcGF0aHMgd2hlbiBlbmFibGVkJywgKCkgPT4ge1xuICAgIGNvbnN0IGZsaWdodFBhdGhzID0gW1xuICAgICAge1xuICAgICAgICBpZDogMSxcbiAgICAgICAgdWF2SWQ6IDEsXG4gICAgICAgIGNvb3JkaW5hdGVzOiBbXG4gICAgICAgICAgeyBsYXRpdHVkZTogNDAuNzEyOCwgbG9uZ2l0dWRlOiAtNzQuMDA2MCB9LFxuICAgICAgICAgIHsgbGF0aXR1ZGU6IDQwLjc1ODksIGxvbmdpdHVkZTogLTczLjk4NTEgfSxcbiAgICAgICAgXSxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICB9LFxuICAgIF1cbiAgICBcbiAgICByZW5kZXIoXG4gICAgICA8SW50ZXJhY3RpdmVNYXAgXG4gICAgICAgIHsuLi5kZWZhdWx0UHJvcHN9IFxuICAgICAgICBmbGlnaHRQYXRocz17ZmxpZ2h0UGF0aHN9XG4gICAgICAgIHNob3dGbGlnaHRQYXRocz17dHJ1ZX1cbiAgICAgIC8+XG4gICAgKVxuICAgIFxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXN0SWQoJ2ZsaWdodC1wYXRoJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcblxuICBpdCgnZmlsdGVycyBVQVZzIGJ5IHN0YXR1cycsICgpID0+IHtcbiAgICBjb25zdCBmaWx0ZXJlZFByb3BzID0ge1xuICAgICAgLi4uZGVmYXVsdFByb3BzLFxuICAgICAgdWF2czogbW9ja1VBVnMuZmlsdGVyKHVhdiA9PiB1YXYub3BlcmF0aW9uYWxTdGF0dXMgPT09ICdBQ1RJVkUnKSxcbiAgICB9XG4gICAgXG4gICAgcmVuZGVyKDxJbnRlcmFjdGl2ZU1hcCB7Li4uZmlsdGVyZWRQcm9wc30gLz4pXG4gICAgXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgndWF2LW1hcmtlci0xJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICBleHBlY3Qoc2NyZWVuLnF1ZXJ5QnlUZXN0SWQoJ3Vhdi1tYXJrZXItMicpKS5ub3QudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCd1cGRhdGVzIG1hcCBjZW50ZXIgd2hlbiBwcm9wIGNoYW5nZXMnLCAoKSA9PiB7XG4gICAgY29uc3QgeyByZXJlbmRlciB9ID0gcmVuZGVyKDxJbnRlcmFjdGl2ZU1hcCB7Li4uZGVmYXVsdFByb3BzfSAvPilcbiAgICBcbiAgICBjb25zdCBuZXdDZW50ZXIgPSB7IGxhdGl0dWRlOiA0MS44NzgxLCBsb25naXR1ZGU6IC04Ny42Mjk4IH1cbiAgICByZXJlbmRlcig8SW50ZXJhY3RpdmVNYXAgey4uLmRlZmF1bHRQcm9wc30gY2VudGVyPXtuZXdDZW50ZXJ9IC8+KVxuICAgIFxuICAgIC8vIE1hcCBjZW50ZXIgdXBkYXRlIHdvdWxkIGJlIGhhbmRsZWQgYnkgdGhlIE1hcENvbnRhaW5lciBjb21wb25lbnRcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdtYXAtY29udGFpbmVyJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcblxuICBpdCgndXBkYXRlcyB6b29tIGxldmVsIHdoZW4gcHJvcCBjaGFuZ2VzJywgKCkgPT4ge1xuICAgIGNvbnN0IHsgcmVyZW5kZXIgfSA9IHJlbmRlcig8SW50ZXJhY3RpdmVNYXAgey4uLmRlZmF1bHRQcm9wc30gLz4pXG4gICAgXG4gICAgcmVyZW5kZXIoPEludGVyYWN0aXZlTWFwIHsuLi5kZWZhdWx0UHJvcHN9IHpvb209ezE1fSAvPilcbiAgICBcbiAgICAvLyBab29tIHVwZGF0ZSB3b3VsZCBiZSBoYW5kbGVkIGJ5IHRoZSBNYXBDb250YWluZXIgY29tcG9uZW50XG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFwLWNvbnRhaW5lcicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gIH0pXG5cbiAgaXQoJ2hhbmRsZXMgZW1wdHkgVUFWIGxpc3QnLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxJbnRlcmFjdGl2ZU1hcCB7Li4uZGVmYXVsdFByb3BzfSB1YXZzPXtbXX0gLz4pXG4gICAgXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFwLWNvbnRhaW5lcicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgZXhwZWN0KHNjcmVlbi5xdWVyeUJ5VGVzdElkKCd1YXYtbWFya2VyLTEnKSkubm90LnRvQmVJblRoZURvY3VtZW50KClcbiAgICBleHBlY3Qoc2NyZWVuLnF1ZXJ5QnlUZXN0SWQoJ3Vhdi1tYXJrZXItMicpKS5ub3QudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdoYW5kbGVzIGVtcHR5IGRvY2tpbmcgc3RhdGlvbnMgbGlzdCcsICgpID0+IHtcbiAgICByZW5kZXIoPEludGVyYWN0aXZlTWFwIHsuLi5kZWZhdWx0UHJvcHN9IGRvY2tpbmdTdGF0aW9ucz17W119IC8+KVxuICAgIFxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXN0SWQoJ21hcC1jb250YWluZXInKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICAgIGV4cGVjdChzY3JlZW4ucXVlcnlCeVRlc3RJZCgnZG9ja2luZy1zdGF0aW9uLTEnKSkubm90LnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcblxuICBpdCgnaGFuZGxlcyBlbXB0eSByZWdpb25zIGxpc3QnLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxJbnRlcmFjdGl2ZU1hcCB7Li4uZGVmYXVsdFByb3BzfSByZWdpb25zPXtbXX0gLz4pXG4gICAgXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFwLWNvbnRhaW5lcicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgZXhwZWN0KHNjcmVlbi5xdWVyeUJ5VGVzdElkKCdnZW9mZW5jZS0xJykpLm5vdC50b0JlSW5UaGVEb2N1bWVudCgpXG4gIH0pXG5cbiAgaXQoJ3Nob3dzIGxvYWRpbmcgc3RhdGUnLCAoKSA9PiB7XG4gICAgcmVuZGVyKDxJbnRlcmFjdGl2ZU1hcCB7Li4uZGVmYXVsdFByb3BzfSBsb2FkaW5nPXt0cnVlfSAvPilcbiAgICBcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCdtYXAtbG9hZGluZycpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gIH0pXG5cbiAgaXQoJ3Nob3dzIGVycm9yIHN0YXRlJywgKCkgPT4ge1xuICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9ICdGYWlsZWQgdG8gbG9hZCBtYXAgZGF0YSdcbiAgICByZW5kZXIoPEludGVyYWN0aXZlTWFwIHsuLi5kZWZhdWx0UHJvcHN9IGVycm9yPXtlcnJvck1lc3NhZ2V9IC8+KVxuICAgIFxuICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KGVycm9yTWVzc2FnZSkpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5Um9sZSgnYWxlcnQnKSkudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdzdXBwb3J0cyBkaWZmZXJlbnQgbWFwIGxheWVycycsICgpID0+IHtcbiAgICByZW5kZXIoPEludGVyYWN0aXZlTWFwIHsuLi5kZWZhdWx0UHJvcHN9IG1hcExheWVyPVwic2F0ZWxsaXRlXCIgLz4pXG4gICAgXG4gICAgLy8gRGlmZmVyZW50IHRpbGUgbGF5ZXIgd291bGQgYmUgcmVuZGVyZWRcbiAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGVzdElkKCd0aWxlLWxheWVyJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcblxuICBpdCgnaGFuZGxlcyByZWFsLXRpbWUgdXBkYXRlcycsIGFzeW5jICgpID0+IHtcbiAgICBjb25zdCB7IHJlcmVuZGVyIH0gPSByZW5kZXIoPEludGVyYWN0aXZlTWFwIHsuLi5kZWZhdWx0UHJvcHN9IC8+KVxuICAgIFxuICAgIGNvbnN0IHVwZGF0ZWRVQVZzID0gW1xuICAgICAgLi4ubW9ja1VBVnMsXG4gICAgICBjcmVhdGVNb2NrVUFWKHtcbiAgICAgICAgaWQ6IDMsXG4gICAgICAgIHJmaWRUYWc6ICdVQVYtMDAzJyxcbiAgICAgICAgbG9jYXRpb246IHsgbGF0aXR1ZGU6IDQwLjczMDAsIGxvbmdpdHVkZTogLTc0LjAwMDAgfSxcbiAgICAgIH0pLFxuICAgIF1cbiAgICBcbiAgICByZXJlbmRlcig8SW50ZXJhY3RpdmVNYXAgey4uLmRlZmF1bHRQcm9wc30gdWF2cz17dXBkYXRlZFVBVnN9IC8+KVxuICAgIFxuICAgIGF3YWl0IHdhaXRGb3IoKCkgPT4ge1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgndWF2LW1hcmtlci0zJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgICB9KVxuICB9KVxuXG4gIGl0KCdoYW5kbGVzIFVBViBsb2NhdGlvbiB1cGRhdGVzJywgKCkgPT4ge1xuICAgIGNvbnN0IHVwZGF0ZWRVQVZzID0gbW9ja1VBVnMubWFwKHVhdiA9PiBcbiAgICAgIHVhdi5pZCA9PT0gMSBcbiAgICAgICAgPyB7IC4uLnVhdiwgbG9jYXRpb246IHsgbGF0aXR1ZGU6IDQwLjcyMDAsIGxvbmdpdHVkZTogLTc0LjAxMDAgfSB9XG4gICAgICAgIDogdWF2XG4gICAgKVxuICAgIFxuICAgIGNvbnN0IHsgcmVyZW5kZXIgfSA9IHJlbmRlcig8SW50ZXJhY3RpdmVNYXAgey4uLmRlZmF1bHRQcm9wc30gLz4pXG4gICAgcmVyZW5kZXIoPEludGVyYWN0aXZlTWFwIHsuLi5kZWZhdWx0UHJvcHN9IHVhdnM9e3VwZGF0ZWRVQVZzfSAvPilcbiAgICBcbiAgICAvLyBVcGRhdGVkIGxvY2F0aW9uIHdvdWxkIGJlIHJlZmxlY3RlZCBpbiB0aGUgbWFya2VyIHBvc2l0aW9uXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgndWF2LW1hcmtlci0xJykpLnRvQmVJblRoZURvY3VtZW50KClcbiAgfSlcblxuICBpdCgnc3VwcG9ydHMgY2x1c3RlcmluZyBmb3IgbWFueSBVQVZzJywgKCkgPT4ge1xuICAgIGNvbnN0IG1hbnlVQVZzID0gQXJyYXkuZnJvbSh7IGxlbmd0aDogNTAgfSwgKF8sIGkpID0+IFxuICAgICAgY3JlYXRlTW9ja1VBVih7XG4gICAgICAgIGlkOiBpICsgMSxcbiAgICAgICAgcmZpZFRhZzogYFVBVi0keyhpICsgMSkudG9TdHJpbmcoKS5wYWRTdGFydCgzLCAnMCcpfWAsXG4gICAgICAgIGxvY2F0aW9uOiB7IFxuICAgICAgICAgIGxhdGl0dWRlOiA0MC43MTI4ICsgKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMC4xLCBcbiAgICAgICAgICBsb25naXR1ZGU6IC03NC4wMDYwICsgKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMC4xIFxuICAgICAgICB9LFxuICAgICAgfSlcbiAgICApXG4gICAgXG4gICAgcmVuZGVyKDxJbnRlcmFjdGl2ZU1hcCB7Li4uZGVmYXVsdFByb3BzfSB1YXZzPXttYW55VUFWc30gZW5hYmxlQ2x1c3RlcmluZz17dHJ1ZX0gLz4pXG4gICAgXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFwLWNvbnRhaW5lcicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgLy8gQ2x1c3RlcmluZyB3b3VsZCBiZSBoYW5kbGVkIGJ5IHRoZSBtYXAgbGlicmFyeVxuICB9KVxuXG4gIGl0KCdoYW5kbGVzIG1hcCBpbnRlcmFjdGlvbiBldmVudHMnLCAoKSA9PiB7XG4gICAgY29uc3Qgb25NYXBDbGljayA9IGplc3QuZm4oKVxuICAgIHJlbmRlcig8SW50ZXJhY3RpdmVNYXAgey4uLmRlZmF1bHRQcm9wc30gb25NYXBDbGljaz17b25NYXBDbGlja30gLz4pXG4gICAgXG4gICAgY29uc3QgbWFwQ29udGFpbmVyID0gc2NyZWVuLmdldEJ5VGVzdElkKCdtYXAtY29udGFpbmVyJylcbiAgICBmaXJlRXZlbnQuY2xpY2sobWFwQ29udGFpbmVyKVxuICAgIFxuICAgIC8vIE1hcCBjbGljayB3b3VsZCBiZSBoYW5kbGVkIGJ5IHRoZSBNYXBDb250YWluZXIgY29tcG9uZW50XG4gICAgZXhwZWN0KG1hcENvbnRhaW5lcikudG9CZUluVGhlRG9jdW1lbnQoKVxuICB9KVxuXG4gIGl0KCdzdXBwb3J0cyBjdXN0b20gbWFwIGNvbnRyb2xzJywgKCkgPT4ge1xuICAgIHJlbmRlcihcbiAgICAgIDxJbnRlcmFjdGl2ZU1hcCBcbiAgICAgICAgey4uLmRlZmF1bHRQcm9wc30gXG4gICAgICAgIHNob3dab29tQ29udHJvbD17dHJ1ZX1cbiAgICAgICAgc2hvd1NjYWxlQ29udHJvbD17dHJ1ZX1cbiAgICAgICAgc2hvd0Z1bGxzY3JlZW5Db250cm9sPXt0cnVlfVxuICAgICAgLz5cbiAgICApXG4gICAgXG4gICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRlc3RJZCgnbWFwLWNvbnRhaW5lcicpKS50b0JlSW5UaGVEb2N1bWVudCgpXG4gICAgLy8gQ3VzdG9tIGNvbnRyb2xzIHdvdWxkIGJlIHJlbmRlcmVkIGFzIHBhcnQgb2YgdGhlIG1hcFxuICB9KVxuXG4gIGl0KCdoYW5kbGVzIHJlc3BvbnNpdmUgZGVzaWduJywgKCkgPT4ge1xuICAgIHJlbmRlcig8SW50ZXJhY3RpdmVNYXAgey4uLmRlZmF1bHRQcm9wc30gY2xhc3NOYW1lPVwiaC05NiB3LWZ1bGxcIiAvPilcbiAgICBcbiAgICBjb25zdCBtYXBDb250YWluZXIgPSBzY3JlZW4uZ2V0QnlUZXN0SWQoJ21hcC1jb250YWluZXInKVxuICAgIGV4cGVjdChtYXBDb250YWluZXIucGFyZW50RWxlbWVudCkudG9IYXZlQ2xhc3MoJ2gtOTYnLCAndy1mdWxsJylcbiAgfSlcblxuICBpdCgnc3VwcG9ydHMgYWNjZXNzaWJpbGl0eSBmZWF0dXJlcycsICgpID0+IHtcbiAgICByZW5kZXIoXG4gICAgICA8SW50ZXJhY3RpdmVNYXAgXG4gICAgICAgIHsuLi5kZWZhdWx0UHJvcHN9IFxuICAgICAgICBhcmlhLWxhYmVsPVwiVUFWIHRyYWNraW5nIG1hcFwiXG4gICAgICAgIHJvbGU9XCJhcHBsaWNhdGlvblwiXG4gICAgICAvPlxuICAgIClcbiAgICBcbiAgICBjb25zdCBtYXBDb250YWluZXIgPSBzY3JlZW4uZ2V0QnlUZXN0SWQoJ21hcC1jb250YWluZXInKVxuICAgIGV4cGVjdChtYXBDb250YWluZXIpLnRvSGF2ZUF0dHJpYnV0ZSgnYXJpYS1sYWJlbCcsICdVQVYgdHJhY2tpbmcgbWFwJylcbiAgICBleHBlY3QobWFwQ29udGFpbmVyKS50b0hhdmVBdHRyaWJ1dGUoJ3JvbGUnLCAnYXBwbGljYXRpb24nKVxuICB9KVxufSlcbiJdLCJuYW1lcyI6WyJqZXN0IiwibW9jayIsIm1vdGlvbiIsImRpdiIsImNoaWxkcmVuIiwicHJvcHMiLCJBbmltYXRlUHJlc2VuY2UiLCJBbmltYXRlZFVBVk1hcmtlciIsInVhdiIsIm9uU2VsZWN0IiwiZGF0YS10ZXN0aWQiLCJpZCIsIm9uQ2xpY2siLCJyZmlkVGFnIiwiQW5pbWF0ZWRHZW9mZW5jZSIsInJlZ2lvbiIsIm5hbWUiLCJBbmltYXRlZEZsaWdodFBhdGgiLCJwYXRoIiwiQW5pbWF0ZWREb2NraW5nU3RhdGlvbiIsInN0YXRpb24iLCJkZXNjcmliZSIsIm1vY2tVQVZzIiwiY3JlYXRlTW9ja1VBViIsInN0YXR1cyIsIm9wZXJhdGlvbmFsU3RhdHVzIiwibG9jYXRpb24iLCJsYXRpdHVkZSIsImxvbmdpdHVkZSIsIm1vY2tEb2NraW5nU3RhdGlvbnMiLCJjcmVhdGVNb2NrRG9ja2luZ1N0YXRpb24iLCJtb2NrUmVnaW9ucyIsImRlc2NyaXB0aW9uIiwiY29vcmRpbmF0ZXMiLCJpc0FjdGl2ZSIsImRlZmF1bHRQcm9wcyIsInVhdnMiLCJzZWxlY3RlZFVBViIsImNlbnRlciIsInpvb20iLCJsYXllcnMiLCJnZW9mZW5jZXMiLCJkb2NraW5nU3RhdGlvbnMiLCJmbGlnaHRQYXRocyIsIndlYXRoZXIiLCJvblVBVlNlbGVjdCIsImZuIiwiYmVmb3JlRWFjaCIsImNsZWFyQWxsTW9ja3MiLCJpdCIsInJlbmRlciIsIkludGVyYWN0aXZlTWFwIiwiZXhwZWN0Iiwic2NyZWVuIiwiZ2V0QnlUZXN0SWQiLCJ0b0JlSW5UaGVEb2N1bWVudCIsImdldEJ5VGV4dCIsInVhdk1hcmtlciIsImZpcmVFdmVudCIsImNsaWNrIiwidG9IYXZlQmVlbkNhbGxlZFdpdGgiLCJvblN0YXRpb25TZWxlY3QiLCJzdGF0aW9uTWFya2VyIiwic2VsZWN0ZWRNYXJrZXIiLCJ1YXZJZCIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInNob3dGbGlnaHRQYXRocyIsImZpbHRlcmVkUHJvcHMiLCJmaWx0ZXIiLCJxdWVyeUJ5VGVzdElkIiwibm90IiwicmVyZW5kZXIiLCJuZXdDZW50ZXIiLCJyZWdpb25zIiwibG9hZGluZyIsImVycm9yTWVzc2FnZSIsImVycm9yIiwiZ2V0QnlSb2xlIiwibWFwTGF5ZXIiLCJ1cGRhdGVkVUFWcyIsIndhaXRGb3IiLCJtYXAiLCJtYW55VUFWcyIsIkFycmF5IiwiZnJvbSIsImxlbmd0aCIsIl8iLCJpIiwidG9TdHJpbmciLCJwYWRTdGFydCIsIk1hdGgiLCJyYW5kb20iLCJlbmFibGVDbHVzdGVyaW5nIiwib25NYXBDbGljayIsIm1hcENvbnRhaW5lciIsInNob3dab29tQ29udHJvbCIsInNob3dTY2FsZUNvbnRyb2wiLCJzaG93RnVsbHNjcmVlbkNvbnRyb2wiLCJjbGFzc05hbWUiLCJwYXJlbnRFbGVtZW50IiwidG9IYXZlQ2xhc3MiLCJhcmlhLWxhYmVsIiwicm9sZSIsInRvSGF2ZUF0dHJpYnV0ZSJdLCJtYXBwaW5ncyI6IjtBQU1BLGtFQUFrRTtBQUNsRSxxQkFBcUI7QUFDckJBLEtBQUtDLElBQUksQ0FBQyxpQkFBaUIsSUFBTyxDQUFBO1FBQ2hDQyxRQUFRO1lBQ05DLEtBQUssQ0FBQyxFQUFFQyxRQUFRLEVBQUUsR0FBR0MsT0FBWSxpQkFBSyxxQkFBQ0Y7b0JBQUssR0FBR0UsS0FBSzs4QkFBR0Q7O1FBQ3pEO1FBQ0FFLGlCQUFpQixDQUFDLEVBQUVGLFFBQVEsRUFBTyxHQUFLQTtJQUMxQyxDQUFBO0FBRUEsbUNBQW1DO0FBQ25DSixLQUFLQyxJQUFJLENBQUMsOEJBQThCLElBQU8sQ0FBQTtRQUM3Q00sbUJBQW1CLENBQUMsRUFBRUMsR0FBRyxFQUFFQyxRQUFRLEVBQU8saUJBQ3hDLHNCQUFDTjtnQkFDQ08sZUFBYSxDQUFDLFdBQVcsRUFBRUYsSUFBSUcsRUFBRSxDQUFDLENBQUM7Z0JBQ25DQyxTQUFTLElBQU1ILFNBQVNEOztvQkFDekI7b0JBQ2NBLElBQUlLLE9BQU87OztRQUc1QkMsa0JBQWtCLENBQUMsRUFBRUMsTUFBTSxFQUFPLGlCQUNoQyxzQkFBQ1o7Z0JBQUlPLGVBQWEsQ0FBQyxTQUFTLEVBQUVLLE9BQU9KLEVBQUUsQ0FBQyxDQUFDOztvQkFBRTtvQkFDOUJJLE9BQU9DLElBQUk7OztRQUcxQkMsb0JBQW9CLENBQUMsRUFBRUMsSUFBSSxFQUFPLGlCQUNoQyxxQkFBQ2Y7Z0JBQUlPLGVBQVk7MEJBQWM7O1FBSWpDUyx3QkFBd0IsQ0FBQyxFQUFFQyxPQUFPLEVBQUVYLFFBQVEsRUFBTyxpQkFDakQsc0JBQUNOO2dCQUNDTyxlQUFhLENBQUMsZ0JBQWdCLEVBQUVVLFFBQVFULEVBQUUsQ0FBQyxDQUFDO2dCQUM1Q0MsU0FBUyxJQUFNSCxTQUFTVzs7b0JBQ3pCO29CQUNtQkEsUUFBUUosSUFBSTs7O0lBR3BDLENBQUE7Ozs7OzhEQTNDa0I7MkJBQ2lDO3VFQUN4Qjs7Ozs7O0FBMkMzQkssU0FBUyw0QkFBNEI7SUFDbkMsTUFBTUMsV0FBa0I7UUFDdEJDLElBQUFBLHdCQUFhLEVBQUM7WUFDWlosSUFBSTtZQUNKRSxTQUFTO1lBQ1RXLFFBQVE7WUFDUkMsbUJBQW1CO1lBQ25CQyxVQUFVO2dCQUFFQyxVQUFVO2dCQUFTQyxXQUFXLENBQUM7WUFBUTtRQUNyRDtRQUNBTCxJQUFBQSx3QkFBYSxFQUFDO1lBQ1paLElBQUk7WUFDSkUsU0FBUztZQUNUVyxRQUFRO1lBQ1JDLG1CQUFtQjtZQUNuQkMsVUFBVTtnQkFBRUMsVUFBVTtnQkFBU0MsV0FBVyxDQUFDO1lBQVE7UUFDckQ7S0FDRDtJQUVELE1BQU1DLHNCQUF3QztRQUM1Q0MsSUFBQUEsbUNBQXdCLEVBQUM7WUFDdkJuQixJQUFJO1lBQ0pLLE1BQU07WUFDTlUsVUFBVTtnQkFBRUMsVUFBVTtnQkFBU0MsV0FBVyxDQUFDO1lBQVE7WUFDbkRKLFFBQVE7UUFDVjtLQUNEO0lBRUQsTUFBTU8sY0FBYztRQUNsQjtZQUNFcEIsSUFBSTtZQUNKSyxNQUFNO1lBQ05nQixhQUFhO1lBQ2JDLGFBQWE7Z0JBQ1g7b0JBQUVOLFVBQVU7b0JBQVNDLFdBQVcsQ0FBQztnQkFBUTtnQkFDekM7b0JBQUVELFVBQVU7b0JBQVNDLFdBQVcsQ0FBQztnQkFBUTtnQkFDekM7b0JBQUVELFVBQVU7b0JBQVNDLFdBQVcsQ0FBQztnQkFBUTtnQkFDekM7b0JBQUVELFVBQVU7b0JBQVNDLFdBQVcsQ0FBQztnQkFBUTthQUMxQztZQUNETSxVQUFVO1FBQ1o7S0FDRDtJQUVELE1BQU1DLGVBQWU7UUFDbkJDLE1BQU1kO1FBQ05lLGFBQWE7UUFDYkMsUUFBUTtZQUFDO1lBQVMsQ0FBQztTQUFRO1FBQzNCQyxNQUFNO1FBQ05DLFFBQVE7WUFDTkosTUFBTTtZQUNOSyxXQUFXO1lBQ1hDLGlCQUFpQjtZQUNqQkMsYUFBYTtZQUNiQyxTQUFTO1FBQ1g7UUFDQUMsYUFBYTdDLEtBQUs4QyxFQUFFO0lBQ3RCO0lBRUFDLFdBQVc7UUFDVC9DLEtBQUtnRCxhQUFhO0lBQ3BCO0lBRUFDLEdBQUcscUJBQXFCO1FBQ3RCQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsdUJBQWM7WUFBRSxHQUFHaEIsWUFBWTs7UUFFdkNpQixPQUFPQyxpQkFBTSxDQUFDQyxXQUFXLENBQUMsa0JBQWtCQyxpQkFBaUI7UUFDN0RILE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxlQUFlQyxpQkFBaUI7SUFDNUQ7SUFFQU4sR0FBRyx1QkFBdUI7UUFDeEJDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyx1QkFBYztZQUFFLEdBQUdoQixZQUFZOztRQUV2Q2lCLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxpQkFBaUJDLGlCQUFpQjtRQUM1REgsT0FBT0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLGlCQUFpQkMsaUJBQWlCO1FBQzVESCxPQUFPQyxpQkFBTSxDQUFDRyxTQUFTLENBQUMsd0JBQXdCRCxpQkFBaUI7UUFDakVILE9BQU9DLGlCQUFNLENBQUNHLFNBQVMsQ0FBQyx3QkFBd0JELGlCQUFpQjtJQUNuRTtJQUVBTixHQUFHLG1DQUFtQztRQUNwQ0MsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLHVCQUFjO1lBQUUsR0FBR2hCLFlBQVk7O1FBRXZDaUIsT0FBT0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLHNCQUFzQkMsaUJBQWlCO1FBQ2pFSCxPQUFPQyxpQkFBTSxDQUFDRyxTQUFTLENBQUMsbUNBQW1DRCxpQkFBaUI7SUFDOUU7SUFFQU4sR0FBRyxpQ0FBaUM7UUFDbENDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyx1QkFBYztZQUFFLEdBQUdoQixZQUFZOztRQUV2Q2lCLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxlQUFlQyxpQkFBaUI7UUFDMURILE9BQU9DLGlCQUFNLENBQUNHLFNBQVMsQ0FBQyxxQkFBcUJELGlCQUFpQjtJQUNoRTtJQUVBTixHQUFHLHlCQUF5QjtRQUMxQixNQUFNSixjQUFjN0MsS0FBSzhDLEVBQUU7UUFDM0JJLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyx1QkFBYztZQUFFLEdBQUdoQixZQUFZO1lBQUVVLGFBQWFBOztRQUV0RCxNQUFNWSxZQUFZSixpQkFBTSxDQUFDQyxXQUFXLENBQUM7UUFDckNJLG9CQUFTLENBQUNDLEtBQUssQ0FBQ0Y7UUFFaEJMLE9BQU9QLGFBQWFlLG9CQUFvQixDQUFDdEMsUUFBUSxDQUFDLEVBQUU7SUFDdEQ7SUFFQTJCLEdBQUcscUNBQXFDO1FBQ3RDLE1BQU1ZLGtCQUFrQjdELEtBQUs4QyxFQUFFO1FBQy9CSSxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsdUJBQWM7WUFBRSxHQUFHaEIsWUFBWTtZQUFFMEIsaUJBQWlCQTs7UUFFMUQsTUFBTUMsZ0JBQWdCVCxpQkFBTSxDQUFDQyxXQUFXLENBQUM7UUFDekNJLG9CQUFTLENBQUNDLEtBQUssQ0FBQ0c7UUFFaEJWLE9BQU9TLGlCQUFpQkQsb0JBQW9CLENBQUMvQixtQkFBbUIsQ0FBQyxFQUFFO0lBQ3JFO0lBRUFvQixHQUFHLDJCQUEyQjtRQUM1QixNQUFNWixjQUFjZixRQUFRLENBQUMsRUFBRTtRQUMvQjRCLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyx1QkFBYztZQUFFLEdBQUdoQixZQUFZO1lBQUVFLGFBQWFBOztRQUV0RCxNQUFNMEIsaUJBQWlCVixpQkFBTSxDQUFDQyxXQUFXLENBQUM7UUFDMUNGLE9BQU9XLGdCQUFnQlIsaUJBQWlCO0lBQ3hDLHdFQUF3RTtJQUMxRTtJQUVBTixHQUFHLG1DQUFtQztRQUNwQyxNQUFNTixjQUFjO1lBQ2xCO2dCQUNFaEMsSUFBSTtnQkFDSnFELE9BQU87Z0JBQ1AvQixhQUFhO29CQUNYO3dCQUFFTixVQUFVO3dCQUFTQyxXQUFXLENBQUM7b0JBQVE7b0JBQ3pDO3dCQUFFRCxVQUFVO3dCQUFTQyxXQUFXLENBQUM7b0JBQVE7aUJBQzFDO2dCQUNEcUMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DO1NBQ0Q7UUFFRGpCLElBQUFBLGlCQUFNLGdCQUNKLHFCQUFDQyx1QkFBYztZQUNaLEdBQUdoQixZQUFZO1lBQ2hCUSxhQUFhQTtZQUNieUIsaUJBQWlCOztRQUlyQmhCLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxnQkFBZ0JDLGlCQUFpQjtJQUM3RDtJQUVBTixHQUFHLDBCQUEwQjtRQUMzQixNQUFNb0IsZ0JBQWdCO1lBQ3BCLEdBQUdsQyxZQUFZO1lBQ2ZDLE1BQU1kLFNBQVNnRCxNQUFNLENBQUM5RCxDQUFBQSxNQUFPQSxJQUFJaUIsaUJBQWlCLEtBQUs7UUFDekQ7UUFFQXlCLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyx1QkFBYztZQUFFLEdBQUdrQixhQUFhOztRQUV4Q2pCLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxpQkFBaUJDLGlCQUFpQjtRQUM1REgsT0FBT0MsaUJBQU0sQ0FBQ2tCLGFBQWEsQ0FBQyxpQkFBaUJDLEdBQUcsQ0FBQ2pCLGlCQUFpQjtJQUNwRTtJQUVBTixHQUFHLHdDQUF3QztRQUN6QyxNQUFNLEVBQUV3QixRQUFRLEVBQUUsR0FBR3ZCLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyx1QkFBYztZQUFFLEdBQUdoQixZQUFZOztRQUU1RCxNQUFNdUMsWUFBWTtZQUFFL0MsVUFBVTtZQUFTQyxXQUFXLENBQUM7UUFBUTtRQUMzRDZDLHVCQUFTLHFCQUFDdEIsdUJBQWM7WUFBRSxHQUFHaEIsWUFBWTtZQUFFRyxRQUFRb0M7O1FBRW5ELG1FQUFtRTtRQUNuRXRCLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxrQkFBa0JDLGlCQUFpQjtJQUMvRDtJQUVBTixHQUFHLHdDQUF3QztRQUN6QyxNQUFNLEVBQUV3QixRQUFRLEVBQUUsR0FBR3ZCLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyx1QkFBYztZQUFFLEdBQUdoQixZQUFZOztRQUU1RHNDLHVCQUFTLHFCQUFDdEIsdUJBQWM7WUFBRSxHQUFHaEIsWUFBWTtZQUFFSSxNQUFNOztRQUVqRCw2REFBNkQ7UUFDN0RhLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxrQkFBa0JDLGlCQUFpQjtJQUMvRDtJQUVBTixHQUFHLDBCQUEwQjtRQUMzQkMsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLHVCQUFjO1lBQUUsR0FBR2hCLFlBQVk7WUFBRUMsTUFBTSxFQUFFOztRQUVqRGdCLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxrQkFBa0JDLGlCQUFpQjtRQUM3REgsT0FBT0MsaUJBQU0sQ0FBQ2tCLGFBQWEsQ0FBQyxpQkFBaUJDLEdBQUcsQ0FBQ2pCLGlCQUFpQjtRQUNsRUgsT0FBT0MsaUJBQU0sQ0FBQ2tCLGFBQWEsQ0FBQyxpQkFBaUJDLEdBQUcsQ0FBQ2pCLGlCQUFpQjtJQUNwRTtJQUVBTixHQUFHLHVDQUF1QztRQUN4Q0MsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLHVCQUFjO1lBQUUsR0FBR2hCLFlBQVk7WUFBRU8saUJBQWlCLEVBQUU7O1FBRTVEVSxPQUFPQyxpQkFBTSxDQUFDQyxXQUFXLENBQUMsa0JBQWtCQyxpQkFBaUI7UUFDN0RILE9BQU9DLGlCQUFNLENBQUNrQixhQUFhLENBQUMsc0JBQXNCQyxHQUFHLENBQUNqQixpQkFBaUI7SUFDekU7SUFFQU4sR0FBRyw4QkFBOEI7UUFDL0JDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyx1QkFBYztZQUFFLEdBQUdoQixZQUFZO1lBQUV3QyxTQUFTLEVBQUU7O1FBRXBEdkIsT0FBT0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLGtCQUFrQkMsaUJBQWlCO1FBQzdESCxPQUFPQyxpQkFBTSxDQUFDa0IsYUFBYSxDQUFDLGVBQWVDLEdBQUcsQ0FBQ2pCLGlCQUFpQjtJQUNsRTtJQUVBTixHQUFHLHVCQUF1QjtRQUN4QkMsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLHVCQUFjO1lBQUUsR0FBR2hCLFlBQVk7WUFBRXlDLFNBQVM7O1FBRWxEeEIsT0FBT0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDLGdCQUFnQkMsaUJBQWlCO0lBQzdEO0lBRUFOLEdBQUcscUJBQXFCO1FBQ3RCLE1BQU00QixlQUFlO1FBQ3JCM0IsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLHVCQUFjO1lBQUUsR0FBR2hCLFlBQVk7WUFBRTJDLE9BQU9EOztRQUVoRHpCLE9BQU9DLGlCQUFNLENBQUNHLFNBQVMsQ0FBQ3FCLGVBQWV0QixpQkFBaUI7UUFDeERILE9BQU9DLGlCQUFNLENBQUMwQixTQUFTLENBQUMsVUFBVXhCLGlCQUFpQjtJQUNyRDtJQUVBTixHQUFHLGlDQUFpQztRQUNsQ0MsSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLHVCQUFjO1lBQUUsR0FBR2hCLFlBQVk7WUFBRTZDLFVBQVM7O1FBRWxELHlDQUF5QztRQUN6QzVCLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxlQUFlQyxpQkFBaUI7SUFDNUQ7SUFFQU4sR0FBRyw2QkFBNkI7UUFDOUIsTUFBTSxFQUFFd0IsUUFBUSxFQUFFLEdBQUd2QixJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsdUJBQWM7WUFBRSxHQUFHaEIsWUFBWTs7UUFFNUQsTUFBTThDLGNBQWM7ZUFDZjNEO1lBQ0hDLElBQUFBLHdCQUFhLEVBQUM7Z0JBQ1paLElBQUk7Z0JBQ0pFLFNBQVM7Z0JBQ1RhLFVBQVU7b0JBQUVDLFVBQVU7b0JBQVNDLFdBQVcsQ0FBQztnQkFBUTtZQUNyRDtTQUNEO1FBRUQ2Qyx1QkFBUyxxQkFBQ3RCLHVCQUFjO1lBQUUsR0FBR2hCLFlBQVk7WUFBRUMsTUFBTTZDOztRQUVqRCxNQUFNQyxJQUFBQSxrQkFBTyxFQUFDO1lBQ1o5QixPQUFPQyxpQkFBTSxDQUFDQyxXQUFXLENBQUMsaUJBQWlCQyxpQkFBaUI7UUFDOUQ7SUFDRjtJQUVBTixHQUFHLGdDQUFnQztRQUNqQyxNQUFNZ0MsY0FBYzNELFNBQVM2RCxHQUFHLENBQUMzRSxDQUFBQSxNQUMvQkEsSUFBSUcsRUFBRSxLQUFLLElBQ1A7Z0JBQUUsR0FBR0gsR0FBRztnQkFBRWtCLFVBQVU7b0JBQUVDLFVBQVU7b0JBQVNDLFdBQVcsQ0FBQztnQkFBUTtZQUFFLElBQy9EcEI7UUFHTixNQUFNLEVBQUVpRSxRQUFRLEVBQUUsR0FBR3ZCLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyx1QkFBYztZQUFFLEdBQUdoQixZQUFZOztRQUM1RHNDLHVCQUFTLHFCQUFDdEIsdUJBQWM7WUFBRSxHQUFHaEIsWUFBWTtZQUFFQyxNQUFNNkM7O1FBRWpELDZEQUE2RDtRQUM3RDdCLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxpQkFBaUJDLGlCQUFpQjtJQUM5RDtJQUVBTixHQUFHLHFDQUFxQztRQUN0QyxNQUFNbUMsV0FBV0MsTUFBTUMsSUFBSSxDQUFDO1lBQUVDLFFBQVE7UUFBRyxHQUFHLENBQUNDLEdBQUdDLElBQzlDbEUsSUFBQUEsd0JBQWEsRUFBQztnQkFDWlosSUFBSThFLElBQUk7Z0JBQ1I1RSxTQUFTLENBQUMsSUFBSSxFQUFFLEFBQUM0RSxDQUFBQSxJQUFJLENBQUEsRUFBR0MsUUFBUSxHQUFHQyxRQUFRLENBQUMsR0FBRyxLQUFLLENBQUM7Z0JBQ3JEakUsVUFBVTtvQkFDUkMsVUFBVSxVQUFVLEFBQUNpRSxDQUFBQSxLQUFLQyxNQUFNLEtBQUssR0FBRSxJQUFLO29CQUM1Q2pFLFdBQVcsQ0FBQyxVQUFVLEFBQUNnRSxDQUFBQSxLQUFLQyxNQUFNLEtBQUssR0FBRSxJQUFLO2dCQUNoRDtZQUNGO1FBR0YzQyxJQUFBQSxpQkFBTSxnQkFBQyxxQkFBQ0MsdUJBQWM7WUFBRSxHQUFHaEIsWUFBWTtZQUFFQyxNQUFNZ0Q7WUFBVVUsa0JBQWtCOztRQUUzRTFDLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxrQkFBa0JDLGlCQUFpQjtJQUM3RCxpREFBaUQ7SUFDbkQ7SUFFQU4sR0FBRyxrQ0FBa0M7UUFDbkMsTUFBTThDLGFBQWEvRixLQUFLOEMsRUFBRTtRQUMxQkksSUFBQUEsaUJBQU0sZ0JBQUMscUJBQUNDLHVCQUFjO1lBQUUsR0FBR2hCLFlBQVk7WUFBRTRELFlBQVlBOztRQUVyRCxNQUFNQyxlQUFlM0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDO1FBQ3hDSSxvQkFBUyxDQUFDQyxLQUFLLENBQUNxQztRQUVoQiwyREFBMkQ7UUFDM0Q1QyxPQUFPNEMsY0FBY3pDLGlCQUFpQjtJQUN4QztJQUVBTixHQUFHLGdDQUFnQztRQUNqQ0MsSUFBQUEsaUJBQU0sZ0JBQ0oscUJBQUNDLHVCQUFjO1lBQ1osR0FBR2hCLFlBQVk7WUFDaEI4RCxpQkFBaUI7WUFDakJDLGtCQUFrQjtZQUNsQkMsdUJBQXVCOztRQUkzQi9DLE9BQU9DLGlCQUFNLENBQUNDLFdBQVcsQ0FBQyxrQkFBa0JDLGlCQUFpQjtJQUM3RCx1REFBdUQ7SUFDekQ7SUFFQU4sR0FBRyw2QkFBNkI7UUFDOUJDLElBQUFBLGlCQUFNLGdCQUFDLHFCQUFDQyx1QkFBYztZQUFFLEdBQUdoQixZQUFZO1lBQUVpRSxXQUFVOztRQUVuRCxNQUFNSixlQUFlM0MsaUJBQU0sQ0FBQ0MsV0FBVyxDQUFDO1FBQ3hDRixPQUFPNEMsYUFBYUssYUFBYSxFQUFFQyxXQUFXLENBQUMsUUFBUTtJQUN6RDtJQUVBckQsR0FBRyxtQ0FBbUM7UUFDcENDLElBQUFBLGlCQUFNLGdCQUNKLHFCQUFDQyx1QkFBYztZQUNaLEdBQUdoQixZQUFZO1lBQ2hCb0UsY0FBVztZQUNYQyxNQUFLOztRQUlULE1BQU1SLGVBQWUzQyxpQkFBTSxDQUFDQyxXQUFXLENBQUM7UUFDeENGLE9BQU80QyxjQUFjUyxlQUFlLENBQUMsY0FBYztRQUNuRHJELE9BQU80QyxjQUFjUyxlQUFlLENBQUMsUUFBUTtJQUMvQztBQUNGIn0=