{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\layout\\app-layout.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Header } from './header'\nimport { Sidebar } from './sidebar'\nimport { PageTransition } from './page-transition'\nimport { cn } from '@/lib/utils'\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function AppLayout({ children, className }: AppLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed)\n  }\n\n  return (\n    <div className=\"flex h-screen bg-background\">\n      {/* Sidebar */}\n      <div className=\"hidden md:flex\">\n        <Sidebar\n          collapsed={sidebarCollapsed}\n          onToggle={toggleSidebar}\n        />\n      </div>\n\n      {/* Main content area */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Header */}\n        <Header onMenuClick={toggleSidebar} />\n\n        {/* Page content */}\n        <main className={cn('flex-1 overflow-y-auto', className)}>\n          <div className=\"container mx-auto px-4 py-6\">\n            <PageTransition>\n              {children}\n            </PageTransition>\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": ["AppLayout", "children", "className", "sidebarCollapsed", "setSidebarCollapsed", "useState", "toggleSidebar", "div", "Sidebar", "collapsed", "onToggle", "Header", "onMenuClick", "main", "cn", "PageTransition"], "mappings": "AAAA;;;;;+BAagBA;;;eAAAA;;;;+DAXgB;wBACT;yBACC;gCACO;uBACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOZ,SAASA,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAkB;IAC/D,MAAM,CAACC,kBAAkBC,oBAAoB,GAAGC,IAAAA,eAAQ,EAAC;IAEzD,MAAMC,gBAAgB;QACpBF,oBAAoB,CAACD;IACvB;IAEA,qBACE,sBAACI;QAAIL,WAAU;;0BAEb,qBAACK;gBAAIL,WAAU;0BACb,cAAA,qBAACM,gBAAO;oBACNC,WAAWN;oBACXO,UAAUJ;;;0BAKd,sBAACC;gBAAIL,WAAU;;kCAEb,qBAACS,cAAM;wBAACC,aAAaN;;kCAGrB,qBAACO;wBAAKX,WAAWY,IAAAA,SAAE,EAAC,0BAA0BZ;kCAC5C,cAAA,qBAACK;4BAAIL,WAAU;sCACb,cAAA,qBAACa,8BAAc;0CACZd;;;;;;;;AAOf"}