{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\scroll-area.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": ["ScrollArea", "<PERSON><PERSON>Bar", "React", "forwardRef", "className", "children", "props", "ref", "ScrollAreaPrimitive", "Root", "cn", "Viewport", "Corner", "displayName", "orientation", "ScrollAreaScrollbar", "ScrollAreaThumb"], "mappings": ";;;;;;;;;;;IA6CSA,UAAU;eAAVA;;IAAYC,SAAS;eAATA;;;;+DA7CE;yEACc;uBAElB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnB,MAAMD,2BAAaE,OAAMC,UAAU,CAGjC,CAAC,EAAEC,SAAS,EAAEC,QAAQ,EAAE,GAAGC,OAAO,EAAEC,oBACpC,sBAACC,iBAAoBC,IAAI;QACvBF,KAAKA;QACLH,WAAWM,IAAAA,SAAE,EAAC,4BAA4BN;QACzC,GAAGE,KAAK;;0BAET,qBAACE,iBAAoBG,QAAQ;gBAACP,WAAU;0BACrCC;;0BAEH,qBAACJ;0BACD,qBAACO,iBAAoBI,MAAM;;;AAG/BZ,WAAWa,WAAW,GAAGL,iBAAoBC,IAAI,CAACI,WAAW;AAE7D,MAAMZ,0BAAYC,OAAMC,UAAU,CAGhC,CAAC,EAAEC,SAAS,EAAEU,cAAc,UAAU,EAAE,GAAGR,OAAO,EAAEC,oBACpD,qBAACC,iBAAoBO,mBAAmB;QACtCR,KAAKA;QACLO,aAAaA;QACbV,WAAWM,IAAAA,SAAE,EACX,iDACAI,gBAAgB,cACd,sDACFA,gBAAgB,gBACd,wDACFV;QAED,GAAGE,KAAK;kBAET,cAAA,qBAACE,iBAAoBQ,eAAe;YAACZ,WAAU;;;AAGnDH,UAAUY,WAAW,GAAGL,iBAAoBO,mBAAmB,CAACF,WAAW"}