{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n        warning:\n          \"border-yellow-500/50 text-yellow-800 bg-yellow-50 [&>svg]:text-yellow-600\",\n        success:\n          \"border-green-500/50 text-green-800 bg-green-50 [&>svg]:text-green-600\",\n        info:\n          \"border-blue-500/50 text-blue-800 bg-blue-50 [&>svg]:text-blue-600\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": ["<PERSON><PERSON>", "AlertDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alertVariants", "cva", "variants", "variant", "default", "destructive", "warning", "success", "info", "defaultVariants", "React", "forwardRef", "className", "props", "ref", "div", "role", "cn", "displayName", "h5"], "mappings": ";;;;;;;;;;;IAgESA,KAAK;eAALA;;IAAmBC,gBAAgB;eAAhBA;;IAAZC,UAAU;eAAVA;;;;+DAhEO;wCACgB;uBAEpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnB,MAAMC,gBAAgBC,IAAAA,2BAAG,EACvB,6JACA;IACEC,UAAU;QACRC,SAAS;YACPC,SAAS;YACTC,aACE;YACFC,SACE;YACFC,SACE;YACFC,MACE;QACJ;IACF;IACAC,iBAAiB;QACfN,SAAS;IACX;AACF;AAGF,MAAMN,sBAAQa,OAAMC,UAAU,CAG5B,CAAC,EAAEC,SAAS,EAAET,OAAO,EAAE,GAAGU,OAAO,EAAEC,oBACnC,qBAACC;QACCD,KAAKA;QACLE,MAAK;QACLJ,WAAWK,IAAAA,SAAE,EAACjB,cAAc;YAAEG;QAAQ,IAAIS;QACzC,GAAGC,KAAK;;AAGbhB,MAAMqB,WAAW,GAAG;AAEpB,MAAMnB,2BAAaW,OAAMC,UAAU,CAGjC,CAAC,EAAEC,SAAS,EAAE,GAAGC,OAAO,EAAEC,oBAC1B,qBAACK;QACCL,KAAKA;QACLF,WAAWK,IAAAA,SAAE,EAAC,gDAAgDL;QAC7D,GAAGC,KAAK;;AAGbd,WAAWmB,WAAW,GAAG;AAEzB,MAAMpB,iCAAmBY,OAAMC,UAAU,CAGvC,CAAC,EAAEC,SAAS,EAAE,GAAGC,OAAO,EAAEC,oBAC1B,qBAACC;QACCD,KAAKA;QACLF,WAAWK,IAAAA,SAAE,EAAC,iCAAiCL;QAC9C,GAAGC,KAAK;;AAGbf,iBAAiBoB,WAAW,GAAG"}