b40248926e6ce61524c8cc39abd96b90
"use strict";
// Mock the API client
jest.mock("@/lib/api-client");
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _authapi = require("../auth-api");
const _apiclient = /*#__PURE__*/ _interop_require_default(require("../../lib/api-client"));
const _testutils = require("../../lib/test-utils");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockedApiClient = _apiclient.default;
describe("AuthApi", ()=>{
    let authApi;
    beforeEach(()=>{
        authApi = new _authapi.AuthApi();
        jest.clearAllMocks();
    });
    describe("login", ()=>{
        it("should login successfully", async ()=>{
            const loginData = {
                username: "testuser",
                password: "password123",
                rememberMe: false
            };
            const mockResponse = {
                user: (0, _testutils.createMockUser)(),
                token: "mock-token",
                refreshToken: "mock-refresh-token",
                expiresIn: 3600
            };
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await authApi.login(loginData);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/auth/login", loginData);
        });
        it("should handle login error", async ()=>{
            const loginData = {
                username: "testuser",
                password: "wrongpassword",
                rememberMe: false
            };
            const error = new Error("Invalid credentials");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(authApi.login(loginData)).rejects.toThrow("Invalid credentials");
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/auth/login", loginData);
        });
    });
    describe("logout", ()=>{
        it("should logout successfully", async ()=>{
            mockedApiClient.post.mockResolvedValue(undefined);
            await authApi.logout();
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/auth/logout");
        });
        it("should handle logout error", async ()=>{
            const error = new Error("Logout failed");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(authApi.logout()).rejects.toThrow("Logout failed");
        });
    });
    describe("register", ()=>{
        it("should register successfully", async ()=>{
            const registerData = {
                username: "newuser",
                email: "<EMAIL>",
                password: "password123",
                firstName: "New",
                lastName: "User"
            };
            const mockResponse = {
                user: (0, _testutils.createMockUser)({
                    username: "newuser",
                    email: "<EMAIL>",
                    firstName: "New",
                    lastName: "User"
                }),
                token: "mock-token",
                refreshToken: "mock-refresh-token",
                expiresIn: 3600
            };
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await authApi.register(registerData);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/auth/register", registerData);
        });
        it("should handle registration error", async ()=>{
            const registerData = {
                username: "existinguser",
                email: "<EMAIL>",
                password: "password123",
                firstName: "Existing",
                lastName: "User"
            };
            const error = new Error("Username already exists");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(authApi.register(registerData)).rejects.toThrow("Username already exists");
        });
    });
    describe("refreshToken", ()=>{
        it("should refresh token successfully", async ()=>{
            const refreshRequest = {
                refreshToken: "refresh-token"
            };
            const mockResponse = {
                token: "new-token",
                refreshToken: "new-refresh-token",
                expiresIn: 3600
            };
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await authApi.refreshToken(refreshRequest);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/auth/refresh", refreshRequest);
        });
        it("should handle refresh token error", async ()=>{
            const refreshRequest = {
                refreshToken: "invalid-token"
            };
            const error = new Error("Invalid refresh token");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(authApi.refreshToken(refreshRequest)).rejects.toThrow("Invalid refresh token");
        });
    });
    describe("changePassword", ()=>{
        it("should change password successfully", async ()=>{
            const changePasswordData = {
                currentPassword: "oldpassword",
                newPassword: "newpassword",
                confirmPassword: "newpassword"
            };
            const mockResponse = {
                success: true,
                message: "Password changed successfully"
            };
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await authApi.changePassword(changePasswordData);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/auth/change-password", changePasswordData);
        });
        it("should handle change password error", async ()=>{
            const changePasswordData = {
                currentPassword: "wrongpassword",
                newPassword: "newpassword",
                confirmPassword: "newpassword"
            };
            const error = new Error("Current password is incorrect");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(authApi.changePassword(changePasswordData)).rejects.toThrow("Current password is incorrect");
        });
    });
    describe("forgotPassword", ()=>{
        it("should send forgot password email successfully", async ()=>{
            const forgotPasswordData = {
                email: "<EMAIL>"
            };
            const mockResponse = {
                success: true,
                message: "Reset email sent"
            };
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await authApi.forgotPassword(forgotPasswordData);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/auth/forgot-password", forgotPasswordData);
        });
        it("should handle forgot password error", async ()=>{
            const forgotPasswordData = {
                email: "<EMAIL>"
            };
            const error = new Error("Email not found");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(authApi.forgotPassword(forgotPasswordData)).rejects.toThrow("Email not found");
        });
    });
    describe("resetPassword", ()=>{
        it("should reset password successfully", async ()=>{
            const resetPasswordData = {
                token: "reset-token",
                newPassword: "newpassword",
                confirmPassword: "newpassword"
            };
            const mockResponse = {
                success: true,
                message: "Password reset successfully"
            };
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await authApi.resetPassword(resetPasswordData);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/auth/reset-password", resetPasswordData);
        });
        it("should handle reset password error", async ()=>{
            const resetPasswordData = {
                token: "invalid-token",
                newPassword: "newpassword",
                confirmPassword: "newpassword"
            };
            const error = new Error("Invalid or expired token");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(authApi.resetPassword(resetPasswordData)).rejects.toThrow("Invalid or expired token");
        });
    });
    describe("getUserProfile", ()=>{
        it("should get user profile successfully", async ()=>{
            const mockUser = (0, _testutils.createMockUser)();
            mockedApiClient.get.mockResolvedValue(mockUser);
            const result = await authApi.getUserProfile();
            expect(result).toEqual(mockUser);
            expect(mockedApiClient.get).toHaveBeenCalledWith("/api/auth/profile");
        });
        it("should handle get profile error", async ()=>{
            const error = new Error("Unauthorized");
            mockedApiClient.get.mockRejectedValue(error);
            await expect(authApi.getUserProfile()).rejects.toThrow("Unauthorized");
        });
    });
    describe("updateProfile", ()=>{
        it("should update profile successfully", async ()=>{
            const updateData = {
                firstName: "Updated",
                lastName: "Name",
                email: "<EMAIL>"
            };
            const mockUpdatedUser = (0, _testutils.createMockUser)(updateData);
            mockedApiClient.put.mockResolvedValue(mockUpdatedUser);
            const result = await authApi.updateProfile(updateData);
            expect(result).toEqual(mockUpdatedUser);
            expect(mockedApiClient.put).toHaveBeenCalledWith("/api/auth/profile", updateData);
        });
        it("should handle update profile error", async ()=>{
            const updateData = {
                email: "invalid-email"
            };
            const error = new Error("Invalid email format");
            mockedApiClient.put.mockRejectedValue(error);
            await expect(authApi.updateProfile(updateData)).rejects.toThrow("Invalid email format");
        });
    });
    describe("verifyEmail", ()=>{
        it("should verify email successfully", async ()=>{
            const token = "verification-token";
            const mockResponse = {
                success: true,
                message: "Email verified"
            };
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await authApi.verifyEmail(token);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/auth/verify-email", {
                token
            });
        });
        it("should handle email verification error", async ()=>{
            const token = "invalid-token";
            const error = new Error("Invalid verification token");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(authApi.verifyEmail(token)).rejects.toThrow("Invalid verification token");
        });
    });
    describe("resendVerificationEmail", ()=>{
        it("should resend verification email successfully", async ()=>{
            const email = "<EMAIL>";
            const mockResponse = {
                success: true,
                message: "Verification email sent"
            };
            mockedApiClient.post.mockResolvedValue(mockResponse);
            const result = await authApi.resendVerificationEmail(email);
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.post).toHaveBeenCalledWith("/api/auth/resend-verification", {
                email
            });
        });
        it("should handle resend verification error", async ()=>{
            const email = "<EMAIL>";
            const error = new Error("Email not found");
            mockedApiClient.post.mockRejectedValue(error);
            await expect(authApi.resendVerificationEmail(email)).rejects.toThrow("Email not found");
        });
    });
    describe("checkSession", ()=>{
        it("should check session successfully", async ()=>{
            const mockResponse = {
                valid: true,
                user: (0, _testutils.createMockUser)()
            };
            mockedApiClient.get.mockResolvedValue(mockResponse);
            const result = await authApi.checkSession();
            expect(result).toEqual(mockResponse);
            expect(mockedApiClient.get).toHaveBeenCalledWith("/api/auth/session");
        });
        it("should handle invalid session", async ()=>{
            const error = new Error("Session expired");
            mockedApiClient.get.mockRejectedValue(error);
            await expect(authApi.checkSession()).rejects.toThrow("Session expired");
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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