{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\stores\\__tests__\\dashboard-store.test.ts"], "sourcesContent": ["import { renderHook, act } from '@testing-library/react'\nimport { useDashboardStore } from '../dashboard-store'\nimport { createMockAlert, createMockUAV } from '@/lib/test-utils'\nimport { DashboardMetrics, FlightActivity, BatteryStatistics } from '../dashboard-store'\nimport { SystemAlert, UAVLocationUpdate } from '@/types/uav'\n\ndescribe('Dashboard Store', () => {\n  beforeEach(() => {\n    // Reset store state\n    useDashboardStore.setState({\n      metrics: null,\n      flightActivity: null,\n      batteryStats: null,\n      hibernatePodMetrics: null,\n      chartData: null,\n      alerts: [],\n      recentLocationUpdates: [],\n      isConnected: false,\n      lastUpdate: null,\n      connectionError: null,\n      selectedTimeRange: '24h',\n      autoRefresh: true,\n      refreshInterval: 30,\n      showAlerts: true,\n    })\n    \n    // Clear all mocks\n    jest.clearAllMocks()\n  })\n\n  describe('metrics management', () => {\n    it('should update metrics', () => {\n      const mockMetrics: DashboardMetrics = {\n        totalUAVs: 15,\n        authorizedUAVs: 12,\n        unauthorizedUAVs: 3,\n        activeFlights: 5,\n        hibernatingUAVs: 4,\n        lowBatteryCount: 2,\n        chargingCount: 3,\n        maintenanceCount: 1,\n        emergencyCount: 0,\n      }\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.updateMetrics(mockMetrics)\n      })\n\n      expect(result.current.metrics).toEqual(mockMetrics)\n      expect(result.current.lastUpdate).toBeTruthy()\n    })\n\n    it('should update flight activity', () => {\n      const mockFlightActivity: FlightActivity = {\n        activeFlights: 3,\n        todayFlights: 8,\n        completedFlights: 5,\n        flights: [\n          {\n            id: 1,\n            uavRfid: 'UAV-001',\n            missionName: 'Test Mission',\n            startTime: '2024-01-01T10:00:00Z',\n            status: 'ACTIVE',\n          },\n        ],\n      }\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.updateFlightActivity(mockFlightActivity)\n      })\n\n      expect(result.current.flightActivity).toEqual(mockFlightActivity)\n    })\n\n    it('should update battery statistics', () => {\n      const mockBatteryStats: BatteryStatistics = {\n        averageBatteryLevel: 75,\n        lowBatteryCount: 2,\n        chargingCount: 3,\n        criticalBatteryCount: 1,\n        batteryDistribution: {\n          '0-25': 1,\n          '26-50': 2,\n          '51-75': 4,\n          '76-100': 8,\n        },\n      }\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.updateBatteryStats(mockBatteryStats)\n      })\n\n      expect(result.current.batteryStats).toEqual(mockBatteryStats)\n    })\n  })\n\n  describe('alerts management', () => {\n    it('should add alert', () => {\n      const mockAlert = createMockAlert({\n        id: 1,\n        type: 'WARNING',\n        title: 'Low Battery',\n        message: 'UAV-001 has low battery',\n      })\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.addAlert(mockAlert)\n      })\n\n      expect(result.current.alerts).toHaveLength(1)\n      expect(result.current.alerts[0]).toEqual(mockAlert)\n    })\n\n    it('should remove alert', () => {\n      const mockAlert1 = createMockAlert({ id: 1, title: 'Alert 1' })\n      const mockAlert2 = createMockAlert({ id: 2, title: 'Alert 2' })\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add alerts\n      act(() => {\n        result.current.addAlert(mockAlert1)\n        result.current.addAlert(mockAlert2)\n      })\n\n      expect(result.current.alerts).toHaveLength(2)\n\n      // Remove one alert\n      act(() => {\n        result.current.removeAlert(1)\n      })\n\n      expect(result.current.alerts).toHaveLength(1)\n      expect(result.current.alerts[0].id).toBe(2)\n    })\n\n    it('should acknowledge alert', () => {\n      const mockAlert = createMockAlert({\n        id: 1,\n        acknowledged: false,\n      })\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add alert\n      act(() => {\n        result.current.addAlert(mockAlert)\n      })\n\n      expect(result.current.alerts[0].acknowledged).toBe(false)\n\n      // Acknowledge alert\n      act(() => {\n        result.current.acknowledgeAlert(1)\n      })\n\n      expect(result.current.alerts[0].acknowledged).toBe(true)\n    })\n\n    it('should clear all alerts', () => {\n      const mockAlert1 = createMockAlert({ id: 1 })\n      const mockAlert2 = createMockAlert({ id: 2 })\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add alerts\n      act(() => {\n        result.current.addAlert(mockAlert1)\n        result.current.addAlert(mockAlert2)\n      })\n\n      expect(result.current.alerts).toHaveLength(2)\n\n      // Clear all alerts\n      act(() => {\n        result.current.clearAlerts()\n      })\n\n      expect(result.current.alerts).toHaveLength(0)\n    })\n\n    it('should limit alerts to maximum count', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add more than 50 alerts (assuming max is 50)\n      act(() => {\n        for (let i = 1; i <= 55; i++) {\n          result.current.addAlert(createMockAlert({ id: i, title: `Alert ${i}` }))\n        }\n      })\n\n      // Should only keep the latest 50 alerts\n      expect(result.current.alerts).toHaveLength(50)\n      expect(result.current.alerts[0].id).toBe(6) // First alert should be #6\n      expect(result.current.alerts[49].id).toBe(55) // Last alert should be #55\n    })\n  })\n\n  describe('location updates', () => {\n    it('should add location update', () => {\n      const mockLocationUpdate: UAVLocationUpdate = {\n        uavId: 1,\n        rfidTag: 'UAV-001',\n        latitude: 40.7128,\n        longitude: -74.0060,\n        altitude: 100,\n        timestamp: new Date().toISOString(),\n        speed: 25,\n        heading: 180,\n      }\n\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.addLocationUpdate(mockLocationUpdate)\n      })\n\n      expect(result.current.recentLocationUpdates).toHaveLength(1)\n      expect(result.current.recentLocationUpdates[0]).toEqual(mockLocationUpdate)\n    })\n\n    it('should limit location updates to maximum count', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add more than 100 location updates (assuming max is 100)\n      act(() => {\n        for (let i = 1; i <= 105; i++) {\n          result.current.addLocationUpdate({\n            uavId: i,\n            rfidTag: `UAV-${i.toString().padStart(3, '0')}`,\n            latitude: 40.7128 + i * 0.001,\n            longitude: -74.0060 + i * 0.001,\n            altitude: 100,\n            timestamp: new Date().toISOString(),\n            speed: 25,\n            heading: 180,\n          })\n        }\n      })\n\n      // Should only keep the latest 100 updates\n      expect(result.current.recentLocationUpdates).toHaveLength(100)\n      expect(result.current.recentLocationUpdates[0].uavId).toBe(6) // First should be #6\n      expect(result.current.recentLocationUpdates[99].uavId).toBe(105) // Last should be #105\n    })\n  })\n\n  describe('connection management', () => {\n    it('should set connection status', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.setConnectionStatus(true)\n      })\n\n      expect(result.current.isConnected).toBe(true)\n      expect(result.current.connectionError).toBeNull()\n\n      act(() => {\n        result.current.setConnectionStatus(false, 'Connection lost')\n      })\n\n      expect(result.current.isConnected).toBe(false)\n      expect(result.current.connectionError).toBe('Connection lost')\n    })\n\n    it('should clear connection error', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Set error first\n      act(() => {\n        result.current.setConnectionStatus(false, 'Connection error')\n      })\n\n      expect(result.current.connectionError).toBe('Connection error')\n\n      // Clear error\n      act(() => {\n        result.current.clearConnectionError()\n      })\n\n      expect(result.current.connectionError).toBeNull()\n    })\n  })\n\n  describe('UI settings', () => {\n    it('should set time range', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.setTimeRange('1h')\n      })\n\n      expect(result.current.selectedTimeRange).toBe('1h')\n\n      act(() => {\n        result.current.setTimeRange('7d')\n      })\n\n      expect(result.current.selectedTimeRange).toBe('7d')\n    })\n\n    it('should toggle auto refresh', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      expect(result.current.autoRefresh).toBe(true)\n\n      act(() => {\n        result.current.toggleAutoRefresh()\n      })\n\n      expect(result.current.autoRefresh).toBe(false)\n\n      act(() => {\n        result.current.toggleAutoRefresh()\n      })\n\n      expect(result.current.autoRefresh).toBe(true)\n    })\n\n    it('should set refresh interval', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      act(() => {\n        result.current.setRefreshInterval(60)\n      })\n\n      expect(result.current.refreshInterval).toBe(60)\n    })\n\n    it('should toggle alerts visibility', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      expect(result.current.showAlerts).toBe(true)\n\n      act(() => {\n        result.current.toggleAlerts()\n      })\n\n      expect(result.current.showAlerts).toBe(false)\n\n      act(() => {\n        result.current.toggleAlerts()\n      })\n\n      expect(result.current.showAlerts).toBe(true)\n    })\n  })\n\n  describe('data reset', () => {\n    it('should reset all data', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Set some data first\n      act(() => {\n        result.current.updateMetrics({\n          totalUAVs: 10,\n          authorizedUAVs: 8,\n          unauthorizedUAVs: 2,\n          activeFlights: 3,\n          hibernatingUAVs: 2,\n          lowBatteryCount: 1,\n          chargingCount: 2,\n          maintenanceCount: 1,\n          emergencyCount: 0,\n        })\n        result.current.addAlert(createMockAlert())\n        result.current.setConnectionStatus(true)\n      })\n\n      expect(result.current.metrics).toBeTruthy()\n      expect(result.current.alerts).toHaveLength(1)\n      expect(result.current.isConnected).toBe(true)\n\n      // Reset all data\n      act(() => {\n        result.current.resetData()\n      })\n\n      expect(result.current.metrics).toBeNull()\n      expect(result.current.flightActivity).toBeNull()\n      expect(result.current.batteryStats).toBeNull()\n      expect(result.current.hibernatePodMetrics).toBeNull()\n      expect(result.current.chartData).toBeNull()\n      expect(result.current.alerts).toHaveLength(0)\n      expect(result.current.recentLocationUpdates).toHaveLength(0)\n      expect(result.current.isConnected).toBe(false)\n      expect(result.current.lastUpdate).toBeNull()\n      expect(result.current.connectionError).toBeNull()\n    })\n  })\n\n  describe('computed values', () => {\n    it('should calculate alert counts correctly', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add different types of alerts\n      act(() => {\n        result.current.addAlert(createMockAlert({ id: 1, type: 'ERROR', severity: 'HIGH' }))\n        result.current.addAlert(createMockAlert({ id: 2, type: 'WARNING', severity: 'MEDIUM' }))\n        result.current.addAlert(createMockAlert({ id: 3, type: 'INFO', severity: 'LOW' }))\n        result.current.addAlert(createMockAlert({ id: 4, type: 'ERROR', severity: 'HIGH' }))\n      })\n\n      const alertCounts = result.current.getAlertCounts()\n\n      expect(alertCounts.total).toBe(4)\n      expect(alertCounts.error).toBe(2)\n      expect(alertCounts.warning).toBe(1)\n      expect(alertCounts.info).toBe(1)\n      expect(alertCounts.high).toBe(2)\n      expect(alertCounts.medium).toBe(1)\n      expect(alertCounts.low).toBe(1)\n    })\n\n    it('should get unacknowledged alerts', () => {\n      const { result } = renderHook(() => useDashboardStore())\n\n      // Add alerts with different acknowledgment status\n      act(() => {\n        result.current.addAlert(createMockAlert({ id: 1, acknowledged: false }))\n        result.current.addAlert(createMockAlert({ id: 2, acknowledged: true }))\n        result.current.addAlert(createMockAlert({ id: 3, acknowledged: false }))\n      })\n\n      const unacknowledgedAlerts = result.current.getUnacknowledgedAlerts()\n\n      expect(unacknowledgedAlerts).toHaveLength(2)\n      expect(unacknowledgedAlerts.map(a => a.id)).toEqual([1, 3])\n    })\n  })\n})\n"], "names": ["describe", "beforeEach", "useDashboardStore", "setState", "metrics", "flightActivity", "batteryStats", "hibernatePodMetrics", "chartData", "alerts", "recentLocationUpdates", "isConnected", "lastUpdate", "connectionError", "selectedTimeRange", "autoRefresh", "refreshInterval", "show<PERSON><PERSON><PERSON>", "jest", "clearAllMocks", "it", "mockMetrics", "totalUAVs", "authorizedUAVs", "unauthorizedUAVs", "activeFlights", "hibernatingUAVs", "lowBatteryCount", "chargingCount", "maintenanceCount", "emergencyCount", "result", "renderHook", "act", "current", "updateMetrics", "expect", "toEqual", "toBeTruthy", "mockFlightActivity", "todayFlights", "completedFlights", "flights", "id", "uavRfid", "missionName", "startTime", "status", "updateFlightActivity", "mockBatteryStats", "averageBatteryLevel", "criticalBatteryCount", "batteryDistribution", "updateBatteryStats", "<PERSON><PERSON><PERSON><PERSON>", "createMockAlert", "type", "title", "message", "add<PERSON><PERSON><PERSON>", "toHave<PERSON>ength", "mockAlert1", "mockAlert2", "<PERSON><PERSON><PERSON><PERSON>", "toBe", "acknowledged", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "i", "mockLocationUpdate", "uavId", "rfidTag", "latitude", "longitude", "altitude", "timestamp", "Date", "toISOString", "speed", "heading", "addLocationUpdate", "toString", "padStart", "setConnectionStatus", "toBeNull", "clearConnectionError", "setTimeRange", "toggleAutoRefresh", "setRefreshInterval", "to<PERSON><PERSON><PERSON><PERSON>", "resetData", "severity", "alertCounts", "getAlertCounts", "total", "error", "warning", "info", "high", "medium", "low", "unacknowledged<PERSON><PERSON><PERSON>", "getUnacknowledgedAlerts", "map", "a"], "mappings": ";;;;uBAAgC;gCACE;2BACa;AAI/CA,SAAS,mBAAmB;IAC1BC,WAAW;QACT,oBAAoB;QACpBC,iCAAiB,CAACC,QAAQ,CAAC;YACzBC,SAAS;YACTC,gBAAgB;YAChBC,cAAc;YACdC,qBAAqB;YACrBC,WAAW;YACXC,QAAQ,EAAE;YACVC,uBAAuB,EAAE;YACzBC,aAAa;YACbC,YAAY;YACZC,iBAAiB;YACjBC,mBAAmB;YACnBC,aAAa;YACbC,iBAAiB;YACjBC,YAAY;QACd;QAEA,kBAAkB;QAClBC,KAAKC,aAAa;IACpB;IAEAnB,SAAS,sBAAsB;QAC7BoB,GAAG,yBAAyB;YAC1B,MAAMC,cAAgC;gBACpCC,WAAW;gBACXC,gBAAgB;gBAChBC,kBAAkB;gBAClBC,eAAe;gBACfC,iBAAiB;gBACjBC,iBAAiB;gBACjBC,eAAe;gBACfC,kBAAkB;gBAClBC,gBAAgB;YAClB;YAEA,MAAM,EAAEC,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACC,aAAa,CAACd;YAC/B;YAEAe,OAAOL,OAAOG,OAAO,CAAC9B,OAAO,EAAEiC,OAAO,CAAChB;YACvCe,OAAOL,OAAOG,OAAO,CAACtB,UAAU,EAAE0B,UAAU;QAC9C;QAEAlB,GAAG,iCAAiC;YAClC,MAAMmB,qBAAqC;gBACzCd,eAAe;gBACfe,cAAc;gBACdC,kBAAkB;gBAClBC,SAAS;oBACP;wBACEC,IAAI;wBACJC,SAAS;wBACTC,aAAa;wBACbC,WAAW;wBACXC,QAAQ;oBACV;iBACD;YACH;YAEA,MAAM,EAAEhB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACc,oBAAoB,CAACT;YACtC;YAEAH,OAAOL,OAAOG,OAAO,CAAC7B,cAAc,EAAEgC,OAAO,CAACE;QAChD;QAEAnB,GAAG,oCAAoC;YACrC,MAAM6B,mBAAsC;gBAC1CC,qBAAqB;gBACrBvB,iBAAiB;gBACjBC,eAAe;gBACfuB,sBAAsB;gBACtBC,qBAAqB;oBACnB,QAAQ;oBACR,SAAS;oBACT,SAAS;oBACT,UAAU;gBACZ;YACF;YAEA,MAAM,EAAErB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACmB,kBAAkB,CAACJ;YACpC;YAEAb,OAAOL,OAAOG,OAAO,CAAC5B,YAAY,EAAE+B,OAAO,CAACY;QAC9C;IACF;IAEAjD,SAAS,qBAAqB;QAC5BoB,GAAG,oBAAoB;YACrB,MAAMkC,YAAYC,IAAAA,0BAAe,EAAC;gBAChCZ,IAAI;gBACJa,MAAM;gBACNC,OAAO;gBACPC,SAAS;YACX;YAEA,MAAM,EAAE3B,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACyB,QAAQ,CAACL;YAC1B;YAEAlB,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEmD,YAAY,CAAC;YAC3CxB,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,EAAE,EAAE4B,OAAO,CAACiB;QAC3C;QAEAlC,GAAG,uBAAuB;YACxB,MAAMyC,aAAaN,IAAAA,0BAAe,EAAC;gBAAEZ,IAAI;gBAAGc,OAAO;YAAU;YAC7D,MAAMK,aAAaP,IAAAA,0BAAe,EAAC;gBAAEZ,IAAI;gBAAGc,OAAO;YAAU;YAE7D,MAAM,EAAE1B,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,aAAa;YACb+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACyB,QAAQ,CAACE;gBACxB9B,OAAOG,OAAO,CAACyB,QAAQ,CAACG;YAC1B;YAEA1B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEmD,YAAY,CAAC;YAE3C,mBAAmB;YACnB3B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAAC6B,WAAW,CAAC;YAC7B;YAEA3B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEmD,YAAY,CAAC;YAC3CxB,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,EAAE,CAACkC,EAAE,EAAEqB,IAAI,CAAC;QAC3C;QAEA5C,GAAG,4BAA4B;YAC7B,MAAMkC,YAAYC,IAAAA,0BAAe,EAAC;gBAChCZ,IAAI;gBACJsB,cAAc;YAChB;YAEA,MAAM,EAAElC,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,YAAY;YACZ+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACyB,QAAQ,CAACL;YAC1B;YAEAlB,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,EAAE,CAACwD,YAAY,EAAED,IAAI,CAAC;YAEnD,oBAAoB;YACpB/B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACgC,gBAAgB,CAAC;YAClC;YAEA9B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,EAAE,CAACwD,YAAY,EAAED,IAAI,CAAC;QACrD;QAEA5C,GAAG,2BAA2B;YAC5B,MAAMyC,aAAaN,IAAAA,0BAAe,EAAC;gBAAEZ,IAAI;YAAE;YAC3C,MAAMmB,aAAaP,IAAAA,0BAAe,EAAC;gBAAEZ,IAAI;YAAE;YAE3C,MAAM,EAAEZ,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,aAAa;YACb+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACyB,QAAQ,CAACE;gBACxB9B,OAAOG,OAAO,CAACyB,QAAQ,CAACG;YAC1B;YAEA1B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEmD,YAAY,CAAC;YAE3C,mBAAmB;YACnB3B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACiC,WAAW;YAC5B;YAEA/B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEmD,YAAY,CAAC;QAC7C;QAEAxC,GAAG,wCAAwC;YACzC,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,+CAA+C;YAC/C+B,IAAAA,UAAG,EAAC;gBACF,IAAK,IAAImC,IAAI,GAAGA,KAAK,IAAIA,IAAK;oBAC5BrC,OAAOG,OAAO,CAACyB,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;wBAAEZ,IAAIyB;wBAAGX,OAAO,CAAC,MAAM,EAAEW,EAAE,CAAC;oBAAC;gBACvE;YACF;YAEA,wCAAwC;YACxChC,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEmD,YAAY,CAAC;YAC3CxB,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,EAAE,CAACkC,EAAE,EAAEqB,IAAI,CAAC,GAAG,2BAA2B;;YACvE5B,OAAOL,OAAOG,OAAO,CAACzB,MAAM,CAAC,GAAG,CAACkC,EAAE,EAAEqB,IAAI,CAAC,IAAI,2BAA2B;;QAC3E;IACF;IAEAhE,SAAS,oBAAoB;QAC3BoB,GAAG,8BAA8B;YAC/B,MAAMiD,qBAAwC;gBAC5CC,OAAO;gBACPC,SAAS;gBACTC,UAAU;gBACVC,WAAW,CAAC;gBACZC,UAAU;gBACVC,WAAW,IAAIC,OAAOC,WAAW;gBACjCC,OAAO;gBACPC,SAAS;YACX;YAEA,MAAM,EAAEhD,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAAC8C,iBAAiB,CAACX;YACnC;YAEAjC,OAAOL,OAAOG,OAAO,CAACxB,qBAAqB,EAAEkD,YAAY,CAAC;YAC1DxB,OAAOL,OAAOG,OAAO,CAACxB,qBAAqB,CAAC,EAAE,EAAE2B,OAAO,CAACgC;QAC1D;QAEAjD,GAAG,kDAAkD;YACnD,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,2DAA2D;YAC3D+B,IAAAA,UAAG,EAAC;gBACF,IAAK,IAAImC,IAAI,GAAGA,KAAK,KAAKA,IAAK;oBAC7BrC,OAAOG,OAAO,CAAC8C,iBAAiB,CAAC;wBAC/BV,OAAOF;wBACPG,SAAS,CAAC,IAAI,EAAEH,EAAEa,QAAQ,GAAGC,QAAQ,CAAC,GAAG,KAAK,CAAC;wBAC/CV,UAAU,UAAUJ,IAAI;wBACxBK,WAAW,CAAC,UAAUL,IAAI;wBAC1BM,UAAU;wBACVC,WAAW,IAAIC,OAAOC,WAAW;wBACjCC,OAAO;wBACPC,SAAS;oBACX;gBACF;YACF;YAEA,0CAA0C;YAC1C3C,OAAOL,OAAOG,OAAO,CAACxB,qBAAqB,EAAEkD,YAAY,CAAC;YAC1DxB,OAAOL,OAAOG,OAAO,CAACxB,qBAAqB,CAAC,EAAE,CAAC4D,KAAK,EAAEN,IAAI,CAAC,GAAG,qBAAqB;;YACnF5B,OAAOL,OAAOG,OAAO,CAACxB,qBAAqB,CAAC,GAAG,CAAC4D,KAAK,EAAEN,IAAI,CAAC,KAAK,sBAAsB;;QACzF;IACF;IAEAhE,SAAS,yBAAyB;QAChCoB,GAAG,gCAAgC;YACjC,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACiD,mBAAmB,CAAC;YACrC;YAEA/C,OAAOL,OAAOG,OAAO,CAACvB,WAAW,EAAEqD,IAAI,CAAC;YACxC5B,OAAOL,OAAOG,OAAO,CAACrB,eAAe,EAAEuE,QAAQ;YAE/CnD,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACiD,mBAAmB,CAAC,OAAO;YAC5C;YAEA/C,OAAOL,OAAOG,OAAO,CAACvB,WAAW,EAAEqD,IAAI,CAAC;YACxC5B,OAAOL,OAAOG,OAAO,CAACrB,eAAe,EAAEmD,IAAI,CAAC;QAC9C;QAEA5C,GAAG,iCAAiC;YAClC,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,kBAAkB;YAClB+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACiD,mBAAmB,CAAC,OAAO;YAC5C;YAEA/C,OAAOL,OAAOG,OAAO,CAACrB,eAAe,EAAEmD,IAAI,CAAC;YAE5C,cAAc;YACd/B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACmD,oBAAoB;YACrC;YAEAjD,OAAOL,OAAOG,OAAO,CAACrB,eAAe,EAAEuE,QAAQ;QACjD;IACF;IAEApF,SAAS,eAAe;QACtBoB,GAAG,yBAAyB;YAC1B,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACoD,YAAY,CAAC;YAC9B;YAEAlD,OAAOL,OAAOG,OAAO,CAACpB,iBAAiB,EAAEkD,IAAI,CAAC;YAE9C/B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACoD,YAAY,CAAC;YAC9B;YAEAlD,OAAOL,OAAOG,OAAO,CAACpB,iBAAiB,EAAEkD,IAAI,CAAC;QAChD;QAEA5C,GAAG,8BAA8B;YAC/B,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErDkC,OAAOL,OAAOG,OAAO,CAACnB,WAAW,EAAEiD,IAAI,CAAC;YAExC/B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACqD,iBAAiB;YAClC;YAEAnD,OAAOL,OAAOG,OAAO,CAACnB,WAAW,EAAEiD,IAAI,CAAC;YAExC/B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACqD,iBAAiB;YAClC;YAEAnD,OAAOL,OAAOG,OAAO,CAACnB,WAAW,EAAEiD,IAAI,CAAC;QAC1C;QAEA5C,GAAG,+BAA+B;YAChC,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACsD,kBAAkB,CAAC;YACpC;YAEApD,OAAOL,OAAOG,OAAO,CAAClB,eAAe,EAAEgD,IAAI,CAAC;QAC9C;QAEA5C,GAAG,mCAAmC;YACpC,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErDkC,OAAOL,OAAOG,OAAO,CAACjB,UAAU,EAAE+C,IAAI,CAAC;YAEvC/B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACuD,YAAY;YAC7B;YAEArD,OAAOL,OAAOG,OAAO,CAACjB,UAAU,EAAE+C,IAAI,CAAC;YAEvC/B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACuD,YAAY;YAC7B;YAEArD,OAAOL,OAAOG,OAAO,CAACjB,UAAU,EAAE+C,IAAI,CAAC;QACzC;IACF;IAEAhE,SAAS,cAAc;QACrBoB,GAAG,yBAAyB;YAC1B,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,sBAAsB;YACtB+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACC,aAAa,CAAC;oBAC3Bb,WAAW;oBACXC,gBAAgB;oBAChBC,kBAAkB;oBAClBC,eAAe;oBACfC,iBAAiB;oBACjBC,iBAAiB;oBACjBC,eAAe;oBACfC,kBAAkB;oBAClBC,gBAAgB;gBAClB;gBACAC,OAAOG,OAAO,CAACyB,QAAQ,CAACJ,IAAAA,0BAAe;gBACvCxB,OAAOG,OAAO,CAACiD,mBAAmB,CAAC;YACrC;YAEA/C,OAAOL,OAAOG,OAAO,CAAC9B,OAAO,EAAEkC,UAAU;YACzCF,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEmD,YAAY,CAAC;YAC3CxB,OAAOL,OAAOG,OAAO,CAACvB,WAAW,EAAEqD,IAAI,CAAC;YAExC,iBAAiB;YACjB/B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACwD,SAAS;YAC1B;YAEAtD,OAAOL,OAAOG,OAAO,CAAC9B,OAAO,EAAEgF,QAAQ;YACvChD,OAAOL,OAAOG,OAAO,CAAC7B,cAAc,EAAE+E,QAAQ;YAC9ChD,OAAOL,OAAOG,OAAO,CAAC5B,YAAY,EAAE8E,QAAQ;YAC5ChD,OAAOL,OAAOG,OAAO,CAAC3B,mBAAmB,EAAE6E,QAAQ;YACnDhD,OAAOL,OAAOG,OAAO,CAAC1B,SAAS,EAAE4E,QAAQ;YACzChD,OAAOL,OAAOG,OAAO,CAACzB,MAAM,EAAEmD,YAAY,CAAC;YAC3CxB,OAAOL,OAAOG,OAAO,CAACxB,qBAAqB,EAAEkD,YAAY,CAAC;YAC1DxB,OAAOL,OAAOG,OAAO,CAACvB,WAAW,EAAEqD,IAAI,CAAC;YACxC5B,OAAOL,OAAOG,OAAO,CAACtB,UAAU,EAAEwE,QAAQ;YAC1ChD,OAAOL,OAAOG,OAAO,CAACrB,eAAe,EAAEuE,QAAQ;QACjD;IACF;IAEApF,SAAS,mBAAmB;QAC1BoB,GAAG,2CAA2C;YAC5C,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,gCAAgC;YAChC+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACyB,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEZ,IAAI;oBAAGa,MAAM;oBAASmC,UAAU;gBAAO;gBACjF5D,OAAOG,OAAO,CAACyB,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEZ,IAAI;oBAAGa,MAAM;oBAAWmC,UAAU;gBAAS;gBACrF5D,OAAOG,OAAO,CAACyB,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEZ,IAAI;oBAAGa,MAAM;oBAAQmC,UAAU;gBAAM;gBAC/E5D,OAAOG,OAAO,CAACyB,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEZ,IAAI;oBAAGa,MAAM;oBAASmC,UAAU;gBAAO;YACnF;YAEA,MAAMC,cAAc7D,OAAOG,OAAO,CAAC2D,cAAc;YAEjDzD,OAAOwD,YAAYE,KAAK,EAAE9B,IAAI,CAAC;YAC/B5B,OAAOwD,YAAYG,KAAK,EAAE/B,IAAI,CAAC;YAC/B5B,OAAOwD,YAAYI,OAAO,EAAEhC,IAAI,CAAC;YACjC5B,OAAOwD,YAAYK,IAAI,EAAEjC,IAAI,CAAC;YAC9B5B,OAAOwD,YAAYM,IAAI,EAAElC,IAAI,CAAC;YAC9B5B,OAAOwD,YAAYO,MAAM,EAAEnC,IAAI,CAAC;YAChC5B,OAAOwD,YAAYQ,GAAG,EAAEpC,IAAI,CAAC;QAC/B;QAEA5C,GAAG,oCAAoC;YACrC,MAAM,EAAEW,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAAM9B,IAAAA,iCAAiB;YAErD,kDAAkD;YAClD+B,IAAAA,UAAG,EAAC;gBACFF,OAAOG,OAAO,CAACyB,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEZ,IAAI;oBAAGsB,cAAc;gBAAM;gBACrElC,OAAOG,OAAO,CAACyB,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEZ,IAAI;oBAAGsB,cAAc;gBAAK;gBACpElC,OAAOG,OAAO,CAACyB,QAAQ,CAACJ,IAAAA,0BAAe,EAAC;oBAAEZ,IAAI;oBAAGsB,cAAc;gBAAM;YACvE;YAEA,MAAMoC,uBAAuBtE,OAAOG,OAAO,CAACoE,uBAAuB;YAEnElE,OAAOiE,sBAAsBzC,YAAY,CAAC;YAC1CxB,OAAOiE,qBAAqBE,GAAG,CAACC,CAAAA,IAAKA,EAAE7D,EAAE,GAAGN,OAAO,CAAC;gBAAC;gBAAG;aAAE;QAC5D;IACF;AACF"}