96058d9948a88e153165c349c2a4ba9b
"use client";
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "Header", {
    enumerable: true,
    get: function() {
        return Header;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _link = /*#__PURE__*/ _interop_require_default(require("next/link"));
const _navigation = require("next/navigation");
const _lucidereact = require("lucide-react");
const _button = require("../ui/button");
const _input = require("../ui/input");
const _badge = require("../ui/badge");
const _dropdownmenu = require("../ui/dropdown-menu");
const _sheet = require("../ui/sheet");
const _mainnav = require("./main-nav");
const _dashboardstore = require("../../stores/dashboard-store");
const _utils = require("../../lib/utils");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
function Header({ onMenuClick }) {
    const pathname = (0, _navigation.usePathname)();
    const [searchQuery, setSearchQuery] = (0, _react.useState)("");
    const [isDarkMode, setIsDarkMode] = (0, _react.useState)(false);
    const [mobileMenuOpen, setMobileMenuOpen] = (0, _react.useState)(false);
    const connectionStatus = (0, _dashboardstore.useConnectionStatus)();
    const unacknowledgedAlerts = (0, _dashboardstore.useUnacknowledgedAlerts)();
    const handleSearch = (e)=>{
        e.preventDefault();
        if (searchQuery.trim()) {
            // Navigate to search results or filter current page
            console.log("Searching for:", searchQuery);
        }
    };
    const toggleDarkMode = ()=>{
        setIsDarkMode(!isDarkMode);
        document.documentElement.classList.toggle("dark");
    };
    const getPageTitle = ()=>{
        const pathSegments = pathname.split("/").filter(Boolean);
        if (pathSegments.length === 0) return "Dashboard";
        const pageMap = {
            dashboard: "Dashboard",
            uavs: "UAV Management",
            map: "Map View",
            "hibernate-pod": "Hibernate Pod",
            "docking-stations": "Docking Stations",
            battery: "Battery Monitor",
            "flight-logs": "Flight Logs",
            analytics: "Analytics",
            alerts: "Alerts",
            regions: "Regions",
            users: "Users",
            settings: "Settings"
        };
        return pageMap[pathSegments[0]] || "UAV Management System";
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("header", {
        className: "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            className: "container flex h-16 items-center justify-between px-4",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "flex items-center space-x-4",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(_sheet.Sheet, {
                            open: mobileMenuOpen,
                            onOpenChange: setMobileMenuOpen,
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_sheet.SheetTrigger, {
                                    asChild: true,
                                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_button.Button, {
                                        variant: "ghost",
                                        size: "icon",
                                        className: "md:hidden",
                                        onClick: ()=>setMobileMenuOpen(true),
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Menu, {
                                                className: "h-5 w-5"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                className: "sr-only",
                                                children: "Toggle menu"
                                            })
                                        ]
                                    })
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_sheet.SheetContent, {
                                    side: "left",
                                    className: "w-80",
                                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "flex flex-col h-full",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-center space-x-2 pb-4 border-b",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Shield, {
                                                        className: "h-6 w-6 text-primary"
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                        className: "font-orbitron font-bold text-lg",
                                                        children: "UAV Control"
                                                    })
                                                ]
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                className: "flex-1 py-4",
                                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {
                                                    onNavigate: ()=>setMobileMenuOpen(false)
                                                })
                                            })
                                        ]
                                    })
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(_link.default, {
                            href: "/dashboard",
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Shield, {
                                    className: "h-8 w-8 text-primary"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "hidden sm:block",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                                            className: "font-orbitron font-bold text-xl text-primary",
                                            children: "UAV Control"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-xs text-muted-foreground",
                                            children: "Management System"
                                        })
                                    ]
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "hidden lg:block",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                                className: "text-lg font-semibold text-foreground",
                                children: getPageTitle()
                            })
                        })
                    ]
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "flex-1 max-w-md mx-4",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("form", {
                        onSubmit: handleSearch,
                        className: "relative",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Search, {
                                className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_input.Input, {
                                type: "search",
                                placeholder: "Search UAVs, regions, or logs...",
                                value: searchQuery,
                                onChange: (e)=>setSearchQuery(e.target.value),
                                className: "pl-10 pr-4"
                            })
                        ]
                    })
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "flex items-center space-x-2",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "hidden sm:flex items-center space-x-2",
                            "data-testid": "connection-status",
                            children: connectionStatus.isConnected ? /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center space-x-1 text-green-600",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wifi, {
                                        className: "h-4 w-4"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                        className: "text-xs font-medium",
                                        children: "Connected"
                                    })
                                ]
                            }) : /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center space-x-1 text-red-600",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.WifiOff, {
                                        className: "h-4 w-4"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                        className: "text-xs font-medium",
                                        children: "Disconnected"
                                    })
                                ]
                            })
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(_button.Button, {
                            variant: "ghost",
                            size: "icon",
                            className: "relative",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Activity, {
                                    className: (0, _utils.cn)("h-5 w-5", connectionStatus.isConnected ? "text-green-600" : "text-red-600")
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                    className: "sr-only",
                                    children: "System status"
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(_dropdownmenu.DropdownMenu, {
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuTrigger, {
                                    asChild: true,
                                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_button.Button, {
                                        variant: "ghost",
                                        size: "icon",
                                        className: "relative",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Bell, {
                                                className: "h-5 w-5"
                                            }),
                                            unacknowledgedAlerts.length > 0 && /*#__PURE__*/ (0, _jsxruntime.jsx)(_badge.Badge, {
                                                variant: "destructive",
                                                className: "absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs",
                                                children: unacknowledgedAlerts.length > 9 ? "9+" : unacknowledgedAlerts.length
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                className: "sr-only",
                                                children: "Notifications"
                                            })
                                        ]
                                    })
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(_dropdownmenu.DropdownMenuContent, {
                                    align: "end",
                                    className: "w-80",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuLabel, {
                                            children: "Notifications"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuSeparator, {}),
                                        unacknowledgedAlerts.length > 0 ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {
                                            children: [
                                                unacknowledgedAlerts.slice(0, 5).map((alert)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)(_dropdownmenu.DropdownMenuItem, {
                                                        className: "flex flex-col items-start p-3",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                                className: "flex items-center space-x-2 w-full",
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_badge.Badge, {
                                                                        variant: alert.type === "CRITICAL" ? "destructive" : "secondary",
                                                                        children: alert.type
                                                                    }),
                                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                                        className: "text-xs text-muted-foreground",
                                                                        children: new Date(alert.timestamp).toLocaleTimeString()
                                                                    })
                                                                ]
                                                            }),
                                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                                className: "text-sm font-medium mt-1",
                                                                children: alert.title
                                                            }),
                                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                                className: "text-xs text-muted-foreground mt-1",
                                                                children: alert.message
                                                            })
                                                        ]
                                                    }, alert.id)),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuSeparator, {}),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuItem, {
                                                    asChild: true,
                                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_link.default, {
                                                        href: "/alerts",
                                                        className: "w-full text-center",
                                                        children: "View all alerts"
                                                    })
                                                })
                                            ]
                                        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuItem, {
                                            disabled: true,
                                            children: "No new notifications"
                                        })
                                    ]
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(_button.Button, {
                            variant: "ghost",
                            size: "icon",
                            onClick: toggleDarkMode,
                            children: [
                                isDarkMode ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Sun, {
                                    className: "h-5 w-5"
                                }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Moon, {
                                    className: "h-5 w-5"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                    className: "sr-only",
                                    children: "Toggle dark mode"
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(_dropdownmenu.DropdownMenu, {
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuTrigger, {
                                    asChild: true,
                                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_button.Button, {
                                        variant: "ghost",
                                        size: "icon",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.User, {
                                                className: "h-5 w-5"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                className: "sr-only",
                                                children: "User menu"
                                            })
                                        ]
                                    })
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(_dropdownmenu.DropdownMenuContent, {
                                    align: "end",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuLabel, {
                                            children: "My Account"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuSeparator, {}),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuItem, {
                                            asChild: true,
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_link.default, {
                                                href: "/profile",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.User, {
                                                        className: "mr-2 h-4 w-4"
                                                    }),
                                                    "Profile"
                                                ]
                                            })
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuItem, {
                                            asChild: true,
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_link.default, {
                                                href: "/settings",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Settings, {
                                                        className: "mr-2 h-4 w-4"
                                                    }),
                                                    "Settings"
                                                ]
                                            })
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_dropdownmenu.DropdownMenuSeparator, {}),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(_dropdownmenu.DropdownMenuItem, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.LogOut, {
                                                    className: "mr-2 h-4 w-4"
                                                }),
                                                "Log out"
                                            ]
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            ]
        })
    });
}

//# sourceMappingURL=data:application/json;base64,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