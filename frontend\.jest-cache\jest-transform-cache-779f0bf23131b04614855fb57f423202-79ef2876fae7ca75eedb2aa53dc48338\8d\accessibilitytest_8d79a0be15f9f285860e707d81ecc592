656300cdf0beec64f6ccf534b82626e8
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../lib/test-utils");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Test components for accessibility
const AccessibleButton = ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
        type: "button",
        "aria-label": props["aria-label"],
        "aria-describedby": props["aria-describedby"],
        disabled: props.disabled,
        ...props,
        children: children
    });
const AccessibleForm = ()=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("form", {
        role: "form",
        "aria-label": "UAV Registration Form",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                        htmlFor: "uav-name",
                        children: "UAV Name"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                        id: "uav-name",
                        type: "text",
                        required: true,
                        "aria-describedby": "uav-name-help",
                        "aria-invalid": "false"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        id: "uav-name-help",
                        children: "Enter a unique name for your UAV"
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                        htmlFor: "uav-type",
                        children: "UAV Type"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("select", {
                        id: "uav-type",
                        required: true,
                        "aria-describedby": "uav-type-help",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                value: "",
                                children: "Select type"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                value: "quadcopter",
                                children: "Quadcopter"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                value: "fixed-wing",
                                children: "Fixed Wing"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        id: "uav-type-help",
                        children: "Choose the type of UAV"
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                type: "submit",
                children: "Register UAV"
            })
        ]
    });
const AccessibleModal = ({ isOpen, onClose })=>{
    _react.default.useEffect(()=>{
        if (isOpen) {
            document.body.style.overflow = "hidden";
        } else {
            document.body.style.overflow = "unset";
        }
        return ()=>{
            document.body.style.overflow = "unset";
        };
    }, [
        isOpen
    ]);
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        role: "dialog",
        "aria-modal": "true",
        "aria-labelledby": "modal-title",
        "aria-describedby": "modal-description",
        "data-testid": "modal",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                onClick: onClose,
                "data-testid": "modal-backdrop"
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                        id: "modal-title",
                        children: "UAV Details"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                        id: "modal-description",
                        children: "View and edit UAV information"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        onClick: onClose,
                        "aria-label": "Close modal",
                        children: "\xd7"
                    })
                ]
            })
        ]
    });
};
const AccessibleDataTable = ()=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("table", {
        role: "table",
        "aria-label": "UAV Fleet Status",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("caption", {
                children: "Current status of all UAVs in the fleet"
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("thead", {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("tr", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("th", {
                            scope: "col",
                            children: "UAV ID"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("th", {
                            scope: "col",
                            children: "Status"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("th", {
                            scope: "col",
                            children: "Battery"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("th", {
                            scope: "col",
                            children: "Actions"
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("tbody", {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("tr", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("td", {
                            children: "UAV-001"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("td", {
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                "aria-label": "Active status",
                                children: "\uD83D\uDFE2 Active"
                            })
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("td", {
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                role: "progressbar",
                                "aria-label": "Battery level",
                                "aria-valuenow": 85,
                                "aria-valuemin": 0,
                                "aria-valuemax": 100,
                                children: "85%"
                            })
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("td", {
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                    "aria-label": "View details for UAV-001",
                                    children: "View"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                    "aria-label": "Edit UAV-001",
                                    children: "Edit"
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
describe("Accessibility Tests", ()=>{
    describe("ARIA Attributes", ()=>{
        it("provides proper ARIA labels", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleButton, {
                "aria-label": "Close dialog",
                children: "\xd7"
            }));
            const button = _testutils.screen.getByRole("button");
            expect(button).toHaveAttribute("aria-label", "Close dialog");
        });
        it("uses aria-describedby correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleButton, {
                        "aria-describedby": "help-text",
                        children: "Submit"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        id: "help-text",
                        children: "This will submit the form"
                    })
                ]
            }));
            const button = _testutils.screen.getByRole("button");
            (0, _testutils.checkAriaAttributes)(button, {
                "aria-describedby": "help-text"
            });
        });
        it("handles aria-expanded for collapsible content", ()=>{
            const CollapsibleComponent = ()=>{
                const [isExpanded, setIsExpanded] = _react.default.useState(false);
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            "aria-expanded": isExpanded,
                            "aria-controls": "collapsible-content",
                            onClick: ()=>setIsExpanded(!isExpanded),
                            children: "Toggle Content"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            id: "collapsible-content",
                            hidden: !isExpanded,
                            children: "Collapsible content"
                        })
                    ]
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(CollapsibleComponent, {}));
            const button = _testutils.screen.getByRole("button");
            expect(button).toHaveAttribute("aria-expanded", "false");
            _testutils.fireEvent.click(button);
            expect(button).toHaveAttribute("aria-expanded", "true");
        });
        it("uses aria-invalid for form validation", ()=>{
            const ValidationForm = ()=>{
                const [error, setError] = _react.default.useState("");
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                            "aria-invalid": !!error,
                            "aria-describedby": error ? "error-message" : undefined,
                            onChange: (e)=>{
                                if (e.target.value.length < 3) {
                                    setError("Name must be at least 3 characters");
                                } else {
                                    setError("");
                                }
                            }
                        }),
                        error && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            id: "error-message",
                            role: "alert",
                            children: error
                        })
                    ]
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(ValidationForm, {}));
            const input = _testutils.screen.getByRole("textbox");
            expect(input).toHaveAttribute("aria-invalid", "false");
            _testutils.fireEvent.change(input, {
                target: {
                    value: "ab"
                }
            });
            expect(input).toHaveAttribute("aria-invalid", "true");
            expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
        });
    });
    describe("Keyboard Navigation", ()=>{
        it("supports tab navigation", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        children: "First"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        children: "Second"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                        type: "text"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        children: "Third"
                    })
                ]
            }));
            const buttons = _testutils.screen.getAllByRole("button");
            const input = _testutils.screen.getByRole("textbox");
            // Test tab order
            buttons[0].focus();
            expect(buttons[0]).toHaveFocus();
            _testutils.fireEvent.keyDown(buttons[0], {
                key: "Tab"
            });
            buttons[1].focus();
            expect(buttons[1]).toHaveFocus();
            _testutils.fireEvent.keyDown(buttons[1], {
                key: "Tab"
            });
            input.focus();
            expect(input).toHaveFocus();
        });
        it("handles Enter and Space key activation", ()=>{
            const handleClick = jest.fn();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleButton, {
                onClick: handleClick,
                children: "Activate"
            }));
            const button = _testutils.screen.getByRole("button");
            _testutils.fireEvent.keyDown(button, {
                key: "Enter"
            });
            expect(handleClick).toHaveBeenCalledTimes(1);
            _testutils.fireEvent.keyDown(button, {
                key: " "
            });
            expect(handleClick).toHaveBeenCalledTimes(2);
        });
        it("handles Escape key for modals", ()=>{
            const handleClose = jest.fn();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleModal, {
                isOpen: true,
                onClose: handleClose
            }));
            _testutils.fireEvent.keyDown(document, {
                key: "Escape"
            });
            expect(handleClose).toHaveBeenCalledTimes(1);
        });
        it("traps focus in modals", ()=>{
            const FocusTrapModal = ({ isOpen })=>{
                if (!isOpen) return null;
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    role: "dialog",
                    "aria-modal": "true",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            children: "First"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                            type: "text"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            children: "Last"
                        })
                    ]
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(FocusTrapModal, {
                isOpen: true
            }));
            const buttons = _testutils.screen.getAllByRole("button");
            const input = _testutils.screen.getByRole("textbox");
            // Focus should be trapped within modal
            buttons[0].focus();
            expect(buttons[0]).toHaveFocus();
        });
    });
    describe("Screen Reader Support", ()=>{
        it("provides proper headings hierarchy", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                        children: "Main Title"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                        children: "Section Title"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                        children: "Subsection Title"
                    })
                ]
            }));
            expect(_testutils.screen.getByRole("heading", {
                level: 1
            })).toBeInTheDocument();
            expect(_testutils.screen.getByRole("heading", {
                level: 2
            })).toBeInTheDocument();
            expect(_testutils.screen.getByRole("heading", {
                level: 3
            })).toBeInTheDocument();
        });
        it("uses landmarks correctly", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("header", {
                        children: "Site Header"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("nav", {
                        "aria-label": "Main navigation",
                        children: "Navigation"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("main", {
                        children: "Main Content"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("aside", {
                        children: "Sidebar"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("footer", {
                        children: "Site Footer"
                    })
                ]
            }));
            expect(_testutils.screen.getByRole("banner")).toBeInTheDocument();
            expect(_testutils.screen.getByRole("navigation")).toBeInTheDocument();
            expect(_testutils.screen.getByRole("main")).toBeInTheDocument();
            expect(_testutils.screen.getByRole("complementary")).toBeInTheDocument();
            expect(_testutils.screen.getByRole("contentinfo")).toBeInTheDocument();
        });
        it("provides live regions for dynamic content", ()=>{
            const LiveRegionComponent = ()=>{
                const [message, setMessage] = _react.default.useState("");
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            onClick: ()=>setMessage("UAV status updated"),
                            children: "Update Status"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            "aria-live": "polite",
                            "aria-atomic": "true",
                            children: message
                        })
                    ]
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(LiveRegionComponent, {}));
            const button = _testutils.screen.getByRole("button");
            const liveRegion = _testutils.screen.getByText("").parentElement;
            expect(liveRegion).toHaveAttribute("aria-live", "polite");
            _testutils.fireEvent.click(button);
            expect(_testutils.screen.getByText("UAV status updated")).toBeInTheDocument();
        });
        it("uses proper table structure", async ()=>{
            const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleDataTable, {}));
            expect(_testutils.screen.getByRole("table")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Current status of all UAVs in the fleet")).toBeInTheDocument();
            const headers = _testutils.screen.getAllByRole("columnheader");
            expect(headers).toHaveLength(4);
            const progressbar = _testutils.screen.getByRole("progressbar");
            expect(progressbar).toHaveAttribute("aria-valuenow", "85");
            await (0, _testutils.runAxeTest)(container);
        });
    });
    describe("Form Accessibility", ()=>{
        it("associates labels with form controls", async ()=>{
            const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleForm, {}));
            const nameInput = _testutils.screen.getByLabelText("UAV Name");
            const typeSelect = _testutils.screen.getByLabelText("UAV Type");
            expect(nameInput).toHaveAttribute("id", "uav-name");
            expect(typeSelect).toHaveAttribute("id", "uav-type");
            await (0, _testutils.runAxeTest)(container);
        });
        it("provides helpful error messages", ()=>{
            const ErrorForm = ()=>{
                const [errors, setErrors] = _react.default.useState({});
                const validate = ()=>{
                    const newErrors = {};
                    const nameInput = document.getElementById("name");
                    if (!nameInput.value) {
                        newErrors.name = "Name is required";
                    }
                    setErrors(newErrors);
                };
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("form", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("label", {
                            htmlFor: "name",
                            children: "Name"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                            id: "name",
                            "aria-invalid": !!errors.name,
                            "aria-describedby": errors.name ? "name-error" : undefined
                        }),
                        errors.name && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            id: "name-error",
                            role: "alert",
                            children: errors.name
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            type: "button",
                            onClick: validate,
                            children: "Validate"
                        })
                    ]
                });
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(ErrorForm, {}));
            const validateButton = _testutils.screen.getByText("Validate");
            _testutils.fireEvent.click(validateButton);
            expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
            expect(_testutils.screen.getByText("Name is required")).toBeInTheDocument();
        });
        it("handles required field indicators", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("label", {
                        htmlFor: "required-field",
                        children: [
                            "Required Field ",
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                "aria-label": "required",
                                children: "*"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                        id: "required-field",
                        required: true
                    })
                ]
            }));
            const input = _testutils.screen.getByRole("textbox");
            expect(input).toHaveAttribute("required");
        });
    });
    describe("Color and Contrast", ()=>{
        it("does not rely solely on color for information", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                        style: {
                            color: "red"
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                "aria-label": "Error",
                                children: "⚠️"
                            }),
                            " Error message"
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                        style: {
                            color: "green"
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                "aria-label": "Success",
                                children: "✅"
                            }),
                            " Success message"
                        ]
                    })
                ]
            }));
            // Icons provide additional context beyond color
            expect(_testutils.screen.getByLabelText("Error")).toBeInTheDocument();
            expect(_testutils.screen.getByLabelText("Success")).toBeInTheDocument();
        });
        it("provides sufficient contrast ratios", ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        style: {
                            backgroundColor: "#007bff",
                            color: "#ffffff"
                        },
                        children: "High Contrast Button"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                        style: {
                            color: "#333333",
                            backgroundColor: "#ffffff"
                        },
                        children: "High contrast text"
                    })
                ]
            }));
            // These would pass WCAG contrast requirements
            expect(_testutils.screen.getByRole("button")).toBeInTheDocument();
            expect(_testutils.screen.getByText("High contrast text")).toBeInTheDocument();
        });
    });
    describe("Comprehensive Accessibility Tests", ()=>{
        it("passes axe accessibility tests for complex components", async ()=>{
            const ComplexComponent = ()=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleForm, {}),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleDataTable, {}),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleModal, {
                            isOpen: false,
                            onClose: ()=>{}
                        })
                    ]
                });
            const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(ComplexComponent, {}));
            await (0, _testutils.runAxeTest)(container);
        });
        it("maintains accessibility during state changes", async ()=>{
            const StatefulComponent = ()=>{
                const [isModalOpen, setIsModalOpen] = _react.default.useState(false);
                return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            onClick: ()=>setIsModalOpen(true),
                            children: "Open Modal"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessibleModal, {
                            isOpen: isModalOpen,
                            onClose: ()=>setIsModalOpen(false)
                        })
                    ]
                });
            };
            const { container } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(StatefulComponent, {}));
            // Test initial state
            await (0, _testutils.runAxeTest)(container);
            // Open modal and test again
            _testutils.fireEvent.click(_testutils.screen.getByText("Open Modal"));
            await (0, _testutils.runAxeTest)(container);
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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