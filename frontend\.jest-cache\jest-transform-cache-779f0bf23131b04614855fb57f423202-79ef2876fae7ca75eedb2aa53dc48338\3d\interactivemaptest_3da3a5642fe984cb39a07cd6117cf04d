36500e7d537fbc1673e7298ada90a838
"use strict";
// Mock react-leaflet components (already mocked in jest.setup.js)
// Mock framer-motion
jest.mock("framer-motion", ()=>({
        motion: {
            div: ({ children, ...props })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    ...props,
                    children: children
                })
        },
        AnimatePresence: ({ children })=>children
    }));
// Mock the animated map components
jest.mock("../animated-map-components", ()=>({
        AnimatedUAVMarker: ({ uav, onSelect })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `uav-marker-${uav.id}`,
                onClick: ()=>onSelect(uav),
                children: [
                    "UAV Marker: ",
                    uav.rfidTag
                ]
            }),
        AnimatedGeofence: ({ name, center, radius, color, type })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `geofence-${name}`,
                children: [
                    "Geofence: ",
                    name
                ]
            }),
        AnimatedFlightPath: ({ path })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                "data-testid": "flight-path",
                children: "Flight Path"
            }),
        AnimatedDockingStation: ({ name, position, status, capacity, occupied })=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                "data-testid": `docking-station-${name}`,
                children: [
                    "Docking Station: ",
                    name
                ]
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../../lib/test-utils");
const _interactivemap = /*#__PURE__*/ _interop_require_default(require("../interactive-map"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
describe("InteractiveMap Component", ()=>{
    const mockUAVs = [
        (0, _testutils.createMockUAV)({
            id: 1,
            rfidTag: "UAV-001",
            status: "AUTHORIZED",
            operationalStatus: "ACTIVE",
            location: {
                latitude: 40.7128,
                longitude: -74.0060
            }
        }),
        (0, _testutils.createMockUAV)({
            id: 2,
            rfidTag: "UAV-002",
            status: "AUTHORIZED",
            operationalStatus: "READY",
            location: {
                latitude: 40.7589,
                longitude: -73.9851
            }
        })
    ];
    const mockDockingStations = [
        (0, _testutils.createMockDockingStation)({
            id: 1,
            name: "Station Alpha",
            location: {
                latitude: 40.7505,
                longitude: -73.9934
            },
            status: "AVAILABLE"
        })
    ];
    const mockRegions = [
        {
            id: 1,
            name: "Zone A",
            description: "Authorized zone A",
            coordinates: [
                {
                    latitude: 40.7000,
                    longitude: -74.0200
                },
                {
                    latitude: 40.7200,
                    longitude: -74.0200
                },
                {
                    latitude: 40.7200,
                    longitude: -73.9800
                },
                {
                    latitude: 40.7000,
                    longitude: -73.9800
                }
            ],
            isActive: true
        }
    ];
    const defaultProps = {
        uavs: mockUAVs,
        selectedUAV: null,
        center: [
            40.7128,
            -74.0060
        ],
        zoom: 12,
        layers: {
            uavs: true,
            geofences: true,
            dockingStations: true,
            flightPaths: true,
            weather: false
        },
        onUAVSelect: jest.fn()
    };
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("tile-layer")).toBeInTheDocument();
    });
    it("renders UAV markers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("uav-marker-2")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Marker: UAV-001")).toBeInTheDocument();
        expect(_testutils.screen.getByText("UAV Marker: UAV-002")).toBeInTheDocument();
    });
    it("renders docking station markers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("docking-station-1")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Docking Station: Station Alpha")).toBeInTheDocument();
    });
    it("renders geofences for regions", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        expect(_testutils.screen.getByTestId("geofence-1")).toBeInTheDocument();
        expect(_testutils.screen.getByText("Geofence: Zone A")).toBeInTheDocument();
    });
    it("handles UAV selection", ()=>{
        const onUAVSelect = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onUAVSelect: onUAVSelect
        }));
        const uavMarker = _testutils.screen.getByTestId("uav-marker-1");
        _testutils.fireEvent.click(uavMarker);
        expect(onUAVSelect).toHaveBeenCalledWith(mockUAVs[0]);
    });
    it("handles docking station selection", ()=>{
        const onStationSelect = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onStationSelect: onStationSelect
        }));
        const stationMarker = _testutils.screen.getByTestId("docking-station-1");
        _testutils.fireEvent.click(stationMarker);
        expect(onStationSelect).toHaveBeenCalledWith(mockDockingStations[0]);
    });
    it("highlights selected UAV", ()=>{
        const selectedUAV = mockUAVs[0];
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            selectedUAV: selectedUAV
        }));
        const selectedMarker = _testutils.screen.getByTestId("uav-marker-1");
        expect(selectedMarker).toBeInTheDocument();
    // The selected state would be passed to the AnimatedUAVMarker component
    });
    it("shows flight paths when enabled", ()=>{
        const flightPaths = [
            {
                id: 1,
                uavId: 1,
                coordinates: [
                    {
                        latitude: 40.7128,
                        longitude: -74.0060
                    },
                    {
                        latitude: 40.7589,
                        longitude: -73.9851
                    }
                ],
                timestamp: new Date().toISOString()
            }
        ];
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            flightPaths: flightPaths,
            showFlightPaths: true
        }));
        expect(_testutils.screen.getByTestId("flight-path")).toBeInTheDocument();
    });
    it("filters UAVs by status", ()=>{
        const filteredProps = {
            ...defaultProps,
            uavs: mockUAVs.filter((uav)=>uav.operationalStatus === "ACTIVE")
        };
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...filteredProps
        }));
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-2")).not.toBeInTheDocument();
    });
    it("updates map center when prop changes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        const newCenter = {
            latitude: 41.8781,
            longitude: -87.6298
        };
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            center: newCenter
        }));
        // Map center update would be handled by the MapContainer component
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    });
    it("updates zoom level when prop changes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            zoom: 15
        }));
        // Zoom update would be handled by the MapContainer component
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    });
    it("handles empty UAV list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-1")).not.toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("uav-marker-2")).not.toBeInTheDocument();
    });
    it("handles empty docking stations list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            dockingStations: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("docking-station-1")).not.toBeInTheDocument();
    });
    it("handles empty regions list", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            regions: []
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
        expect(_testutils.screen.queryByTestId("geofence-1")).not.toBeInTheDocument();
    });
    it("shows loading state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            loading: true
        }));
        expect(_testutils.screen.getByTestId("map-loading")).toBeInTheDocument();
    });
    it("shows error state", ()=>{
        const errorMessage = "Failed to load map data";
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            error: errorMessage
        }));
        expect(_testutils.screen.getByText(errorMessage)).toBeInTheDocument();
        expect(_testutils.screen.getByRole("alert")).toBeInTheDocument();
    });
    it("supports different map layers", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            mapLayer: "satellite"
        }));
        // Different tile layer would be rendered
        expect(_testutils.screen.getByTestId("tile-layer")).toBeInTheDocument();
    });
    it("handles real-time updates", async ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        const updatedUAVs = [
            ...mockUAVs,
            (0, _testutils.createMockUAV)({
                id: 3,
                rfidTag: "UAV-003",
                location: {
                    latitude: 40.7300,
                    longitude: -74.0000
                }
            })
        ];
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: updatedUAVs
        }));
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByTestId("uav-marker-3")).toBeInTheDocument();
        });
    });
    it("handles UAV location updates", ()=>{
        const updatedUAVs = mockUAVs.map((uav)=>uav.id === 1 ? {
                ...uav,
                location: {
                    latitude: 40.7200,
                    longitude: -74.0100
                }
            } : uav);
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: updatedUAVs
        }));
        // Updated location would be reflected in the marker position
        expect(_testutils.screen.getByTestId("uav-marker-1")).toBeInTheDocument();
    });
    it("supports clustering for many UAVs", ()=>{
        const manyUAVs = Array.from({
            length: 50
        }, (_, i)=>(0, _testutils.createMockUAV)({
                id: i + 1,
                rfidTag: `UAV-${(i + 1).toString().padStart(3, "0")}`,
                location: {
                    latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
                    longitude: -74.0060 + (Math.random() - 0.5) * 0.1
                }
            }));
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            uavs: manyUAVs,
            enableClustering: true
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    // Clustering would be handled by the map library
    });
    it("handles map interaction events", ()=>{
        const onMapClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            onMapClick: onMapClick
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        _testutils.fireEvent.click(mapContainer);
        // Map click would be handled by the MapContainer component
        expect(mapContainer).toBeInTheDocument();
    });
    it("supports custom map controls", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            showZoomControl: true,
            showScaleControl: true,
            showFullscreenControl: true
        }));
        expect(_testutils.screen.getByTestId("map-container")).toBeInTheDocument();
    // Custom controls would be rendered as part of the map
    });
    it("handles responsive design", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            className: "h-96 w-full"
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        expect(mapContainer.parentElement).toHaveClass("h-96", "w-full");
    });
    it("supports accessibility features", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_interactivemap.default, {
            ...defaultProps,
            "aria-label": "UAV tracking map",
            role: "application"
        }));
        const mapContainer = _testutils.screen.getByTestId("map-container");
        expect(mapContainer).toHaveAttribute("aria-label", "UAV tracking map");
        expect(mapContainer).toHaveAttribute("role", "application");
    });
});

//# sourceMappingURL=data:application/json;base64,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