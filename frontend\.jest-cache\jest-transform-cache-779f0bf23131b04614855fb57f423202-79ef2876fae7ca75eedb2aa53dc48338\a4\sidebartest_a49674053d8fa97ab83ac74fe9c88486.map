{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\layout\\__tests__\\sidebar.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent } from '@/lib/test-utils'\nimport { Sidebar } from '../sidebar'\nimport { mockFramerMotion, runAxeTest } from '@/lib/test-utils'\n\n// Mock the main nav component\njest.mock('../main-nav', () => ({\n  MainNav: () => (\n    <div data-testid=\"main-nav\">\n      <a href=\"/dashboard\">Dashboard</a>\n      <a href=\"/uavs\">UAVs</a>\n    </div>\n  ),\n}))\n\n// Mock animations\njest.mock('@/lib/animations', () => ({\n  sidebarVariants: {\n    expanded: { width: 280 },\n    collapsed: { width: 80 },\n  },\n  getAnimationVariants: jest.fn((variants) => variants),\n}))\n\n// Mock framer-motion\nmockFramerMotion()\n\ndescribe('Sidebar Component', () => {\n  const mockOnToggle = jest.fn()\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n  })\n\n  it('renders correctly', () => {\n    render(<Sidebar />)\n\n    expect(screen.getByTestId('main-nav')).toBeInTheDocument()\n    expect(screen.getByText('UAV Control')).toBeInTheDocument()\n    expect(screen.getByText('Management System')).toBeInTheDocument()\n  })\n\n  it('displays logo and branding when expanded', () => {\n    render(<Sidebar collapsed={false} />)\n\n    expect(screen.getByText('UAV Control')).toBeInTheDocument()\n    expect(screen.getByText('Management System')).toBeInTheDocument()\n  })\n\n  it('hides text when collapsed', () => {\n    render(<Sidebar collapsed={true} />)\n\n    // Text should be hidden when collapsed\n    const brandingText = screen.queryByText('UAV Control')\n    expect(brandingText).not.toBeInTheDocument()\n  })\n\n  it('shows toggle button', () => {\n    render(<Sidebar onToggle={mockOnToggle} />)\n\n    const toggleButton = screen.getByRole('button', { name: /toggle sidebar/i })\n    expect(toggleButton).toBeInTheDocument()\n  })\n\n  it('handles toggle button click', () => {\n    render(<Sidebar onToggle={mockOnToggle} />)\n\n    const toggleButton = screen.getByRole('button', { name: /toggle sidebar/i })\n    fireEvent.click(toggleButton)\n\n    expect(mockOnToggle).toHaveBeenCalledTimes(1)\n  })\n\n  it('shows correct toggle icon when expanded', () => {\n    render(<Sidebar collapsed={false} onToggle={mockOnToggle} />)\n\n    const toggleButton = screen.getByRole('button', { name: /toggle sidebar/i })\n    // Should show ChevronLeft icon when expanded\n    expect(toggleButton).toBeInTheDocument()\n  })\n\n  it('shows correct toggle icon when collapsed', () => {\n    render(<Sidebar collapsed={true} onToggle={mockOnToggle} />)\n\n    const toggleButton = screen.getByRole('button', { name: /toggle sidebar/i })\n    // Should show ChevronRight icon when collapsed\n    expect(toggleButton).toBeInTheDocument()\n  })\n\n  it('applies custom className', () => {\n    render(<Sidebar className=\"custom-sidebar\" />)\n\n    const sidebar = screen.getByTestId('main-nav').closest('div')\n    expect(sidebar).toHaveClass('custom-sidebar')\n  })\n\n  it('renders navigation component', () => {\n    render(<Sidebar />)\n\n    expect(screen.getByTestId('main-nav')).toBeInTheDocument()\n    expect(screen.getByText('Dashboard')).toBeInTheDocument()\n    expect(screen.getByText('UAVs')).toBeInTheDocument()\n  })\n\n  it('displays version information when expanded', () => {\n    render(<Sidebar collapsed={false} />)\n\n    expect(screen.getByText('Version 1.0.0')).toBeInTheDocument()\n    expect(screen.getByText('© 2024 UAV Systems')).toBeInTheDocument()\n  })\n\n  it('hides version information when collapsed', () => {\n    render(<Sidebar collapsed={true} />)\n\n    expect(screen.queryByText('Version 1.0.0')).not.toBeInTheDocument()\n    expect(screen.queryByText('© 2024 UAV Systems')).not.toBeInTheDocument()\n  })\n\n  it('has proper layout structure', () => {\n    render(<Sidebar />)\n\n    const sidebar = screen.getByTestId('main-nav').closest('div')\n    expect(sidebar).toHaveClass('flex', 'flex-col', 'h-full', 'bg-card', 'border-r')\n  })\n\n  it('handles animation states', () => {\n    const { rerender } = render(<Sidebar collapsed={false} />)\n\n    // Should be in expanded state\n    let sidebar = screen.getByTestId('main-nav').closest('div')\n    expect(sidebar).toBeInTheDocument()\n\n    // Change to collapsed\n    rerender(<Sidebar collapsed={true} />)\n    sidebar = screen.getByTestId('main-nav').closest('div')\n    expect(sidebar).toBeInTheDocument()\n  })\n\n  it('maintains accessibility standards', async () => {\n    const { container } = render(<Sidebar onToggle={mockOnToggle} />)\n\n    // Check for proper button accessibility\n    const toggleButton = screen.getByRole('button', { name: /toggle sidebar/i })\n    expect(toggleButton).toBeInTheDocument()\n\n    // Check for navigation accessibility\n    expect(screen.getByTestId('main-nav')).toBeInTheDocument()\n\n    // Run accessibility tests\n    await runAxeTest(container)\n  })\n\n  it('supports keyboard navigation', () => {\n    render(<Sidebar onToggle={mockOnToggle} />)\n\n    const toggleButton = screen.getByRole('button', { name: /toggle sidebar/i })\n    \n    toggleButton.focus()\n    expect(toggleButton).toHaveFocus()\n\n    // Test Enter key\n    fireEvent.keyDown(toggleButton, { key: 'Enter' })\n    expect(mockOnToggle).toHaveBeenCalledTimes(1)\n\n    // Test Space key\n    fireEvent.keyDown(toggleButton, { key: ' ' })\n    expect(mockOnToggle).toHaveBeenCalledTimes(2)\n  })\n\n  it('handles missing onToggle prop gracefully', () => {\n    render(<Sidebar />)\n\n    const toggleButton = screen.getByRole('button', { name: /toggle sidebar/i })\n    \n    // Should not throw error when clicked without onToggle\n    expect(() => {\n      fireEvent.click(toggleButton)\n    }).not.toThrow()\n  })\n\n  it('adjusts padding based on collapsed state', () => {\n    const { rerender } = render(<Sidebar collapsed={false} />)\n\n    let navContainer = screen.getByTestId('main-nav').parentElement\n    expect(navContainer).toHaveClass('px-3')\n\n    rerender(<Sidebar collapsed={true} />)\n    navContainer = screen.getByTestId('main-nav').parentElement\n    expect(navContainer).toHaveClass('px-2')\n  })\n\n  it('shows logo icon consistently', () => {\n    const { rerender } = render(<Sidebar collapsed={false} />)\n\n    // Logo should be present when expanded\n    expect(screen.getByTestId('shield-icon')).toBeInTheDocument()\n\n    rerender(<Sidebar collapsed={true} />)\n\n    // Logo should still be present when collapsed\n    expect(screen.getByTestId('shield-icon')).toBeInTheDocument()\n  })\n\n  it('handles responsive behavior', () => {\n    render(<Sidebar />)\n\n    const sidebar = screen.getByTestId('main-nav').closest('div')\n    \n    // Should have proper responsive classes\n    expect(sidebar).toHaveClass('flex', 'flex-col', 'h-full')\n  })\n\n  it('maintains proper z-index and positioning', () => {\n    render(<Sidebar />)\n\n    const sidebar = screen.getByTestId('main-nav').closest('div')\n    \n    // Should have proper background and border\n    expect(sidebar).toHaveClass('bg-card', 'border-r')\n  })\n\n  it('handles animation variants correctly', () => {\n    const { rerender } = render(<Sidebar collapsed={false} />)\n\n    // Should use expanded variant\n    let sidebar = screen.getByTestId('main-nav').closest('div')\n    expect(sidebar).toBeInTheDocument()\n\n    rerender(<Sidebar collapsed={true} />)\n\n    // Should use collapsed variant\n    sidebar = screen.getByTestId('main-nav').closest('div')\n    expect(sidebar).toBeInTheDocument()\n  })\n\n  it('provides proper semantic structure', () => {\n    render(<Sidebar />)\n\n    // Should have proper navigation structure\n    const nav = screen.getByTestId('main-nav')\n    expect(nav).toBeInTheDocument()\n\n    // Should have header section with logo\n    expect(screen.getByText('UAV Control')).toBeInTheDocument()\n\n    // Should have footer section with version info\n    expect(screen.getByText('Version 1.0.0')).toBeInTheDocument()\n  })\n})\n"], "names": ["jest", "mock", "MainNav", "div", "data-testid", "a", "href", "sidebarVariants", "expanded", "width", "collapsed", "getAnimationVariants", "fn", "variants", "mockFramerMotion", "describe", "mockOnToggle", "beforeEach", "clearAllMocks", "it", "render", "Sidebar", "expect", "screen", "getByTestId", "toBeInTheDocument", "getByText", "brandingText", "queryByText", "not", "onToggle", "to<PERSON><PERSON><PERSON><PERSON>", "getByRole", "name", "fireEvent", "click", "toHaveBeenCalledTimes", "className", "sidebar", "closest", "toHaveClass", "rerender", "container", "runAxeTest", "focus", "toHaveFocus", "keyDown", "key", "toThrow", "navContainer", "parentElement", "nav"], "mappings": ";AAKA,8BAA8B;AAC9BA,KAAKC,IAAI,CAAC,eAAe,IAAO,CAAA;QAC9BC,SAAS,kBACP,sBAACC;gBAAIC,eAAY;;kCACf,qBAACC;wBAAEC,MAAK;kCAAa;;kCACrB,qBAACD;wBAAEC,MAAK;kCAAQ;;;;IAGtB,CAAA;AAEA,kBAAkB;AAClBN,KAAKC,IAAI,CAAC,oBAAoB,IAAO,CAAA;QACnCM,iBAAiB;YACfC,UAAU;gBAAEC,OAAO;YAAI;YACvBC,WAAW;gBAAED,OAAO;YAAG;QACzB;QACAE,sBAAsBX,KAAKY,EAAE,CAAC,CAACC,WAAaA;IAC9C,CAAA;;;;;8DAtBkB;2BACwB;yBAClB;;;;;;AAsBxB,qBAAqB;AACrBC,IAAAA,2BAAgB;AAEhBC,SAAS,qBAAqB;IAC5B,MAAMC,eAAehB,KAAKY,EAAE;IAE5BK,WAAW;QACTjB,KAAKkB,aAAa;IACpB;IAEAC,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEfC,OAAOC,iBAAM,CAACC,WAAW,CAAC,aAAaC,iBAAiB;QACxDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;QACzDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,sBAAsBD,iBAAiB;IACjE;IAEAN,GAAG,4CAA4C;QAC7CC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACX,WAAW;;QAE3BY,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;QACzDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,sBAAsBD,iBAAiB;IACjE;IAEAN,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACX,WAAW;;QAE3B,uCAAuC;QACvC,MAAMiB,eAAeJ,iBAAM,CAACK,WAAW,CAAC;QACxCN,OAAOK,cAAcE,GAAG,CAACJ,iBAAiB;IAC5C;IAEAN,GAAG,uBAAuB;QACxBC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACS,UAAUd;;QAE1B,MAAMe,eAAeR,iBAAM,CAACS,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAkB;QAC1EX,OAAOS,cAAcN,iBAAiB;IACxC;IAEAN,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACS,UAAUd;;QAE1B,MAAMe,eAAeR,iBAAM,CAACS,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAkB;QAC1EC,oBAAS,CAACC,KAAK,CAACJ;QAEhBT,OAAON,cAAcoB,qBAAqB,CAAC;IAC7C;IAEAjB,GAAG,2CAA2C;QAC5CC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACX,WAAW;YAAOoB,UAAUd;;QAE5C,MAAMe,eAAeR,iBAAM,CAACS,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAkB;QAC1E,6CAA6C;QAC7CX,OAAOS,cAAcN,iBAAiB;IACxC;IAEAN,GAAG,4CAA4C;QAC7CC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACX,WAAW;YAAMoB,UAAUd;;QAE3C,MAAMe,eAAeR,iBAAM,CAACS,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAkB;QAC1E,+CAA+C;QAC/CX,OAAOS,cAAcN,iBAAiB;IACxC;IAEAN,GAAG,4BAA4B;QAC7BC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACgB,WAAU;;QAE1B,MAAMC,UAAUf,iBAAM,CAACC,WAAW,CAAC,YAAYe,OAAO,CAAC;QACvDjB,OAAOgB,SAASE,WAAW,CAAC;IAC9B;IAEArB,GAAG,gCAAgC;QACjCC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEfC,OAAOC,iBAAM,CAACC,WAAW,CAAC,aAAaC,iBAAiB;QACxDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,cAAcD,iBAAiB;QACvDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,SAASD,iBAAiB;IACpD;IAEAN,GAAG,8CAA8C;QAC/CC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACX,WAAW;;QAE3BY,OAAOC,iBAAM,CAACG,SAAS,CAAC,kBAAkBD,iBAAiB;QAC3DH,OAAOC,iBAAM,CAACG,SAAS,CAAC,0BAAuBD,iBAAiB;IAClE;IAEAN,GAAG,4CAA4C;QAC7CC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACX,WAAW;;QAE3BY,OAAOC,iBAAM,CAACK,WAAW,CAAC,kBAAkBC,GAAG,CAACJ,iBAAiB;QACjEH,OAAOC,iBAAM,CAACK,WAAW,CAAC,0BAAuBC,GAAG,CAACJ,iBAAiB;IACxE;IAEAN,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEf,MAAMiB,UAAUf,iBAAM,CAACC,WAAW,CAAC,YAAYe,OAAO,CAAC;QACvDjB,OAAOgB,SAASE,WAAW,CAAC,QAAQ,YAAY,UAAU,WAAW;IACvE;IAEArB,GAAG,4BAA4B;QAC7B,MAAM,EAAEsB,QAAQ,EAAE,GAAGrB,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACX,WAAW;;QAEhD,8BAA8B;QAC9B,IAAI4B,UAAUf,iBAAM,CAACC,WAAW,CAAC,YAAYe,OAAO,CAAC;QACrDjB,OAAOgB,SAASb,iBAAiB;QAEjC,sBAAsB;QACtBgB,uBAAS,qBAACpB,gBAAO;YAACX,WAAW;;QAC7B4B,UAAUf,iBAAM,CAACC,WAAW,CAAC,YAAYe,OAAO,CAAC;QACjDjB,OAAOgB,SAASb,iBAAiB;IACnC;IAEAN,GAAG,qCAAqC;QACtC,MAAM,EAAEuB,SAAS,EAAE,GAAGtB,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACS,UAAUd;;QAEhD,wCAAwC;QACxC,MAAMe,eAAeR,iBAAM,CAACS,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAkB;QAC1EX,OAAOS,cAAcN,iBAAiB;QAEtC,qCAAqC;QACrCH,OAAOC,iBAAM,CAACC,WAAW,CAAC,aAAaC,iBAAiB;QAExD,0BAA0B;QAC1B,MAAMkB,IAAAA,qBAAU,EAACD;IACnB;IAEAvB,GAAG,gCAAgC;QACjCC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACS,UAAUd;;QAE1B,MAAMe,eAAeR,iBAAM,CAACS,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAkB;QAE1EF,aAAaa,KAAK;QAClBtB,OAAOS,cAAcc,WAAW;QAEhC,iBAAiB;QACjBX,oBAAS,CAACY,OAAO,CAACf,cAAc;YAAEgB,KAAK;QAAQ;QAC/CzB,OAAON,cAAcoB,qBAAqB,CAAC;QAE3C,iBAAiB;QACjBF,oBAAS,CAACY,OAAO,CAACf,cAAc;YAAEgB,KAAK;QAAI;QAC3CzB,OAAON,cAAcoB,qBAAqB,CAAC;IAC7C;IAEAjB,GAAG,4CAA4C;QAC7CC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEf,MAAMU,eAAeR,iBAAM,CAACS,SAAS,CAAC,UAAU;YAAEC,MAAM;QAAkB;QAE1E,uDAAuD;QACvDX,OAAO;YACLY,oBAAS,CAACC,KAAK,CAACJ;QAClB,GAAGF,GAAG,CAACmB,OAAO;IAChB;IAEA7B,GAAG,4CAA4C;QAC7C,MAAM,EAAEsB,QAAQ,EAAE,GAAGrB,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACX,WAAW;;QAEhD,IAAIuC,eAAe1B,iBAAM,CAACC,WAAW,CAAC,YAAY0B,aAAa;QAC/D5B,OAAO2B,cAAcT,WAAW,CAAC;QAEjCC,uBAAS,qBAACpB,gBAAO;YAACX,WAAW;;QAC7BuC,eAAe1B,iBAAM,CAACC,WAAW,CAAC,YAAY0B,aAAa;QAC3D5B,OAAO2B,cAAcT,WAAW,CAAC;IACnC;IAEArB,GAAG,gCAAgC;QACjC,MAAM,EAAEsB,QAAQ,EAAE,GAAGrB,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACX,WAAW;;QAEhD,uCAAuC;QACvCY,OAAOC,iBAAM,CAACC,WAAW,CAAC,gBAAgBC,iBAAiB;QAE3DgB,uBAAS,qBAACpB,gBAAO;YAACX,WAAW;;QAE7B,8CAA8C;QAC9CY,OAAOC,iBAAM,CAACC,WAAW,CAAC,gBAAgBC,iBAAiB;IAC7D;IAEAN,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEf,MAAMiB,UAAUf,iBAAM,CAACC,WAAW,CAAC,YAAYe,OAAO,CAAC;QAEvD,wCAAwC;QACxCjB,OAAOgB,SAASE,WAAW,CAAC,QAAQ,YAAY;IAClD;IAEArB,GAAG,4CAA4C;QAC7CC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEf,MAAMiB,UAAUf,iBAAM,CAACC,WAAW,CAAC,YAAYe,OAAO,CAAC;QAEvD,2CAA2C;QAC3CjB,OAAOgB,SAASE,WAAW,CAAC,WAAW;IACzC;IAEArB,GAAG,wCAAwC;QACzC,MAAM,EAAEsB,QAAQ,EAAE,GAAGrB,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;YAACX,WAAW;;QAEhD,8BAA8B;QAC9B,IAAI4B,UAAUf,iBAAM,CAACC,WAAW,CAAC,YAAYe,OAAO,CAAC;QACrDjB,OAAOgB,SAASb,iBAAiB;QAEjCgB,uBAAS,qBAACpB,gBAAO;YAACX,WAAW;;QAE7B,+BAA+B;QAC/B4B,UAAUf,iBAAM,CAACC,WAAW,CAAC,YAAYe,OAAO,CAAC;QACjDjB,OAAOgB,SAASb,iBAAiB;IACnC;IAEAN,GAAG,sCAAsC;QACvCC,IAAAA,iBAAM,gBAAC,qBAACC,gBAAO;QAEf,0CAA0C;QAC1C,MAAM8B,MAAM5B,iBAAM,CAACC,WAAW,CAAC;QAC/BF,OAAO6B,KAAK1B,iBAAiB;QAE7B,uCAAuC;QACvCH,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;QAEzD,+CAA+C;QAC/CH,OAAOC,iBAAM,CAACG,SAAS,CAAC,kBAAkBD,iBAAiB;IAC7D;AACF"}