{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": ["Label", "labelVariants", "cva", "React", "forwardRef", "className", "props", "ref", "LabelPrimitive", "Root", "cn", "displayName"], "mappings": ";;;;+BAuBSA;;;eAAAA;;;;+DAvBc;oEACS;wCACO;uBAEpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnB,MAAMC,gBAAgBC,IAAAA,2BAAG,EACvB;AAGF,MAAMF,sBAAQG,OAAMC,UAAU,CAI5B,CAAC,EAAEC,SAAS,EAAE,GAAGC,OAAO,EAAEC,oBAC1B,qBAACC,YAAeC,IAAI;QAClBF,KAAKA;QACLF,WAAWK,IAAAA,SAAE,EAACT,iBAAiBI;QAC9B,GAAGC,KAAK;;AAGbN,MAAMW,WAAW,GAAGH,YAAeC,IAAI,CAACE,WAAW"}