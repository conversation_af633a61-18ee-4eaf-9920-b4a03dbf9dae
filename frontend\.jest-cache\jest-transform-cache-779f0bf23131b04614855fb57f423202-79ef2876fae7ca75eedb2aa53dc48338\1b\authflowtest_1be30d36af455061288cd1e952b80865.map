{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\__tests__\\integration\\auth-flow.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent, waitFor } from '@/lib/test-utils'\nimport { useAuthStore } from '@/stores/auth-store'\nimport { authApi } from '@/api/auth-api'\nimport { createMockUser } from '@/lib/test-utils'\nimport { useRouter } from 'next/navigation'\n\n// Mock the API\njest.mock('@/api/auth-api')\nconst mockedAuthApi = authApi as jest.Mocked<typeof authApi>\n\n// Mock Next.js router\njest.mock('next/navigation', () => ({\n  useRouter: jest.fn(),\n}))\n\n// Mock react-hot-toast\njest.mock('react-hot-toast', () => ({\n  toast: {\n    success: jest.fn(),\n    error: jest.fn(),\n  },\n}))\n\n// Integration test component that uses auth flow\nconst AuthFlowTestComponent = () => {\n  const { user, login, logout, isLoading, error } = useAuthStore()\n  const [formData, setFormData] = React.useState({\n    username: '',\n    password: '',\n  })\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault()\n    await login({\n      username: formData.username,\n      password: formData.password,\n      rememberMe: false,\n    })\n  }\n\n  const handleLogout = async () => {\n    await logout()\n  }\n\n  if (user) {\n    return (\n      <div>\n        <h1>Welcome, {user.firstName}!</h1>\n        <p>Email: {user.email}</p>\n        <p>Role: {user.roles?.[0]?.name}</p>\n        <button onClick={handleLogout} disabled={isLoading}>\n          {isLoading ? 'Logging out...' : 'Logout'}\n        </button>\n      </div>\n    )\n  }\n\n  return (\n    <form onSubmit={handleLogin}>\n      <h1>Login</h1>\n      {error && <div role=\"alert\">{error}</div>}\n      \n      <div>\n        <label htmlFor=\"username\">Username</label>\n        <input\n          id=\"username\"\n          type=\"text\"\n          value={formData.username}\n          onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}\n          required\n        />\n      </div>\n      \n      <div>\n        <label htmlFor=\"password\">Password</label>\n        <input\n          id=\"password\"\n          type=\"password\"\n          value={formData.password}\n          onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}\n          required\n        />\n      </div>\n      \n      <button type=\"submit\" disabled={isLoading}>\n        {isLoading ? 'Logging in...' : 'Login'}\n      </button>\n    </form>\n  )\n}\n\ndescribe('Authentication Flow Integration Tests', () => {\n  const mockPush = jest.fn()\n\n  beforeEach(() => {\n    jest.clearAllMocks()\n    \n    // Reset auth store\n    useAuthStore.setState({\n      user: null,\n      token: null,\n      refreshToken: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n    })\n\n    // Mock router\n    ;(useRouter as jest.Mock).mockReturnValue({\n      push: mockPush,\n    })\n  })\n\n  it('completes successful login flow', async () => {\n    const mockUser = createMockUser({\n      firstName: 'John',\n      email: '<EMAIL>',\n      roles: [{ id: 1, name: 'ADMIN', description: 'Administrator', permissions: [] }],\n    })\n\n    const mockResponse = {\n      user: mockUser,\n      token: 'mock-token',\n      refreshToken: 'mock-refresh-token',\n      expiresIn: 3600,\n    }\n\n    mockedAuthApi.login.mockResolvedValue(mockResponse)\n\n    render(<AuthFlowTestComponent />)\n\n    // Initially shows login form\n    expect(screen.getByText('Login')).toBeInTheDocument()\n    expect(screen.getByLabelText('Username')).toBeInTheDocument()\n    expect(screen.getByLabelText('Password')).toBeInTheDocument()\n\n    // Fill in form\n    fireEvent.change(screen.getByLabelText('Username'), {\n      target: { value: '<EMAIL>' }\n    })\n    fireEvent.change(screen.getByLabelText('Password'), {\n      target: { value: 'password123' }\n    })\n\n    // Submit form\n    fireEvent.click(screen.getByRole('button', { name: 'Login' }))\n\n    // Should show loading state\n    expect(screen.getByText('Logging in...')).toBeInTheDocument()\n\n    // Wait for login to complete\n    await waitFor(() => {\n      expect(screen.getByText('Welcome, John!')).toBeInTheDocument()\n    })\n\n    // Should show user info\n    expect(screen.getByText('Email: <EMAIL>')).toBeInTheDocument()\n    expect(screen.getByText('Role: ADMIN')).toBeInTheDocument()\n    expect(screen.getByRole('button', { name: 'Logout' })).toBeInTheDocument()\n\n    // Verify API was called correctly\n    expect(mockedAuthApi.login).toHaveBeenCalledWith({\n      username: '<EMAIL>',\n      password: 'password123',\n      rememberMe: false,\n    })\n  })\n\n  it('handles login failure', async () => {\n    mockedAuthApi.login.mockRejectedValue(new Error('Invalid credentials'))\n\n    render(<AuthFlowTestComponent />)\n\n    // Fill in form with invalid credentials\n    fireEvent.change(screen.getByLabelText('Username'), {\n      target: { value: '<EMAIL>' }\n    })\n    fireEvent.change(screen.getByLabelText('Password'), {\n      target: { value: 'wrongpassword' }\n    })\n\n    // Submit form\n    fireEvent.click(screen.getByRole('button', { name: 'Login' }))\n\n    // Wait for error to appear\n    await waitFor(() => {\n      expect(screen.getByRole('alert')).toBeInTheDocument()\n      expect(screen.getByText('Invalid credentials')).toBeInTheDocument()\n    })\n\n    // Should still show login form\n    expect(screen.getByText('Login')).toBeInTheDocument()\n    expect(screen.getByRole('button', { name: 'Login' })).toBeInTheDocument()\n  })\n\n  it('completes logout flow', async () => {\n    const mockUser = createMockUser()\n\n    // Start with authenticated state\n    useAuthStore.setState({\n      user: mockUser,\n      token: 'mock-token',\n      refreshToken: 'mock-refresh-token',\n      isAuthenticated: true,\n      isLoading: false,\n      error: null,\n    })\n\n    mockedAuthApi.logout.mockResolvedValue()\n\n    render(<AuthFlowTestComponent />)\n\n    // Should show authenticated state\n    expect(screen.getByText(`Welcome, ${mockUser.firstName}!`)).toBeInTheDocument()\n\n    // Click logout\n    fireEvent.click(screen.getByRole('button', { name: 'Logout' }))\n\n    // Should show loading state\n    expect(screen.getByText('Logging out...')).toBeInTheDocument()\n\n    // Wait for logout to complete\n    await waitFor(() => {\n      expect(screen.getByText('Login')).toBeInTheDocument()\n    })\n\n    // Should show login form again\n    expect(screen.getByLabelText('Username')).toBeInTheDocument()\n    expect(screen.getByLabelText('Password')).toBeInTheDocument()\n\n    // Verify API was called\n    expect(mockedAuthApi.logout).toHaveBeenCalled()\n  })\n\n  it('handles network errors during login', async () => {\n    mockedAuthApi.login.mockRejectedValue(new Error('Network error'))\n\n    render(<AuthFlowTestComponent />)\n\n    // Fill in form\n    fireEvent.change(screen.getByLabelText('Username'), {\n      target: { value: '<EMAIL>' }\n    })\n    fireEvent.change(screen.getByLabelText('Password'), {\n      target: { value: 'password123' }\n    })\n\n    // Submit form\n    fireEvent.click(screen.getByRole('button', { name: 'Login' }))\n\n    // Wait for error\n    await waitFor(() => {\n      expect(screen.getByText('Network error')).toBeInTheDocument()\n    })\n\n    // Should remain on login form\n    expect(screen.getByText('Login')).toBeInTheDocument()\n  })\n\n  it('maintains loading state consistency', async () => {\n    let resolveLogin: (value: any) => void\n    const loginPromise = new Promise(resolve => {\n      resolveLogin = resolve\n    })\n\n    mockedAuthApi.login.mockReturnValue(loginPromise as any)\n\n    render(<AuthFlowTestComponent />)\n\n    // Fill in form\n    fireEvent.change(screen.getByLabelText('Username'), {\n      target: { value: '<EMAIL>' }\n    })\n    fireEvent.change(screen.getByLabelText('Password'), {\n      target: { value: 'password123' }\n    })\n\n    // Submit form\n    fireEvent.click(screen.getByRole('button', { name: 'Login' }))\n\n    // Should show loading immediately\n    expect(screen.getByText('Logging in...')).toBeInTheDocument()\n    expect(screen.getByRole('button')).toBeDisabled()\n\n    // Resolve login\n    resolveLogin!({\n      user: createMockUser(),\n      token: 'token',\n      refreshToken: 'refresh-token',\n      expiresIn: 3600,\n    })\n\n    // Wait for completion\n    await waitFor(() => {\n      expect(screen.queryByText('Logging in...')).not.toBeInTheDocument()\n    })\n  })\n\n  it('handles token refresh flow', async () => {\n    const mockUser = createMockUser()\n\n    // Start with authenticated state\n    useAuthStore.setState({\n      user: mockUser,\n      token: 'old-token',\n      refreshToken: 'refresh-token',\n      isAuthenticated: true,\n      isLoading: false,\n      error: null,\n    })\n\n    const mockRefreshResponse = {\n      token: 'new-token',\n      refreshToken: 'new-refresh-token',\n      expiresIn: 3600,\n    }\n\n    mockedAuthApi.refreshToken.mockResolvedValue(mockRefreshResponse)\n\n    render(<AuthFlowTestComponent />)\n\n    // Should show authenticated state\n    expect(screen.getByText(`Welcome, ${mockUser.firstName}!`)).toBeInTheDocument()\n\n    // Trigger token refresh (this would normally happen automatically)\n    const { refreshToken } = useAuthStore.getState()\n    await refreshToken()\n\n    // Should remain authenticated with new token\n    await waitFor(() => {\n      const state = useAuthStore.getState()\n      expect(state.token).toBe('new-token')\n      expect(state.refreshToken).toBe('new-refresh-token')\n      expect(state.isAuthenticated).toBe(true)\n    })\n  })\n\n  it('handles refresh token failure and logout', async () => {\n    const mockUser = createMockUser()\n\n    // Start with authenticated state\n    useAuthStore.setState({\n      user: mockUser,\n      token: 'old-token',\n      refreshToken: 'invalid-refresh-token',\n      isAuthenticated: true,\n      isLoading: false,\n      error: null,\n    })\n\n    mockedAuthApi.refreshToken.mockRejectedValue(new Error('Invalid refresh token'))\n\n    render(<AuthFlowTestComponent />)\n\n    // Should show authenticated state initially\n    expect(screen.getByText(`Welcome, ${mockUser.firstName}!`)).toBeInTheDocument()\n\n    // Trigger token refresh\n    const { refreshToken } = useAuthStore.getState()\n    await refreshToken()\n\n    // Should logout user when refresh fails\n    await waitFor(() => {\n      expect(screen.getByText('Login')).toBeInTheDocument()\n    })\n\n    // Should clear auth state\n    const state = useAuthStore.getState()\n    expect(state.user).toBeNull()\n    expect(state.token).toBeNull()\n    expect(state.isAuthenticated).toBe(false)\n  })\n})\n"], "names": ["jest", "mock", "useRouter", "fn", "toast", "success", "error", "mockedAuth<PERSON>pi", "authApi", "AuthFlowTestComponent", "user", "login", "logout", "isLoading", "useAuthStore", "formData", "setFormData", "React", "useState", "username", "password", "handleLogin", "e", "preventDefault", "rememberMe", "handleLogout", "div", "h1", "firstName", "p", "email", "roles", "name", "button", "onClick", "disabled", "form", "onSubmit", "role", "label", "htmlFor", "input", "id", "type", "value", "onChange", "prev", "target", "required", "describe", "mockPush", "beforeEach", "clearAllMocks", "setState", "token", "refreshToken", "isAuthenticated", "mockReturnValue", "push", "it", "mockUser", "createMockUser", "description", "permissions", "mockResponse", "expiresIn", "mockResolvedValue", "render", "expect", "screen", "getByText", "toBeInTheDocument", "getByLabelText", "fireEvent", "change", "click", "getByRole", "waitFor", "toHaveBeenCalledWith", "mockRejectedValue", "Error", "toHaveBeenCalled", "resolveLogin", "loginPromise", "Promise", "resolve", "toBeDisabled", "queryByText", "not", "mockRefreshResponse", "getState", "state", "toBe", "toBeNull"], "mappings": ";AAOA,eAAe;AACfA,KAAKC,IAAI,CAAC;AAGV,sBAAsB;AACtBD,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC,WAAWF,KAAKG,EAAE;IACpB,CAAA;AAEA,uBAAuB;AACvBH,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCG,OAAO;YACLC,SAASL,KAAKG,EAAE;YAChBG,OAAON,KAAKG,EAAE;QAChB;IACF,CAAA;;;;;8DAtBkB;2BACiC;2BACtB;yBACL;4BAEE;;;;;;AAI1B,MAAMI,gBAAgBC,gBAAO;AAe7B,iDAAiD;AACjD,MAAMC,wBAAwB;IAC5B,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEP,KAAK,EAAE,GAAGQ,IAAAA,uBAAY;IAC9D,MAAM,CAACC,UAAUC,YAAY,GAAGC,cAAK,CAACC,QAAQ,CAAC;QAC7CC,UAAU;QACVC,UAAU;IACZ;IAEA,MAAMC,cAAc,OAAOC;QACzBA,EAAEC,cAAc;QAChB,MAAMZ,MAAM;YACVQ,UAAUJ,SAASI,QAAQ;YAC3BC,UAAUL,SAASK,QAAQ;YAC3BI,YAAY;QACd;IACF;IAEA,MAAMC,eAAe;QACnB,MAAMb;IACR;IAEA,IAAIF,MAAM;QACR,qBACE,sBAACgB;;8BACC,sBAACC;;wBAAG;wBAAUjB,KAAKkB,SAAS;wBAAC;;;8BAC7B,sBAACC;;wBAAE;wBAAQnB,KAAKoB,KAAK;;;8BACrB,sBAACD;;wBAAE;wBAAOnB,KAAKqB,KAAK,EAAE,CAAC,EAAE,EAAEC;;;8BAC3B,qBAACC;oBAAOC,SAAST;oBAAcU,UAAUtB;8BACtCA,YAAY,mBAAmB;;;;IAIxC;IAEA,qBACE,sBAACuB;QAAKC,UAAUhB;;0BACd,qBAACM;0BAAG;;YACHrB,uBAAS,qBAACoB;gBAAIY,MAAK;0BAAShC;;0BAE7B,sBAACoB;;kCACC,qBAACa;wBAAMC,SAAQ;kCAAW;;kCAC1B,qBAACC;wBACCC,IAAG;wBACHC,MAAK;wBACLC,OAAO7B,SAASI,QAAQ;wBACxB0B,UAAU,CAACvB,IAAMN,YAAY8B,CAAAA,OAAS,CAAA;oCAAE,GAAGA,IAAI;oCAAE3B,UAAUG,EAAEyB,MAAM,CAACH,KAAK;gCAAC,CAAA;wBAC1EI,QAAQ;;;;0BAIZ,sBAACtB;;kCACC,qBAACa;wBAAMC,SAAQ;kCAAW;;kCAC1B,qBAACC;wBACCC,IAAG;wBACHC,MAAK;wBACLC,OAAO7B,SAASK,QAAQ;wBACxByB,UAAU,CAACvB,IAAMN,YAAY8B,CAAAA,OAAS,CAAA;oCAAE,GAAGA,IAAI;oCAAE1B,UAAUE,EAAEyB,MAAM,CAACH,KAAK;gCAAC,CAAA;wBAC1EI,QAAQ;;;;0BAIZ,qBAACf;gBAAOU,MAAK;gBAASR,UAAUtB;0BAC7BA,YAAY,kBAAkB;;;;AAIvC;AAEAoC,SAAS,yCAAyC;IAChD,MAAMC,WAAWlD,KAAKG,EAAE;IAExBgD,WAAW;QACTnD,KAAKoD,aAAa;QAElB,mBAAmB;QACnBtC,uBAAY,CAACuC,QAAQ,CAAC;YACpB3C,MAAM;YACN4C,OAAO;YACPC,cAAc;YACdC,iBAAiB;YACjB3C,WAAW;YACXP,OAAO;QACT;QAGEJ,qBAAS,CAAeuD,eAAe,CAAC;YACxCC,MAAMR;QACR;IACF;IAEAS,GAAG,mCAAmC;QACpC,MAAMC,WAAWC,IAAAA,yBAAc,EAAC;YAC9BjC,WAAW;YACXE,OAAO;YACPC,OAAO;gBAAC;oBAAEW,IAAI;oBAAGV,MAAM;oBAAS8B,aAAa;oBAAiBC,aAAa,EAAE;gBAAC;aAAE;QAClF;QAEA,MAAMC,eAAe;YACnBtD,MAAMkD;YACNN,OAAO;YACPC,cAAc;YACdU,WAAW;QACb;QAEA1D,cAAcI,KAAK,CAACuD,iBAAiB,CAACF;QAEtCG,IAAAA,iBAAM,gBAAC,qBAAC1D;QAER,6BAA6B;QAC7B2D,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;QACnDH,OAAOC,iBAAM,CAACG,cAAc,CAAC,aAAaD,iBAAiB;QAC3DH,OAAOC,iBAAM,CAACG,cAAc,CAAC,aAAaD,iBAAiB;QAE3D,eAAe;QACfE,oBAAS,CAACC,MAAM,CAACL,iBAAM,CAACG,cAAc,CAAC,aAAa;YAClDzB,QAAQ;gBAAEH,OAAO;YAAmB;QACtC;QACA6B,oBAAS,CAACC,MAAM,CAACL,iBAAM,CAACG,cAAc,CAAC,aAAa;YAClDzB,QAAQ;gBAAEH,OAAO;YAAc;QACjC;QAEA,cAAc;QACd6B,oBAAS,CAACE,KAAK,CAACN,iBAAM,CAACO,SAAS,CAAC,UAAU;YAAE5C,MAAM;QAAQ;QAE3D,4BAA4B;QAC5BoC,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAE3D,6BAA6B;QAC7B,MAAMM,IAAAA,kBAAO,EAAC;YACZT,OAAOC,iBAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;QAC9D;QAEA,wBAAwB;QACxBH,OAAOC,iBAAM,CAACC,SAAS,CAAC,4BAA4BC,iBAAiB;QACrEH,OAAOC,iBAAM,CAACC,SAAS,CAAC,gBAAgBC,iBAAiB;QACzDH,OAAOC,iBAAM,CAACO,SAAS,CAAC,UAAU;YAAE5C,MAAM;QAAS,IAAIuC,iBAAiB;QAExE,kCAAkC;QAClCH,OAAO7D,cAAcI,KAAK,EAAEmE,oBAAoB,CAAC;YAC/C3D,UAAU;YACVC,UAAU;YACVI,YAAY;QACd;IACF;IAEAmC,GAAG,yBAAyB;QAC1BpD,cAAcI,KAAK,CAACoE,iBAAiB,CAAC,IAAIC,MAAM;QAEhDb,IAAAA,iBAAM,gBAAC,qBAAC1D;QAER,wCAAwC;QACxCgE,oBAAS,CAACC,MAAM,CAACL,iBAAM,CAACG,cAAc,CAAC,aAAa;YAClDzB,QAAQ;gBAAEH,OAAO;YAAsB;QACzC;QACA6B,oBAAS,CAACC,MAAM,CAACL,iBAAM,CAACG,cAAc,CAAC,aAAa;YAClDzB,QAAQ;gBAAEH,OAAO;YAAgB;QACnC;QAEA,cAAc;QACd6B,oBAAS,CAACE,KAAK,CAACN,iBAAM,CAACO,SAAS,CAAC,UAAU;YAAE5C,MAAM;QAAQ;QAE3D,2BAA2B;QAC3B,MAAM6C,IAAAA,kBAAO,EAAC;YACZT,OAAOC,iBAAM,CAACO,SAAS,CAAC,UAAUL,iBAAiB;YACnDH,OAAOC,iBAAM,CAACC,SAAS,CAAC,wBAAwBC,iBAAiB;QACnE;QAEA,+BAA+B;QAC/BH,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;QACnDH,OAAOC,iBAAM,CAACO,SAAS,CAAC,UAAU;YAAE5C,MAAM;QAAQ,IAAIuC,iBAAiB;IACzE;IAEAZ,GAAG,yBAAyB;QAC1B,MAAMC,WAAWC,IAAAA,yBAAc;QAE/B,iCAAiC;QACjC/C,uBAAY,CAACuC,QAAQ,CAAC;YACpB3C,MAAMkD;YACNN,OAAO;YACPC,cAAc;YACdC,iBAAiB;YACjB3C,WAAW;YACXP,OAAO;QACT;QAEAC,cAAcK,MAAM,CAACsD,iBAAiB;QAEtCC,IAAAA,iBAAM,gBAAC,qBAAC1D;QAER,kCAAkC;QAClC2D,OAAOC,iBAAM,CAACC,SAAS,CAAC,CAAC,SAAS,EAAEV,SAAShC,SAAS,CAAC,CAAC,CAAC,GAAG2C,iBAAiB;QAE7E,eAAe;QACfE,oBAAS,CAACE,KAAK,CAACN,iBAAM,CAACO,SAAS,CAAC,UAAU;YAAE5C,MAAM;QAAS;QAE5D,4BAA4B;QAC5BoC,OAAOC,iBAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;QAE5D,8BAA8B;QAC9B,MAAMM,IAAAA,kBAAO,EAAC;YACZT,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;QACrD;QAEA,+BAA+B;QAC/BH,OAAOC,iBAAM,CAACG,cAAc,CAAC,aAAaD,iBAAiB;QAC3DH,OAAOC,iBAAM,CAACG,cAAc,CAAC,aAAaD,iBAAiB;QAE3D,wBAAwB;QACxBH,OAAO7D,cAAcK,MAAM,EAAEqE,gBAAgB;IAC/C;IAEAtB,GAAG,uCAAuC;QACxCpD,cAAcI,KAAK,CAACoE,iBAAiB,CAAC,IAAIC,MAAM;QAEhDb,IAAAA,iBAAM,gBAAC,qBAAC1D;QAER,eAAe;QACfgE,oBAAS,CAACC,MAAM,CAACL,iBAAM,CAACG,cAAc,CAAC,aAAa;YAClDzB,QAAQ;gBAAEH,OAAO;YAAmB;QACtC;QACA6B,oBAAS,CAACC,MAAM,CAACL,iBAAM,CAACG,cAAc,CAAC,aAAa;YAClDzB,QAAQ;gBAAEH,OAAO;YAAc;QACjC;QAEA,cAAc;QACd6B,oBAAS,CAACE,KAAK,CAACN,iBAAM,CAACO,SAAS,CAAC,UAAU;YAAE5C,MAAM;QAAQ;QAE3D,iBAAiB;QACjB,MAAM6C,IAAAA,kBAAO,EAAC;YACZT,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC7D;QAEA,8BAA8B;QAC9BH,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;IACrD;IAEAZ,GAAG,uCAAuC;QACxC,IAAIuB;QACJ,MAAMC,eAAe,IAAIC,QAAQC,CAAAA;YAC/BH,eAAeG;QACjB;QAEA9E,cAAcI,KAAK,CAAC8C,eAAe,CAAC0B;QAEpChB,IAAAA,iBAAM,gBAAC,qBAAC1D;QAER,eAAe;QACfgE,oBAAS,CAACC,MAAM,CAACL,iBAAM,CAACG,cAAc,CAAC,aAAa;YAClDzB,QAAQ;gBAAEH,OAAO;YAAmB;QACtC;QACA6B,oBAAS,CAACC,MAAM,CAACL,iBAAM,CAACG,cAAc,CAAC,aAAa;YAClDzB,QAAQ;gBAAEH,OAAO;YAAc;QACjC;QAEA,cAAc;QACd6B,oBAAS,CAACE,KAAK,CAACN,iBAAM,CAACO,SAAS,CAAC,UAAU;YAAE5C,MAAM;QAAQ;QAE3D,kCAAkC;QAClCoC,OAAOC,iBAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC3DH,OAAOC,iBAAM,CAACO,SAAS,CAAC,WAAWU,YAAY;QAE/C,gBAAgB;QAChBJ,aAAc;YACZxE,MAAMmD,IAAAA,yBAAc;YACpBP,OAAO;YACPC,cAAc;YACdU,WAAW;QACb;QAEA,sBAAsB;QACtB,MAAMY,IAAAA,kBAAO,EAAC;YACZT,OAAOC,iBAAM,CAACkB,WAAW,CAAC,kBAAkBC,GAAG,CAACjB,iBAAiB;QACnE;IACF;IAEAZ,GAAG,8BAA8B;QAC/B,MAAMC,WAAWC,IAAAA,yBAAc;QAE/B,iCAAiC;QACjC/C,uBAAY,CAACuC,QAAQ,CAAC;YACpB3C,MAAMkD;YACNN,OAAO;YACPC,cAAc;YACdC,iBAAiB;YACjB3C,WAAW;YACXP,OAAO;QACT;QAEA,MAAMmF,sBAAsB;YAC1BnC,OAAO;YACPC,cAAc;YACdU,WAAW;QACb;QAEA1D,cAAcgD,YAAY,CAACW,iBAAiB,CAACuB;QAE7CtB,IAAAA,iBAAM,gBAAC,qBAAC1D;QAER,kCAAkC;QAClC2D,OAAOC,iBAAM,CAACC,SAAS,CAAC,CAAC,SAAS,EAAEV,SAAShC,SAAS,CAAC,CAAC,CAAC,GAAG2C,iBAAiB;QAE7E,mEAAmE;QACnE,MAAM,EAAEhB,YAAY,EAAE,GAAGzC,uBAAY,CAAC4E,QAAQ;QAC9C,MAAMnC;QAEN,6CAA6C;QAC7C,MAAMsB,IAAAA,kBAAO,EAAC;YACZ,MAAMc,QAAQ7E,uBAAY,CAAC4E,QAAQ;YACnCtB,OAAOuB,MAAMrC,KAAK,EAAEsC,IAAI,CAAC;YACzBxB,OAAOuB,MAAMpC,YAAY,EAAEqC,IAAI,CAAC;YAChCxB,OAAOuB,MAAMnC,eAAe,EAAEoC,IAAI,CAAC;QACrC;IACF;IAEAjC,GAAG,4CAA4C;QAC7C,MAAMC,WAAWC,IAAAA,yBAAc;QAE/B,iCAAiC;QACjC/C,uBAAY,CAACuC,QAAQ,CAAC;YACpB3C,MAAMkD;YACNN,OAAO;YACPC,cAAc;YACdC,iBAAiB;YACjB3C,WAAW;YACXP,OAAO;QACT;QAEAC,cAAcgD,YAAY,CAACwB,iBAAiB,CAAC,IAAIC,MAAM;QAEvDb,IAAAA,iBAAM,gBAAC,qBAAC1D;QAER,4CAA4C;QAC5C2D,OAAOC,iBAAM,CAACC,SAAS,CAAC,CAAC,SAAS,EAAEV,SAAShC,SAAS,CAAC,CAAC,CAAC,GAAG2C,iBAAiB;QAE7E,wBAAwB;QACxB,MAAM,EAAEhB,YAAY,EAAE,GAAGzC,uBAAY,CAAC4E,QAAQ;QAC9C,MAAMnC;QAEN,wCAAwC;QACxC,MAAMsB,IAAAA,kBAAO,EAAC;YACZT,OAAOC,iBAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;QACrD;QAEA,0BAA0B;QAC1B,MAAMoB,QAAQ7E,uBAAY,CAAC4E,QAAQ;QACnCtB,OAAOuB,MAAMjF,IAAI,EAAEmF,QAAQ;QAC3BzB,OAAOuB,MAAMrC,KAAK,EAAEuC,QAAQ;QAC5BzB,OAAOuB,MAAMnC,eAAe,EAAEoC,IAAI,CAAC;IACrC;AACF"}