eeebc10469dd6d80a4cdc0fdccd8131a
"use client";
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "Sidebar", {
    enumerable: true,
    get: function() {
        return Sidebar;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _link = /*#__PURE__*/ _interop_require_default(require("next/link"));
const _framermotion = require("framer-motion");
const _lucidereact = require("lucide-react");
const _button = require("../ui/button");
const _mainnav = require("./main-nav");
const _utils = require("../../lib/utils");
const _animations = require("../../lib/animations");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function Sidebar({ collapsed = false, onToggle, className }) {
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_framermotion.motion.div, {
        variants: (0, _animations.getAnimationVariants)(_animations.sidebarVariants),
        animate: collapsed ? "collapsed" : "expanded",
        className: (0, _utils.cn)("flex flex-col h-full bg-card border-r", className),
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "flex items-center justify-between p-4 border-b",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(_link.default, {
                        href: "/dashboard",
                        className: "flex items-center space-x-2",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Shield, {
                                className: "h-8 w-8 text-primary flex-shrink-0"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.AnimatePresence, {
                                children: !collapsed && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_framermotion.motion.div, {
                                    initial: {
                                        opacity: 0,
                                        x: -20
                                    },
                                    animate: {
                                        opacity: 1,
                                        x: 0
                                    },
                                    exit: {
                                        opacity: 0,
                                        x: -20
                                    },
                                    transition: {
                                        duration: 0.2
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                                            className: "font-orbitron font-bold text-lg text-primary",
                                            children: "UAV Control"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-xs text-muted-foreground",
                                            children: "Management System"
                                        })
                                    ]
                                })
                            })
                        ]
                    }),
                    onToggle && /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                        whileHover: {
                            scale: 1.1
                        },
                        whileTap: {
                            scale: 0.9
                        },
                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_button.Button, {
                            variant: "ghost",
                            size: "icon",
                            onClick: onToggle,
                            className: "h-8 w-8",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.motion.div, {
                                    animate: {
                                        rotate: collapsed ? 0 : 180
                                    },
                                    transition: {
                                        duration: 0.2
                                    },
                                    children: collapsed ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.ChevronRight, {
                                        className: "h-4 w-4"
                                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.ChevronLeft, {
                                        className: "h-4 w-4"
                                    })
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                    className: "sr-only",
                                    children: "Toggle sidebar"
                                })
                            ]
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "flex-1 overflow-y-auto py-4",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: (0, _utils.cn)("px-3", collapsed && "px-2"),
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_mainnav.MainNav, {})
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "p-4 border-t",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_framermotion.AnimatePresence, {
                    children: !collapsed && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_framermotion.motion.div, {
                        initial: {
                            opacity: 0,
                            y: 10
                        },
                        animate: {
                            opacity: 1,
                            y: 0
                        },
                        exit: {
                            opacity: 0,
                            y: 10
                        },
                        transition: {
                            duration: 0.2,
                            delay: 0.1
                        },
                        className: "text-xs text-muted-foreground",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                children: "Version 1.0.0"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                children: "\xa9 2024 UAV Systems"
                            })
                        ]
                    })
                })
            })
        ]
    });
}

//# sourceMappingURL=data:application/json;base64,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