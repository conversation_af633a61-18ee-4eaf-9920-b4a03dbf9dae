{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": ["Card", "<PERSON><PERSON><PERSON><PERSON>", "CardDescription", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "React", "forwardRef", "className", "props", "ref", "div", "cn", "displayName", "h3", "p"], "mappings": ";;;;;;;;;;;IA8ESA,IAAI;eAAJA;;IAA0DC,WAAW;eAAXA;;IAAjBC,eAAe;eAAfA;;IAAvBC,UAAU;eAAVA;;IAAZC,UAAU;eAAVA;;IAAwBC,SAAS;eAATA;;;;+DA9EhB;uBAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnB,MAAML,qBAAOM,OAAMC,UAAU,CAG3B,CAAC,EAAEC,SAAS,EAAE,GAAGC,OAAO,EAAEC,oBAC1B,qBAACC;QACCD,KAAKA;QACLF,WAAWI,IAAAA,SAAE,EACX,4DACAJ;QAED,GAAGC,KAAK;;AAGbT,KAAKa,WAAW,GAAG;AAEnB,MAAMT,2BAAaE,OAAMC,UAAU,CAGjC,CAAC,EAAEC,SAAS,EAAE,GAAGC,OAAO,EAAEC,oBAC1B,qBAACC;QACCD,KAAKA;QACLF,WAAWI,IAAAA,SAAE,EAAC,iCAAiCJ;QAC9C,GAAGC,KAAK;;AAGbL,WAAWS,WAAW,GAAG;AAEzB,MAAMR,0BAAYC,OAAMC,UAAU,CAGhC,CAAC,EAAEC,SAAS,EAAE,GAAGC,OAAO,EAAEC,oBAC1B,qBAACI;QACCJ,KAAKA;QACLF,WAAWI,IAAAA,SAAE,EACX,sDACAJ;QAED,GAAGC,KAAK;;AAGbJ,UAAUQ,WAAW,GAAG;AAExB,MAAMX,gCAAkBI,OAAMC,UAAU,CAGtC,CAAC,EAAEC,SAAS,EAAE,GAAGC,OAAO,EAAEC,oBAC1B,qBAACK;QACCL,KAAKA;QACLF,WAAWI,IAAAA,SAAE,EAAC,iCAAiCJ;QAC9C,GAAGC,KAAK;;AAGbP,gBAAgBW,WAAW,GAAG;AAE9B,MAAMZ,4BAAcK,OAAMC,UAAU,CAGlC,CAAC,EAAEC,SAAS,EAAE,GAAGC,OAAO,EAAEC,oBAC1B,qBAACC;QAAID,KAAKA;QAAKF,WAAWI,IAAAA,SAAE,EAAC,YAAYJ;QAAa,GAAGC,KAAK;;AAEhER,YAAYY,WAAW,GAAG;AAE1B,MAAMV,2BAAaG,OAAMC,UAAU,CAGjC,CAAC,EAAEC,SAAS,EAAE,GAAGC,OAAO,EAAEC,oBAC1B,qBAACC;QACCD,KAAKA;QACLF,WAAWI,IAAAA,SAAE,EAAC,8BAA8BJ;QAC3C,GAAGC,KAAK;;AAGbN,WAAWU,WAAW,GAAG"}