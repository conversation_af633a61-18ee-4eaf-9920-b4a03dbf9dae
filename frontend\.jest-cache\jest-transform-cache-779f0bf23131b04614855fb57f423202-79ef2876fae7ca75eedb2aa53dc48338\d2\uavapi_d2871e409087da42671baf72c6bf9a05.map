{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\api\\uav-api.ts"], "sourcesContent": ["import apiClient, { createQueryString } from '@/lib/api-client';\nimport {\n  UAV,\n  CreateUAVRequest,\n  UpdateUAVRequest,\n  UAVFilter,\n  PaginationParams,\n  SystemStatistics,\n  HibernatePodStatus,\n  Region,\n  APIResponse,\n} from '@/types/uav';\n\nexport class UAVApi {\n  private basePath = '/api/uav';\n\n  // Get all UAVs with optional filtering and pagination\n  async getUAVs(filter?: UAVFilter, pagination?: PaginationParams): Promise<UAV[]> {\n    const params = {\n      ...filter,\n      ...pagination,\n    };\n    \n    const queryString = createQueryString(params);\n    const url = queryString ? `${this.basePath}/all?${queryString}` : `${this.basePath}/all`;\n    \n    return apiClient.get<UAV[]>(url);\n  }\n\n  // Get UAV by ID\n  async getUAVById(id: number): Promise<UAV> {\n    return apiClient.get<UAV>(`${this.basePath}/${id}`);\n  }\n\n  // Create new UAV\n  async createUAV(uav: CreateUAVRequest): Promise<APIResponse<UAV>> {\n    return apiClient.post<APIResponse<UAV>>(`${this.basePath}`, uav);\n  }\n\n  // Update existing UAV\n  async updateUAV(id: number, uav: Partial<UpdateUAVRequest>): Promise<APIResponse<UAV>> {\n    return apiClient.put<APIResponse<UAV>>(`${this.basePath}/${id}`, uav);\n  }\n\n  // Delete UAV\n  async deleteUAV(id: number): Promise<APIResponse<void>> {\n    return apiClient.delete<APIResponse<void>>(`${this.basePath}/${id}`);\n  }\n\n  // Update UAV status\n  async updateUAVStatus(id: number): Promise<APIResponse<any>> {\n    return apiClient.put<APIResponse<any>>(`${this.basePath}/${id}/status`);\n  }\n\n  // Validate RFID tag uniqueness\n  async validateRfidTag(rfidTag: string, excludeId?: number): Promise<{ isUnique: boolean }> {\n    const params = excludeId ? { excludeId } : {};\n    const queryString = createQueryString(params);\n    const url = queryString \n      ? `${this.basePath}/validate-rfid/${encodeURIComponent(rfidTag)}?${queryString}`\n      : `${this.basePath}/validate-rfid/${encodeURIComponent(rfidTag)}`;\n    \n    return apiClient.get<{ isUnique: boolean }>(url);\n  }\n\n  // Add region to UAV\n  async addRegionToUAV(uavId: number, regionId: number): Promise<APIResponse<UAV>> {\n    return apiClient.post<APIResponse<UAV>>(`${this.basePath}/${uavId}/regions/${regionId}`);\n  }\n\n  // Remove region from UAV\n  async removeRegionFromUAV(uavId: number, regionId: number): Promise<APIResponse<void>> {\n    return apiClient.delete<APIResponse<void>>(`${this.basePath}/${uavId}/regions/${regionId}`);\n  }\n\n  // Get available regions for UAV (not already assigned)\n  async getAvailableRegionsForUAV(uavId: number): Promise<Region[]> {\n    return apiClient.get<Region[]>(`${this.basePath}/${uavId}/available-regions`);\n  }\n\n  // Get system statistics\n  async getSystemStatistics(): Promise<SystemStatistics> {\n    return apiClient.get<SystemStatistics>(`${this.basePath}/statistics`);\n  }\n\n  // Search UAVs\n  async searchUAVs(query: string): Promise<UAV[]> {\n    const params = { search: query };\n    const queryString = createQueryString(params);\n    return apiClient.get<UAV[]>(`${this.basePath}/search?${queryString}`);\n  }\n\n  // Get UAVs by status\n  async getUAVsByStatus(status: string): Promise<UAV[]> {\n    const params = { status };\n    const queryString = createQueryString(params);\n    return apiClient.get<UAV[]>(`${this.basePath}/all?${queryString}`);\n  }\n\n  // Get UAVs by region\n  async getUAVsByRegion(regionId: number): Promise<UAV[]> {\n    const params = { regionId };\n    const queryString = createQueryString(params);\n    return apiClient.get<UAV[]>(`${this.basePath}/all?${queryString}`);\n  }\n\n  // Bulk operations\n  async bulkUpdateStatus(uavIds: number[], status: string): Promise<APIResponse<void>> {\n    return apiClient.post<APIResponse<void>>(`${this.basePath}/bulk/status`, {\n      uavIds,\n      status,\n    });\n  }\n\n  async bulkDelete(uavIds: number[]): Promise<APIResponse<void>> {\n    return apiClient.post<APIResponse<void>>(`${this.basePath}/bulk/delete`, {\n      uavIds,\n    });\n  }\n\n  // Export UAVs data\n  async exportUAVs(format: 'csv' | 'excel' = 'csv'): Promise<Blob> {\n    const response = await apiClient.get(`${this.basePath}/export?format=${format}`, {\n      responseType: 'blob',\n    });\n    return response;\n  }\n\n  // Import UAVs data\n  async importUAVs(file: File, onProgress?: (progress: number) => void): Promise<APIResponse<any>> {\n    return apiClient.uploadFile<APIResponse<any>>(`${this.basePath}/import`, file, onProgress);\n  }\n}\n\n// Hibernate Pod API\nexport class HibernatePodApi {\n  private basePath = '/api/hibernate-pod';\n\n  // Get hibernate pod status\n  async getStatus(): Promise<HibernatePodStatus> {\n    return apiClient.get<HibernatePodStatus>(`${this.basePath}/status`);\n  }\n\n  // Add UAV to hibernate pod\n  async addUAV(uavId: number): Promise<APIResponse<any>> {\n    return apiClient.post<APIResponse<any>>(`${this.basePath}/add`, { uavId });\n  }\n\n  // Remove UAV from hibernate pod\n  async removeUAV(uavId: number): Promise<APIResponse<any>> {\n    return apiClient.post<APIResponse<any>>(`${this.basePath}/remove`, { uavId });\n  }\n\n  // Get UAVs in hibernate pod\n  async getUAVsInPod(): Promise<UAV[]> {\n    return apiClient.get<UAV[]>(`${this.basePath}/uavs`);\n  }\n\n  // Update hibernate pod capacity\n  async updateCapacity(maxCapacity: number): Promise<APIResponse<any>> {\n    return apiClient.put<APIResponse<any>>(`${this.basePath}/capacity`, { maxCapacity });\n  }\n}\n\n// Region API\nexport class RegionApi {\n  private basePath = '/api/regions';\n\n  // Get all regions\n  async getRegions(): Promise<Region[]> {\n    return apiClient.get<Region[]>(this.basePath);\n  }\n\n  // Get region by ID\n  async getRegionById(id: number): Promise<Region> {\n    return apiClient.get<Region>(`${this.basePath}/${id}`);\n  }\n\n  // Create new region\n  async createRegion(region: { regionName: string }): Promise<APIResponse<Region>> {\n    return apiClient.post<APIResponse<Region>>(this.basePath, region);\n  }\n\n  // Update region\n  async updateRegion(id: number, region: { regionName: string }): Promise<APIResponse<Region>> {\n    return apiClient.put<APIResponse<Region>>(`${this.basePath}/${id}`, region);\n  }\n\n  // Delete region\n  async deleteRegion(id: number): Promise<APIResponse<void>> {\n    return apiClient.delete<APIResponse<void>>(`${this.basePath}/${id}`);\n  }\n\n  // Get UAVs in region\n  async getUAVsInRegion(regionId: number): Promise<UAV[]> {\n    return apiClient.get<UAV[]>(`${this.basePath}/${regionId}/uavs`);\n  }\n}\n\n// Create singleton instances\nexport const uavApi = new UAVApi();\nexport const hibernatePodApi = new HibernatePodApi();\nexport const regionApi = new RegionApi();\n\n// Export default as UAV API for convenience\nexport default uavApi;\n"], "names": ["HibernatePodApi", "RegionApi", "UAVApi", "hibernatePodApi", "regionApi", "uavApi", "getUAVs", "filter", "pagination", "params", "queryString", "createQueryString", "url", "basePath", "apiClient", "get", "getUAVById", "id", "createUAV", "uav", "post", "updateUAV", "put", "deleteUAV", "delete", "updateUAVStatus", "validateRfidTag", "rfidTag", "excludeId", "encodeURIComponent", "addRegionToUAV", "uavId", "regionId", "removeRegionFromUAV", "getAvailableRegionsForUAV", "getSystemStatistics", "searchUAVs", "query", "search", "getUAVsByStatus", "status", "getUAVsByRegion", "bulkUpdateStatus", "uavIds", "bulkDelete", "exportUAVs", "format", "response", "responseType", "importUAVs", "file", "onProgress", "uploadFile", "getStatus", "addUAV", "removeUAV", "getUAVsInPod", "updateCapacity", "maxCapacity", "getRegions", "getRegionById", "createRegion", "region", "updateRegion", "deleteRegion", "getUAVsInRegion"], "mappings": ";;;;;;;;;;;IAuIaA,eAAe;eAAfA;;IA8BAC,SAAS;eAATA;;IAxJAC,MAAM;eAANA;;IA+Lb,4CAA4C;IAC5C,OAAsB;eAAtB;;IAJaC,eAAe;eAAfA;;IACAC,SAAS;eAATA;;IAFAC,MAAM;eAANA;;;mEAxMgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAatC,MAAMH;IAGX,sDAAsD;IACtD,MAAMI,QAAQC,MAAkB,EAAEC,UAA6B,EAAkB;QAC/E,MAAMC,SAAS;YACb,GAAGF,MAAM;YACT,GAAGC,UAAU;QACf;QAEA,MAAME,cAAcC,IAAAA,4BAAiB,EAACF;QACtC,MAAMG,MAAMF,cAAc,CAAC,EAAE,IAAI,CAACG,QAAQ,CAAC,KAAK,EAAEH,YAAY,CAAC,GAAG,CAAC,EAAE,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC;QAExF,OAAOC,kBAAS,CAACC,GAAG,CAAQH;IAC9B;IAEA,gBAAgB;IAChB,MAAMI,WAAWC,EAAU,EAAgB;QACzC,OAAOH,kBAAS,CAACC,GAAG,CAAM,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC,CAAC,EAAEI,GAAG,CAAC;IACpD;IAEA,iBAAiB;IACjB,MAAMC,UAAUC,GAAqB,EAA6B;QAChE,OAAOL,kBAAS,CAACM,IAAI,CAAmB,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,CAAC,EAAEM;IAC9D;IAEA,sBAAsB;IACtB,MAAME,UAAUJ,EAAU,EAAEE,GAA8B,EAA6B;QACrF,OAAOL,kBAAS,CAACQ,GAAG,CAAmB,CAAC,EAAE,IAAI,CAACT,QAAQ,CAAC,CAAC,EAAEI,GAAG,CAAC,EAAEE;IACnE;IAEA,aAAa;IACb,MAAMI,UAAUN,EAAU,EAA8B;QACtD,OAAOH,kBAAS,CAACU,MAAM,CAAoB,CAAC,EAAE,IAAI,CAACX,QAAQ,CAAC,CAAC,EAAEI,GAAG,CAAC;IACrE;IAEA,oBAAoB;IACpB,MAAMQ,gBAAgBR,EAAU,EAA6B;QAC3D,OAAOH,kBAAS,CAACQ,GAAG,CAAmB,CAAC,EAAE,IAAI,CAACT,QAAQ,CAAC,CAAC,EAAEI,GAAG,OAAO,CAAC;IACxE;IAEA,+BAA+B;IAC/B,MAAMS,gBAAgBC,OAAe,EAAEC,SAAkB,EAAkC;QACzF,MAAMnB,SAASmB,YAAY;YAAEA;QAAU,IAAI,CAAC;QAC5C,MAAMlB,cAAcC,IAAAA,4BAAiB,EAACF;QACtC,MAAMG,MAAMF,cACR,CAAC,EAAE,IAAI,CAACG,QAAQ,CAAC,eAAe,EAAEgB,mBAAmBF,SAAS,CAAC,EAAEjB,YAAY,CAAC,GAC9E,CAAC,EAAE,IAAI,CAACG,QAAQ,CAAC,eAAe,EAAEgB,mBAAmBF,SAAS,CAAC;QAEnE,OAAOb,kBAAS,CAACC,GAAG,CAAwBH;IAC9C;IAEA,oBAAoB;IACpB,MAAMkB,eAAeC,KAAa,EAAEC,QAAgB,EAA6B;QAC/E,OAAOlB,kBAAS,CAACM,IAAI,CAAmB,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,CAAC,EAAEkB,MAAM,SAAS,EAAEC,SAAS,CAAC;IACzF;IAEA,yBAAyB;IACzB,MAAMC,oBAAoBF,KAAa,EAAEC,QAAgB,EAA8B;QACrF,OAAOlB,kBAAS,CAACU,MAAM,CAAoB,CAAC,EAAE,IAAI,CAACX,QAAQ,CAAC,CAAC,EAAEkB,MAAM,SAAS,EAAEC,SAAS,CAAC;IAC5F;IAEA,uDAAuD;IACvD,MAAME,0BAA0BH,KAAa,EAAqB;QAChE,OAAOjB,kBAAS,CAACC,GAAG,CAAW,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC,CAAC,EAAEkB,MAAM,kBAAkB,CAAC;IAC9E;IAEA,wBAAwB;IACxB,MAAMI,sBAAiD;QACrD,OAAOrB,kBAAS,CAACC,GAAG,CAAmB,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC,WAAW,CAAC;IACtE;IAEA,cAAc;IACd,MAAMuB,WAAWC,KAAa,EAAkB;QAC9C,MAAM5B,SAAS;YAAE6B,QAAQD;QAAM;QAC/B,MAAM3B,cAAcC,IAAAA,4BAAiB,EAACF;QACtC,OAAOK,kBAAS,CAACC,GAAG,CAAQ,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC,QAAQ,EAAEH,YAAY,CAAC;IACtE;IAEA,qBAAqB;IACrB,MAAM6B,gBAAgBC,MAAc,EAAkB;QACpD,MAAM/B,SAAS;YAAE+B;QAAO;QACxB,MAAM9B,cAAcC,IAAAA,4BAAiB,EAACF;QACtC,OAAOK,kBAAS,CAACC,GAAG,CAAQ,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC,KAAK,EAAEH,YAAY,CAAC;IACnE;IAEA,qBAAqB;IACrB,MAAM+B,gBAAgBT,QAAgB,EAAkB;QACtD,MAAMvB,SAAS;YAAEuB;QAAS;QAC1B,MAAMtB,cAAcC,IAAAA,4BAAiB,EAACF;QACtC,OAAOK,kBAAS,CAACC,GAAG,CAAQ,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC,KAAK,EAAEH,YAAY,CAAC;IACnE;IAEA,kBAAkB;IAClB,MAAMgC,iBAAiBC,MAAgB,EAAEH,MAAc,EAA8B;QACnF,OAAO1B,kBAAS,CAACM,IAAI,CAAoB,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,YAAY,CAAC,EAAE;YACvE8B;YACAH;QACF;IACF;IAEA,MAAMI,WAAWD,MAAgB,EAA8B;QAC7D,OAAO7B,kBAAS,CAACM,IAAI,CAAoB,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,YAAY,CAAC,EAAE;YACvE8B;QACF;IACF;IAEA,mBAAmB;IACnB,MAAME,WAAWC,SAA0B,KAAK,EAAiB;QAC/D,MAAMC,WAAW,MAAMjC,kBAAS,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC,eAAe,EAAEiC,OAAO,CAAC,EAAE;YAC/EE,cAAc;QAChB;QACA,OAAOD;IACT;IAEA,mBAAmB;IACnB,MAAME,WAAWC,IAAU,EAAEC,UAAuC,EAA6B;QAC/F,OAAOrC,kBAAS,CAACsC,UAAU,CAAmB,CAAC,EAAE,IAAI,CAACvC,QAAQ,CAAC,OAAO,CAAC,EAAEqC,MAAMC;IACjF;;aArHQtC,WAAW;;AAsHrB;AAGO,MAAMb;IAGX,2BAA2B;IAC3B,MAAMqD,YAAyC;QAC7C,OAAOvC,kBAAS,CAACC,GAAG,CAAqB,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC,OAAO,CAAC;IACpE;IAEA,2BAA2B;IAC3B,MAAMyC,OAAOvB,KAAa,EAA6B;QACrD,OAAOjB,kBAAS,CAACM,IAAI,CAAmB,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,IAAI,CAAC,EAAE;YAAEkB;QAAM;IAC1E;IAEA,gCAAgC;IAChC,MAAMwB,UAAUxB,KAAa,EAA6B;QACxD,OAAOjB,kBAAS,CAACM,IAAI,CAAmB,CAAC,EAAE,IAAI,CAACP,QAAQ,CAAC,OAAO,CAAC,EAAE;YAAEkB;QAAM;IAC7E;IAEA,4BAA4B;IAC5B,MAAMyB,eAA+B;QACnC,OAAO1C,kBAAS,CAACC,GAAG,CAAQ,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC,KAAK,CAAC;IACrD;IAEA,gCAAgC;IAChC,MAAM4C,eAAeC,WAAmB,EAA6B;QACnE,OAAO5C,kBAAS,CAACQ,GAAG,CAAmB,CAAC,EAAE,IAAI,CAACT,QAAQ,CAAC,SAAS,CAAC,EAAE;YAAE6C;QAAY;IACpF;;aAzBQ7C,WAAW;;AA0BrB;AAGO,MAAMZ;IAGX,kBAAkB;IAClB,MAAM0D,aAAgC;QACpC,OAAO7C,kBAAS,CAACC,GAAG,CAAW,IAAI,CAACF,QAAQ;IAC9C;IAEA,mBAAmB;IACnB,MAAM+C,cAAc3C,EAAU,EAAmB;QAC/C,OAAOH,kBAAS,CAACC,GAAG,CAAS,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC,CAAC,EAAEI,GAAG,CAAC;IACvD;IAEA,oBAAoB;IACpB,MAAM4C,aAAaC,MAA8B,EAAgC;QAC/E,OAAOhD,kBAAS,CAACM,IAAI,CAAsB,IAAI,CAACP,QAAQ,EAAEiD;IAC5D;IAEA,gBAAgB;IAChB,MAAMC,aAAa9C,EAAU,EAAE6C,MAA8B,EAAgC;QAC3F,OAAOhD,kBAAS,CAACQ,GAAG,CAAsB,CAAC,EAAE,IAAI,CAACT,QAAQ,CAAC,CAAC,EAAEI,GAAG,CAAC,EAAE6C;IACtE;IAEA,gBAAgB;IAChB,MAAME,aAAa/C,EAAU,EAA8B;QACzD,OAAOH,kBAAS,CAACU,MAAM,CAAoB,CAAC,EAAE,IAAI,CAACX,QAAQ,CAAC,CAAC,EAAEI,GAAG,CAAC;IACrE;IAEA,qBAAqB;IACrB,MAAMgD,gBAAgBjC,QAAgB,EAAkB;QACtD,OAAOlB,kBAAS,CAACC,GAAG,CAAQ,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC,CAAC,EAAEmB,SAAS,KAAK,CAAC;IACjE;;aA9BQnB,WAAW;;AA+BrB;AAGO,MAAMR,SAAS,IAAIH;AACnB,MAAMC,kBAAkB,IAAIH;AAC5B,MAAMI,YAAY,IAAIH;MAG7B,WAAeI"}