24db6e0f17b5f664c1491bcd2e6f1586
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _testutils = require("../../../lib/test-utils");
const _animatedbutton = require("../animated-button");
const _lucidereact = require("lucide-react");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock framer-motion for testing
(0, _testutils.mockFramerMotion)();
describe("AnimatedButton Component", ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            children: "Animated Button"
        }));
        expect(_testutils.screen.getByRole("button", {
            name: /animated button/i
        })).toBeInTheDocument();
    });
    it("handles click events", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            onClick: handleClick,
            children: "Click me"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    it("shows loading state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            loading: true,
            children: "Loading Button"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeDisabled();
        expect(_testutils.screen.getByTestId("loader")).toBeInTheDocument();
    });
    it("is disabled when disabled prop is true", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            disabled: true,
            children: "Disabled Button"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeDisabled();
    });
    it("does not trigger click when disabled", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            disabled: true,
            onClick: handleClick,
            children: "Disabled"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).not.toHaveBeenCalled();
    });
    it("does not trigger click when loading", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            loading: true,
            onClick: handleClick,
            children: "Loading"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).not.toHaveBeenCalled();
    });
    it("applies custom className", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            className: "custom-class",
            children: "Custom"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("custom-class");
    });
    it("handles ripple effect when enabled", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            ripple: true,
            children: "Ripple Button"
        }));
        const button = _testutils.screen.getByRole("button");
        _testutils.fireEvent.click(button);
        // Ripple effect should be triggered (tested through animation state)
        expect(button).toBeInTheDocument();
    });
    it("respects prefers-reduced-motion", ()=>{
        (0, _testutils.mockPrefersReducedMotion)(true);
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            children: "Reduced Motion"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
    // Animation should be disabled when prefers-reduced-motion is set
    });
    it("supports glow effect", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            glow: true,
            children: "Glow Button"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
    // Glow effect should be applied through CSS classes
    });
    it("supports magnetic effect", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedButton, {
            magnetic: true,
            children: "Magnetic Button"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
    // Magnetic effect should be applied through motion properties
    });
});
describe("FloatingActionButton Component", ()=>{
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.FloatingActionButton, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {
                className: "h-6 w-6"
            })
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
        expect(button).toHaveClass("fixed");
    });
    it("applies correct position classes", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.FloatingActionButton, {
            position: "bottom-right",
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {
                className: "h-6 w-6"
            })
        }));
        let button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("bottom-6", "right-6");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.FloatingActionButton, {
            position: "bottom-left",
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {
                className: "h-6 w-6"
            })
        }));
        button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("bottom-6", "left-6");
    });
    it("handles click events", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.FloatingActionButton, {
            onClick: handleClick,
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {
                className: "h-6 w-6"
            })
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    it("shows tooltip when provided", async ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.FloatingActionButton, {
            tooltip: "Add new item",
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Plus, {
                className: "h-6 w-6"
            })
        }));
        const button = _testutils.screen.getByRole("button");
        _testutils.fireEvent.mouseEnter(button);
        await (0, _testutils.waitFor)(()=>{
            expect(_testutils.screen.getByText("Add new item")).toBeInTheDocument();
        });
    });
});
describe("AnimatedIconButton Component", ()=>{
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: _lucidereact.Plus,
            "aria-label": "Add item"
        }));
        const button = _testutils.screen.getByRole("button", {
            name: /add item/i
        });
        expect(button).toBeInTheDocument();
    });
    it("applies size classes correctly", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: _lucidereact.Plus,
            size: "sm",
            "aria-label": "Small button"
        }));
        let button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("h-8", "w-8");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: _lucidereact.Plus,
            size: "lg",
            "aria-label": "Large button"
        }));
        button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("h-12", "w-12");
    });
    it("applies variant styles correctly", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: _lucidereact.Plus,
            variant: "ghost",
            "aria-label": "Ghost button"
        }));
        let button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("hover:bg-accent");
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: _lucidereact.Plus,
            variant: "outline",
            "aria-label": "Outline button"
        }));
        button = _testutils.screen.getByRole("button");
        expect(button).toHaveClass("border");
    });
    it("handles click events", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: _lucidereact.Plus,
            onClick: handleClick,
            "aria-label": "Clickable button"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    it("is disabled when disabled prop is true", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.AnimatedIconButton, {
            icon: _lucidereact.Plus,
            disabled: true,
            "aria-label": "Disabled button"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeDisabled();
    });
});
describe("ProgressButton Component", ()=>{
    it("renders correctly", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 0,
            children: "Download"
        }));
        expect(_testutils.screen.getByRole("button", {
            name: /download/i
        })).toBeInTheDocument();
    });
    it("shows progress bar", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 50,
            children: "Downloading..."
        }));
        const progressBar = _testutils.screen.getByRole("progressbar");
        expect(progressBar).toBeInTheDocument();
        expect(progressBar).toHaveAttribute("aria-valuenow", "50");
    });
    it("shows completion state", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 100,
            completed: true,
            children: "Completed"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).toBeInTheDocument();
        expect(_testutils.screen.getByTestId("check-icon")).toBeInTheDocument();
    });
    it("handles click events when not in progress", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 0,
            onClick: handleClick,
            children: "Start"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    it("does not handle click events when in progress", ()=>{
        const handleClick = jest.fn();
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 50,
            onClick: handleClick,
            children: "In Progress"
        }));
        _testutils.fireEvent.click(_testutils.screen.getByRole("button"));
        expect(handleClick).not.toHaveBeenCalled();
    });
    it("shows custom icon when provided", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 0,
            icon: _lucidereact.Download,
            children: "Download File"
        }));
        expect(_testutils.screen.getByTestId("download-icon")).toBeInTheDocument();
    });
    it("applies correct progress width", ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 75,
            children: "Progress"
        }));
        const progressBar = _testutils.screen.getByRole("progressbar");
        const progressFill = progressBar.querySelector('[style*="width"]');
        expect(progressFill).toHaveStyle("width: 75%");
    });
    it("handles progress animation", async ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 0,
            children: "Start"
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 50,
            children: "Progress"
        }));
        await (0, _testutils.waitFor)(()=>{
            const progressBar = _testutils.screen.getByRole("progressbar");
            expect(progressBar).toHaveAttribute("aria-valuenow", "50");
        });
    });
    it("resets to initial state when progress is 0", ()=>{
        const { rerender } = (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 100,
            completed: true,
            children: "Done"
        }));
        rerender(/*#__PURE__*/ (0, _jsxruntime.jsx)(_animatedbutton.ProgressButton, {
            progress: 0,
            children: "Start Again"
        }));
        const button = _testutils.screen.getByRole("button");
        expect(button).not.toBeDisabled();
        expect(_testutils.screen.queryByTestId("check-icon")).not.toBeInTheDocument();
    });
});

//# sourceMappingURL=data:application/json;base64,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