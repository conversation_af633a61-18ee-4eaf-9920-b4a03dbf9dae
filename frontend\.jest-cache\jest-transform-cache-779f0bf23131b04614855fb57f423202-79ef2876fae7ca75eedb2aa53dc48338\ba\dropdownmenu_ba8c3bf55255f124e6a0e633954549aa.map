{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\ui\\dropdown-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": ["DropdownMenu", "DropdownMenuCheckboxItem", "DropdownMenuContent", "DropdownMenuGroup", "DropdownMenuItem", "DropdownMenuLabel", "DropdownMenuPortal", "DropdownMenuRadioGroup", "DropdownMenuRadioItem", "DropdownMenuSeparator", "DropdownMenuShortcut", "DropdownMenuSub", "DropdownMenuSubContent", "DropdownMenuSubTrigger", "DropdownMenuTrigger", "DropdownMenuPrimitive", "Root", "<PERSON><PERSON>", "Group", "Portal", "Sub", "RadioGroup", "React", "forwardRef", "className", "inset", "children", "props", "ref", "SubTrigger", "cn", "ChevronRight", "displayName", "SubContent", "sideOffset", "Content", "<PERSON><PERSON>", "checked", "CheckboxItem", "span", "ItemIndicator", "Check", "RadioItem", "Circle", "Label", "Separator"], "mappings": ";;;;;;;;;;;IAsLEA,YAAY;eAAZA;;IAIAC,wBAAwB;eAAxBA;;IAFAC,mBAAmB;eAAnBA;;IAOAC,iBAAiB;eAAjBA;;IANAC,gBAAgB;eAAhBA;;IAGAC,iBAAiB;eAAjBA;;IAIAC,kBAAkB;eAAlBA;;IAIAC,sBAAsB;eAAtBA;;IATAC,qBAAqB;eAArBA;;IAEAC,qBAAqB;eAArBA;;IACAC,oBAAoB;eAApBA;;IAGAC,eAAe;eAAfA;;IACAC,sBAAsB;eAAtBA;;IACAC,sBAAsB;eAAtBA;;IAZAC,mBAAmB;eAAnBA;;;;+DAvLqB;2EACgB;6BACK;uBAEzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnB,MAAMd,eAAee,mBAAsBC,IAAI;AAE/C,MAAMF,sBAAsBC,mBAAsBE,OAAO;AAEzD,MAAMd,oBAAoBY,mBAAsBG,KAAK;AAErD,MAAMZ,qBAAqBS,mBAAsBI,MAAM;AAEvD,MAAMR,kBAAkBI,mBAAsBK,GAAG;AAEjD,MAAMb,yBAAyBQ,mBAAsBM,UAAU;AAE/D,MAAMR,uCAAyBS,OAAMC,UAAU,CAK7C,CAAC,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAE,GAAGC,OAAO,EAAEC,oBAC3C,sBAACb,mBAAsBc,UAAU;QAC/BD,KAAKA;QACLJ,WAAWM,IAAAA,SAAE,EACX,wIACAL,SAAS,QACTD;QAED,GAAGG,KAAK;;YAERD;0BACD,qBAACK,yBAAY;gBAACP,WAAU;;;;AAG5BX,uBAAuBmB,WAAW,GAChCjB,mBAAsBc,UAAU,CAACG,WAAW;AAE9C,MAAMpB,uCAAyBU,OAAMC,UAAU,CAG7C,CAAC,EAAEC,SAAS,EAAE,GAAGG,OAAO,EAAEC,oBAC1B,qBAACb,mBAAsBkB,UAAU;QAC/BL,KAAKA;QACLJ,WAAWM,IAAAA,SAAE,EACX,ybACAN;QAED,GAAGG,KAAK;;AAGbf,uBAAuBoB,WAAW,GAChCjB,mBAAsBkB,UAAU,CAACD,WAAW;AAE9C,MAAM9B,oCAAsBoB,OAAMC,UAAU,CAG1C,CAAC,EAAEC,SAAS,EAAEU,aAAa,CAAC,EAAE,GAAGP,OAAO,EAAEC,oBAC1C,qBAACb,mBAAsBI,MAAM;kBAC3B,cAAA,qBAACJ,mBAAsBoB,OAAO;YAC5BP,KAAKA;YACLM,YAAYA;YACZV,WAAWM,IAAAA,SAAE,EACX,ybACAN;YAED,GAAGG,KAAK;;;AAIfzB,oBAAoB8B,WAAW,GAAGjB,mBAAsBoB,OAAO,CAACH,WAAW;AAE3E,MAAM5B,iCAAmBkB,OAAMC,UAAU,CAKvC,CAAC,EAAEC,SAAS,EAAEC,KAAK,EAAE,GAAGE,OAAO,EAAEC,oBACjC,qBAACb,mBAAsBqB,IAAI;QACzBR,KAAKA;QACLJ,WAAWM,IAAAA,SAAE,EACX,mOACAL,SAAS,QACTD;QAED,GAAGG,KAAK;;AAGbvB,iBAAiB4B,WAAW,GAAGjB,mBAAsBqB,IAAI,CAACJ,WAAW;AAErE,MAAM/B,yCAA2BqB,OAAMC,UAAU,CAG/C,CAAC,EAAEC,SAAS,EAAEE,QAAQ,EAAEW,OAAO,EAAE,GAAGV,OAAO,EAAEC,oBAC7C,sBAACb,mBAAsBuB,YAAY;QACjCV,KAAKA;QACLJ,WAAWM,IAAAA,SAAE,EACX,wOACAN;QAEFa,SAASA;QACR,GAAGV,KAAK;;0BAET,qBAACY;gBAAKf,WAAU;0BACd,cAAA,qBAACT,mBAAsByB,aAAa;8BAClC,cAAA,qBAACC,kBAAK;wBAACjB,WAAU;;;;YAGpBE;;;AAGLzB,yBAAyB+B,WAAW,GAClCjB,mBAAsBuB,YAAY,CAACN,WAAW;AAEhD,MAAMxB,sCAAwBc,OAAMC,UAAU,CAG5C,CAAC,EAAEC,SAAS,EAAEE,QAAQ,EAAE,GAAGC,OAAO,EAAEC,oBACpC,sBAACb,mBAAsB2B,SAAS;QAC9Bd,KAAKA;QACLJ,WAAWM,IAAAA,SAAE,EACX,wOACAN;QAED,GAAGG,KAAK;;0BAET,qBAACY;gBAAKf,WAAU;0BACd,cAAA,qBAACT,mBAAsByB,aAAa;8BAClC,cAAA,qBAACG,mBAAM;wBAACnB,WAAU;;;;YAGrBE;;;AAGLlB,sBAAsBwB,WAAW,GAAGjB,mBAAsB2B,SAAS,CAACV,WAAW;AAE/E,MAAM3B,kCAAoBiB,OAAMC,UAAU,CAKxC,CAAC,EAAEC,SAAS,EAAEC,KAAK,EAAE,GAAGE,OAAO,EAAEC,oBACjC,qBAACb,mBAAsB6B,KAAK;QAC1BhB,KAAKA;QACLJ,WAAWM,IAAAA,SAAE,EACX,qCACAL,SAAS,QACTD;QAED,GAAGG,KAAK;;AAGbtB,kBAAkB2B,WAAW,GAAGjB,mBAAsB6B,KAAK,CAACZ,WAAW;AAEvE,MAAMvB,sCAAwBa,OAAMC,UAAU,CAG5C,CAAC,EAAEC,SAAS,EAAE,GAAGG,OAAO,EAAEC,oBAC1B,qBAACb,mBAAsB8B,SAAS;QAC9BjB,KAAKA;QACLJ,WAAWM,IAAAA,SAAE,EAAC,4BAA4BN;QACzC,GAAGG,KAAK;;AAGblB,sBAAsBuB,WAAW,GAAGjB,mBAAsB8B,SAAS,CAACb,WAAW;AAE/E,MAAMtB,uBAAuB,CAAC,EAC5Bc,SAAS,EACT,GAAGG,OACmC;IACtC,qBACE,qBAACY;QACCf,WAAWM,IAAAA,SAAE,EAAC,8CAA8CN;QAC3D,GAAGG,KAAK;;AAGf;AACAjB,qBAAqBsB,WAAW,GAAG"}