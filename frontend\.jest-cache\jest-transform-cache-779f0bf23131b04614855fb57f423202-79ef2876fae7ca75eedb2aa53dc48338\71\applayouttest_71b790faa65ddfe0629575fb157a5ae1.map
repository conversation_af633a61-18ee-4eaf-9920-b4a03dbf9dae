{"version": 3, "sources": ["D:\\Project\\DaChuangBackend\\frontend\\src\\components\\layout\\__tests__\\app-layout.test.tsx"], "sourcesContent": ["import React from 'react'\nimport { render, screen, fireEvent } from '@/lib/test-utils'\nimport { AppLayout } from '../app-layout'\nimport { mockFramerMotion, runAxeTest } from '@/lib/test-utils'\n\n// Mock the layout components\njest.mock('../header', () => ({\n  Header: ({ onMenuClick }: { onMenuClick?: () => void }) => (\n    <div data-testid=\"header\">\n      <button onClick={onMenuClick} data-testid=\"menu-button\">\n        Menu\n      </button>\n    </div>\n  ),\n}))\n\njest.mock('../sidebar', () => ({\n  Sidebar: ({ collapsed, onToggle }: { collapsed?: boolean; onToggle?: () => void }) => (\n    <div data-testid=\"sidebar\" data-collapsed={collapsed}>\n      <button onClick={onToggle} data-testid=\"sidebar-toggle\">\n        Toggle\n      </button>\n    </div>\n  ),\n}))\n\njest.mock('../page-transition', () => ({\n  PageTransition: ({ children }: { children: React.ReactNode }) => (\n    <div data-testid=\"page-transition\">{children}</div>\n  ),\n}))\n\n// Mock framer-motion\nmockFramerMotion()\n\ndescribe('AppLayout Component', () => {\n  beforeEach(() => {\n    jest.clearAllMocks()\n  })\n\n  it('renders correctly', () => {\n    render(\n      <AppLayout>\n        <div>Test content</div>\n      </AppLayout>\n    )\n\n    expect(screen.getByTestId('header')).toBeInTheDocument()\n    expect(screen.getByTestId('sidebar')).toBeInTheDocument()\n    expect(screen.getByTestId('page-transition')).toBeInTheDocument()\n    expect(screen.getByText('Test content')).toBeInTheDocument()\n  })\n\n  it('applies custom className', () => {\n    render(\n      <AppLayout className=\"custom-class\">\n        <div>Content</div>\n      </AppLayout>\n    )\n\n    const main = screen.getByRole('main')\n    expect(main).toHaveClass('custom-class')\n  })\n\n  it('manages sidebar collapsed state', () => {\n    render(\n      <AppLayout>\n        <div>Content</div>\n      </AppLayout>\n    )\n\n    const sidebar = screen.getByTestId('sidebar')\n    const sidebarToggle = screen.getByTestId('sidebar-toggle')\n\n    // Initially not collapsed\n    expect(sidebar).toHaveAttribute('data-collapsed', 'false')\n\n    // Toggle sidebar\n    fireEvent.click(sidebarToggle)\n    expect(sidebar).toHaveAttribute('data-collapsed', 'true')\n\n    // Toggle again\n    fireEvent.click(sidebarToggle)\n    expect(sidebar).toHaveAttribute('data-collapsed', 'false')\n  })\n\n  it('handles header menu click', () => {\n    render(\n      <AppLayout>\n        <div>Content</div>\n      </AppLayout>\n    )\n\n    const sidebar = screen.getByTestId('sidebar')\n    const menuButton = screen.getByTestId('menu-button')\n\n    // Initially not collapsed\n    expect(sidebar).toHaveAttribute('data-collapsed', 'false')\n\n    // Click menu button\n    fireEvent.click(menuButton)\n    expect(sidebar).toHaveAttribute('data-collapsed', 'true')\n  })\n\n  it('has proper layout structure', () => {\n    render(\n      <AppLayout>\n        <div>Content</div>\n      </AppLayout>\n    )\n\n    // Check main layout structure\n    const container = screen.getByTestId('header').parentElement?.parentElement\n    expect(container).toHaveClass('flex', 'h-screen', 'bg-background')\n\n    // Check main content area\n    const main = screen.getByRole('main')\n    expect(main).toHaveClass('flex-1', 'overflow-y-auto')\n\n    // Check content container\n    const contentContainer = main.firstElementChild\n    expect(contentContainer).toHaveClass('container', 'mx-auto', 'px-4', 'py-6')\n  })\n\n  it('hides sidebar on mobile', () => {\n    render(\n      <AppLayout>\n        <div>Content</div>\n      </AppLayout>\n    )\n\n    const sidebarContainer = screen.getByTestId('sidebar').parentElement\n    expect(sidebarContainer).toHaveClass('hidden', 'md:flex')\n  })\n\n  it('renders children inside page transition', () => {\n    render(\n      <AppLayout>\n        <div data-testid=\"child-content\">Child content</div>\n      </AppLayout>\n    )\n\n    const pageTransition = screen.getByTestId('page-transition')\n    const childContent = screen.getByTestId('child-content')\n    \n    expect(pageTransition).toContainElement(childContent)\n  })\n\n  it('maintains accessibility standards', async () => {\n    const { container } = render(\n      <AppLayout>\n        <h1>Page Title</h1>\n        <p>Page content</p>\n      </AppLayout>\n    )\n\n    // Check for proper semantic structure\n    expect(screen.getByRole('main')).toBeInTheDocument()\n    \n    // Run accessibility tests\n    await runAxeTest(container)\n  })\n\n  it('handles responsive design', () => {\n    render(\n      <AppLayout>\n        <div>Content</div>\n      </AppLayout>\n    )\n\n    // Check responsive classes\n    const mainContentArea = screen.getByTestId('header').parentElement\n    expect(mainContentArea).toHaveClass('flex-1', 'flex', 'flex-col', 'overflow-hidden')\n  })\n\n  it('passes props correctly to child components', () => {\n    render(\n      <AppLayout>\n        <div>Content</div>\n      </AppLayout>\n    )\n\n    const sidebar = screen.getByTestId('sidebar')\n    const header = screen.getByTestId('header')\n\n    // Sidebar should receive collapsed prop\n    expect(sidebar).toHaveAttribute('data-collapsed', 'false')\n    \n    // Header should have menu button (indicating onMenuClick prop was passed)\n    expect(screen.getByTestId('menu-button')).toBeInTheDocument()\n  })\n\n  it('handles keyboard navigation', () => {\n    render(\n      <AppLayout>\n        <div>Content</div>\n      </AppLayout>\n    )\n\n    const menuButton = screen.getByTestId('menu-button')\n    const sidebarToggle = screen.getByTestId('sidebar-toggle')\n\n    // Focus should work on interactive elements\n    menuButton.focus()\n    expect(menuButton).toHaveFocus()\n\n    sidebarToggle.focus()\n    expect(sidebarToggle).toHaveFocus()\n  })\n\n  it('supports nested content structure', () => {\n    render(\n      <AppLayout>\n        <div>\n          <header>Page Header</header>\n          <section>\n            <article>Article content</article>\n          </section>\n          <footer>Page Footer</footer>\n        </div>\n      </AppLayout>\n    )\n\n    expect(screen.getByText('Page Header')).toBeInTheDocument()\n    expect(screen.getByText('Article content')).toBeInTheDocument()\n    expect(screen.getByText('Page Footer')).toBeInTheDocument()\n  })\n\n  it('maintains state consistency', () => {\n    render(\n      <AppLayout>\n        <div>Content</div>\n      </AppLayout>\n    )\n\n    const sidebar = screen.getByTestId('sidebar')\n    const menuButton = screen.getByTestId('menu-button')\n    const sidebarToggle = screen.getByTestId('sidebar-toggle')\n\n    // Both buttons should affect the same state\n    fireEvent.click(menuButton)\n    expect(sidebar).toHaveAttribute('data-collapsed', 'true')\n\n    fireEvent.click(sidebarToggle)\n    expect(sidebar).toHaveAttribute('data-collapsed', 'false')\n\n    fireEvent.click(menuButton)\n    expect(sidebar).toHaveAttribute('data-collapsed', 'true')\n  })\n})\n"], "names": ["jest", "mock", "Header", "onMenuClick", "div", "data-testid", "button", "onClick", "Sidebar", "collapsed", "onToggle", "data-collapsed", "PageTransition", "children", "mockFramerMotion", "describe", "beforeEach", "clearAllMocks", "it", "render", "AppLayout", "expect", "screen", "getByTestId", "toBeInTheDocument", "getByText", "className", "main", "getByRole", "toHaveClass", "sidebar", "sidebarToggle", "toHaveAttribute", "fireEvent", "click", "menuButton", "container", "parentElement", "contentContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "sidebarContainer", "pageTransition", "childContent", "toContainElement", "h1", "p", "runAxeTest", "mainContentArea", "header", "focus", "toHaveFocus", "section", "article", "footer"], "mappings": ";AAKA,6BAA6B;AAC7BA,KAAKC,IAAI,CAAC,aAAa,IAAO,CAAA;QAC5BC,QAAQ,CAAC,EAAEC,WAAW,EAAgC,iBACpD,qBAACC;gBAAIC,eAAY;0BACf,cAAA,qBAACC;oBAAOC,SAASJ;oBAAaE,eAAY;8BAAc;;;IAK9D,CAAA;AAEAL,KAAKC,IAAI,CAAC,cAAc,IAAO,CAAA;QAC7BO,SAAS,CAAC,EAAEC,SAAS,EAAEC,QAAQ,EAAkD,iBAC/E,qBAACN;gBAAIC,eAAY;gBAAUM,kBAAgBF;0BACzC,cAAA,qBAACH;oBAAOC,SAASG;oBAAUL,eAAY;8BAAiB;;;IAK9D,CAAA;AAEAL,KAAKC,IAAI,CAAC,sBAAsB,IAAO,CAAA;QACrCW,gBAAgB,CAAC,EAAEC,QAAQ,EAAiC,iBAC1D,qBAACT;gBAAIC,eAAY;0BAAmBQ;;IAExC,CAAA;;;;;8DA9BkB;2BACwB;2BAChB;;;;;;AA8B1B,qBAAqB;AACrBC,IAAAA,2BAAgB;AAEhBC,SAAS,uBAAuB;IAC9BC,WAAW;QACThB,KAAKiB,aAAa;IACpB;IAEAC,GAAG,qBAAqB;QACtBC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;sBACR,cAAA,qBAAChB;0BAAI;;;QAITiB,OAAOC,iBAAM,CAACC,WAAW,CAAC,WAAWC,iBAAiB;QACtDH,OAAOC,iBAAM,CAACC,WAAW,CAAC,YAAYC,iBAAiB;QACvDH,OAAOC,iBAAM,CAACC,WAAW,CAAC,oBAAoBC,iBAAiB;QAC/DH,OAAOC,iBAAM,CAACG,SAAS,CAAC,iBAAiBD,iBAAiB;IAC5D;IAEAN,GAAG,4BAA4B;QAC7BC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;YAACM,WAAU;sBACnB,cAAA,qBAACtB;0BAAI;;;QAIT,MAAMuB,OAAOL,iBAAM,CAACM,SAAS,CAAC;QAC9BP,OAAOM,MAAME,WAAW,CAAC;IAC3B;IAEAX,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;sBACR,cAAA,qBAAChB;0BAAI;;;QAIT,MAAM0B,UAAUR,iBAAM,CAACC,WAAW,CAAC;QACnC,MAAMQ,gBAAgBT,iBAAM,CAACC,WAAW,CAAC;QAEzC,0BAA0B;QAC1BF,OAAOS,SAASE,eAAe,CAAC,kBAAkB;QAElD,iBAAiB;QACjBC,oBAAS,CAACC,KAAK,CAACH;QAChBV,OAAOS,SAASE,eAAe,CAAC,kBAAkB;QAElD,eAAe;QACfC,oBAAS,CAACC,KAAK,CAACH;QAChBV,OAAOS,SAASE,eAAe,CAAC,kBAAkB;IACpD;IAEAd,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;sBACR,cAAA,qBAAChB;0BAAI;;;QAIT,MAAM0B,UAAUR,iBAAM,CAACC,WAAW,CAAC;QACnC,MAAMY,aAAab,iBAAM,CAACC,WAAW,CAAC;QAEtC,0BAA0B;QAC1BF,OAAOS,SAASE,eAAe,CAAC,kBAAkB;QAElD,oBAAoB;QACpBC,oBAAS,CAACC,KAAK,CAACC;QAChBd,OAAOS,SAASE,eAAe,CAAC,kBAAkB;IACpD;IAEAd,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;sBACR,cAAA,qBAAChB;0BAAI;;;QAIT,8BAA8B;QAC9B,MAAMgC,YAAYd,iBAAM,CAACC,WAAW,CAAC,UAAUc,aAAa,EAAEA;QAC9DhB,OAAOe,WAAWP,WAAW,CAAC,QAAQ,YAAY;QAElD,0BAA0B;QAC1B,MAAMF,OAAOL,iBAAM,CAACM,SAAS,CAAC;QAC9BP,OAAOM,MAAME,WAAW,CAAC,UAAU;QAEnC,0BAA0B;QAC1B,MAAMS,mBAAmBX,KAAKY,iBAAiB;QAC/ClB,OAAOiB,kBAAkBT,WAAW,CAAC,aAAa,WAAW,QAAQ;IACvE;IAEAX,GAAG,2BAA2B;QAC5BC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;sBACR,cAAA,qBAAChB;0BAAI;;;QAIT,MAAMoC,mBAAmBlB,iBAAM,CAACC,WAAW,CAAC,WAAWc,aAAa;QACpEhB,OAAOmB,kBAAkBX,WAAW,CAAC,UAAU;IACjD;IAEAX,GAAG,2CAA2C;QAC5CC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;sBACR,cAAA,qBAAChB;gBAAIC,eAAY;0BAAgB;;;QAIrC,MAAMoC,iBAAiBnB,iBAAM,CAACC,WAAW,CAAC;QAC1C,MAAMmB,eAAepB,iBAAM,CAACC,WAAW,CAAC;QAExCF,OAAOoB,gBAAgBE,gBAAgB,CAACD;IAC1C;IAEAxB,GAAG,qCAAqC;QACtC,MAAM,EAAEkB,SAAS,EAAE,GAAGjB,IAAAA,iBAAM,gBAC1B,sBAACC,oBAAS;;8BACR,qBAACwB;8BAAG;;8BACJ,qBAACC;8BAAE;;;;QAIP,sCAAsC;QACtCxB,OAAOC,iBAAM,CAACM,SAAS,CAAC,SAASJ,iBAAiB;QAElD,0BAA0B;QAC1B,MAAMsB,IAAAA,qBAAU,EAACV;IACnB;IAEAlB,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;sBACR,cAAA,qBAAChB;0BAAI;;;QAIT,2BAA2B;QAC3B,MAAM2C,kBAAkBzB,iBAAM,CAACC,WAAW,CAAC,UAAUc,aAAa;QAClEhB,OAAO0B,iBAAiBlB,WAAW,CAAC,UAAU,QAAQ,YAAY;IACpE;IAEAX,GAAG,8CAA8C;QAC/CC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;sBACR,cAAA,qBAAChB;0BAAI;;;QAIT,MAAM0B,UAAUR,iBAAM,CAACC,WAAW,CAAC;QACnC,MAAMyB,SAAS1B,iBAAM,CAACC,WAAW,CAAC;QAElC,wCAAwC;QACxCF,OAAOS,SAASE,eAAe,CAAC,kBAAkB;QAElD,0EAA0E;QAC1EX,OAAOC,iBAAM,CAACC,WAAW,CAAC,gBAAgBC,iBAAiB;IAC7D;IAEAN,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;sBACR,cAAA,qBAAChB;0BAAI;;;QAIT,MAAM+B,aAAab,iBAAM,CAACC,WAAW,CAAC;QACtC,MAAMQ,gBAAgBT,iBAAM,CAACC,WAAW,CAAC;QAEzC,4CAA4C;QAC5CY,WAAWc,KAAK;QAChB5B,OAAOc,YAAYe,WAAW;QAE9BnB,cAAckB,KAAK;QACnB5B,OAAOU,eAAemB,WAAW;IACnC;IAEAhC,GAAG,qCAAqC;QACtCC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;sBACR,cAAA,sBAAChB;;kCACC,qBAAC4C;kCAAO;;kCACR,qBAACG;kCACC,cAAA,qBAACC;sCAAQ;;;kCAEX,qBAACC;kCAAO;;;;;QAKdhC,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;QACzDH,OAAOC,iBAAM,CAACG,SAAS,CAAC,oBAAoBD,iBAAiB;QAC7DH,OAAOC,iBAAM,CAACG,SAAS,CAAC,gBAAgBD,iBAAiB;IAC3D;IAEAN,GAAG,+BAA+B;QAChCC,IAAAA,iBAAM,gBACJ,qBAACC,oBAAS;sBACR,cAAA,qBAAChB;0BAAI;;;QAIT,MAAM0B,UAAUR,iBAAM,CAACC,WAAW,CAAC;QACnC,MAAMY,aAAab,iBAAM,CAACC,WAAW,CAAC;QACtC,MAAMQ,gBAAgBT,iBAAM,CAACC,WAAW,CAAC;QAEzC,4CAA4C;QAC5CU,oBAAS,CAACC,KAAK,CAACC;QAChBd,OAAOS,SAASE,eAAe,CAAC,kBAAkB;QAElDC,oBAAS,CAACC,KAAK,CAACH;QAChBV,OAAOS,SAASE,eAAe,CAAC,kBAAkB;QAElDC,oBAAS,CAACC,KAAK,CAACC;QAChBd,OAAOS,SAASE,eAAe,CAAC,kBAAkB;IACpD;AACF"}