import React from 'react'
import { render, screen, fireEvent, waitFor } from '@/lib/test-utils'
import InteractiveMap from '../interactive-map'
import { createMockUAV, createMockDockingStation } from '@/lib/test-utils'
import { UAV, DockingStation } from '@/types/uav'

// Mock react-leaflet components (already mocked in jest.setup.js)
// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock the animated map components
jest.mock('../animated-map-components', () => ({
  AnimatedUAVMarker: ({ uav, onSelect }: any) => (
    <div 
      data-testid={`uav-marker-${uav.id}`}
      onClick={() => onSelect(uav)}
    >
      UAV Marker: {uav.rfidTag}
    </div>
  ),
  AnimatedGeofence: ({ name, center, radius, color, type }: any) => (
    <div data-testid={`geofence-${name}`}>
      Geofence: {name}
    </div>
  ),
  AnimatedFlightPath: ({ path }: any) => (
    <div data-testid="flight-path">
      Flight Path
    </div>
  ),
  AnimatedDockingStation: ({ name, position, status, capacity, occupied }: any) => (
    <div
      data-testid={`docking-station-${name}`}
    >
      Docking Station: {name}
    </div>
  ),
}))

describe('InteractiveMap Component', () => {
  const mockUAVs: UAV[] = [
    createMockUAV({
      id: 1,
      rfidTag: 'UAV-001',
      status: 'AUTHORIZED',
      operationalStatus: 'ACTIVE',
      location: { latitude: 40.7128, longitude: -74.0060 },
    }),
    createMockUAV({
      id: 2,
      rfidTag: 'UAV-002',
      status: 'AUTHORIZED',
      operationalStatus: 'READY',
      location: { latitude: 40.7589, longitude: -73.9851 },
    }),
  ]

  const mockDockingStations: DockingStation[] = [
    createMockDockingStation({
      id: 1,
      name: 'Station Alpha',
      location: { latitude: 40.7505, longitude: -73.9934 },
      status: 'AVAILABLE',
    }),
  ]

  const mockRegions = [
    {
      id: 1,
      name: 'Zone A',
      description: 'Authorized zone A',
      coordinates: [
        { latitude: 40.7000, longitude: -74.0200 },
        { latitude: 40.7200, longitude: -74.0200 },
        { latitude: 40.7200, longitude: -73.9800 },
        { latitude: 40.7000, longitude: -73.9800 },
      ],
      isActive: true,
    },
  ]

  const defaultProps = {
    uavs: mockUAVs,
    selectedUAV: null,
    center: [40.7128, -74.0060] as [number, number],
    zoom: 12,
    layers: {
      uavs: true,
      geofences: true,
      dockingStations: true,
      flightPaths: true,
      weather: false,
    },
    onUAVSelect: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders correctly', () => {
    render(<InteractiveMap {...defaultProps} />)
    
    expect(screen.getByTestId('map-container')).toBeInTheDocument()
    expect(screen.getByTestId('tile-layer')).toBeInTheDocument()
  })

  it('renders UAV markers', () => {
    render(<InteractiveMap {...defaultProps} />)
    
    expect(screen.getByTestId('uav-marker-1')).toBeInTheDocument()
    expect(screen.getByTestId('uav-marker-2')).toBeInTheDocument()
    expect(screen.getByText('UAV Marker: UAV-001')).toBeInTheDocument()
    expect(screen.getByText('UAV Marker: UAV-002')).toBeInTheDocument()
  })

  it('renders docking station markers', () => {
    render(<InteractiveMap {...defaultProps} />)

    expect(screen.getByTestId('docking-station-Station Alpha')).toBeInTheDocument()
    expect(screen.getByText('Docking Station: Station Alpha')).toBeInTheDocument()
  })

  it('renders geofences for regions', () => {
    render(<InteractiveMap {...defaultProps} />)

    expect(screen.getByTestId('geofence-Restricted Zone A')).toBeInTheDocument()
    expect(screen.getByText('Geofence: Restricted Zone A')).toBeInTheDocument()
  })

  it('handles UAV selection', () => {
    const onUAVSelect = jest.fn()
    render(<InteractiveMap {...defaultProps} onUAVSelect={onUAVSelect} />)
    
    const uavMarker = screen.getByTestId('uav-marker-1')
    fireEvent.click(uavMarker)
    
    expect(onUAVSelect).toHaveBeenCalledWith(mockUAVs[0])
  })

  it('handles docking station selection', () => {
    const onStationSelect = jest.fn()
    render(<InteractiveMap {...defaultProps} onStationSelect={onStationSelect} />)

    const stationMarker = screen.getByTestId('docking-station-Station Alpha')
    fireEvent.click(stationMarker)

    // Note: The mock component doesn't have click handler, so this test needs to be adjusted
    expect(screen.getByText('Docking Station: Station Alpha')).toBeInTheDocument()
  })

  it('highlights selected UAV', () => {
    const selectedUAV = mockUAVs[0]
    render(<InteractiveMap {...defaultProps} selectedUAV={selectedUAV} />)
    
    const selectedMarker = screen.getByTestId('uav-marker-1')
    expect(selectedMarker).toBeInTheDocument()
    // The selected state would be passed to the AnimatedUAVMarker component
  })

  it('shows flight paths when enabled', () => {
    const flightPaths = [
      {
        id: 1,
        uavId: 1,
        coordinates: [
          { latitude: 40.7128, longitude: -74.0060 },
          { latitude: 40.7589, longitude: -73.9851 },
        ],
        timestamp: new Date().toISOString(),
      },
    ]
    
    render(
      <InteractiveMap 
        {...defaultProps} 
        flightPaths={flightPaths}
        showFlightPaths={true}
      />
    )
    
    expect(screen.getByTestId('flight-path')).toBeInTheDocument()
  })

  it('filters UAVs by status', () => {
    const filteredProps = {
      ...defaultProps,
      uavs: mockUAVs.filter(uav => uav.operationalStatus === 'ACTIVE'),
    }
    
    render(<InteractiveMap {...filteredProps} />)
    
    expect(screen.getByTestId('uav-marker-1')).toBeInTheDocument()
    expect(screen.queryByTestId('uav-marker-2')).not.toBeInTheDocument()
  })

  it('updates map center when prop changes', () => {
    const { rerender } = render(<InteractiveMap {...defaultProps} />)
    
    const newCenter = { latitude: 41.8781, longitude: -87.6298 }
    rerender(<InteractiveMap {...defaultProps} center={newCenter} />)
    
    // Map center update would be handled by the MapContainer component
    expect(screen.getByTestId('map-container')).toBeInTheDocument()
  })

  it('updates zoom level when prop changes', () => {
    const { rerender } = render(<InteractiveMap {...defaultProps} />)
    
    rerender(<InteractiveMap {...defaultProps} zoom={15} />)
    
    // Zoom update would be handled by the MapContainer component
    expect(screen.getByTestId('map-container')).toBeInTheDocument()
  })

  it('handles empty UAV list', () => {
    render(<InteractiveMap {...defaultProps} uavs={[]} />)
    
    expect(screen.getByTestId('map-container')).toBeInTheDocument()
    expect(screen.queryByTestId('uav-marker-1')).not.toBeInTheDocument()
    expect(screen.queryByTestId('uav-marker-2')).not.toBeInTheDocument()
  })

  it('handles empty docking stations list', () => {
    render(<InteractiveMap {...defaultProps} dockingStations={[]} />)
    
    expect(screen.getByTestId('map-container')).toBeInTheDocument()
    expect(screen.queryByTestId('docking-station-1')).not.toBeInTheDocument()
  })

  it('handles empty regions list', () => {
    render(<InteractiveMap {...defaultProps} regions={[]} />)
    
    expect(screen.getByTestId('map-container')).toBeInTheDocument()
    expect(screen.queryByTestId('geofence-1')).not.toBeInTheDocument()
  })

  it('shows loading state', () => {
    render(<InteractiveMap {...defaultProps} loading={true} />)
    
    expect(screen.getByTestId('map-loading')).toBeInTheDocument()
  })

  it('shows error state', () => {
    const errorMessage = 'Failed to load map data'
    render(<InteractiveMap {...defaultProps} error={errorMessage} />)
    
    expect(screen.getByText(errorMessage)).toBeInTheDocument()
    expect(screen.getByRole('alert')).toBeInTheDocument()
  })

  it('supports different map layers', () => {
    render(<InteractiveMap {...defaultProps} mapLayer="satellite" />)
    
    // Different tile layer would be rendered
    expect(screen.getByTestId('tile-layer')).toBeInTheDocument()
  })

  it('handles real-time updates', async () => {
    const { rerender } = render(<InteractiveMap {...defaultProps} />)
    
    const updatedUAVs = [
      ...mockUAVs,
      createMockUAV({
        id: 3,
        rfidTag: 'UAV-003',
        location: { latitude: 40.7300, longitude: -74.0000 },
      }),
    ]
    
    rerender(<InteractiveMap {...defaultProps} uavs={updatedUAVs} />)
    
    await waitFor(() => {
      expect(screen.getByTestId('uav-marker-3')).toBeInTheDocument()
    })
  })

  it('handles UAV location updates', () => {
    const updatedUAVs = mockUAVs.map(uav => 
      uav.id === 1 
        ? { ...uav, location: { latitude: 40.7200, longitude: -74.0100 } }
        : uav
    )
    
    const { rerender } = render(<InteractiveMap {...defaultProps} />)
    rerender(<InteractiveMap {...defaultProps} uavs={updatedUAVs} />)
    
    // Updated location would be reflected in the marker position
    expect(screen.getByTestId('uav-marker-1')).toBeInTheDocument()
  })

  it('supports clustering for many UAVs', () => {
    const manyUAVs = Array.from({ length: 50 }, (_, i) => 
      createMockUAV({
        id: i + 1,
        rfidTag: `UAV-${(i + 1).toString().padStart(3, '0')}`,
        location: { 
          latitude: 40.7128 + (Math.random() - 0.5) * 0.1, 
          longitude: -74.0060 + (Math.random() - 0.5) * 0.1 
        },
      })
    )
    
    render(<InteractiveMap {...defaultProps} uavs={manyUAVs} enableClustering={true} />)
    
    expect(screen.getByTestId('map-container')).toBeInTheDocument()
    // Clustering would be handled by the map library
  })

  it('handles map interaction events', () => {
    const onMapClick = jest.fn()
    render(<InteractiveMap {...defaultProps} onMapClick={onMapClick} />)
    
    const mapContainer = screen.getByTestId('map-container')
    fireEvent.click(mapContainer)
    
    // Map click would be handled by the MapContainer component
    expect(mapContainer).toBeInTheDocument()
  })

  it('supports custom map controls', () => {
    render(
      <InteractiveMap 
        {...defaultProps} 
        showZoomControl={true}
        showScaleControl={true}
        showFullscreenControl={true}
      />
    )
    
    expect(screen.getByTestId('map-container')).toBeInTheDocument()
    // Custom controls would be rendered as part of the map
  })

  it('handles responsive design', () => {
    render(<InteractiveMap {...defaultProps} className="h-96 w-full" />)
    
    const mapContainer = screen.getByTestId('map-container')
    expect(mapContainer.parentElement).toHaveClass('h-96', 'w-full')
  })

  it('supports accessibility features', () => {
    render(
      <InteractiveMap 
        {...defaultProps} 
        aria-label="UAV tracking map"
        role="application"
      />
    )
    
    const mapContainer = screen.getByTestId('map-container')
    expect(mapContainer).toHaveAttribute('aria-label', 'UAV tracking map')
    expect(mapContainer).toHaveAttribute('role', 'application')
  })
})
